#include "TestSocketGateway.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDebug>
#include <QCoreApplication>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QDateTime>
#include <chrono>
#include <thread>

Q_LOGGING_CATEGORY(testSocketGateway, "TestSocketGateway")

TestSocketGateway::TestSocketGateway(QObject* parent)
    : QObject(parent), m_reportTimer(nullptr), m_networkManager(nullptr),
      m_mqttBroker("192.168.88.44"), m_mqttPort(1883), m_deviceId("A320021760") {
    qCInfo(testSocketGateway) << "=== 增强版测试Socket网关 v2.0 ===";
    qCInfo(testSocketGateway) << "Socket通信 + HTTP模拟MQTT发布";

    // 初始化网络管理器
    m_networkManager = new QNetworkAccessManager(this);
}

TestSocketGateway::~TestSocketGateway() {
    Stop();
}

bool TestSocketGateway::Initialize(int port) {
    qCInfo(testSocketGateway) << "开始初始化增强版测试Socket网关...";

    // 初始化Socket服务器
    m_socketServer = std::make_unique<SocketServer>();

    if (!m_socketServer->Initialize(port)) {
        qCCritical(testSocketGateway) << "Socket服务器初始化失败";
        return false;
    }

    // 创建定时器
    m_reportTimer = new QTimer(this);
    connect(m_reportTimer, &QTimer::timeout, this, &TestSocketGateway::OnPeriodicReport);

    // 测试MQTT连接
    TestMQTTConnection();

    qCInfo(testSocketGateway) << "增强版测试Socket网关初始化完成";
    return true;
}

void TestSocketGateway::Run() {
    if (m_running) {
        qCWarning(testSocketGateway) << "网关已在运行中";
        return;
    }
    
    m_running = true;
    
    // 启动Socket服务器线程
    std::thread socketThread([this]() { 
        if (m_socketServer) {
            m_socketServer->Run(); 
        }
    });
    socketThread.detach();
    
    // 启动周期性上报
    m_reportTimer->start(30000); // 30秒间隔
    
    qCInfo(testSocketGateway) << "测试Socket网关启动成功";
    
    // 主循环处理Socket消息
    while (m_running) {
        ProcessSocketMessages();
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

void TestSocketGateway::Stop() {
    if (!m_running) {
        return;
    }
    
    qCInfo(testSocketGateway) << "正在停止测试Socket网关...";
    
    m_running = false;
    
    // 停止定时器
    if (m_reportTimer) {
        m_reportTimer->stop();
    }
    
    // 停止Socket服务器
    if (m_socketServer) {
        m_socketServer->Stop();
    }
    
    qCInfo(testSocketGateway) << "测试Socket网关已停止";
}

bool TestSocketGateway::IsSocketConnected() const {
    return m_socketServer ? m_socketServer->IsConnected() : false;
}

bool TestSocketGateway::IsMQTTConnected() const {
    return m_mqttConnected;
}

// Socket消息处理
void TestSocketGateway::ProcessSocketMessages() {
    if (!m_socketServer) {
        return;
    }
    
    SocketMessage msg;
    while (m_running && m_socketServer->ReceiveMessage(msg)) {
        try {
            qCInfo(testSocketGateway) << "📨 收到Socket消息 - 类型:" << QString::fromStdString(msg.type)
                                     << "大小:" << msg.data.length() << "字节";
            
            emit messageReceived(QString::fromStdString(msg.type), QString::fromStdString(msg.data));
            
            QJsonParseError error;
            QJsonDocument doc = QJsonDocument::fromJson(QByteArray::fromStdString(msg.data), &error);
            if (error.error != QJsonParseError::NoError) {
                qCWarning(testSocketGateway) << "Socket消息JSON解析失败:" << error.errorString();
                continue;
            }
            
            QJsonObject data = doc.object();
            
            // 根据消息类型分发处理
            if (msg.type == "DEVICE_STATUS") {
                HandleDeviceStatusMessage(data);
            } else if (msg.type.find("EVENT_") == 0) {
                HandleEventMessage(msg.type, data);
            } else {
                qCWarning(testSocketGateway) << "未知Socket消息类型:" << QString::fromStdString(msg.type);
            }
            
        } catch (const std::exception& e) {
            qCCritical(testSocketGateway) << "处理Socket消息异常:" << e.what();
        }
    }
}

void TestSocketGateway::HandleDeviceStatusMessage(const QJsonObject& data) {
    qCInfo(testSocketGateway) << "✅ 处理设备状态消息 - 数据点数量:" << data.size();

    // 简单打印一些关键数据点
    if (data.contains("A00002")) {
        qCInfo(testSocketGateway) << "设备运行状态:" << (data["A00002"].toBool() ? "正常生产" : "停止");
    }
    if (data.contains("C00001")) {
        qCInfo(testSocketGateway) << "当前SN:" << data["C00001"].toString();
    }

    // 构建SCADA格式的services消息
    QJsonObject servicesMsg;
    QJsonArray services;

    QJsonObject defaultService;
    defaultService["service_id"] = "DefaultService";
    defaultService["properties"] = data;
    defaultService["event_time"] = QString::fromStdString(GetCurrentTimestamp());

    services.append(defaultService);
    servicesMsg["services"] = services;

    // 发布到MQTT
    QString topic = BuildMQTTTopic("property_report");
    PublishToMQTT(topic, servicesMsg, 1);
}

void TestSocketGateway::HandleEventMessage(const std::string& eventType, const QJsonObject& data) {
    qCInfo(testSocketGateway) << "✅ 处理事件消息 - 类型:" << QString::fromStdString(eventType);

    QString requestId;

    // 解析services数组
    if (data.contains("services") && data["services"].isArray()) {
        QJsonArray services = data["services"].toArray();
        for (const auto& serviceValue : services) {
            QJsonObject service = serviceValue.toObject();
            if (service.contains("properties")) {
                QJsonObject properties = service["properties"].toObject();
                if (properties.contains("sn")) {
                    qCInfo(testSocketGateway) << "事件SN:" << properties["sn"].toString();
                }
            }
            if (service.contains("request_id")) {
                requestId = service["request_id"].toString();
            }
        }

        // 发布事件到MQTT (QoS 2)
        QString topic = BuildMQTTTopic("event_report", requestId);
        PublishToMQTT(topic, data, 2);
    }

    // 模拟发送成功响应
    SocketMessage response;
    response.type = "EVENT_RESPONSE";
    response.requestId = requestId.toStdString();

    QJsonObject responseData;
    responseData["result_code"] = 0;
    responseData["result_message"] = "事件处理成功";
    responseData["request_id"] = requestId;

    QJsonDocument responseDoc(responseData);
    response.data = responseDoc.toJson(QJsonDocument::Compact).toStdString();
    response.timestamp = GetCurrentTimestamp();

    if (m_socketServer && m_socketServer->SendMessage(response)) {
        qCInfo(testSocketGateway) << "📤 已发送事件响应 - RequestId:" << requestId;
    }
}

void TestSocketGateway::OnPeriodicReport() {
    qCInfo(testSocketGateway) << "⏰ 周期性状态报告 - Socket连接:" << (IsSocketConnected() ? "已连接" : "断开")
                             << "MQTT连接:" << (IsMQTTConnected() ? "已连接" : "断开");
    emit connectionStatusChanged(IsSocketConnected());

    // 发送周期性设备状态
    QJsonObject statusData;
    statusData["A00002"] = true;  // 正常生产
    statusData["A00003"] = false; // 运行暂停
    statusData["A00004"] = false; // 设备故障
    statusData["C00001"] = "PERIODIC_REPORT_" + QString::number(QDateTime::currentSecsSinceEpoch());
    statusData["A00014"] = 30.5;  // 运行周期
    statusData["A00015"] = 1250;  // 累计产能

    HandleDeviceStatusMessage(statusData);
}

// MQTT模拟发布方法
void TestSocketGateway::PublishToMQTT(const QString& topic, const QJsonObject& payload, int qos) {
    if (!m_mqttConnected) {
        qCWarning(testSocketGateway) << "MQTT未连接，跳过发布";
        return;
    }

    QJsonDocument doc(payload);
    QString payloadStr = doc.toJson(QJsonDocument::Compact);

    qCInfo(testSocketGateway) << "📤 模拟MQTT发布 - 主题:" << topic
                             << "QoS:" << qos << "大小:" << payloadStr.length() << "字节";

    // 这里可以通过HTTP API或其他方式实际发布到MQTT服务器
    // 目前仅作日志记录
    qCDebug(testSocketGateway) << "MQTT消息内容:" << payloadStr;
}

void TestSocketGateway::TestMQTTConnection() {
    // 简单的连接测试 - 实际项目中可以ping MQTT服务器
    qCInfo(testSocketGateway) << "测试MQTT连接到" << m_mqttBroker << ":" << m_mqttPort;

    // 模拟连接成功
    m_mqttConnected = true;
    qCInfo(testSocketGateway) << "✅ MQTT连接模拟成功";
}

QString TestSocketGateway::BuildMQTTTopic(const QString& topicType, const QString& requestId) {
    QString baseTopic = QString("$oc/devices/%1/sys").arg(m_deviceId);

    if (topicType == "property_report") {
        return baseTopic + "/properties/report";
    } else if (topicType == "event_report") {
        if (!requestId.isEmpty()) {
            return baseTopic + "/events/up/" + requestId;
        } else {
            return baseTopic + "/events/up";
        }
    } else if (topicType == "property_get_response") {
        if (!requestId.isEmpty()) {
            return baseTopic + "/properties/get/response/" + requestId;
        }
    }

    return baseTopic + "/" + topicType;
}

std::string TestSocketGateway::GetCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;

    std::tm* tm = std::localtime(&time_t);
    char buffer[32];
    std::strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", tm);

    return std::string(buffer) + "." + std::to_string(ms.count()).substr(0, 3);
}
