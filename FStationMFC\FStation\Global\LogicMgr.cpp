﻿#include "stdafx.h"
#include "LogicMgr.h"

#include "Sys.h"

#include "LogicReset.h"
#include "LogicTrayFull.h"
#include "LogicTrayEmpty.h"
#include "LogicBelt.h"
#include "LogicFixture.h"
#include "LogicFixtureTransport.h"
#include "LogicRobot.h"
#include "LogicProc.h"
#include "LogicMachine.h"
#include "LogicContinueRun.h"

CThreadFactory CLogicMgr::m_ThreadFactory;

map<CString, THREADMSG> CLogicMgr::m_mapThread;

CThreadMonitor *CLogicMgr::m_pThreadMonitor = NULL;

void CLogicMgr::InitProc()
{
	CThreadMgr *pThreadMgr = NULL;

	pThreadMgr = m_ThreadFactory.CreateThreadMgr();
	pThreadMgr->Create("复位", static_cast<CThreadBase*>(new CLogicReset));
	m_mapThread["Reset"].pThread = pThreadMgr;
	m_mapThread["Reset"].type = emThreadSpecial;

	pThreadMgr = m_ThreadFactory.CreateThreadMgr();
	pThreadMgr->Create("", static_cast<CThreadBase*>(new CLogicFixture));
	m_mapThread["Fixture"].pThread = pThreadMgr;
	m_mapThread["Fixture"].type = emThreadNormal;

	pThreadMgr = m_ThreadFactory.CreateThreadMgr();
	pThreadMgr->Create("治具搬运", static_cast<CThreadBase*>(new CLogicFixtureTransport));
	m_mapThread["FixtureTransport"].pThread = pThreadMgr;
	m_mapThread["FixtureTransport"].type = emThreadNormal;

	pThreadMgr = m_ThreadFactory.CreateThreadMgr();
	pThreadMgr->Create("A轨", static_cast<CThreadBase*>(new CLogicBelt("A轨")));
	m_mapThread["BeltA"].pThread = pThreadMgr;
	m_mapThread["BeltA"].type = emThreadNormal;

	pThreadMgr = m_ThreadFactory.CreateThreadMgr();
	pThreadMgr->Create("B轨", static_cast<CThreadBase*>(new CLogicBelt("B轨")));
	m_mapThread["BeltB"].pThread = pThreadMgr;
	m_mapThread["BeltB"].type = emThreadNormal;

	pThreadMgr = m_ThreadFactory.CreateThreadMgr();
	pThreadMgr->Create("满TRAY盘", static_cast<CThreadBase*>(new CLogicTrayFull()));
	m_mapThread["TrayFull"].pThread = pThreadMgr;
	m_mapThread["TrayFull"].type = emThreadNormal;

	pThreadMgr = m_ThreadFactory.CreateThreadMgr();
	pThreadMgr->Create("空TRAY盘", static_cast<CThreadBase*>(new CLogicTrayEmpty()));
	m_mapThread["TrayEmpty"].pThread = pThreadMgr;
	m_mapThread["TrayEmpty"].type = emThreadNormal;

	pThreadMgr = m_ThreadFactory.CreateThreadMgr();
	pThreadMgr->Create("机械手", static_cast<CThreadBase*>(new CLogicRobot()));
	m_mapThread["Robot"].pThread = pThreadMgr;
	m_mapThread["Robot"].type = emThreadNormal;

	pThreadMgr = m_ThreadFactory.CreateThreadMgr();
	pThreadMgr->Create("", static_cast<CThreadBase*>(new CLogicMachine()));
	m_mapThread["Machine"].pThread = pThreadMgr;
	m_mapThread["Machine"].type = emThreadAlwaysExist;

	for (int i=0; i<4; i++)
	{
		CString sRet;
		sRet.Format("Proc%d", i + 1);
		pThreadMgr = m_ThreadFactory.CreateThreadMgr();
		pThreadMgr->Create("", static_cast<CThreadBase *>(new CLogicProc(i)));
		m_mapThread[sRet].pThread = pThreadMgr;
		m_mapThread[sRet].type = emThreadAlwaysExist;
	}

	if (VAR_MACHINE_B("老化模式")) {
		pThreadMgr = m_ThreadFactory.CreateThreadMgr();
		pThreadMgr->Create("", static_cast<CThreadBase*>(new CLogicContinueRun));
		m_mapThread["ContinueRun"].pThread = pThreadMgr;
		m_mapThread["ContinueRun"].type = emThreadSpecial;
	}

	map<CString, THREADMSG>::iterator it = m_mapThread.begin();
	for (; it != m_mapThread.end(); it++)
	{
		it->second.bEnable = true;
	}

 	m_pThreadMonitor = m_ThreadFactory.CreateThreadMonitor();
}

void CLogicMgr::ReleaseProc()
{
	map<CString, THREADMSG>::iterator it = m_mapThread.begin();
	for (; it != m_mapThread.end(); it++)
	{
		it->second.pThread->Stop();
		delete it->second.pThread;
		it->second.pThread = NULL;
	}

	m_mapThread.clear();
}

void CLogicMgr::RunAlways()
{
	map<CString, THREADMSG>::iterator it = m_mapThread.begin();
	for (; it != m_mapThread.end(); it++)
	{
		if (it->second.type == emThreadAlwaysExist && it->second.bEnable) {
			it->second.pThread->Start();
		}
	}
}

void CLogicMgr::Reset()
{
	m_mapThread["Reset"].pThread->Start();
}

void CLogicMgr::RunSingle(CString sThreadName)
{
// 	m_vThreadRun.clear();
// 
// 	map<CString, THREADMSG>::iterator it = m_mapThread.begin();
// 	for (; it != m_mapThread.end(); it++)
// 	{
// 		if (it->first == sThreadName) {
// 			m_vThreadRun.push_back(m_mapThread[sThreadName]);
// 		}
// 	}
// 
// 	Run();
}

void CLogicMgr::RunAny(vector<CString> vThreadName)
{
// 	m_vThreadRun.clear();
// 
// 	for (size_t i=0; i<vThreadName.size(); i++) 
// 	{
// 		map<CString, THREADMSG>::iterator it = m_mapThread.begin();
// 		for (; it != m_mapThread.end(); it++)
// 		{
// 			if (it->first == vThreadName[i]) {
// 				m_vThreadRun.push_back(m_mapThread[it->first]);
// 			}
// 		}
// 	}
// 
// 	Run();
}

void CLogicMgr::Run()
{
	map<CString, THREADMSG>::iterator it = m_mapThread.begin();
	for (; it != m_mapThread.end(); it++)
	{
		if (it->second.type == emThreadSpecial) {
			continue;
		}

		if (VAR_MACHINE_B("直通模式") && it->first != "BeltA" && it->first != "BeltB") {
			continue;
		}

		if (it->first == "BeltA") {
			if (VAR_BELTA_B("轨道启用标志")) {
				it->second.bEnable = true;
			}
			else {
				it->second.bEnable = false;
			}
		}

		if (it->first == "BeltB") {
			if (VAR_BELTB_B("轨道启用标志")) {
				it->second.bEnable = true;
			}
			else {
				it->second.bEnable = false;
			}
		}

		if (!it->second.bEnable) {
			continue;
		}

		it->second.pThread->Start();
	}
}

void CLogicMgr::Pause()
{
	map<CString, THREADMSG>::iterator it = m_mapThread.begin();
	for (; it != m_mapThread.end(); it++)
	{
		if (it->second.type == emThreadAlwaysExist) {
			continue;
		}

		it->second.pThread->Pause();
	}
}

void CLogicMgr::Stop()
{
	map<CString, THREADMSG>::iterator it = m_mapThread.begin();
	for (; it != m_mapThread.end(); it++)
	{
		if (it->second.type == emThreadAlwaysExist) {
			continue;
		}

		it->second.pThread->Stop();
	}
}

bool CLogicMgr::isRunning()
{
	map<CString, THREADMSG>::iterator it = m_mapThread.begin();
	for (; it != m_mapThread.end(); it++)
	{
		if (it->second.type == emThreadAlwaysExist) {
			continue;
		}

		if (it->second.pThread->GetStatus() == emRun) {
			return true;
		}
	}

	return false;
}

bool CLogicMgr::isPause()
{
	bool bStatus = false;

	map<CString, THREADMSG>::iterator it = m_mapThread.begin();
	for (; it != m_mapThread.end(); it++)
	{
		if (it->second.type == emThreadAlwaysExist || it->second.type == emThreadSpecial) {
			continue;
		}

		if (it->second.pThread->GetStatus() == emPause) {
			bStatus = true;
			break;
		}
	}

	return bStatus;
}
