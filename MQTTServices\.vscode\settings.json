{
    "files.autoGuessEncoding": true,
    // CMake 配置
    "cmake.sourceDirectory": "${workspaceFolder}",
    "cmake.buildDirectory": "${workspaceFolder}/build",
    "cmake.configureOnOpen": true,
    "cmake.buildOnOpen": false,
    "cmake.configureOnEdit": true,
    "cmake.generator": "Visual Studio 17 2022",
    "cmake.preferredGenerators": [
        "Visual Studio 17 2022",
        "Visual Studio 16 2019",
        "Ninja"
    ],
    "cmake.configureArgs": [
        "-DCMAKE_TOOLCHAIN_FILE=D:/vcpkg/scripts/buildsystems/vcpkg.cmake"
    ],
    "cmake.buildArgs": [
        "--config",
        "Debug"
    ],
    "cmake.parallelJobs": 8,
    // C++ IntelliSense 配置
    "C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools",
    "C_Cpp.intelliSenseEngine": "default",
    "C_Cpp.intelliSenseEngineFallback": "enabled",
    "C_Cpp.autocomplete": "default",
    "C_Cpp.errorSquiggles": "enabled",
    "C_Cpp.dimInactiveRegions": true,
    "C_Cpp.enhancedColorization": "enabled",
    "C_Cpp.workspaceParsingPriority": "highest",
    "C_Cpp.maxCachedProcesses": 8,
    "C_Cpp.maxMemory": 4096,
    // 文件关联
    "files.associations": {
        "*.h": "cpp",
        "*.hpp": "cpp",
        "*.cpp": "cpp",
        "*.cc": "cpp",
        "*.cxx": "cpp",
        "*.ui": "xml",
        "*.qrc": "xml",
        "*.pro": "makefile",
        "*.pri": "makefile",
        "CMakeLists.txt": "cmake",
        "*.cmake": "cmake"
    },
    // Qt 相关配置
    "qt.searchPaths": [
        "D:/Qt6"
    ],
    "qt.qmakePath": "D:/Qt6/6.9.1/msvc2022_64/bin/qmake.exe",
    // 格式化控制 - 禁用自动格式化，保留手动格式化
    "editor.formatOnSave": false,
    "editor.formatOnPaste": false,
    "editor.formatOnType": false,
    "editor.autoIndent": "keep",
    "editor.trimAutoWhitespace": false,
    // C++ 特定的格式化设置
    "[cpp]": {
        "editor.formatOnSave": false,
        "editor.formatOnPaste": false,
        "editor.formatOnType": false,
        "editor.autoIndent": "keep",
        "editor.insertSpaces": true,
        "editor.tabSize": 4
    },
    "[cmake]": {
        "editor.insertSpaces": true,
        "editor.tabSize": 2
    },
    // 编辑器优化
    "editor.wordWrap": "on",
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.detectIndentation": true,
    "editor.minimap.enabled": true,
    "editor.codeLens": true,
    "editor.semanticHighlighting.enabled": true,
    // 搜索和导航优化
    "search.exclude": {
        "**/build/**": true,
        "**/build_*/**": true,
        "**/vcpkg_installed/**": true,
        "**/.vs/**": true,
        "**/Debug/**": true,
        "**/Release/**": true,
        "**/*.vcxproj*": true,
        "**/*.sln": true
    },
    "files.exclude": {
        "**/build": true,
        "**/build_*": true,
        "**/.vs": true,
        "**/vcpkg_installed": true
    },
    // Git 配置
    "git.ignoreLimitWarning": true,
    "git.autofetch": true
}