﻿// DialogWarn.cpp : 实现文件
//

#include "stdafx.h"
#include "FStation.h"
#include "DialogWarn.h"
#include "afxdialogex.h"

#include "LogicMgr.h"

// CDialogWarn 对话框

IMPLEMENT_DYNAMIC(CDialogWarn, CDialogEx)

CDialogWarn::CDialogWarn(CString sMsg, CString sMsgEx, bool bWarn, CWnd* pParent /*=NULL*/)
	: CDialogEx(CDialogWarn::IDD, pParent)
{
	m_sMsg = sMsg;

	m_sMsgEx = sMsgEx;

	g_bWarn = bWarn;

	m_bWarn = bWarn;

	m_bResult = false;

	m_bShowFlag = false;
	
	m_font.CreatePointFont(180, "宋体");

	if (m_sMsg.Find("满TRAY盘无料") >= 0) {
		return;
	}

	if (m_sMsg.Find("安全光栅") >= 0) {
		if (!CLogicMgr::isRunning()) {
			return;
		}

		CLogicMgr::Pause();
	}

	g_pDatLog->UpdateErrorLog(sMsg);

	g_nWarnTimes++;

	CDat::UpdateValue("设备故障信息", sMsg);
	CDat::UpdateValue("设备故障开始/结束状态", 1);
	CDat::UpdateValueErrorStart();
}

CDialogWarn::~CDialogWarn()
{
}

void CDialogWarn::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
}

BEGIN_MESSAGE_MAP(CDialogWarn, CDialogEx)
	ON_WM_PAINT()
	ON_WM_DESTROY()
	ON_WM_TIMER()
	ON_WM_CTLCOLOR()
END_MESSAGE_MAP()

// CDialogWarn 消息处理程序

BOOL CDialogWarn::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	if (m_sMsg.Find("安全门") < 0 && m_sMsg.Find("安全光栅") < 0) {
		g_bWarnFlag = true;
	}
	else {
		g_bWarnFlag = false;
	}

	SetTimer(1, 100, NULL);

	SetTimer(3, 10, NULL);

	SetTimer(4, 1000, NULL);

	GetDlgItem(IDC_STATIC1)->SetFont(&m_font);

	return TRUE;
}

void CDialogWarn::OnDestroy()
{
	CDialogEx::OnDestroy();

	KillTimer(1);
	KillTimer(2);
	KillTimer(3);
	KillTimer(4);
	KillTimer(5);

	g_bWarnFlag = false;

	if (g_nWarnTimes > 0 && m_sMsg.Find("满TRAY盘无料") < 0) {
		g_nWarnTimes--;
		CDat::UpdateValue("设备故障信息", m_sMsg);
		CDat::UpdateValue("设备故障开始/结束状态", 0);
		CDat::UpdateValueErrorEnd();
	}

}

void CDialogWarn::OnPaint()
{
	if (!IsIconic()) {
		CPaintDC dc(this);

		CRect rect;

		GetClientRect(&rect);
		dc.FillSolidRect(rect, RGB(153, 217, 234));

		GetDlgItem(IDC_STATIC1)->GetWindowRect(&rect);
		ScreenToClient(rect);

		CFont* pFont = NULL;

		pFont = dc.SelectObject(&m_font);

		dc.SetBkMode(TRANSPARENT);

		dc.SetTextColor(RGB(255, 0, 0));

		dc.DrawText(m_sMsg, -1, rect, DT_LEFT | DT_VCENTER | DT_WORDBREAK);
		
		dc.SelectObject(pFont);

		CDialogEx::OnPaint();
	}
}

void CDialogWarn::OnTimer(UINT_PTR nIDEvent)
{
	switch (nIDEvent)
	{
	case 1:
		if (g_bWarnFlag) {
			if (m_sMsgEx.Find("满TRAY盘检测") >= 0) {
				int nCnt = 0;
				CString sRet;

				sRet = g_pRobot->InBeltAInStatus();
				if (sRet == "On") {
					nCnt++;
				}

				sRet = g_pRobot->InBeltBInStatus();
				if (sRet == "On") {
					nCnt++;
				}

				sRet = g_pRobot->InBeltCInStatus();
				if (sRet == "On") {
					nCnt++;
				}

				if (nCnt > 0) {
					g_bWarnFlag = false;
				}
			}
		}
		break;
	case 2:
		break;
	case 3:
		if (AutoExitCheck()) {
			PostMessage(WM_CLOSE);
		}
		break;
	case 4:
		if (GetForegroundWindow() != this) {
			if (!m_bShowFlag) {
				m_bShowFlag = true;
				SetTimer(5, 8000, NULL);
			}
		}
		else {
			KillTimer(5);
			m_bShowFlag = false;
		}
		break;
	case 5:
		KillTimer(5);
		SetForegroundWindow();
		break;
	default:
		break;
	}

	CDialogEx::OnTimer(nIDEvent);
}


HBRUSH CDialogWarn::OnCtlColor(CDC* pDC, CWnd* pWnd, UINT nCtlColor)
{
	HBRUSH hbr = CDialogEx::OnCtlColor(pDC, pWnd, nCtlColor);

	return hbr;
}

bool CDialogWarn::AutoExitCheck()
{
	CString sRet1, sRet2;

	if (g_bExit) {
		return true;
	}

	if (m_sMsgEx == "A轨测高") {
		sRet1 = g_pBeltA->FixtureOutInPosStatus();
		if (sRet1 == "Off") {
			return true;
		}
	}

	if (m_sMsgEx == "B轨测高") {
		sRet1 = g_pBeltB->FixtureOutInPosStatus();
		if (sRet1 == "Off") {
			return true;
		}
	}

	if (m_sMsgEx == "检测空治具") {
		sRet1 = g_pFixture->InStatus();		

		sRet2 = g_pFixture->InPosStatus();

		if (sRet1 == "On" || sRet2 == "On") {
			return true;
		}

		if (VAR_FIXTURE_B("AGV送料标志")) {
			g_pFixture->UploadBeltOn(VAR_FIXTURE_B("皮带正转方向"));
			return true;
		}
	}

	if (m_sMsgEx == "NG皮带线") {
		sRet1 = g_pMachine->NGBeltSignal3Status();
		if (sRet1 == "Off") {
			return true;
		}
	}

	if (m_sMsgEx == "空TRAY盘检测") {
		sRet1 = g_pTray->EmptyTrayOutOfRangeWarnStatus();
		if (sRet1 == "Off") {
			return true;
		}
	}

	if (m_sMsgEx == "满TRAY盘检测") {
		sRet1 = g_pTray->FullTrayBeltInMaterialStatus();
		if (sRet1 == "On") {
			return true;
		}

		if (VAR_ROBOT_B("主板回放标志")) {
			m_bResult = true;
			return true;
		}
	}

	if (m_sMsgEx == "安全光栅") {
		sRet1 = g_pMachine->SafetyGratingStatus();
		if (sRet1 == "On") {
			return true;
		}
		else {
			CLogicMgr::Pause();
		}
	}

	if (m_sMsgEx == "前安全门") {
		sRet1 = g_pMachine->FrontDoorStatus();
		if (sRet1 == "On") {
			return true;
		}
		else {
			CLogicMgr::Pause();
		}
	}

	if (m_sMsgEx == "后安全门") {
		sRet1 = g_pMachine->BackDoorStatus();
		if (sRet1 == "On") {
			return true;
		}
		else {
			CLogicMgr::Pause();
		}
	}

	if (m_sMsgEx == "左安全门") {
		sRet1 = g_pMachine->LeftDoorStatus();
		if (sRet1 == "On") {
			return true;
		}
		else {
			CLogicMgr::Pause();
		}
	}

	if (m_sMsgEx == "右安全门") {
		sRet1 = g_pMachine->RightDoorStatus();
		if (sRet1 == "On") {
			return true;
		}
		else {
			CLogicMgr::Pause();
		}
	}

	return false;
}

bool CDialogWarn::GetResult()
{
	return m_bResult;
}
