# LogicContinueRun - 连续运行协调线程

## 概述
LogicContinueRun是系统的总协调器，负责协调各个子系统的连续运行，确保整个生产线的流畅运行。

## 类结构
```cpp
class CLogicContinueRun : public CThreadBase {
private:
    CThreadFunc*    m_pFuncTray;            // TRAY老化
    CThreadFunc*    m_pFuncFixture;         // 治具老化
    CThreadFunc*    m_pFuncFixtureThransport; // 治具搬运老化
    CThreadFunc*    m_pFuncBeltA;           // A轨老化
    CThreadFunc*    m_pFuncBeltB;           // B轨老化
};
```

## 主要功能

### 1. 五个并发协调线程
- **Tray线程**: 协调托盘系统连续运行
- **Fixture线程**: 协调治具系统连续运行
- **FixtureTransport线程**: 协调治具传输连续运行
- **BeltA/B线程**: 协调A/B传送带连续运行

### 2. 系统级控制
```cpp
// 启动时同时启动托盘子系统
CLogicMgr::m_mapThread["TrayFull"].pThread->Start();
CLogicMgr::m_mapThread["TrayEmpty"].pThread->Start();

// 停止时统一停止相关系统
CLogicMgr::m_mapThread["TrayFull"].pThread->Stop();
CLogicMgr::m_mapThread["TrayEmpty"].pThread->Stop();
```

### 3. 主循环流程
```
OnContinue00() → Tray协调
OnContinue01() → Fixture协调  
OnContinue02() → FixtureTransport协调
OnContinue03() → BeltA协调
OnContinue04() → BeltB协调
```

## 安全停止机制
在Pause和Stop时会安全停止所有相关设备：
- 皮带停止
- 轴运动停止
- 子线程状态同步

## 设计意义
- 作为系统的"指挥中心"
- 确保各模块协调一致
- 提供系统级的启停控制
- 实现生产线的连续高效运行 