﻿#include "stdafx.h"
#include "LogicMachine.h"

#include "Sys.h"
#include "LogicMgr.h"

CLogicMachine::CLogicMachine()
{
	m_pFuncNGBelt = CLogicMgr::m_ThreadFactory.CreateThreadFunc("");

	m_pFuncButton = CLogicMgr::m_ThreadFactory.CreateThreadFunc("");
}

CLogicMachine::~CLogicMachine()
{
}

EnumStatus CLogicMachine::OnSafeCheck()
{
	Sleep(1);
	return emRun;
}

EnumStatus CLogicMachine::OnStart()
{
	return emRun;
}

EnumStatus CLogicMachine::OnPause()
{
	m_pMachine->NGBeltOff();
	m_pMachine->NGBeltLightOff();

	m_pFuncButton->SetStatus(emPause);
	m_pFuncNGBelt->SetStatus(emPause);

	return emRun;
}

EnumStatus CLogicMachine::OnResume()
{
	map<CString, DWORD>::iterator it = m_mapTick.begin();
	for (; it!=m_mapTick.end(); it++)
	{
		it->second = GetTickCount();
	}

	return emRun;
}

EnumStatus CLogicMachine::OnStop()
{
	m_pMachine->NGBeltOff();
	m_pMachine->NGBeltLightOff();

	m_pFuncButton->SetStatus(emStop);
	m_pFuncNGBelt->SetStatus(emStop);

	return emRun;
}

CStatus CLogicMachine::OnRun()
{
	m_mapFlag["安全光栅"] = true;
	m_mapFlag["左安全门"] = true;
	m_mapFlag["右安全门"] = true;
	m_mapFlag["前安全门"] = true;
	m_mapFlag["后安全门"] = true;
	m_mapFlag["急停信号"] = true;

	m_pFuncNGBelt->SetAction(static_cast<THREAD_FUNC>(&CLogicMachine::OnNGBelt00));

	m_pFuncButton->SetAction(static_cast<THREAD_FUNC>(&CLogicMachine::OnButton00));

	RETURN_STATE(&CLogicMachine::OnMachine00, true);
}

CStatus CLogicMachine::OnMachine00()
{
	EnumStatus  status = m_pFuncButton->Run(this);

	switch (status)
	{
	case emRun:
		break;
	case emStop:
		RETURN_STOP();
	case emPause:
		RETURN_PAUSE(NULL);
	default:
		break;
	}

	RETURN_STATE(&CLogicMachine::OnMachine01, false);
}

CStatus CLogicMachine::OnMachine01()
{
	EnumStatus  status = m_pFuncNGBelt->Run(this);

	switch (status)
	{
	case emRun:
		break;
	case emStop:
		RETURN_STOP();
	case emPause:
		RETURN_PAUSE(NULL);
	default:
		break;
	}

	RETURN_STATE(&CLogicMachine::OnMachine00, false);
}

CStatus CLogicMachine::OnNGBelt00()
{
	RUN_STOP_IF_ERROR(m_pMachine->NGBeltSignal1Status());

	if (m_sRet != "On") {
		VAR_MACHINE("NG皮带允许放料标志") = true;
		RETURN_SELF("", false);
	}

	VAR_MACHINE("NG皮带允许放料标志") = false;
	
	RETURN_STATE(&CLogicMachine::OnNGBelt01, true);
}

CStatus CLogicMachine::OnNGBelt01()
{
	if (!VAR_MACHINE_B("NG皮带放料完成标志")) {
		RETURN_SELF("", false);
	}

	RETURN_STATE(&CLogicMachine::OnNGBelt02, true);
}

CStatus CLogicMachine::OnNGBelt02()
{
	RUN_STOP_IF_ERROR(m_pMachine->NGBeltSignal3Status());

	if (m_sRet == "On") {
		MESSAGEBOX("请取走NG皮带线尾端NG主板", "NG皮带线", true);
		RETURN_SELF("", false);
	}

	m_mapTick["NG皮带转动计时"] = GetTickCount();

	RETURN_STATE(&CLogicMachine::OnNGBelt03, true);
}

CStatus CLogicMachine::OnNGBelt03()
{
	RUN_STOP_IF_ERROR(m_pMachine->NGBeltOn(!VAR_MACHINE_B("NG皮带正转方向")));

	do 
	{
		RUN_STOP_IF_ERROR(m_pMachine->NGBeltSignal3Status());
		if (m_sRet == "On") {
			break;
		}

		if (GetTickCount() - m_mapTick["NG皮带转动计时"] < VAR_MACHINE_I("NG皮带转动延时")) {
			RETURN_SELF("", false);
		}
	} while (false);

	RUN_STOP_IF_ERROR(m_pMachine->NGBeltOff());

	RETURN_STATE(&CLogicMachine::OnNGBelt00, true);
}

CStatus CLogicMachine::OnButton00()
{
	RUN_STOP_IF_ERROR(m_pMachine->EmergencyStatus());

	if (m_sRet != "On") {
		CSys::m_bInit = false;
		if (m_mapFlag["急停信号"]) {
			m_mapFlag["急停信号"] = false;
			REPORT("急停被按下", emLogLevelError);
		}
	}
	else {
		m_mapFlag["急停信号"] = true;
	}

	RETURN_STATE(&CLogicMachine::OnButton01, false);
}

CStatus CLogicMachine::OnButton01()
{
	RUN_STOP_IF_ERROR(m_pMachine->SafetyGratingStatus());
	if (m_sRet != "On") {
		CLogicMgr::Pause();
		MESSAGEBOX("安全光栅被触发", "安全光栅", true);
		if (m_mapFlag["安全光栅"]) {
			m_mapFlag["安全光栅"] = false;
			REPORT("安全光栅被触发", emLogLevelWarn);
			g_bWarnFlag = false;
		}
	}
	else {
		m_mapFlag["安全光栅"] = true;
	}

	RETURN_STATE(&CLogicMachine::OnButton02, false);
}

CStatus CLogicMachine::OnButton02()
{
	RUN_STOP_IF_ERROR(m_pMachine->LeftDoorStatus());
	if (m_sRet != "On") {
		CLogicMgr::Pause();
		MESSAGEBOX("左安全门被打开", "左安全门", true);
		if (m_mapFlag["左安全门"]) {
			m_mapFlag["左安全门"] = false;
			REPORT("左安全门被打开", emLogLevelWarn);
			g_bWarnFlag = false;
		}
	}
	else {
		m_mapFlag["左安全门"] = true;
	}

	RETURN_STATE(&CLogicMachine::OnButton03, false);
}

CStatus CLogicMachine::OnButton03()
{
	RUN_STOP_IF_ERROR(m_pMachine->RightDoorStatus());
	if (m_sRet != "On") {
		CLogicMgr::Pause();
		MESSAGEBOX("右安全门被打开", "右安全门", true);
		if (m_mapFlag["右安全门"]) {
			m_mapFlag["右安全门"] = false;
			REPORT("右安全门被打开", emLogLevelWarn);
			g_bWarnFlag = false;
		}
	}
	else {
		m_mapFlag["右安全门"] = true;
	}

	RETURN_STATE(&CLogicMachine::OnButton04, false);
}

CStatus CLogicMachine::OnButton04()
{
	RUN_STOP_IF_ERROR(m_pMachine->FrontDoorStatus());
	if (m_sRet != "On") {
		CLogicMgr::Pause();
		MESSAGEBOX("前安全门被打开", "前安全门", true);
		if (m_mapFlag["前安全门"]) {
			m_mapFlag["前安全门"] = false;
			REPORT("前安全门被打开", emLogLevelWarn);
			g_bWarnFlag = false;
		}
	}
	else {
		m_mapFlag["前安全门"] = true;
	}

	RETURN_STATE(&CLogicMachine::OnButton05, false);
}

CStatus CLogicMachine::OnButton05()
{
	RUN_STOP_IF_ERROR(m_pMachine->BackDoorStatus());
	if (m_sRet != "On") {
		CLogicMgr::Pause();
		MESSAGEBOX("后安全门被打开", "后安全门", true);
		if (m_mapFlag["后安全门"]) {
			m_mapFlag["后安全门"] = false;
			REPORT("后安全门被打开", emLogLevelWarn);
			g_bWarnFlag = false;
		}
	}
	else {
		m_mapFlag["后安全门"] = true;
	}

	RETURN_STATE(&CLogicMachine::OnButton06, false);
}

CStatus CLogicMachine::OnButton06()
{
	RUN_STOP_IF_ERROR(m_pMachine->BootStatus());

	if (m_sRet != "On") {
		if (m_mapFlag["启动按钮"]) {
			m_mapFlag["启动按钮"] = false;
			CLogicMgr::Run();
		}
	}
	else {
		m_mapFlag["启动按钮"] = true;
	}

	RETURN_STATE(&CLogicMachine::OnButton07, false);
}

CStatus CLogicMachine::OnButton07()
{
	RETURN_STATE(&CLogicMachine::OnButton08, false);
}

CStatus CLogicMachine::OnButton08()
{
	CString sRet1, sRet2;

	RUN_STOP_IF_ERROR(m_pMachine->NGBeltSignal3Status());
	sRet1 = m_sRet;

	RUN_STOP_IF_ERROR(m_pMachine->NGBeltBootStatus());
	sRet2 = m_sRet;

	if (sRet1 == "On" || sRet2 == "Off") {
		RUN_STOP_IF_ERROR(m_pMachine->NGBeltOff());
		RUN_STOP_IF_ERROR(m_pMachine->NGBeltLightOff());
	}
	else {
		RUN_STOP_IF_ERROR(m_pMachine->NGBeltLightOn());
		RUN_STOP_IF_ERROR(m_pMachine->NGBeltOn(!VAR_MACHINE_B("NG皮带正转方向")));
	}

	RETURN_STATE(&CLogicMachine::OnButton00, false);
}
