﻿#pragma once

#include "Resource.h"
#include "afxcmn.h"

#include "ColorList.h"

// CDialogCamera 对话框

class CDialogCamera : public CDialogEx
{
	DECLARE_DYNAMIC(CDialogCamera)

public:
	CDialogCamera(CWnd* pParent = NULL);   // 标准构造函数
	virtual ~CDialogCamera();

// 对话框数据
	enum { IDD = IDD_DIALOG_CAMERA };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持
	virtual BOOL DestroyWindow();

	DECLARE_MESSAGE_MAP()
public:
	virtual BOOL OnInitDialog();
	afx_msg void OnTimer(UINT_PTR nIDEvent);
	afx_msg LRESULT OnUmRightMove(WPARAM wParam, LPARAM lParam);
	afx_msg void OnHScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar);
	afx_msg LRESULT OnUmDrawFinish(WPARAM wParam, LPARAM lParam);
	afx_msg void OnNMDblclkList1(NMHDR *pNMHDR, LRESULT *pResult);
	afx_msg void OnNMClickList1(NMHDR *pNMHDR, LRESULT *pResult);
	afx_msg void OnEnKillfocusEdit4();
	afx_msg void OnBnClickedRadio1();
	afx_msg void OnBnClickedRadio2();
	afx_msg void OnBnClickedRadio3();
	afx_msg void OnBnClickedRadio4();
	afx_msg void OnBnClickedRadio5();
	afx_msg void OnBnClickedRadio6();
	afx_msg void OnBnClickedButton1();
	afx_msg void OnBnClickedButton2();
	afx_msg void OnBnClickedButton3();
	afx_msg void OnBnClickedButton4();
	afx_msg void OnBnClickedButton5();
	afx_msg void OnBnClickedButton6();
	afx_msg void OnBnClickedButton7();
	afx_msg void OnBnClickedButton8();
	afx_msg void OnBnClickedButton9();
	afx_msg void OnBnClickedButton10();
	afx_msg void OnBnClickedButton11();
	afx_msg void OnBnClickedButton12();
	afx_msg void OnBnClickedButton13();
	afx_msg void OnBnClickedButton14();

public:
	CImageWindow*	m_pImageWindow;
	CSliderCtrl		m_slider1;
	CSliderCtrl		m_slider2;

	CImageList		m_imageList;

	CColorList		m_list;

	CEdit			m_edit;

	static bool		m_bHasOne;

private:
	int				m_nCamera;

	int				m_nRowForEdit;
	int				m_nColForEdit;
public:
	afx_msg void OnBnClickedButton15();
};
