﻿#pragma once

// VS2010 MFC兼容性头文件
#include <string>
#include <map>
#include "json/json.h"


/**
 * SCADA命令处理器
 * 
 * 功能：
 * - 处理MQTT网关转发的SCADA标准命令
 * - 支持5种标准命令类型
 * - 完整的错误处理和响应机制
 * - VS2010兼容设计
 */
class CScadaCommandHandler
{
public:
    /**
     * 命令执行结果
     */
    struct CommandResult {
        int resultCode;           // 结果码：0=成功，负数=失败
        std::string resultMessage; // 结果消息
        std::string responseData;   // 响应数据（JSON格式）
        Json::Value data;         // 响应数据对象
        bool success;             // 执行成功标志

        CommandResult() : resultCode(-1), success(false) {}
        CommandResult(int code, const std::string& message)
            : resultCode(code), resultMessage(message), success(code == 0) {}
    };

    /**
     * 命令类型枚举
     */
    enum CommandType {
        COMMAND_UNKNOWN = 0,
        COMMAND_SN_DELIVER = 1,         // SN下发
        COMMAND_BOP_DELIVER = 2,        // 转产/下发生产参数
        COMMAND_PAUSE = 3,              // 暂停生产
        COMMAND_PRODUCTION = 4,         // 恢复生产
        COMMAND_MQTT_CONFIG_DELIVER = 5 // MQTT配置下发
    };

private:
    // 单例实例
    static CScadaCommandHandler* m_pInstance;
    static CRITICAL_SECTION m_instanceMutex;
    static bool m_instanceMutexInitialized;
    
    // 初始化状态
    bool m_bInitialized;
    
    // 命令类型映射
    std::map<std::string, CommandType> m_commandTypeMap;
    
    // 当前执行的命令信息
    std::string m_currentRequestId;
    CommandType m_currentCommandType;
    Json::Value m_currentCommandData;

public:
    // 单例访问
    static CScadaCommandHandler* GetInstance();
    static void DestroyInstance();
    
    // 初始化和清理
    bool Initialize();
    void Cleanup();
    bool IsInitialized() const { return m_bInitialized; }
    
    /**
     * 处理SCADA命令
     * @param commandJson SCADA标准命令JSON字符串
     * @param requestId 请求ID
     * @return 命令执行结果
     */
    CommandResult ProcessCommand(const CString& commandJson, const CString& requestId);
    
    /**
     * 处理Socket转发的命令
     * @param socketMessage Socket消息JSON字符串
     * @return 命令执行结果
     */
    CommandResult ProcessSocketCommand(const std::string& socketMessage);

private:
    // 构造函数私有
    CScadaCommandHandler();
    ~CScadaCommandHandler();
    
    // 禁用拷贝构造
    CScadaCommandHandler(const CScadaCommandHandler&);
    CScadaCommandHandler& operator=(const CScadaCommandHandler&);
    
    // 命令类型解析
    CommandType ParseCommandType(const std::string& commandName);
    
    // 具体命令处理方法
    CommandResult HandleSnDeliverCommand(const Json::Value& properties);
    CommandResult HandleBopDeliverCommand(const Json::Value& properties);
    CommandResult HandlePauseCommand(const Json::Value& properties);
    CommandResult HandleProductionCommand(const Json::Value& properties);
    CommandResult HandleMqttConfigDeliverCommand(const Json::Value& properties);
    
    // 参数验证
    bool ValidateSnDeliverParams(const Json::Value& properties, std::string& errorMsg);
    bool ValidateBopDeliverParams(const Json::Value& properties, std::string& errorMsg);
    bool ValidatePauseParams(const Json::Value& properties, std::string& errorMsg);
    bool ValidateProductionParams(const Json::Value& properties, std::string& errorMsg);
    bool ValidateMqttConfigParams(const Json::Value& properties, std::string& errorMsg);
    
    // 工具方法
    std::string JsonValueToString(const Json::Value& value);
    CString StdStringToCString(const std::string& str);
    std::string CStringToStdString(const CString& str);
    
    // 日志方法
    void LogInfo(const CString& message);
    void LogWarning(const CString& message);
    void LogError(const CString& message);
    
    // 响应构建
    std::string BuildSuccessResponse(const std::string& message = "命令执行成功");
    std::string BuildErrorResponse(int errorCode, const std::string& errorMessage);
    
    // 业务逻辑接口
    bool SetCurrentSN(const std::string& sn, const std::string& productionModel);
    bool SetProductionParameters(const Json::Value& parameters);
    bool PauseProduction(const std::string& reason);
    bool ResumeProduction();
    bool UpdateMqttConfig(const Json::Value& config);
    
    // 状态检查
    bool IsProductionRunning();
    bool IsProductionPaused();
    std::string GetCurrentProductionModel();
};

// 全局访问函数
CScadaCommandHandler* GetScadaCommandHandler();

// Socket命令处理回调函数（在FStationDlg.h中声明，这里实现）
int OnSocketCommandReceived(const CString& command, const CString& params, const CString& requestId);
