#!/usr/bin/env python3
"""
测试 MQTT 网关属性查询功能
模拟 SCADA 平台发送属性查询请求
"""

import paho.mqtt.client as mqtt
import json
import time
import sys

# MQTT 配置
MQTT_BROKER = "192.168.88.44"
MQTT_PORT = 1883
DEVICE_ID = "A320021760"

# 认证信息（需要根据实际情况调整）
CLIENT_ID = f"test_client_{int(time.time())}"
USERNAME = "test_user"
PASSWORD = "test_password"

def on_connect(client, userdata, flags, rc):
    """连接回调"""
    if rc == 0:
        print(f"✅ 连接成功到 MQTT Broker: {MQTT_BROKER}:{MQTT_PORT}")
        
        # 订阅响应主题
        response_topic = f"$oc/devices/{DEVICE_ID}/sys/properties/get/response/+"
        client.subscribe(response_topic, qos=2)
        print(f"📥 订阅响应主题: {response_topic}")
        
    else:
        print(f"❌ 连接失败，返回码: {rc}")

def on_message(client, userdata, msg):
    """消息接收回调"""
    print(f"📨 收到响应消息:")
    print(f"   主题: {msg.topic}")
    print(f"   QoS: {msg.qos}")
    print(f"   大小: {len(msg.payload)} 字节")
    
    try:
        payload = json.loads(msg.payload.decode())
        print(f"   内容: {json.dumps(payload, indent=2, ensure_ascii=False)}")
    except:
        print(f"   原始内容: {msg.payload}")

def on_subscribe(client, userdata, mid, granted_qos):
    """订阅成功回调"""
    print(f"📥 订阅成功，QoS: {granted_qos}")

def send_property_query(client, request_id="test123"):
    """发送属性查询请求"""
    query_topic = f"$oc/devices/{DEVICE_ID}/sys/properties/get/request_id={request_id}"

    # 空消息体（按照协议要求）
    payload = {}

    print(f"📤 发送属性查询请求:")
    print(f"   主题: {query_topic}")
    print(f"   RequestId: {request_id}")
    print(f"   消息体: {json.dumps(payload)}")

    result = client.publish(query_topic, json.dumps(payload), qos=2)

    if result.rc == mqtt.MQTT_ERR_SUCCESS:
        print(f"✅ 属性查询请求发送成功")
    else:
        print(f"❌ 属性查询请求发送失败，错误码: {result.rc}")

def test_idempotency(client):
    """测试幂等性功能"""
    print("\n=== 幂等性测试 ===")

    # 使用相同的request_id发送多次请求
    test_request_id = f"idempotency_test_{int(time.time())}"

    print(f"🔄 使用相同RequestId发送3次请求: {test_request_id}")

    # 第一次请求
    print("\n📤 第1次请求:")
    send_property_query(client, test_request_id)
    time.sleep(2)

    # 第二次请求（应该返回缓存结果）
    print("\n📤 第2次请求 (应该返回缓存):")
    send_property_query(client, test_request_id)
    time.sleep(2)

    # 第三次请求（应该返回缓存结果）
    print("\n📤 第3次请求 (应该返回缓存):")
    send_property_query(client, test_request_id)
    time.sleep(2)

def main():
    """主函数"""
    print("=== MQTT 网关属性查询测试工具 ===")
    print(f"目标设备: {DEVICE_ID}")
    print(f"MQTT Broker: {MQTT_BROKER}:{MQTT_PORT}")
    print()
    
    # 创建 MQTT 客户端
    client = mqtt.Client(CLIENT_ID)
    
    # 设置回调函数
    client.on_connect = on_connect
    client.on_message = on_message
    client.on_subscribe = on_subscribe
    
    # 设置认证（如果需要）
    # client.username_pw_set(USERNAME, PASSWORD)
    
    try:
        # 连接到 MQTT Broker
        print(f"🔗 正在连接到 {MQTT_BROKER}:{MQTT_PORT}...")
        client.connect(MQTT_BROKER, MQTT_PORT, 60)
        
        # 启动网络循环
        client.loop_start()
        
        # 等待连接建立
        time.sleep(2)

        # 测试幂等性功能
        test_idempotency(client)

        # 等待响应
        print("⏳ 等待响应...")
        time.sleep(10)  # 等待10秒观察结果
        
        print("🔚 测试完成")
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断测试")
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    finally:
        client.loop_stop()
        client.disconnect()

if __name__ == "__main__":
    main()
