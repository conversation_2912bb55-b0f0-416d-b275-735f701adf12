#!/usr/bin/env python3
"""
简单的SCADA命令测试脚本
测试FStationMFC的SCADA命令处理功能
"""

import paho.mqtt.client as mqtt
import json
import time
import threading

# MQTT 配置
MQTT_BROKER = "192.168.88.44"
MQTT_PORT = 1883
DEVICE_ID = "A320021760"
CLIENT_ID = f"simple_test_{int(time.time())}"

class SimpleCommandTester:
    def __init__(self):
        self.client = None
        self.response_received = False
        self.response_data = None
        
    def on_connect(self, client, userdata, flags, rc):
        if rc == 0:
            print(f"✅ 连接成功到 EMQX")
            # 订阅命令响应
            response_topic = f"$oc/devices/{DEVICE_ID}/sys/commands/response/+"
            client.subscribe(response_topic, qos=1)
            print(f"📥 订阅命令响应主题")
        else:
            print(f"❌ 连接失败: {rc}")
    
    def on_message(self, client, userdata, msg):
        print(f"\n🎉 收到FStationMFC响应!")
        print(f"   主题: {msg.topic}")
        
        # 提取request_id
        topic_parts = msg.topic.split('/')
        request_id = None
        for part in topic_parts:
            if part.startswith('request_id='):
                request_id = part.split('=', 1)[1]
                break
        
        try:
            response = json.loads(msg.payload.decode())
            print(f"   RequestId: {request_id}")
            print(f"   结果码: {response.get('result_code', 'N/A')}")
            print(f"   结果消息: {response.get('result_message', 'N/A')}")
            
            if 'data' in response and response['data']:
                print(f"   响应数据: {json.dumps(response['data'], indent=2, ensure_ascii=False)}")
            
            self.response_received = True
            self.response_data = response
                
        except Exception as e:
            print(f"   JSON解析失败: {e}")
            print(f"   原始数据: {msg.payload.decode()}")
    
    def send_sn_deliver_command(self):
        """发送SN下发命令"""
        request_id = f"simple_test_{int(time.time())}"
        command = {
            "service_id": "CommandService",
            "command_name": "COMMAND_SN_DELIVER",
            "properties": {
                "sn": "TEST_SN_12345",
                "production_model": "A121000185"
            }
        }
        
        command_topic = f"$oc/devices/{DEVICE_ID}/sys/commands/request_id={request_id}"
        
        print(f"\n📤 发送SN下发命令")
        print(f"   主题: {command_topic}")
        print(f"   RequestId: {request_id}")
        print(f"   SN: {command['properties']['sn']}")
        print(f"   型号: {command['properties']['production_model']}")
        
        result = self.client.publish(command_topic, json.dumps(command), qos=2)
        
        if result.rc == mqtt.MQTT_ERR_SUCCESS:
            print(f"✅ 命令发送成功")
            return True
        else:
            print(f"❌ 命令发送失败: {result.rc}")
            return False
    
    def wait_for_response(self, timeout=15):
        """等待响应"""
        print(f"⏳ 等待FStationMFC响应 (超时: {timeout}秒)...")
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self.response_received:
                return True
            time.sleep(0.1)
        return False
    
    def run_test(self):
        """运行测试"""
        print("=" * 60)
        print("FStationMFC SCADA命令简单测试")
        print("=" * 60)
        
        # 创建MQTT客户端
        self.client = mqtt.Client(CLIENT_ID)
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        
        try:
            # 连接到MQTT Broker
            print(f"🔗 连接到MQTT Broker: {MQTT_BROKER}:{MQTT_PORT}")
            self.client.connect(MQTT_BROKER, MQTT_PORT, 60)
            self.client.loop_start()
            
            time.sleep(3)  # 等待连接建立
            
            # 发送测试命令
            if self.send_sn_deliver_command():
                # 等待响应
                if self.wait_for_response():
                    print("\n🎉 测试成功!")
                    print("✅ FStationMFC正确处理了SCADA命令")
                    print("✅ MQTT网关正确转发了命令和响应")
                    print("✅ 完整的通信链路工作正常")
                    
                    # 分析响应
                    if self.response_data:
                        result_code = self.response_data.get('result_code', -999)
                        if result_code == 0:
                            print("✅ 命令执行成功")
                        else:
                            print(f"⚠️ 命令执行返回错误码: {result_code}")
                else:
                    print("\n❌ 测试失败: 超时无响应")
                    print("🔧 可能的问题:")
                    print("   1. FStationMFC应用程序未运行")
                    print("   2. SCADA命令处理器未初始化")
                    print("   3. Socket连接断开")
                    print("   4. 命令处理过程中出现异常")
            else:
                print("\n❌ 测试失败: 命令发送失败")
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")
        finally:
            if self.client:
                self.client.loop_stop()
                self.client.disconnect()
                print("\n🔌 MQTT连接已断开")

def main():
    print("FStationMFC SCADA命令简单测试")
    print("=" * 40)
    print("测试目标:")
    print("1. 验证FStationMFC接收SCADA命令")
    print("2. 验证命令处理和响应")
    print("3. 验证完整通信链路")
    print()
    
    tester = SimpleCommandTester()
    tester.run_test()

if __name__ == "__main__":
    main()
