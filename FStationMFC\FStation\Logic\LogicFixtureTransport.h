﻿#pragma once

#include "ThreadBase.h"
using namespace yzBase;

#include "Fixture.h"

class CLogicFixtureTransport : public CThreadBase
{
public:
	CLogicFixtureTransport();
	virtual ~CLogicFixtureTransport();

public:
	// 基础控制函数
	EnumStatus OnSafeCheck();       // 安全检查：检查治具搬运系统安全状态
	EnumStatus OnStart();           // 启动控制：初始化治具搬运系统
	EnumStatus OnPause();           // 暂停控制：暂停治具搬运操作
	EnumStatus OnResume();          // 恢复控制：从暂停状态恢复治具搬运操作
	EnumStatus OnStop();            // 停止控制：停止治具搬运系统

public:
	// 主运行函数
	CStatus OnRun();                // 主状态机运行函数：协调治具搬运工作流程

	// ========== 治具搬运主流程 (OnFixtureTransport00-OnFixtureTransport02) ==========
	CStatus OnFixtureTransport00(); // 治具搬运流程控制：控制治具搬运主流程
	CStatus OnFixtureTransport01(); // 治具搬运准备：准备治具搬运操作
	CStatus OnFixtureTransport01_0();   // 治具搬运准备子流程：执行治具搬运准备详细操作
	CStatus OnFixtureTransport01_1();   // 治具搬运准备验证：验证治具搬运准备状态
	CStatus OnFixtureTransport01_2();   // 治具搬运准备完成：完成治具搬运准备
	CStatus OnFixtureTransport01_3();   // 治具搬运准备后处理：治具搬运准备后处理
	CStatus OnFixtureTransport01_4();   // 治具搬运准备清理：清理治具搬运准备区域
	CStatus OnFixtureTransport01_5();   // 治具搬运准备记录：记录治具搬运准备数据
	CStatus OnFixtureTransport01_6();   // 治具搬运准备状态更新：更新治具搬运准备状态
	CStatus OnFixtureTransport01_7();   // 治具搬运准备流程验证：验证治具搬运准备流程
	CStatus OnFixtureTransport01_8();   // 治具搬运准备流程完成：完成治具搬运准备流程
	CStatus OnFixtureTransport01_9();   // 治具搬运准备流程结束：结束治具搬运准备流程
	CStatus OnFixtureTransport01_10();  // 治具搬运准备最终检查：最终检查治具搬运准备状态
	CStatus OnFixtureTransport01_11();  // 治具搬运准备最终验证：最终验证治具搬运准备结果
	CStatus OnFixtureTransport01_12();  // 治具搬运准备最终完成：最终完成治具搬运准备
	CStatus OnFixtureTransport02(); // 治具搬运执行：执行治具搬运操作
	CStatus OnFixtureTransport02_0();   // 治具搬运执行子流程：执行治具搬运详细操作
	CStatus OnFixtureTransport02_1();   // 治具搬运执行验证：验证治具搬运执行状态
	CStatus OnFixtureTransport02_2();   // 治具搬运执行完成：完成治具搬运执行
	CStatus OnFixtureTransport02_3();   // 治具搬运执行后处理：治具搬运执行后处理
	CStatus OnFixtureTransport02_4();   // 治具搬运执行清理：清理治具搬运执行区域
	CStatus OnFixtureTransport02_5();   // 治具搬运执行记录：记录治具搬运执行数据
	CStatus OnFixtureTransport02_6();   // 治具搬运执行状态更新：更新治具搬运执行状态
	CStatus OnFixtureTransport02_7();   // 治具搬运执行流程验证：验证治具搬运执行流程
	CStatus OnFixtureTransport02_8();   // 治具搬运执行流程完成：完成治具搬运执行流程
	CStatus OnFixtureTransport02_8_0(); // 治具搬运执行流程完成子流程：执行治具搬运执行流程完成详细操作
	CStatus OnFixtureTransport02_9();   // 治具搬运执行流程结束：结束治具搬运执行流程
	CStatus OnFixtureTransport02_10();  // 治具搬运执行最终检查：最终检查治具搬运执行状态
	CStatus OnFixtureTransport02_10_0(); // 治具搬运执行最终检查子流程：执行治具搬运执行最终检查详细操作
	CStatus OnFixtureTransport02_10_1(); // 治具搬运执行最终检查验证：验证治具搬运执行最终检查结果
	CStatus OnFixtureTransport02_10_2(); // 治具搬运执行最终检查完成：完成治具搬运执行最终检查
	CStatus OnFixtureTransport02_10_3(); // 治具搬运执行最终检查后处理：治具搬运执行最终检查后处理
	CStatus OnFixtureTransport02_10_4(); // 治具搬运执行最终检查清理：清理治具搬运执行最终检查区域
	CStatus OnFixtureTransport02_11();  // 治具搬运执行最终验证：最终验证治具搬运执行结果
	CStatus OnFixtureTransport02_12();  // 治具搬运执行最终完成：最终完成治具搬运执行
	CStatus OnFixtureTransport02_13();  // 治具搬运执行最终结束：最终结束治具搬运执行

private:
	CFixture*				m_pFixture;         // 治具设备对象指针

	CString					m_sRet;             // 函数返回字符串结果

	map<CString, bool>		m_mapFlag;          // 标志映射表，记录各种状态标志
	map<CString, DWORD>		m_mapTick;          // 时间计时器映射表，用于超时控制
	map<CString, double>	m_mapPos;           // 位置映射表，记录各种位置信息
};
