# FStation系统错误处理和监控机制详解

## 概述

FStation作为工业自动化设备，对系统的可靠性和安全性有着极高的要求。系统采用了多层次的错误处理和监控机制，确保设备在各种异常情况下都能安全停机，保护设备和操作人员的安全。

## 错误处理架构体系

### 1. 分层错误处理架构

```cpp
FStation错误处理体系
├── 硬件层错误检测
│   ├── 传感器状态监控
│   ├── 运动轴位置检查
│   └── I/O信号验证
├── 模块层错误处理
│   ├── 功能接口返回值检查
│   ├── 参数范围验证
│   └── 状态一致性检查
├── 逻辑层错误控制
│   ├── 流程执行验证
│   ├── 线程状态管理
│   └── 安全检查机制
└── 系统层异常处理
    ├── 全局异常捕获
    ├── 系统状态监控
    └── 日志记录系统
```

### 2. 错误级别分类

```cpp
typedef enum LogLevel {
    emLogLevelNormal,    // 正常信息
    emLogLevelWarn,      // 警告信息
    emLogLevelError,     // 错误信息
    emLogLevelSum        // 级别总数
} EnumLogLevel;

// 错误级别对应的显示颜色
const COLORREF g_clrLogLevel[emLogLevelSum] = {
    RGB(0, 0, 0),       // 正常信息 - 黑色
    RGB(255, 165, 0),   // 警告信息 - 橙色
    RGB(255, 0, 0)      // 错误信息 - 红色
};
```

## 核心错误处理宏

### 1. EXCUTE_RETURN宏 - 模块层错误处理

```cpp
#define EXCUTE_RETURN(Func) \
{ \
    CString sRet; \
    sRet = Func; \
    if (sRet != "OK") { \
        return sRet; \
    } \
}

// 功能说明：
// 1. 执行硬件操作函数
// 2. 检查返回值是否为"OK"
// 3. 如果不是"OK"，则返回错误信息
// 4. 用于模块层硬件接口的错误检查

// 使用示例：
EXCUTE_RETURN(g_pControl->WriteOutput("三色灯红", true));
EXCUTE_RETURN(g_pRobot->Move("取料位置"));
EXCUTE_RETURN(g_pFixture->ClampOn());
```

### 2. RUN_STOP_IF_ERROR宏 - 逻辑层错误处理

```cpp
#define RUN_STOP_IF_ERROR(Func) \
{ \
    m_sRet = Func; \
    if (m_sRet != "On" && m_sRet != "Off" && m_sRet != "UnKnow" && \
        m_sRet != "OK" && m_sRet != "Yes" && m_sRet != "No") { \
        REPORT(m_sRet, emLogLevelError); \
        MESSAGEBOX(m_sRet + "流程已停止，请检查原因并复位相关模块后点击启动", "", false); \
        RETURN_STOP(); \
    } \
}

// 功能说明：
// 1. 执行硬件操作或状态检查函数
// 2. 检查返回值是否为有效状态值
// 3. 如果返回错误，记录日志并显示消息框
// 4. 停止当前线程执行，等待复位
// 5. 用于逻辑线程中的错误处理

// 有效返回值：
// "OK"     - 操作成功
// "On"     - 信号/状态开启
// "Off"    - 信号/状态关闭
// "Yes"    - 位置到达/条件满足
// "No"     - 位置未到达/条件不满足
// "UnKnow" - 状态未知（某些特殊情况）

// 使用示例：
RUN_STOP_IF_ERROR(m_pBelt->BeltOn(!PARAM_BOOL("皮带正转方向")));
RUN_STOP_IF_ERROR(m_pFixture->IsTransportYInPos(100.0));
RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumOn(1));
```

### 3. RUN_PAUSE_IF_WARN宏 - 警告级错误处理

```cpp
#define RUN_PAUSE_IF_WARN(Func) \
{ \
    m_sRet = Func; \
    if (m_sRet != "On" && m_sRet != "Off" && m_sRet != "UnKnow" && \
        m_sRet != "OK" && m_sRet != "Yes" && m_sRet != "No") { \
        REPORT(m_sRet, emLogLevelWarn); \
        RETURN_PAUSE(NULL); \
    } \
}

// 功能说明：
// 1. 检查函数返回值
// 2. 如果有错误，记录警告级日志
// 3. 暂停线程执行，等待人工干预
// 4. 用于非致命性错误的处理
```

### 4. 线程状态控制宏

```cpp
// 返回当前状态，等待指定条件
#define RETURN_SELF(strWaitMsg, bNeedWarning) \
{ \
    return CStatus(NULL, emRun, strWaitMsg, bNeedWarning); \
}

// 转移到指定状态函数
#define RETURN_STATE(pThreadFunc, bTraceFlag) \
{ \
    if (bTraceFlag) { \
        TRACE("Line %d %s\n", __LINE__, __FUNCTION__); \
    } \
    return CStatus(static_cast<THREAD_FUNC>(pThreadFunc), emRun); \
}

// 暂停线程执行
#define RETURN_PAUSE(pThreadFunc) \
{ \
    TRACE("Line %d %s\n", __LINE__, __FUNCTION__); \
    return CStatus(static_cast<THREAD_FUNC>(pThreadFunc), emPause); \
}

// 停止线程执行
#define RETURN_STOP() \
{ \
    TRACE("Line %d %s\n", __LINE__, __FUNCTION__); \
    return CStatus(NULL, emStop); \
}
```

## 系统运行状态管理

### 1. 运行状态枚举

```cpp
typedef enum RunStatus {
    emRunStatusPreReset,    // 预复位状态
    emRunStatusReset,       // 复位状态
    emRunStatusIdle,        // 空闲状态
    emRunStatusRunning,     // 运行状态
    emRunStatusSuspending,  // 暂停状态
    emRunStatusWarning,     // 警告状态
    emRunStatusSum          // 状态总数
} EnumRunStatus;

// 状态显示字符串
const CString g_strRunStatus[emRunStatusSum] = {
    "预复位",
    "复位中",
    "空闲",
    "运行中",
    "暂停",
    "警告"
};
```

### 2. 状态转换控制

```cpp
// 状态转换流程
预复位 → 复位 → 空闲 → 运行 → 暂停/警告 → 空闲

// 状态转换条件
emRunStatusPreReset:
- 系统启动后初始状态
- 等待用户确认复位操作

emRunStatusReset:
- 执行各模块复位动作
- 检查所有轴回原点
- 验证传感器状态

emRunStatusIdle:
- 复位完成，等待启动
- 系统就绪，可以开始生产

emRunStatusRunning:
- 正常生产状态
- 各线程协调工作

emRunStatusSuspending:
- 暂停状态，等待恢复
- 保持当前位置不动

emRunStatusWarning:
- 警告状态，需要处理
- 部分功能受限
```

## 权限控制和安全检查

### 1. 权限级别定义

```cpp
typedef enum PremissionLevel {
    emPremissionLevelOperator = 1,    // 操作员权限
    emPremissionLevelTechnician,      // 技术员权限
    emPremissionLevelEngineer         // 工程师权限
} EnumPremissionLevel;

extern int g_nPermissionLevel;  // 当前用户权限级别
```

### 2. 权限控制宏

```cpp
#define PREMISSION_CTRL() \
if (g_bAuto && !VAR_MACHINE_B("强制自动模式")) { \
    AfxMessageBox("对不起，自动模式下禁止操作此功能！"); \
    return; \
} \
if (g_nPermissionLevel < emPremissionLevelTechnician) { \
    AfxMessageBox("对不起，您没有权限操作此功能！"); \
    return; \
}

// 功能说明：
// 1. 检查当前是否为自动模式
// 2. 检查用户权限级别
// 3. 限制危险操作的执行
```

### 3. 手动模式检查

```cpp
#define MANNUAL_MODE() \
if (!g_bAuto) { \
    REPORT("手动模式下运行一次后停止", emLogLevelWarn); \
    RETURN_STOP(); \
}

// 功能说明：
// 确保在手动模式下单步执行
// 防止手动模式下的连续运行
```

## 日志记录系统

### 1. 日志报告宏

```cpp
#define REPORT(sRet, nLevel) \
{ \
    CString str1, str2 = sRet; \
    if (CSys::m_bEngineer) { \
        str1.Format("Line %d %s : %s", __LINE__, __FUNCTION__, str2.GetBuffer()); \
    } \
    else { \
        str1.Format("%s", str2.GetBuffer()); \
    } \
    ::SendMessage(g_hMainWnd, UM_UPDATE_LOG, (WPARAM)nLevel, (LPARAM)str1.GetBuffer()); \
}

// 功能说明：
// 1. 格式化日志信息
// 2. 工程师模式显示详细信息（行号、函数名）
// 3. 普通模式显示简化信息
// 4. 发送到主窗口显示
```

### 2. 日志类型

```cpp
// 数据日志管理
class CDat {
public:
    void UpdateLog(CString strLog);           // 更新普通日志
    void UpdateErrorLog(CString strErr);      // 更新错误日志
    void UpdateParameterLog(CString strLog);  // 更新参数日志
    
    // 日志文件管理
    void GenDateDir();                        // 生成日期目录
    void ClearOutDateDir(int nOutDay);        // 清理过期日志
};

// 全局日志对象
extern CDat* g_pDatLog;     // 日志记录
extern CDat* g_pDatImage;   // 图像记录
```

## 安全检查机制

### 1. 运动安全检查

```cpp
// 安全检查回调函数
bool __stdcall OnSafeCheck(CString sName, CString* pMsg);

// 功能说明：
// 1. 在运动开始前检查安全条件
// 2. 检查急停、安全门、安全光栅状态
// 3. 检查运动范围是否合理
// 4. 返回false时阻止运动执行
```

### 2. 系统状态监控

```cpp
// 全局状态标志
extern bool g_bWaitFlag;    // 等待标志
extern bool g_bWarn;        // 警告标志
extern bool g_bWarnFlag;    // 警告状态
extern int  g_nWarnTimes;   // 警告次数
extern bool g_bAuto;        // 自动模式标志
extern bool g_bExit;        // 退出标志

// 状态监控要点：
// 1. 实时监控系统运行状态
// 2. 记录警告发生次数
// 3. 管理自动/手动模式切换
// 4. 控制系统安全退出
```

## 超时监控机制

### 1. 超时参数配置

```cpp
// Machine模块超时参数
"皮带待料超时":           30000ms    // 皮带来料超时时间
"皮带拥堵超时":           30000ms    // 皮带拥堵检测超时
"MES响应超时时间":        15000ms    // MES通信超时

// Robot模块超时参数
"吸嘴吸料延时":           200ms      // 吸嘴吸料等待时间
"吸嘴贴装延时":           200ms      // 吸嘴贴装等待时间

// Tray模块超时参数
"满TRAY盘进料延时":       2000ms     // 满托盘进料延时
"满TRAY盘退料延时":       3000ms     // 满托盘退料延时
"空TRAY盘进料延时":       2000ms     // 空托盘进料延时
"空TRAY盘退料延时":       3000ms     // 空托盘退料延时

// Fixture模块超时参数
"治具开合延时":           1000ms     // 治具开合动作延时
"分板动作延时":           2000ms     // 分板动作延时
"治具夹紧延时":           500ms      // 治具夹紧延时
```

### 2. 超时检测实现

```cpp
// 超时检测示例（在Logic线程中）
DWORD dwStartTime = GetTickCount();
while (true) {
    // 检查超时
    if (GetTickCount() - dwStartTime > VAR_MACHINE_I("皮带待料超时")) {
        REPORT("皮带待料超时", emLogLevelError);
        RETURN_STOP();
    }
    
    // 检查条件是否满足
    RUN_STOP_IF_ERROR(m_pBelt->Signal1Status());
    if (m_sRet == "On") {
        break;  // 条件满足，退出等待
    }
    
    Sleep(10);  // 短暂等待
}
```

## 用户管理和访问控制

### 1. 用户数据结构

```cpp
typedef struct _USER_ {
    CString sName;      // 用户名
    CString sKey;       // 密码
    int     nLevel;     // 权限级别
} USER, *PUSER;

extern vector<USER> g_vUser;    // 用户列表
```

### 2. 权限验证机制

```cpp
// 权限验证流程
1. 用户登录验证
2. 获取用户权限级别
3. 根据权限级别限制功能访问
4. 参数编辑权限控制
5. 操作功能权限控制

// 参数权限级别
权限级别1：操作员可见参数
权限级别2：技术员可编辑参数
权限级别3：工程师可编辑参数
权限级别4：管理员可编辑参数
```

## 异常恢复机制

### 1. 自动恢复策略

```cpp
// 异常分类和恢复策略
轻微异常：
- 传感器信号不稳定
- 运动轴轻微偏差
- 恢复策略：重试3次，超时报警

中等异常：
- 运动超时
- 真空检测失败
- 恢复策略：回到安全位置，等待手动复位

严重异常：
- 急停触发
- 安全门打开
- 恢复策略：立即停止所有运动，等待系统复位
```

### 2. 错误恢复流程

```cpp
// 错误恢复流程图
错误发生 → 错误检测 → 错误分类 → 恢复策略选择 → 执行恢复操作 → 状态验证 → 恢复完成

// 具体实现步骤
1. 错误检测：通过宏和返回值检查
2. 错误分类：根据错误类型分级处理
3. 日志记录：记录错误信息和恢复过程
4. 用户通知：显示错误信息和处理建议
5. 系统恢复：执行相应的恢复操作
6. 状态确认：验证系统是否恢复正常
```

## 故障诊断支持

### 1. 诊断信息收集

```cpp
// 故障诊断数据
1. 错误发生时间和位置
2. 系统运行状态
3. 硬件模块状态
4. 参数配置信息
5. 最近操作历史
6. 传感器状态快照
```

### 2. 故障诊断界面

```cpp
// 故障诊断功能
1. 实时状态监控界面
2. 错误日志查看功能
3. 硬件状态检查工具
4. 参数配置验证
5. 系统自检功能
6. 故障排除指导
```

## 总结

FStation的错误处理和监控机制通过以下特点确保系统的可靠性：

### 关键特性

1. **分层防护**：从硬件到系统层的多层错误检测
2. **实时监控**：连续监控系统状态和硬件运行状况
3. **快速响应**：错误发生时立即停止并报告
4. **用户友好**：清晰的错误信息和恢复指导
5. **权限控制**：基于用户权限的安全访问控制
6. **完整记录**：详细的日志记录便于故障分析

### 安全保证

1. **硬件安全**：保护设备不受损坏
2. **人员安全**：确保操作人员安全
3. **产品质量**：防止不良品产生
4. **系统稳定**：保证长期稳定运行

该错误处理体系体现了工业4.0对设备智能化和安全性的高标准要求，为复杂自动化设备的可靠运行提供了坚实保障。 