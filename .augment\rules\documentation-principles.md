---
type: "always_apply"
---

# 三文档原则 - 项目文档维护策略

## 原则概述

本项目采用"三文档原则"来简化文档维护，降低维护成本，确保文档与代码同步。

## 三文档原则

### 1. 官方协议文档（只读，不维护）
- **文件**: [doc/重要/SCADA平台MQTT协议设备接入指南.md](mdc:doc/重要/SCADA平台MQTT协议设备接入指南.md)
- **作用**: 平台官方标准，具有权威性和不可变性
- **维护**: 不进行任何修改，仅作为参考标准

### 2. 项目对接指南（唯一维护的文档）
- **文件**: [doc/本项目SCADA对接指南.md](mdc:doc/本项目SCADA对接指南.md)
- **作用**: 项目特有的补充说明，包含实际实现细节
- **内容**:
  - 当前使用的协议版本
  - 实际的消息格式示例
  - 与官方协议的差异说明
  - 常见问题和解决方案
  - 代码变更记录

### 3. 代码注释（随代码维护）
- **位置**: 关键函数和类中
- **作用**: 在代码中直接说明协议细节和实现逻辑
- **要求**:
  - 在消息处理函数中写格式示例
  - 在关键函数开头写协议说明
  - 标注协议版本和变更记录

## 文档维护规则

### 禁止创建的文件类型
- ❌ 详细的接口文档
- ❌ 重复的协议说明文档
- ❌ 过时的示例文档
- ❌ 修复报告类文档
- ❌ 临时分析文档

### 代码注释规范
```cpp
// 协议版本：SCADA v1.0
// 消息格式：{"services": [{"service_id": "EventService", ...}]}
// 变更记录：2024-01-15 移除外层包裹字段
void ProcessSocketMessages() {
    // 处理FStation发送的Socket消息
    // 支持的消息类型：DEVICE_STATUS, PRODUCTION_EVENT, EVENT_SN_IN等
    // 消息格式：直接发送services数组，无需外层包裹
}
```

### 维护流程
1. **协议变更时**: 只更新项目对接指南
2. **代码变更时**: 同步更新相关注释
3. **定期清理**: 删除过时的代码注释和文档

## 文件引用规范

### 核心文件
- 官方协议: [doc/重要/SCADA平台MQTT协议设备接入指南.md](mdc:doc/重要/SCADA平台MQTT协议设备接入指南.md)
- 数采清单: [doc/重要/科瑞F站--数采清单.md](mdc:doc/重要/科瑞F站--数采清单.md)
- 项目对接: [doc/本项目SCADA对接指南.md](mdc:doc/本项目SCADA对接指南.md)
- Socket协议: [doc/socket_protocol.md](mdc:doc/socket_protocol.md)

### 关键代码文件
- MQTT网关主文件: [MQTTGateway/MQTTGateway.cpp](mdc:MQTTGateway/MQTTGateway.cpp)
- 配置管理: [MQTTGateway/ConfigManager.cpp](mdc:MQTTGateway/ConfigManager.cpp)
- 数据点管理: [MQTTGateway/DataPointManager.cpp](mdc:MQTTGateway/DataPointManager.cpp)

## 质量检查清单

在创建或修改文档时，请检查：
- [ ] 是否遵循三文档原则
- [ ] 是否与当前代码实现一致
- [ ] 是否引用了正确的文件
- [ ] 是否避免了重复信息
- [ ] 是否标注了协议版本和变更记录

## 违反原则的处理

如果发现违反三文档原则的情况：
1. 删除多余的文档文件
2. 将重要信息合并到项目对接指南
3. 在代码中添加必要的注释
4. 更新本规则文件以反映变更
description:
globs:
alwaysApply: false
---
