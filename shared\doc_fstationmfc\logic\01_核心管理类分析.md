# 核心管理类分析

## 概述

FStation线程系统的核心管理类提供了完整的线程管理框架，包括线程基类、管理器、工厂类等。这些类构成了整个线程系统的基础架构。

## 类关系图

```
yzBase命名空间
├── CThreadBase (抽象基类)
│   ├── 所有Logic线程类继承
│   └── 定义标准接口
├── CThreadMgr (线程管理器)
│   ├── 创建和管理具体线程
│   └── 控制线程生命周期
├── CThreadFunc (子线程功能类)
│   ├── 执行具体业务逻辑
│   └── 状态机实现
├── CThreadFactory (线程工厂)
│   ├── 创建ThreadMgr实例
│   ├── 创建ThreadFunc实例
│   └── 创建ThreadMonitor实例
├── CThreadMonitor (线程监控)
│   └── 监控线程运行状态
└── CLogicMgr (逻辑管理器)
    ├── 统一管理所有线程
    ├── 系统级控制接口
    └── 线程协调机制
```

## 1. CThreadBase (线程基类)

### 定义位置
`FStationMFC/build/inc/ThreadBase.h`

### 核心接口
```cpp
class MODULE_BASE_API CThreadBase
{
public:
    virtual EnumStatus OnSafeCheck() = 0;  // 安全检查
    virtual EnumStatus OnStart() = 0;      // 启动
    virtual EnumStatus OnPause() = 0;      // 暂停
    virtual EnumStatus OnResume() = 0;     // 恢复
    virtual EnumStatus OnStop() = 0;       // 停止
    virtual CStatus OnRun() = 0;           // 运行主逻辑
};
```

### 设计特点
1. **纯虚类设计**: 强制子类实现所有接口
2. **标准化生命周期**: 统一的启动/暂停/停止流程
3. **安全优先**: OnSafeCheck确保系统安全
4. **状态返回**: 通过EnumStatus返回执行状态

### 状态枚举
```cpp
enum EnumStatus
{
    emRun,    // 运行状态
    emPause,  // 暂停状态
    emStop    // 停止状态
};
```

## 2. CStatus (状态类)

### 核心结构
```cpp
class MODULE_BASE_API CStatus
{
public:
    THREAD_FUNC m_pThreadFunc;  // 下一个要执行的函数指针
    EnumStatus  m_emStatus;     // 线程状态
    CString     m_strMsg;       // 状态消息
    bool        m_bNeedWarning; // 是否需要警告
};
```

### 状态控制宏
```cpp
// 保持当前状态，显示等待消息
#define RETURN_SELF(strWaitMsg, bNeedWarning) \
{ \
    return CStatus(NULL, emRun, strWaitMsg, bNeedWarning); \
}

// 跳转到指定状态
#define RETURN_STATE(pThreadFunc, bTraceFlag) \
{ \
    if (bTraceFlag) { \
        TRACE("Line %d %s\n", __LINE__, __FUNCTION__); \
    } \
    return CStatus(static_cast<THREAD_FUNC>(pThreadFunc), emRun); \
}

// 暂停到指定状态
#define RETURN_PAUSE(pThreadFunc) \
{ \
    TRACE("Line %d %s\n", __LINE__, __FUNCTION__); \
    return CStatus(static_cast<THREAD_FUNC>(pThreadFunc), emPause); \
}

// 停止线程
#define RETURN_STOP() \
{ \
    TRACE("Line %d %s\n", __LINE__, __FUNCTION__); \
    return CStatus(NULL, emStop); \
}
```

## 3. CThreadFunc (子线程功能类)

### 核心接口
```cpp
class MODULE_BASE_API CThreadFunc
{
public:
    virtual EnumStatus Run(CThreadBase *pThreadBase) = 0;      // 执行线程逻辑
    virtual void SetAction(THREAD_FUNC pThreadFunc) = 0;       // 设置执行动作
    virtual EnumStatus GetStatus() = 0;                        // 获取状态
    virtual void SetStatus(EnumStatus emResult) = 0;           // 设置状态
};
```

### 使用方式
```cpp
// 创建子线程功能对象
m_pFuncNGBelt = CLogicMgr::m_ThreadFactory.CreateThreadFunc("NG皮带");
// 设置执行动作
m_pFuncNGBelt->SetAction(static_cast<THREAD_FUNC>(&CLogicMachine::OnNGBelt00));
// 运行子线程
m_pFuncNGBelt->Run(this);
```

## 4. CThreadFactory (线程工厂)

### 核心方法
```cpp
class MODULE_BASE_API CThreadFactory
{
public:
    CThreadMgr*    CreateThreadMgr();                    // 创建线程管理器
    CThreadFunc*   CreateThreadFunc(CString strName);    // 创建子线程功能
    CThreadMonitor* CreateThreadMonitor();               // 创建线程监控器
};
```

### 工厂模式优势
1. **统一创建**: 通过工厂统一创建对象
2. **解耦合**: 调用者不需要知道具体实现类
3. **易扩展**: 新增类型时只需修改工厂
4. **资源管理**: 统一管理对象生命周期

### 使用示例
```cpp
// 通过LogicMgr的静态工厂创建
CThreadFunc* pFunc = CLogicMgr::m_ThreadFactory.CreateThreadFunc("功能名称");
```

## 5. CLogicMgr (逻辑管理器)

### 定义位置
`FStationMFC/FStation/Global/LogicMgr.h`

### 核心结构
```cpp
class CLogicMgr
{
public:
    static void InitProc();     // 初始化所有线程
    static void ReleaseProc();  // 释放所有线程
    static void Run();          // 启动所有线程
    static void Pause();        // 暂停所有线程
    static void Stop();         // 停止所有线程
    
    static bool isRunning();    // 检查运行状态
    static bool isPause();      // 检查暂停状态

public:
    static CThreadFactory           m_ThreadFactory;  // 线程工厂
    static map<CString, THREADMSG>  m_mapThread;      // 线程映射表
    static CThreadMonitor*          m_pThreadMonitor; // 线程监控器
};
```

### 线程消息结构
```cpp
typedef struct THREAD_MSG
{
    CThreadMgr*   pThread;  // 线程管理器指针
    enThreadType  type;     // 线程类型
    bool          bEnable;  // 是否启用
} THREADMSG;
```

### 线程类型枚举
```cpp
enum enThreadType
{
    emThreadSpecial = 0,    // 特殊线程
    emThreadNormal,         // 普通线程
    emThreadAlwaysExist     // 常驻线程
};
```

### 线程初始化过程
```cpp
void CLogicMgr::InitProc()
{
    CThreadMgr *pThreadMgr = NULL;

    // 创建Reset线程
    pThreadMgr = m_ThreadFactory.CreateThreadMgr();
    pThreadMgr->Create("复位", static_cast<CThreadBase*>(new CLogicReset));
    m_mapThread["Reset"].pThread = pThreadMgr;
    m_mapThread["Reset"].type = emThreadSpecial;

    // 创建Machine线程（常驻）
    pThreadMgr = m_ThreadFactory.CreateThreadMgr();
    pThreadMgr->Create("", static_cast<CThreadBase*>(new CLogicMachine()));
    m_mapThread["Machine"].pThread = pThreadMgr;
    m_mapThread["Machine"].type = emThreadAlwaysExist;

    // 创建多个Proc线程实例
    for (int i=0; i<4; i++)
    {
        CString sRet;
        sRet.Format("Proc%d", i + 1);
        pThreadMgr = m_ThreadFactory.CreateThreadMgr();
        pThreadMgr->Create("", static_cast<CThreadBase *>(new CLogicProc(i)));
        m_mapThread[sRet].pThread = pThreadMgr;
        m_mapThread[sRet].type = emThreadAlwaysExist;
    }
    // ... 其他线程创建
}
```

## 6. CThreadMgr (线程管理器)

### 核心接口
```cpp
class MODULE_BASE_API CThreadMgr
{
public:
    virtual void Create(CString strThreadName, CThreadBase *pThreadBase) = 0;
    virtual void Start() = 0;
    virtual void Pause() = 0;
    virtual void Stop() = 0;
    virtual EnumStatus GetStatus() = 0;
};
```

### 职责
1. **线程创建**: 将ThreadBase对象包装成可执行线程
2. **生命周期管理**: 控制线程的启动、暂停、停止
3. **状态查询**: 提供线程当前状态查询

## 线程通信机制

### 参数映射表
各线程类通过参数映射表进行间接通信：

```cpp
// 公共参数映射表类型
map<CString, bool>   m_mapFlag;   // 布尔标志位
map<CString, DWORD>  m_mapTick;   // 时间戳记录
map<CString, double> m_mapPos;    // 位置参数
map<CString, int>    m_mapIndex;  // 索引参数
```

### 通信模式
1. **间接通信**: 通过共享参数表避免直接依赖
2. **标准化接口**: 统一的参数命名和访问方式
3. **松耦合**: 线程间可独立开发和测试
4. **数据同步**: 通过映射表实现数据共享

## 7. CThreadMonitor (线程监控)

### 核心接口
```cpp
class MODULE_BASE_API CThreadMonitor
{
public:
    virtual CString AttatchWindow(CRect rcWnd, HWND hMainWnd, unsigned int nMsgId) = 0;
    virtual CString Reset() = 0;
    virtual CString Start() = 0;
};
```

### 监控功能
1. **状态监控**: 监控所有线程的运行状态
2. **界面集成**: 与主界面集成显示监控信息
3. **异常检测**: 检测线程异常并报告
4. **性能统计**: 统计线程执行性能

## 设计模式分析

### 1. 工厂模式
- **CThreadFactory**: 统一创建各种线程相关对象
- **优势**: 解耦创建逻辑，易于扩展和维护

### 2. 状态机模式
- **CStatus**: 封装状态转换逻辑
- **优势**: 清晰的状态管理，复杂流程简化

### 3. 单例模式
- **CLogicMgr**: 静态成员实现全局唯一管理器
- **优势**: 全局访问点，统一管理所有线程

### 4. 策略模式
- **CThreadFunc**: 不同线程功能的不同实现策略
- **优势**: 灵活的功能组合，运行时切换

## 异常处理机制

### 1. 安全检查
每个线程都必须实现OnSafeCheck方法：
```cpp
EnumStatus CLogicXXX::OnSafeCheck()
{
    if (!CSys::m_bInit) {
        return emStop;  // 系统未初始化，停止运行
    }
    return emRun;       // 安全检查通过
}
```

### 2. 分级响应
- **emStop**: 立即停止线程
- **emPause**: 暂停线程等待恢复
- **emRun**: 正常运行状态

### 3. 错误恢复
- **自动重试**: 某些错误条件下自动重试
- **状态重置**: 通过Reset线程恢复初始状态
- **安全停止**: 确保设备安全的停止流程

## 性能优化

### 1. 内存管理
- **对象池**: 重用线程对象减少创建开销
- **智能指针**: 自动管理内存生命周期
- **映射表**: 高效的参数查找和访问

### 2. 并发优化
- **子线程**: 复杂线程内部使用子线程并发处理
- **状态缓存**: 缓存频繁访问的状态信息
- **批量操作**: 减少频繁的小操作

## 总结

FStation线程系统的核心管理类构建了一个完整的线程管理框架：

### 核心优势
1. **标准化**: 统一的接口和生命周期管理
2. **模块化**: 清晰的职责分离和接口定义
3. **可扩展**: 通过工厂模式易于扩展新功能
4. **安全性**: 多重安全检查和异常处理
5. **可维护**: 清晰的架构和规范的代码结构

### 架构价值
这套管理框架为复杂工控软件提供了：
- **稳定可靠**: 经过工业验证的线程管理机制
- **开发效率**: 标准化开发流程和模板
- **系统集成**: 统一的管理和监控接口
- **质量保证**: 完善的错误处理和恢复机制

该架构设计体现了工控软件对安全性、可靠性和可维护性的高要求，为其他类似系统的开发提供了有价值的参考。 