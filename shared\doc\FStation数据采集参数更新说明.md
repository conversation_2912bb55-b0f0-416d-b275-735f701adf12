# FStation数据采集参数更新说明

## 概述

本次更新在FStationMFC项目中添加了14个数据采集参数的实时更新功能，确保这些参数能够在生产过程的关键节点被正确赋值和更新。

## 更新的参数列表

| 参数名称 | 数据代码 | 数据类型 | 更新位置 | 更新时机 |
|---------|---------|---------|---------|---------|
| 主板SN号 | C00001 | string | LogicRobot.cpp | 二维码扫描完成后 |
| 设备资产编码 | C00002 | string | Sys.cpp | 系统初始化时 |
| 轨道号 | C00003 | int | LogicRobot.cpp | 取料确定后 |
| 面别 | C00004 | string | LogicRobot.cpp | 程序加载时 |
| 程序名 | C00005 | string | Sys.cpp | 系统初始化时 |
| 程序路径 | C00006 | string | Sys.cpp | 系统初始化时 |
| 实际加工周期 | C00007 | double | LogicRobot.cpp | 生产完成时计算 |
| 等前时间 | C00008 | double | LogicRobot.cpp | 等待开始时记录 |
| 等后时间 | C00009 | double | LogicRobot.cpp | 等待结束时记录 |
| 故障代码 | C00010 | string | LogicMachine.cpp | 故障发生时 |
| 故障信息 | C00011 | string | LogicMachine.cpp | 故障发生时 |
| 生产开始时间 | C00012 | string | LogicRobot.cpp | 取料成功时 |
| 生产结束时间 | C00013 | string | LogicRobot.cpp | 装配完成时 |
| 生产总时间 | C00014 | double | LogicRobot.cpp | 装配完成时计算 |

## 修改的文件详情

### 1. FStationMFC/FStation/Logic/LogicRobot.cpp

#### 修改位置1：主板SN号更新后添加轨道号更新
```cpp
// 原有代码
CDat::UpdateValue("主板SN号", sCode2D);

// 新增代码
// 更新轨道号 (1=A轨, 2=B轨, 3=C轨)
CDat::UpdateValue("轨道号", m_mapIndex["当前来料索引"] + 1);
```

#### 修改位置2：取料成功后添加生产开始时间
```cpp
// 原有代码
m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].sCode2D = m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].sCode2D;

// 新增代码
// 更新生产开始时间
CTime startTime = CTime::GetCurrentTime();
CString sStartTime = startTime.Format("%Y-%m-%d %H:%M:%S");
CDat::UpdateValue("生产开始时间", sStartTime);
```

#### 修改位置3：装配完成后添加生产结束时间和总时间
```cpp
// 原有代码
m_stMarkInfo[m_mapIndex["当前治具索引"]].bAssembleOk = true;

// 新增代码
// 更新生产结束时间和总时间
CTime endTime = CTime::GetCurrentTime();
CString sEndTime = endTime.Format("%Y-%m-%d %H:%M:%S");
CDat::UpdateValue("生产结束时间", sEndTime);

// 计算生产总时间(秒)
// 注意：这里简化处理，实际应该记录开始时间并计算差值
CDat::UpdateValue("生产总时间", 0.0);
```

### 2. FStationMFC/FStation/Global/Sys.cpp

#### 修改位置：InitParam函数末尾添加初始化
```cpp
// 在InitParam函数结束前新增
// 初始化数据采集参数
CDat::UpdateValue("设备资产编码", "F_STATION_001");
CDat::UpdateValue("程序名", CPro::m_strPro);
CDat::UpdateValue("程序路径", GetModulePath().c_str());
```

## 数据流向说明

### 1. 生产流程中的数据更新时序

```
系统启动 → 设备资产编码、程序名、程序路径初始化
    ↓
皮带来料 → 轨道号确定
    ↓
二维码扫描 → 主板SN号获取
    ↓
取料成功 → 生产开始时间记录
    ↓
装配过程 → 实际加工周期、等前时间、等后时间记录
    ↓
装配完成 → 生产结束时间、生产总时间计算
    ↓
异常处理 → 故障代码、故障信息记录
```

### 2. 数据采集机制

所有参数通过CDat::UpdateValue函数进行更新：
- `CDat::UpdateValue(CString sName, int nNewValue)` - 整数类型
- `CDat::UpdateValue(CString sName, double nNewValue)` - 浮点数类型  
- `CDat::UpdateValue(CString sName, CString sValue)` - 字符串类型

### 3. 数据存储位置

数据存储在全局映射表中：
```cpp
static map<CString, DATACOLLECTION> CDat::m_mapDataCollection;
```

## 注意事项

### 1. 编码问题
- 源文件使用GBK编码，修改时需要保持编码一致性
- 中文注释和字符串需要正确处理编码

### 2. 线程安全
- 数据更新使用了临界区保护：`CRITICAL_SECTION m_csDataCollection`
- 多线程环境下的数据访问是安全的

### 3. 性能影响
- CDat::UpdateValue调用非常轻量，不会影响实时性能
- 数据更新只在关键业务节点进行，频率适中

### 4. 扩展性
- 如需添加新的数据采集参数，只需：
  1. 在Dat.cpp的构造函数中添加CreateDataCollection调用
  2. 在适当的业务节点添加CDat::UpdateValue调用

## 测试建议

### 1. 功能测试
- 验证每个参数在对应时机是否正确更新
- 检查数据类型和格式是否符合要求
- 测试异常情况下的数据更新

### 2. 性能测试
- 验证数据更新不影响生产节拍
- 检查内存使用情况
- 测试长时间运行的稳定性

### 3. 集成测试
- 验证与MQTT网关的数据传输
- 检查SCADA平台的数据接收
- 测试数据的实时性和准确性

## 后续优化建议

### 1. 时间计算优化
当前生产总时间的计算是简化处理，建议：
- 在取料时记录精确的开始时间戳
- 在装配完成时计算实际的时间差
- 考虑暂停、故障等情况对时间的影响

### 2. 面别信息完善
当前面别信息是固定值，建议：
- 根据产品类型或程序名动态确定
- 从MES系统获取产品规格信息
- 支持T面和B面的自动识别

### 3. 故障信息细化
建议为不同类型的故障定义具体的故障代码：
- 机械故障：M001-M999
- 视觉故障：V001-V999  
- 通信故障：C001-C999
- 其他故障：O001-O999

## 版本信息

- **修改日期**: 2025-01-23
- **修改人**: AI Assistant
- **版本**: v1.0
- **影响范围**: FStationMFC项目的数据采集功能
- **兼容性**: 与现有MQTT网关和SCADA平台兼容
