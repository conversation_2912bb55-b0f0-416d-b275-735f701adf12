﻿#pragma once

#include "Resource.h"

#include "ColorList.h"
#include "afxcmn.h"

#define UM_AGV_RECV WM_USER + 1

// CDialogAgv 对话框

class CDialogAgv : public CDialogEx
{
	DECLARE_DYNAMIC(CDialogAgv)

public:
	CDialogAgv(CWnd* pParent = NULL);   // 标准构造函数
	virtual ~CDialogAgv();

// 对话框数据
	enum { IDD = IDD_DIALOG_AGV };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持
	virtual BOOL PreTranslateMessage(MSG* pMsg);
	virtual void OnOK() {};
	virtual BOOL OnInitDialog();

	DECLARE_MESSAGE_MAP()
	afx_msg LRESULT OnUmAgvRecv(WPARAM wParam, LPARAM lParam);
	afx_msg void OnTimer(UINT_PTR nIDEvent);
	afx_msg void OnDestroy();

public:
	void GetIPAddress(CString &sIp);

protected:
	CImageList	m_imageList;
	CColorList	m_list;

	CIPAddressCtrl m_ip;
};
