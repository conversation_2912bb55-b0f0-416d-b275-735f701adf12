#pragma once

#include <string>
#include <vector>
#include <map>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>

// 数据点配置结构
struct DataPointConfig {
    std::string code;
    std::string name;
    std::string type;
    std::string source;
    std::string unit;
    std::string category;
    bool readonly;
};

// 事件配置结构
struct EventConfig {
    std::string type;
    std::string name;
    std::string description;
    int qos;
    bool requiresResponse;
    int timeoutMs;
    int maxRetries;
    std::vector<std::string> properties;
    bool requiresRealtimeData;
};

// 命令配置结构
struct CommandConfig {
    std::string type;
    std::string name;
    std::string description;
    std::vector<std::string> properties;
    bool responseRequired;
    int timeoutMs;
};

// 订阅配置结构
struct SubscriptionConfig {
    std::string name;
    std::string topicTemplate;
    int qos;
    bool enabled;
    std::string handler;
    std::string description;
};

// 发布主题配置结构
struct PublishTopicConfig {
    std::string name;
    std::string topicTemplate;
    int qos;
    bool retain;
    std::string description;
    std::string messageType;
};

// 基础配置结构
struct GatewayConfig {
    struct Device {
        std::string deviceId;
        std::string secret;
        std::string userName;
    } device;
    
    struct Mqtt {
        std::string broker;
        int port;
        std::string version;
        int keepAliveInterval;
        bool cleanSession;
        bool automaticReconnect;
        int connectionTimeout;
    } mqtt;
    
    struct Socket {
        int listenPort;
        std::string bindAddress;
        int maxConnections;
        bool keepAlive;
    } socket;
    
    struct Features {
        int defaultReportCycleS;
        bool requestDataEnabled;
        bool propertyGetEnabled;
        bool commandsEnabled;
        bool eventsEnabled;
    } features;

    struct Performance {
        int maxQueueSize;
        int publishTimeoutS;
        int responseTimeoutS;
        int maxRetries;
    } performance;
};

// 简化的配置管理器
class SimpleConfigManager {
public:
    SimpleConfigManager();
    ~SimpleConfigManager() = default;
    
    // 配置加载
    bool LoadAllConfigs(const std::string& configDir = "config");
    bool LoadGatewayConfig(const std::string& configPath);
    bool LoadDataPointsConfig(const std::string& configPath);
    bool LoadEventsConfig(const std::string& configPath);
    bool LoadCommandsConfig(const std::string& configPath);
    bool LoadSubscriptionsConfig(const std::string& configPath);
    bool LoadPublishTopicsConfig(const std::string& configPath);
    
    // 配置访问
    const GatewayConfig& GetGatewayConfig() const { return m_gatewayConfig; }
    const std::vector<DataPointConfig>& GetDataPoints() const { return m_dataPoints; }
    const std::vector<EventConfig>& GetEvents() const { return m_events; }
    const std::vector<CommandConfig>& GetCommands() const { return m_commands; }
    const std::vector<SubscriptionConfig>& GetSubscriptions() const { return m_subscriptions; }
    const std::vector<PublishTopicConfig>& GetPublishTopics() const { return m_publishTopics; }
    
    // 查找方法
    const EventConfig* FindEventConfig(const std::string& eventType) const;
    const CommandConfig* FindCommandConfig(const std::string& commandType) const;
    const SubscriptionConfig* FindSubscriptionConfig(const std::string& name) const;
    const PublishTopicConfig* FindPublishTopicConfig(const std::string& name) const;
    
    // 数据点查询
    std::vector<DataPointConfig> GetDataPointsByCategory(const std::string& category) const;
    std::vector<DataPointConfig> GetDataPointsBySource(const std::string& source) const;
    
    // 配置验证
    bool ValidateConfigs() const;
    
private:
    GatewayConfig m_gatewayConfig;
    std::vector<DataPointConfig> m_dataPoints;
    std::vector<EventConfig> m_events;
    std::vector<CommandConfig> m_commands;
    std::vector<SubscriptionConfig> m_subscriptions;
    std::vector<PublishTopicConfig> m_publishTopics;
    
    // 工具方法
    QJsonDocument LoadJsonFile(const std::string& filePath) const;
    bool ValidateJsonStructure(const QJsonObject& json, const std::string& configType) const;
};
