﻿// DialogFixture.cpp : 实现文件
//

#include "stdafx.h"
#include "FStation.h"
#include "DialogFixture.h"
#include "afxdialogex.h"

#include "LogicMgr.h"

// CDialogFixture 对话框

IMPLEMENT_DYNAMIC(CDialogFixture, CDialogEx)

CDialogFixture::CDialogFixture(CPoint pt, CWnd* pParent /*=NULL*/)
	: CDialogEx(CDialogFixture::IDD, pParent)
{
	m_pt = pt;
}

CDialogFixture::~CDialogFixture()
{
}

void CDialogFixture::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_BUTTON1, m_btnReset);
	DDX_Control(pDX, IDC_BUTTON2, m_btnStart);
	DDX_Control(pDX, IDC_BUTTON3, m_btnPause);
	DDX_Control(pDX, IDC_BUTTON4, m_btnStop);
}

BEGIN_MESSAGE_MAP(CDialogFixture, CDialogEx)
	ON_BN_CLICKED(IDC_BUTTON1, &CDialogFixture::OnBnClickedButton1)
	ON_BN_CLICKED(IDC_BUTTON2, &CDialogFixture::OnBnClickedButton2)
	ON_BN_CLICKED(IDC_BUTTON3, &CDialogFixture::OnBnClickedButton3)
	ON_BN_CLICKED(IDC_BUTTON4, &CDialogFixture::OnBnClickedButton4)
	ON_BN_CLICKED(IDC_BUTTON5, &CDialogFixture::OnBnClickedButton5)
	ON_BN_CLICKED(IDC_BUTTON6, &CDialogFixture::OnBnClickedButton6)
	ON_BN_CLICKED(IDC_BUTTON7, &CDialogFixture::OnBnClickedButton7)
	ON_BN_CLICKED(IDC_BUTTON8, &CDialogFixture::OnBnClickedButton8)
	ON_BN_CLICKED(IDC_BUTTON9, &CDialogFixture::OnBnClickedButton9)
	ON_BN_CLICKED(IDC_BUTTON10, &CDialogFixture::OnBnClickedButton10)
	ON_BN_CLICKED(IDC_BUTTON11, &CDialogFixture::OnBnClickedButton11)
	ON_BN_CLICKED(IDC_BUTTON12, &CDialogFixture::OnBnClickedButton12)
	ON_BN_CLICKED(IDC_BUTTON13, &CDialogFixture::OnBnClickedButton13)
	ON_BN_CLICKED(IDC_BUTTON14, &CDialogFixture::OnBnClickedButton14)
	ON_BN_CLICKED(IDC_BUTTON15, &CDialogFixture::OnBnClickedButton15)
	ON_BN_CLICKED(IDC_BUTTON16, &CDialogFixture::OnBnClickedButton16)
	ON_BN_CLICKED(IDC_BUTTON17, &CDialogFixture::OnBnClickedButton17)
	ON_BN_CLICKED(IDC_BUTTON18, &CDialogFixture::OnBnClickedButton18)
	ON_BN_CLICKED(IDC_BUTTON19, &CDialogFixture::OnBnClickedButton19)
	ON_BN_CLICKED(IDC_BUTTON20, &CDialogFixture::OnBnClickedButton20)
	ON_BN_CLICKED(IDC_BUTTON21, &CDialogFixture::OnBnClickedButton21)
	ON_BN_CLICKED(IDC_BUTTON22, &CDialogFixture::OnBnClickedButton22)
	ON_BN_CLICKED(IDC_BUTTON23, &CDialogFixture::OnBnClickedButton23)
	ON_BN_CLICKED(IDC_BUTTON24, &CDialogFixture::OnBnClickedButton24)
	ON_BN_CLICKED(IDC_BUTTON25, &CDialogFixture::OnBnClickedButton25)
	ON_BN_CLICKED(IDC_BUTTON26, &CDialogFixture::OnBnClickedButton26)
	ON_BN_CLICKED(IDC_BUTTON27, &CDialogFixture::OnBnClickedButton27)
	ON_BN_CLICKED(IDC_BUTTON28, &CDialogFixture::OnBnClickedButton28)
	ON_BN_CLICKED(IDC_BUTTON29, &CDialogFixture::OnBnClickedButton29)
	ON_WM_TIMER()
END_MESSAGE_MAP()

BOOL CDialogFixture::PreTranslateMessage( MSG* pMsg )
{
	if ((WM_SYSKEYDOWN == pMsg->message && VK_F4 == pMsg->wParam) || (pMsg->message == WM_KEYDOWN && pMsg->wParam == VK_ESCAPE)) { //屏蔽Alt + F4
		return TRUE;
	}

	return CDialogEx::PreTranslateMessage(pMsg);
}

BOOL CDialogFixture::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	CRect rc;

	GetWindowRect(&rc);

	MoveWindow(m_pt.x, m_pt.y, rc.Width(), rc.Height());

	CString strPath = CString(GetModulePath().c_str());
	strPath += "\\Sys\\images\\";

	CString strName[] = { 
		"resetbk.bmp", 
		"start.bmp", 
		"pause.bmp", 
		"stop.bmp"
	};

	CColorButton *pBtn[] = { 
		&m_btnReset, 
		&m_btnStart, 
		&m_btnPause, 
		&m_btnStop
	};

	for (int i=0; i<4; i++)
	{
		pBtn[i]->SetColor(RGB(240, 240, 240), RGB(0, 0, 0));

		pBtn[i]->SetImagePos(5, 0);
		pBtn[i]->SetIcon(strPath + strName[i], strPath + strName[i], false);

		pBtn[i]->SetFontSize(20, true);
		pBtn[i]->SetTextPos(68, 0);

	}

	SetTimer(99, 100, NULL);

	return TRUE;
}

bool CDialogFixture::Excute()
{
	CString sRet;

	DWORD nTick = 0;

	double nPos = 0;

	nTick = GetTickCount();
	while (!m_bExit)
	{
		nPos = VAR_FIXTURE_D("治具搬运Z轴安全位置");

		sRet = g_pFixture->TransportZMove(nPos);
		if (sRet != "OK") {
			AfxMessageBox("治具搬运Z轴移动失败!");
			return false;
		}

		sRet = g_pFixture->IsTransportZInPos(nPos);
		if (sRet == "Yes") {
			break;
		}

		if (GetTickCount() - nTick > 10000) {
			AfxMessageBox("治具搬运Z轴移动超时!");
			return false;
		}
	}
	
	nTick = GetTickCount();
	while (!m_bExit)
	{
		if (m_eTrack == emTrackA) {
			nPos = VAR_FIXTURE_D("治具搬运Y轴A轨测高位置1");
		}
		else {
			nPos = VAR_FIXTURE_D("治具搬运Y轴B轨测高位置1");
		}

		sRet = g_pFixture->TransportYMove(nPos);
		if (sRet != "OK") {
			AfxMessageBox("治具搬运Y轴移动失败!");
			return false;
		}

		sRet = g_pFixture->IsTransportYInPos(nPos);
		if (sRet == "Yes") {
			break;
		}

		if (GetTickCount() - nTick > 10000) {
			AfxMessageBox("治具搬运Y轴移动超时!");
			return false;
		}
	}

	nTick = GetTickCount();
	while (!m_bExit)
	{
		nPos = VAR_FIXTURE_D("治具搬运Z轴测高位置");

		sRet = g_pFixture->TransportZMove(nPos);
		if (sRet != "OK") {
			AfxMessageBox("治具搬运Z轴移动失败!");
			return false;
		}

		sRet = g_pFixture->IsTransportZInPos(nPos);
		if (sRet == "Yes") {
			break;
		}

		if (GetTickCount() - nTick > 10000) {
			AfxMessageBox("治具搬运Z轴移动超时!");
			return false;
		}
	}

	Sleep(1000);

	double nHeight1 = 0, nHeight2 = 0;

	sRet = g_pHeightSensorL->GetHeight(nHeight1);
	if (sRet != "OK") {
		AfxMessageBox("左测高失败!");
		return false;
	}

	if (m_eTrack == emTrackA) {
		VAR_FIXTURE("治具搬运Y轴A轨测高位置1高度标准值1") = nHeight1;
	}
	else {
		VAR_FIXTURE("治具搬运Y轴B轨测高位置1高度标准值1") = nHeight1;
	}

	sRet = g_pHeightSensorR->GetHeight(nHeight2);
	if (sRet != "OK") {
		AfxMessageBox("右测高失败!");
		return false;
	}

	if (m_eTrack == emTrackA) {
		VAR_FIXTURE("治具搬运Y轴A轨测高位置1高度标准值2") = nHeight2;
	}
	else {
		VAR_FIXTURE("治具搬运Y轴B轨测高位置1高度标准值2") = nHeight2;
	}

	nTick = GetTickCount();
	while (!m_bExit)
	{
		nPos = VAR_FIXTURE_D("治具搬运Z轴安全位置");

		sRet = g_pFixture->TransportZMove(nPos);
		if (sRet != "OK") {
			AfxMessageBox("治具搬运Z轴移动失败!");
			return false;
		}

		sRet = g_pFixture->IsTransportZInPos(nPos);
		if (sRet == "Yes") {
			break;
		}

		if (GetTickCount() - nTick > 10000) {
			AfxMessageBox("治具搬运Z轴移动超时!");
			return false;
		}
	}

	nTick = GetTickCount();
	while (!m_bExit)
	{
		if (m_eTrack == emTrackA) {
			nPos = VAR_FIXTURE_D("治具搬运Y轴A轨测高位置2");
		}
		else {
			nPos = VAR_FIXTURE_D("治具搬运Y轴B轨测高位置2");
		}

		sRet = g_pFixture->TransportYMove(nPos);
		if (sRet != "OK") {
			AfxMessageBox("治具搬运Y轴移动失败!");
			return false;
		}

		sRet = g_pFixture->IsTransportYInPos(nPos);
		if (sRet == "Yes") {
			break;
		}

		if (GetTickCount() - nTick > 10000) {
			AfxMessageBox("治具搬运Y轴移动超时!");
			return false;
		}
	}

	nTick = GetTickCount();
	while (!m_bExit)
	{
		nPos = VAR_FIXTURE_D("治具搬运Z轴测高位置");

		sRet = g_pFixture->TransportZMove(nPos);
		if (sRet != "OK") {
			AfxMessageBox("治具搬运Z轴移动失败!");
			return false;
		}

		sRet = g_pFixture->IsTransportZInPos(nPos);
		if (sRet == "Yes") {
			break;
		}

		if (GetTickCount() - nTick > 10000) {
			AfxMessageBox("治具搬运Z轴移动超时!");
			return false;
		}
	}

	Sleep(1000);

	sRet = g_pHeightSensorL->GetHeight(nHeight1);
	if (sRet != "OK") {
		AfxMessageBox("左测高失败!");
		return false;
	}

	if (m_eTrack == emTrackA) {
		VAR_FIXTURE("治具搬运Y轴A轨测高位置2高度标准值1") = nHeight1;
	}
	else {
		VAR_FIXTURE("治具搬运Y轴B轨测高位置2高度标准值1") = nHeight1;
	}

	sRet = g_pHeightSensorR->GetHeight(nHeight2);
	if (sRet != "OK") {
		AfxMessageBox("右测高失败!");
		return false;
	}

	if (m_eTrack == emTrackA) {
		VAR_FIXTURE("治具搬运Y轴A轨测高位置2高度标准值2") = nHeight2;
	}
	else {
		VAR_FIXTURE("治具搬运Y轴B轨测高位置2高度标准值2") = nHeight2;
	}

	nTick = GetTickCount();
	while (!m_bExit)
	{
		nPos = VAR_FIXTURE_D("治具搬运Z轴安全位置");

		sRet = g_pFixture->TransportZMove(nPos);
		if (sRet != "OK") {
			AfxMessageBox("治具搬运Z轴移动失败!");
			return false;
		}

		sRet = g_pFixture->IsTransportZInPos(nPos);
		if (sRet == "Yes") {
			break;
		}

		if (GetTickCount() - nTick > 10000) {
			AfxMessageBox("治具搬运Z轴移动超时!");
			return false;
		}
	}

	g_pFixture->Save();

	if (m_eTrack == emTrackA) {
		AfxMessageBox("A轨高度标准值测定成功!");
	}
	else {
		AfxMessageBox("B轨高度标准值测定成功!");
	}

	return true;
}

// CDialogFixture 消息处理程序

void CDialogFixture::OnBnClickedButton1()
{
	CLogicMgr::m_mapThread["Fixture"].pThread->Stop();
	CLogicMgr::m_mapThread["FixtureTransport"].pThread->Stop();

	g_pFixture->SeparateCylinderOff();

	g_pFixture->UpLidLiftCylinderOff();

	g_pFixture->DnLidSetPosCylinderOff();

	g_pFixture->UpLidSetPosCylinderOff();

	g_pFixture->SeparateZHome();

	g_pFixture->LidZHome();

	g_pFixture->TransportZHome();
	
	m_bResetFlag = false;

	m_nResetTimer = GetTickCount();

	SetTimer(1, 100, NULL);
}

void CDialogFixture::OnBnClickedButton2()
{
	CLogicMgr::m_mapThread["Fixture"].pThread->Start();
	CLogicMgr::m_mapThread["FixtureTransport"].pThread->Start();
}

void CDialogFixture::OnBnClickedButton3()
{
	CLogicMgr::m_mapThread["Fixture"].pThread->Pause();
	CLogicMgr::m_mapThread["FixtureTransport"].pThread->Pause();
}

void CDialogFixture::OnBnClickedButton4()
{
	CLogicMgr::m_mapThread["Fixture"].pThread->Stop();
	CLogicMgr::m_mapThread["FixtureTransport"].pThread->Stop();
}

void CDialogFixture::OnBnClickedButton5()
{
	PREMISSION_CTRL();

	CString sRet;

	bool bFlag = false;

	bFlag = OnSafeCheck("治具搬运Y轴", &sRet);

	if (!bFlag) {
		MessageBox(sRet);
		return;
	}

	g_pFixture->TransportYMove(VAR_FIXTURE_D("治具搬运Y轴A轨测高位置1"));
}

void CDialogFixture::OnBnClickedButton6()
{
	PREMISSION_CTRL();

	CString sRet;

	bool bFlag = false;

	bFlag = OnSafeCheck("治具搬运Y轴", &sRet);

	if (!bFlag) {
		MessageBox(sRet);
		return;
	}

	g_pFixture->TransportYMove(VAR_FIXTURE_D("治具搬运Y轴A轨测高位置2"));
}

void CDialogFixture::OnBnClickedButton7()
{
	PREMISSION_CTRL();

	if (!isThreadFinish()) {
		AfxMessageBox("请耐心等待测高流程完成!");
		return;
	}

	m_eTrack = emTrackA;

	Start();
} 

void CDialogFixture::OnBnClickedButton8()
{
	PREMISSION_CTRL();

	if (!isThreadFinish()) {
		AfxMessageBox("请耐心等待测高流程完成!");
		return;
	}

	m_eTrack = emTrackB;

	Start();
}

void CDialogFixture::OnBnClickedButton9()
{
	// TODO: 在此添加控件通知处理程序代码
}

void CDialogFixture::OnBnClickedButton10()
{
	PREMISSION_CTRL();

	int nID = IDC_BUTTON10;

	CString str;
	GetDlgItemText(nID, str);

	if (str.Find("伸出") >= 0) {
		g_pFixture->UpLidStopCylinderOn();
		SetDlgItemText(nID, "治具上盖位\n阻挡气缸-缩回");
	}
	else {
		g_pFixture->UpLidStopCylinderOff();
		SetDlgItemText(nID, "治具上盖位\n阻挡气缸-伸出");
	}
}

void CDialogFixture::OnBnClickedButton11()
{
	PREMISSION_CTRL();

	int nID = IDC_BUTTON11;

	CString str;
	GetDlgItemText(nID, str);

	if (str.Find("伸出") >= 0) {
		g_pFixture->SupportCylinderOn();
		SetDlgItemText(nID, "治具入口\n支撑气缸-缩回");
	}
	else {
		g_pFixture->SupportCylinderOff();
		SetDlgItemText(nID, "治具入口\n支撑气缸-伸出");
	}
}

void CDialogFixture::OnBnClickedButton12()
{
	PREMISSION_CTRL();

	int nID = IDC_BUTTON12;

	CString str;
	GetDlgItemText(nID, str);

	if (str.Find("伸出") >= 0) {
		g_pFixture->SeparateCylinderOn();
		SetDlgItemText(nID, "治具入口\n分盘气缸-缩回");
	}
	else {
		g_pFixture->SeparateCylinderOff();
		SetDlgItemText(nID, "治具入口\n分盘气缸-伸出");
	}
}

void CDialogFixture::OnBnClickedButton13()
{
	PREMISSION_CTRL();

	g_pFixture->SeparateZMove(VAR_FIXTURE_D("治具分板Z轴放料位置"));
}

void CDialogFixture::OnBnClickedButton14()
{
	PREMISSION_CTRL();

	g_pFixture->SeparateZMove(VAR_FIXTURE_D("治具分板Z轴分板位置"));
}

void CDialogFixture::OnBnClickedButton15()
{
	PREMISSION_CTRL();

	g_pFixture->SeparateZMove(VAR_FIXTURE_D("治具分板Z轴支撑位置"));
}

void CDialogFixture::OnBnClickedButton16()
{
	PREMISSION_CTRL();

	g_pFixture->LidZMove(VAR_FIXTURE_D("治具开盖Z轴放料位置"));
}

void CDialogFixture::OnBnClickedButton17()
{
	PREMISSION_CTRL();

	g_pFixture->LidZMove(VAR_FIXTURE_D("治具开盖Z轴上层位置"));
}

void CDialogFixture::OnBnClickedButton18()
{
	PREMISSION_CTRL();

	g_pFixture->LidZMove(VAR_FIXTURE_D("治具开盖Z轴下层位置"));
}

void CDialogFixture::OnBnClickedButton19()
{
	PREMISSION_CTRL();

	int nID = IDC_BUTTON19;

	CString str;
	GetDlgItemText(nID, str);

	if (str.Find("伸出") >= 0) {
		g_pFixture->UpLidLiftCylinderOn();
		SetDlgItemText(nID, "治具上盖位\n顶升气缸-缩回");
	}
	else {
		g_pFixture->UpLidLiftCylinderOff();
		SetDlgItemText(nID, "治具上盖位\n顶升气缸-伸出");
	}
}

void CDialogFixture::OnBnClickedButton20()
{
	PREMISSION_CTRL();

	int nID = IDC_BUTTON20;

	CString str;
	GetDlgItemText(nID, str);

	if (str.Find("伸出") >= 0) {
		g_pFixture->RollCylinderOn();
		SetDlgItemText(nID, "治具上盖位\n旋转气缸-缩回");
	}
	else {
		g_pFixture->RollCylinderOff();
		SetDlgItemText(nID, "治具上盖位\n旋转气缸-伸出");
	}
}

void CDialogFixture::OnBnClickedButton21()
{
	PREMISSION_CTRL();

	int nID = IDC_BUTTON21;

	CString str;
	GetDlgItemText(nID, str);

	if (str.Find("开") >= 0) {
		g_pFixture->VacuumOn();
		SetDlgItemText(nID, "治具搬运\n真空-关");
	}
	else {
		g_pFixture->BrokenVacuumOn();
		Sleep(100);
		g_pFixture->VacuumOff();
		SetDlgItemText(nID, "治具搬运\n真空-开");
	}
}

void CDialogFixture::OnBnClickedButton22()
{
	PREMISSION_CTRL();

	CString sRet;

	bool bFlag = false;

	bFlag = OnSafeCheck("治具搬运Y轴", &sRet);

	if (!bFlag) {
		MessageBox(sRet);
		return;
	}

	g_pFixture->TransportYMove(VAR_FIXTURE_D("治具搬运Y轴取下盖位置"));
}

void CDialogFixture::OnBnClickedButton23()
{
	PREMISSION_CTRL();

	CString sRet;

	bool bFlag = false;

	bFlag = OnSafeCheck("治具搬运Y轴", &sRet);

	if (!bFlag) {
		MessageBox(sRet);
		return;
	}

	g_pFixture->TransportYMove(VAR_FIXTURE_D("治具搬运Y轴取上盖位置"));
}

void CDialogFixture::OnBnClickedButton24()
{
	PREMISSION_CTRL();

	CString sRet;

	bool bFlag = false;

	bFlag = OnSafeCheck("治具搬运Y轴", &sRet);

	if (!bFlag) {
		MessageBox(sRet);
		return;
	}

	g_pFixture->TransportYMove(VAR_FIXTURE_D("治具搬运Y轴A轨放下盖位置"));
}

void CDialogFixture::OnBnClickedButton25()
{
	PREMISSION_CTRL();

	CString sRet;

	bool bFlag = false;

	bFlag = OnSafeCheck("治具搬运Y轴", &sRet);

	if (!bFlag) {
		MessageBox(sRet);
		return;
	}

	g_pFixture->TransportYMove(VAR_FIXTURE_D("治具搬运Y轴B轨放下盖位置"));
}

void CDialogFixture::OnBnClickedButton26()
{
	PREMISSION_CTRL();

	CString sRet;

	bool bFlag = false;

	bFlag = OnSafeCheck("治具搬运Y轴", &sRet);

	if (!bFlag) {
		MessageBox(sRet);
		return;
	}
	g_pFixture->TransportYMove(VAR_FIXTURE_D("治具搬运Y轴A轨放上盖位置"));
}

void CDialogFixture::OnBnClickedButton27()
{
	PREMISSION_CTRL();

	CString sRet;

	bool bFlag = false;

	bFlag = OnSafeCheck("治具搬运Y轴", &sRet);

	if (!bFlag) {
		MessageBox(sRet);
		return;
	}

	g_pFixture->TransportYMove(VAR_FIXTURE_D("治具搬运Y轴B轨放上盖位置"));
}

void CDialogFixture::OnBnClickedButton28()
{
	PREMISSION_CTRL();

	CString sRet;

	bool bFlag = false;

	bFlag = OnSafeCheck("治具搬运Y轴", &sRet);

	if (!bFlag) {
		MessageBox(sRet);
		return;
	}

	g_pFixture->TransportYMove(VAR_FIXTURE_D("治具搬运Y轴B轨测高位置1"));
}

void CDialogFixture::OnBnClickedButton29()
{
	PREMISSION_CTRL();

	CString sRet;

	bool bFlag = false;

	bFlag = OnSafeCheck("治具搬运Y轴", &sRet);

	if (!bFlag) {
		MessageBox(sRet);
		return;
	}

	g_pFixture->TransportYMove(VAR_FIXTURE_D("治具搬运Y轴B轨测高位置2"));
}

void CDialogFixture::OnTimer(UINT_PTR nIDEvent)
{
	CString sRet;

	if (nIDEvent == 99) {
		UpdateButtonState();
	}

	if (nIDEvent == 1) {
		if (m_bResetFlag) {
			do 
			{
				sRet = g_pFixture->IsSeparateZHomeOK();
				if (sRet != "Yes") {
					break;
				}

				sRet = g_pFixture->IsLidZHomeOK();
				if (sRet != "Yes") {
					break;
				}

				sRet = g_pFixture->IsTransportYHomeOK();
				if (sRet != "Yes") {
					break;
				}

				KillTimer(1);

				if (GetTickCount() - m_nResetTimer > 30000) {
					AfxMessageBox("复位超时");
					break;
				}

				AfxMessageBox("复位成功");
			} while (false);
		}
		else {
			do 
			{
				sRet = g_pFixture->IsTransportZHomeOK();
				if (sRet != "Yes") {
					break;
				}

				g_pFixture->TransportYHome();

				m_bResetFlag = true;
			} while (false);
		}
	}

	CDialogEx::OnTimer(nIDEvent);
}

void CDialogFixture::UpdateButtonState()
{
	CString strPath = CString(GetModulePath().c_str());
	strPath += "\\Sys\\images\\";

	EnumStatus eStat1 = CLogicMgr::m_mapThread["Fixture"].pThread->GetStatus();
	EnumStatus eStat2 = CLogicMgr::m_mapThread["FixtureTransport"].pThread->GetStatus();

	if (eStat1 == emStop && eStat2 == emStop) {
		m_btnReset.SetIcon(strPath + "resetbk.bmp", strPath + "resetbk.bmp");
		m_btnReset.EnableWindow(TRUE);
	}
	else {
		m_btnReset.SetIcon(strPath + "resetbk_d.bmp", strPath + "resetbk_d.bmp");
		m_btnReset.EnableWindow(TRUE);
	}

	if (eStat1 == emPause || eStat2 == emPause || eStat1 == emStop || eStat2 == emStop) {
		m_btnStart.SetIcon(strPath + "start.bmp", strPath + "start.bmp");
		m_btnStart.EnableWindow(TRUE);
	}
	else {
		m_btnStart.SetIcon(strPath + "start_d.bmp", strPath + "start_d.bmp");
		m_btnStart.EnableWindow(FALSE);
	}

	if (eStat1 == emRun || eStat2 == emRun) {
		m_btnPause.SetIcon(strPath + "pause.bmp", strPath + "pause.bmp");
		m_btnPause.EnableWindow(TRUE);
	}
	else {
		m_btnPause.SetIcon(strPath + "pause_d.bmp", strPath + "pause_d.bmp");
		m_btnPause.EnableWindow(FALSE);
	}

	if (eStat1 == emRun || eStat2 == emRun || eStat1 == emPause || eStat2 == emPause) {
		m_btnStop.SetIcon(strPath + "stop.bmp", strPath + "stop.bmp");
		m_btnStop.EnableWindow(TRUE);
	}
	else {
		m_btnStop.SetIcon(strPath + "stop_d.bmp", strPath + "stop_d.bmp");
		m_btnStop.EnableWindow(FALSE);
	}
}
