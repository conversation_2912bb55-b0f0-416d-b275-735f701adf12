#!/usr/bin/env python3
"""
简单的属性查询测试
"""

import paho.mqtt.client as mqtt
import json
import time

# MQTT 配置
MQTT_BROKER = "192.168.88.44"
MQTT_PORT = 1883
DEVICE_ID = "A320021760"
CLIENT_ID = f"simple_test_{int(time.time())}"

def on_connect(client, userdata, flags, rc):
    if rc == 0:
        print(f"✅ 连接成功")
        response_topic = f"$oc/devices/{DEVICE_ID}/sys/properties/get/response/+"
        client.subscribe(response_topic, qos=2)
        print(f"📥 订阅响应主题")
    else:
        print(f"❌ 连接失败: {rc}")

def on_message(client, userdata, msg):
    print(f"📨 收到响应:")
    print(f"   主题: {msg.topic}")
    print(f"   大小: {len(msg.payload)} 字节")
    try:
        payload = json.loads(msg.payload.decode())
        print(f"   内容: 成功解析JSON，包含 {len(payload.get('services', []))} 个服务")
    except:
        print(f"   内容: JSON解析失败")

def main():
    print("=== 简单属性查询测试 ===")
    
    client = mqtt.Client(CLIENT_ID)
    client.on_connect = on_connect
    client.on_message = on_message
    
    try:
        client.connect(MQTT_BROKER, MQTT_PORT, 60)
        client.loop_start()
        
        time.sleep(2)
        
        # 发送一个简单的属性查询
        request_id = f"simple_test_{int(time.time())}"
        query_topic = f"$oc/devices/{DEVICE_ID}/sys/properties/get/request_id={request_id}"
        
        print(f"📤 发送属性查询 - RequestId: {request_id}")
        result = client.publish(query_topic, json.dumps({}), qos=2)
        
        if result.rc == mqtt.MQTT_ERR_SUCCESS:
            print(f"✅ 请求发送成功")
        else:
            print(f"❌ 请求发送失败: {result.rc}")
        
        # 等待响应
        print("⏳ 等待响应...")
        time.sleep(8)
        
        print("🔚 测试完成")
        
    except Exception as e:
        print(f"❌ 异常: {e}")
    finally:
        client.loop_stop()
        client.disconnect()

if __name__ == "__main__":
    main()
