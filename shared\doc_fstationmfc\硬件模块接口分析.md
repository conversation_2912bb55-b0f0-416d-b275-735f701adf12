# FStation硬件模块接口分析

## 概述

FStation硬件模块系统采用分层抽象设计，通过Module目录下的7个核心模块类封装所有硬件设备的控制接口。每个模块都继承自基类CModule，提供统一的参数管理、数据存储和硬件控制接口。

## 模块架构体系

### 1. 基类系统
```cpp
CModule (基类)
├── 参数管理系统 (MakePair)
├── 数据持久化 (Load/Save)
├── 标准化接口定义
└── 错误处理机制

// 模块继承结构
CModule
├── CMachine        // 机器基础控制
├── CRobot          // 机器人硬件接口
├── CTray           // 托盘硬件控制
├── CFixture        // 治具硬件控制
├── CBelt           // 传送带控制
├── CHeightSensor   // 高度检测
└── (自定义模块扩展)
```

### 2. 参数管理机制
```cpp
// 统一参数管理接口
MakePair(参数名, new CData(默认值, 最小值, 最大值, 可编辑, 权限级别));

// 数据类型支持
CData支持类型：
├── bool        // 布尔开关
├── int         // 整数参数  
├── double      // 浮点数参数
├── CString     // 字符串参数
└── 复合类型    // 自定义结构
```

### 3. 统一数据类型
```cpp
// 标准数据访问接口
B()             // 获取布尔值
I()             // 获取整数值
D()             // 获取浮点值
S()             // 获取字符串值
operator=()     // 赋值操作
```

## 核心硬件模块详解

### 1. Machine模块 (CMachine)
**文件**: Machine.cpp/h (364行)  
**功能**: 机器基础状态管理、安全监控、三色灯控制

#### 核心功能
```cpp
// 三色灯控制
CString RedLightOn();          // 红灯点亮
CString RedLightOff();         // 红灯关闭
CString GreenLightOn();        // 绿灯点亮
CString GreenLightOff();       // 绿灯关闭
CString YellowLightOn();       // 黄灯点亮
CString YellowLightOff();      // 黄灯关闭

// 按钮灯控制
CString BootLightOn();         // 启动按钮灯开
CString BootLightOff();        // 启动按钮灯关
CString NGBeltLightOn();       // NG皮带灯开
CString NGBeltLightOff();      // NG皮带灯关

// 安全监控
CString EmergencyStatus();     // 急停状态
CString SafetyGratingStatus(); // 安全光栅状态
CString LeftSafetyDoorStatus();// 左安全门状态
CString RightSafetyDoorStatus();// 右安全门状态

// NG皮带控制
CString NGBeltOn(bool bDir);   // NG皮带启动(方向)
CString NGBeltOff();           // NG皮带停止
CString NGBeltSignal1Status(); // NG皮带信号1
CString NGBeltSignal3Status(); // NG皮带信号3

// 蜂鸣器控制
CString BuzzerOn();            // 蜂鸣器开
CString BuzzerOff();           // 蜂鸣器关
```

#### 主要参数
```cpp
// 运行模式参数
老化模式：                     false (布尔型，权限级别4)
直通模式：                     false (布尔型，权限级别3)
托盘优先模式：                 false (布尔型，权限级别2)
强制自动模式：                 false (布尔型权限级别4)

// 监控参数
皮带待料超时：                 30000ms (1-999000ms，权限级别2)
皮带拥堵超时：                 30000ms (1-999000ms，权限级别2)
生产周期监控数量：             5 (1-100，权限级别2)
生产周期待料周期数量：         3 (1-100，权限级别2)
皮带来料监控数量：             5 (1-100，权限级别2)
皮带来料拥堵数量：             3 (1-100，权限级别2)
机台待机变灯时间：             90000ms (30000-300000ms，权限级别4)

// NG皮带参数
NG皮带允许放料标志：           true (布尔型)
NG皮带放料完成标志：           true (布尔型)
NG皮带正转方向：               true (布尔型，权限级别4)
NG皮带转动延时：               500ms

// 系统参数
图片存储大小：                 0.35 (0.1-10.0，权限级别4)
图片存储格式：                 "jpg" (字符串，权限级别4)
历史记录天数：                 7 (0-100，权限级别4)
硬盘剩余空间：                 50 (0-100，权限级别4)

// MES集成参数
MES功能启用：                  false (布尔型，权限级别4)
MES服务器地址：                "172.16.255.226" (字符串，权限级别4)
MES工厂：                      "CASMT01" (字符串，权限级别4)
MES用户名：                    "PT11608" (字符串，权限级别4)
MES密码：                      "SMTPT11608" (字符串，权限级别4)
MES响应超时时间：              15000ms (5000-60000ms，权限级别4)
```

#### 核心数据结构
```cpp
// 皮带状态监控
struct INBELTSTATUS {
    int nIndex;                // 皮带索引
    bool bInFlag;              // 来料标志
    DWORD tLastPick;           // 上次取料时间
    DWORD tNowIn;              // 当前进料时间
    DWORD tNowPick;            // 当前取料时间
};
INBELTSTATUS m_stInBeltStatus[3];  // 3条皮带状态

// 生产周期监控
struct PRODUCTIONCIRCLE {
    bool bFree;                // 空闲标志
    // 其他生产周期相关数据
};
PRODUCTIONCIRCLE m_stProductionCircle;
```

### 2. Robot模块 (CRobot)
**文件**: Robot.cpp/h (987行)  
**功能**: 机器人硬件接口、运动控制、吸嘴控制、视觉集成

#### 核心功能
```cpp
// 运动控制
CString Move(ROBOTPOINT stRobPnt, bool bCtrlSpeed = true, 
             bool bSafeCheck = true, CString *pStr = NULL, 
             int nSpeedRate = -1);
CString Move(CString sName, double nX, double nY, double nZ, double nR,
             bool bCtrlSpeed = true, bool bSafeCheck = true,
             CString *pStr = NULL, int nSpeedRate = -1);
CString Move(CString sName, bool bCtrlSpeed = true, 
             bool bSafeCheck = true, CString *pStr = NULL,
             int nSpeedRate = -1);

// 位置检测
CString IsInPos(CString sName);
CString IsInPos(ROBOTPOINT stRobPnt);

// 吸嘴真空控制
CString PickCylinderVacuumOn(int nIndex);       // 吸嘴真空开
CString PickCylinderBrokenVacuumOn(int nIndex); // 吸嘴破真空
CString PickCylinderVacuumOff(int nIndex);      // 吸嘴真空关
CString PickCylinderVacuumStatus(int nIndex);   // 吸嘴真空状态

// 吸嘴气缸控制
CString PickCylinderOn(int nIndex);             // 吸嘴气缸下降
CString PickCylinderOff(int nIndex);            // 吸嘴气缸上升
CString PickCylinderStatus(int nIndex);         // 吸嘴气缸状态

// 皮带来料检测
CString InBeltAInStatus();                      // A皮带来料状态
CString InBeltBInStatus();                      // B皮带来料状态  
CString InBeltCInStatus();                      // C皮带来料状态

// 视觉处理
CString PushImage(unsigned char *pBuff, int nWidth, int nHeight,
                  bool bTranspose, bool bFlipX, bool bFlipY,
                  CString sCamera, int nIndex);
CString PushImageFlow(CImageFlow *pFlow, CImageFlow* pFlowScanCode,
                      CImageWindow* pWnd, CString sDir, CString sName,
                      CString sCode2D, bool bScanCode2D, bool bScanAll,
                      int nHeadIndex, bool bBadCheck, int nIndex);

// 工艺处理
CString ProcPrepare(int nIndex);                // 工艺准备
CString ProcStart(int nIndex);                  // 工艺开始
CString ProcStop(int nIndex);                   // 工艺停止

// 位置点管理
void CreateRobotPoint(CString sName);           // 创建位置点
void SaveRobotPoint(CString sName);             // 保存位置点
void SaveMainBoardBackPos(int nIndex);          // 保存主板回拍位置
```

#### 主要参数
```cpp
// 运行模式
调试模式：                     false (布尔型，权限级别3)
纯收板模式：                   false (布尔型，权限级别3)
双主板装配模式：               false (布尔型)
MARK全拍模式：                 false (布尔型，权限级别4)
A轨自动计算补偿：              false (布尔型，权限级别3)
B轨自动计算补偿：              false (布尔型，权限级别3)

// 托盘参数
TRAY盘主板行数：               3
TRAY盘主板列数：               5
TRAY盘进料标志：               false
TRAY盘退料标志：               false

// 高度补偿参数
Tray取料高度偏移：             0.0mm
Tray放料高度偏移：             0.0mm
皮带取料高度偏移：             0.0mm
A轨贴装高度偏移：              0.0mm
B轨贴装高度偏移：              0.0mm
治具取主板高度偏移：           0.0mm
A轨贴装角度偏移：              0.0°
B轨贴装角度偏移：              0.0°

// 位置补偿参数（每个吸嘴独立）
皮带来料吸嘴1-4取料补偿X：     0.0mm (±9999.999，权限级别3)
皮带来料吸嘴1-4取料补偿Y：     0.0mm (±9999.999，权限级别3)
TRAY盘来料吸嘴1-4取料补偿X：   0.0mm (±9999.999，权限级别3)
TRAY盘来料吸嘴1-4取料补偿Y：   0.0mm (±9999.999，权限级别3)

// 贴装补偿参数（A轨B轨各4个位置）
A轨位置1-4贴装补偿X/Y/R：      0.0mm/° (±9999.999，权限级别3)
B轨位置1-4贴装补偿X/Y/R：      0.0mm/° (±9999.999，权限级别3)

// 全局补偿
贴装位置补偿X/Y/R：            0.0mm/° (±10.0，权限级别4)

// 动作延时
吸嘴吸料延时：                 200ms
吸嘴贴装延时：                 200ms

// 速度控制
机械手速度百分比：             100% (1-100%)
机械手Z轴上升速度百分比：      100% (1-100%)
机械手Z轴下降速度百分比：      100% (1-100%)

// 相机参数
上相机(治具)曝光时间：         10000μs
上相机(治具)增益：             0
上相机(治具二维码)曝光时间：   10000μs
上相机(治具二维码)增益：       0
上相机(皮带)曝光时间：         10000μs
上相机(皮带)增益：             0
下相机曝光时间：               10000μs
下相机增益：                   0
下相机(参考位)曝光时间：       10000μs
下相机(参考位)增益：           0
TRAY相机曝光时间：             10000μs
TRAY相机增益：                 0

// 治具角度
A轨治具角度R：                 0.0° (±9999.999，权限级别3)
B轨治具角度R：                 0.0° (±9999.999，权限级别3)

// 二维码功能
治具二维码扫描功能启用：       false
治具二维码位置偏移X：          32.0mm (-100.0~100.0，权限级别2)
治具二维码位置偏移Y：          32.0mm (-100.0~100.0，权限级别2)

// 主板回拍
主板回拍标志：                 false
主板回拍位置偏移X：            0.0mm (-100.0~100.0，权限级别2)
主板回拍位置偏移Y：            18.0mm (-100.0~100.0，权限级别2)
```

#### 图像缓存管理
```cpp
// 相机图像缓存
unsigned char *m_pImageBufferUp[4];     // 上相机缓存（4个吸嘴）
unsigned char *m_pImageBufferDn[4];     // 下相机缓存（4个吸嘴）
unsigned char *m_pImageBufferTray;      // TRAY相机缓存

// 图像尺寸
上相机/下相机：5472 × 3648像素
TRAY相机：4024 × 3036像素
```

### 3. Tray模块 (CTray)
**文件**: Tray.cpp/h (586行)  
**功能**: 托盘系统硬件控制、满空托盘管理、升降传送控制

#### 核心功能
```cpp
// 满托盘控制
CString FullTrayBeltOn();              // 满托盘皮带开
CString FullTrayBeltOff();             // 满托盘皮带关
CString FullTrayUpDnZMove(double nPos); // 满托盘Z轴移动
CString FullTrayUpDnZStop();           // 满托盘Z轴停止
CString IsFullTrayUpDnZInPos(double nPos); // 满托盘Z轴到位检测

// 空托盘控制
CString EmptyTrayBeltOn();             // 空托盘皮带开
CString EmptyTrayBeltOff();            // 空托盘皮带关
CString EmptyTrayTransportYMove(double nPos); // 空托盘Y轴移动
CString EmptyTrayTransportYStop();     // 空托盘Y轴停止
CString IsEmptyTrayTransportYInPos(double nPos); // 空托盘Y轴到位检测

// 空托盘分盘
CString EmptyTraySeparateZMove(double nPos); // 分盘Z轴移动
CString EmptyTraySeparateZStop();      // 分盘Z轴停止
CString IsEmptyTraySeparateZInPos(double nPos); // 分盘Z轴到位检测

// 状态检测
CString FullTraySignal1Status();       // 满托盘信号1
CString FullTraySignal2Status();       // 满托盘信号2
CString FullTraySignal3Status();       // 满托盘信号3
CString EmptyTraySignal1Status();      // 空托盘信号1
CString EmptyTraySignal2Status();      // 空托盘信号2
CString EmptyTraySignal3Status();      // 空托盘信号3
```

#### 主要参数
```cpp
// 满托盘参数
满TRAY盘进料标志：             false
满TRAY盘退料标志：             false
满TRAY盘皮带速度：             50% (1-100%)
满TRAY盘升降Z轴待机位：        0.0mm
满TRAY盘升降Z轴工作位：        10.0mm
满TRAY盘升降Z轴速度：          50% (1-100%)

// 空托盘参数
空TRAY盘进料标志：             false
空TRAY盘退料标志：             false
空TRAY盘皮带速度：             50% (1-100%)
空TRAY盘传送Y轴待机位：        0.0mm
空TRAY盘传送Y轴工作位：        100.0mm
空TRAY盘传送Y轴速度：          50% (1-100%)

// 分盘机构参数
空TRAY盘分盘Z轴支撑位：        0.0mm
空TRAY盘分盘Z轴分离位：        20.0mm
空TRAY盘分盘Z轴速度：          30% (1-100%)

// 延时参数
满TRAY盘进料延时：             2000ms
满TRAY盘退料延时：             3000ms
空TRAY盘进料延时：             2000ms
空TRAY盘退料延时：             3000ms
分盘动作延时：                 1000ms
```

### 4. Fixture模块 (CFixture)
**文件**: Fixture.cpp/h (735行)  
**功能**: 治具系统控制、治具开合、分板机构、搬运系统

#### 核心功能
```cpp
// 治具上传皮带
CString UploadBeltOn();                // 上传皮带开
CString UploadBeltOff();               // 上传皮带关

// 治具开合控制
CString LidZMove(double nPos);         // 治具盖Z轴移动
CString LidZStop();                    // 治具盖Z轴停止
CString IsLidZInPos(double nPos);      // 治具盖Z轴到位检测

// 分板机构
CString SeparateZMove(double nPos);    // 分板Z轴移动
CString SeparateZStop();               // 分板Z轴停止
CString IsSeparateZInPos(double nPos); // 分板Z轴到位检测

// 治具传输
CString TransportYMove(double nPos);   // 传输Y轴移动
CString TransportYStop();              // 传输Y轴停止
CString IsTransportYInPos(double nPos); // 传输Y轴到位检测

CString TransportZMove(double nPos);   // 传输Z轴移动
CString TransportZStop();              // 传输Z轴停止  
CString IsTransportZInPos(double nPos); // 传输Z轴到位检测

// 治具夹紧
CString ClampOn();                     // 治具夹紧
CString ClampOff();                    // 治具松开
CString ClampStatus();                 // 治具夹紧状态

// 状态检测
CString UploadSignal1Status();         // 上传信号1
CString UploadSignal2Status();         // 上传信号2
CString FixtureSignal1Status();        // 治具信号1
CString FixtureSignal2Status();        // 治具信号2
CString SeparateSignal1Status();       // 分板信号1
CString SeparateSignal2Status();       // 分板信号2
```

#### 主要参数
```cpp
// 治具上传参数
治具上传皮带速度：             50% (1-100%)
治具上传信号延时：             500ms

// 治具开合参数
治具盖Z轴关闭位：              0.0mm
治具盖Z轴打开位：              50.0mm
治具盖Z轴速度：                30% (1-100%)
治具开合延时：                 1000ms

// 分板机构参数
分板Z轴初始位：                0.0mm
分板Z轴工作位：                30.0mm
分板Z轴速度：                  20% (1-100%)
分板动作延时：                 2000ms

// 传输系统参数
传输Y轴待机位：                0.0mm
传输Y轴A轨位：                 150.0mm
传输Y轴B轨位：                 300.0mm
传输Y轴速度：                  50% (1-100%)

传输Z轴上升位：                0.0mm
传输Z轴下降位：                20.0mm
传输Z轴速度：                  30% (1-100%)

// 夹紧参数
治具夹紧延时：                 500ms
治具松开延时：                 300ms

// 流程控制标志
治具预警标志：                 false
治具上传完成标志：             false
治具传输完成标志：             false
分板完成标志：                 false
```

### 5. Belt模块 (CBelt)
**文件**: Belt.cpp/h (212行)  
**功能**: 传送带控制、A轨B轨管理、传送速度控制

#### 核心功能
```cpp
// 传送带控制
CString BeltOn();                      // 传送带启动
CString BeltOff();                     // 传送带停止
CString BeltReverse();                 // 传送带反转

// 状态检测  
CString BeltSignal1Status();           // 传送带信号1
CString BeltSignal2Status();           // 传送带信号2
CString BeltSignal3Status();           // 传送带信号3
CString BeltSignal4Status();           // 传送带信号4

// 阻挡器控制
CString BlockerUp();                   // 阻挡器上升
CString BlockerDown();                 // 阻挡器下降
CString BlockerStatus();               // 阻挡器状态
```

#### 主要参数
```cpp
// 传送带基础参数
轨道启用标志：                 true (布尔型)
传送带速度：                   50% (1-100%)
传送带方向：                   正转 (布尔型)

// 信号检测参数
来料检测延时：                 100ms
出料检测延时：                 200ms
信号稳定时间：                 50ms

// 阻挡器参数
阻挡器动作延时：               300ms
阻挡器等待时间：               1000ms

// 流程控制
允许机械手装配主板标志：       false
传送带工作完成标志：           false
```

### 6. HeightSensor模块 (CHeightSensor)
**文件**: HeightSensor.cpp/h (166行)  
**功能**: 高度检测、厚度测量、质量控制

#### 核心功能
```cpp
// 高度测量
CString MeasureHeight();               // 执行高度测量
CString GetHeightValue();              // 获取高度值
CString ResetSensor();                 // 传感器复位
CString CalibrateZero();               // 零点标定

// 状态检测
CString SensorStatus();                // 传感器状态
CString SensorReady();                 // 传感器就绪
```

#### 主要参数
```cpp
// 测量参数
高度测量范围：                 100.0mm
测量精度：                     0.001mm
测量速度：                     快速/精密可选

// 标定参数
零点标定值：                   0.000mm
线性标定系数：                 1.000
温度补偿系数：                 0.001

// 控制参数
测量延时：                     100ms
稳定时间：                     50ms
重复测量次数：                 3
```

## 硬件接口统一规范

### 1. 返回值标准
```cpp
// 标准返回值定义
"OK"                                   // 操作成功
"Error: [错误描述]"                    // 操作失败
"On"/"Off"                            // 状态检测结果
"Yes"/"No"                            // 位置检测结果
```

### 2. 错误处理机制
```cpp
// 宏定义错误处理
#define EXCUTE_RETURN(func) \
    do { \
        CString sRet = func; \
        if (sRet != "OK") { \
            return sRet; \
        } \
    } while(0)

// 使用示例
EXCUTE_RETURN(g_pControl->WriteOutput("三色灯红", true));
```

### 3. 参数访问宏
```cpp
// 简化参数访问
#define VAR_MACHINE(NAME)    (*g_pMachine->m_mapParam[NAME])
#define VAR_ROBOT(NAME)      (*g_pRobot->m_mapParam[NAME])
#define VAR_TRAY(NAME)       (*g_pTray->m_mapParam[NAME])
#define VAR_FIXTURE(NAME)    (*g_pFixture->m_mapParam[NAME])

// 类型化访问
#define VAR_MACHINE_B(NAME)  (*g_pMachine->m_mapParam[NAME]).B()
#define VAR_MACHINE_I(NAME)  (*g_pMachine->m_mapParam[NAME]).I()
#define VAR_MACHINE_D(NAME)  (*g_pMachine->m_mapParam[NAME]).D()
#define VAR_MACHINE_S(NAME)  (*g_pMachine->m_mapParam[NAME]).S()
```

## 模块间通信机制

### 1. 全局对象访问
```cpp
// 全局硬件对象
extern CMachine*      g_pMachine;      // 机器控制
extern CRobot*        g_pRobot;        // 机器人控制
extern CTray*         g_pTray;         // 托盘控制
extern CFixture*      g_pFixture;      // 治具控制
extern CBelt*         g_pBeltA;        // A轨控制
extern CBelt*         g_pBeltB;        // B轨控制
extern CHeightSensor* g_pHeightSensor; // 高度检测
```

### 2. 参数同步机制
```cpp
// 跨模块参数引用
if (VAR_ROBOT_B("TRAY盘进料标志")) {
    VAR_TRAY("满TRAY盘进料标志") = true;
}

// 状态标志传递
VAR_MACHINE("NG皮带允许放料标志") = true;
```

### 3. 事件通知机制
```cpp
// 模块间事件通知
机器人完成取料 → 通知托盘系统
治具到位 → 通知机器人系统
传送带故障 → 通知机器监控系统
```

## 配置管理系统

### 1. 参数持久化
```cpp
// 自动保存加载
构造函数：Load();                      // 加载参数
析构函数：Save();                      // 保存参数

// 文件存储格式
.ini格式存储，分模块管理
[Machine] / [Robot] / [Tray] / [Fixture] / [Belt]
```

### 2. 权限级别管理
```cpp
// 5级权限体系
权限级别1：操作员可见
权限级别2：技术员可编辑
权限级别3：工程师可编辑
权限级别4：管理员可编辑
权限级别5：系统级参数
```

### 3. 参数验证
```cpp
// 范围检查
new CData(默认值, 最小值, 最大值, 可编辑标志, 权限级别)

// 类型检查
自动类型转换和验证
```

## 扩展接口设计

### 1. 新模块添加
```cpp
// 继承基类
class CNewModule : public CModule {
public:
    CNewModule() : CModule(权限级别) {
        // 参数定义
        MakePair("参数名", new CData(...));
        Load();
    }
    
    // 实现硬件接口
    CString NewFunction();
};
```

### 2. 标准接口实现
```cpp
// 必须实现的标准接口
virtual CString Init();                // 初始化
virtual CString DeInit();              // 反初始化  
virtual CString Reset();               // 复位
virtual CString SelfCheck();           // 自检
```

### 3. 插件化支持
```cpp
// 动态加载模块
支持DLL插件形式的模块扩展
标准化的模块注册机制
运行时模块发现和加载
```

## 总结

FStation硬件模块系统通过精心设计的分层架构，实现了：

- **统一接口**：所有硬件模块采用统一的接口规范
- **参数管理**：完善的参数管理和持久化机制
- **错误处理**：标准化的错误处理和状态报告
- **模块通信**：高效的模块间通信和数据共享
- **配置管理**：灵活的配置管理和权限控制
- **扩展支持**：良好的可扩展性和插件化支持

该架构为复杂工业设备的硬件控制提供了坚实的基础，体现了现代工业软件设计的最佳实践。 