﻿#include "stdafx.h"
#include "LogicBelt.h"

#include "Sys.h"
#include "LogicMgr.h"

#include "Mes.h"

#define PARAM(NAME)			(*m_pBelt->m_mapParam[NAME])

#define PARAM_BOOL(NAME)	(*m_pBelt->m_mapParam[NAME]).B()
#define PARAM_INT(NAME)		(*m_pBelt->m_mapParam[NAME]).I()
#define PARAM_DOUBLE(NAME)	(*m_pBelt->m_mapParam[NAME]).D()
#define PARAM_STRING(NAME)	(*m_pBelt->m_mapParam[NAME]).S()

CLogicBelt::CLogicBelt(CString sTrack)
{
	m_sTrack = sTrack;

	m_pBelt = m_sTrack.Find("A") >= 0 ? g_pBeltA : g_pBeltB;

}

CLogicBelt::~CLogicBelt()
{
}

EnumStatus CLogicBelt::OnSafeCheck()
{
	if (!CSys::m_bInit) {
		return emStop;
	}

	return emRun;
}

EnumStatus CLogicBelt::OnStart()
{
	return emRun;
}

EnumStatus CLogicBelt::OnPause()
{
	m_pBelt->BeltOff();

	return emRun;
}

EnumStatus CLogicBelt::OnResume()
{
	map<CString, DWORD>::iterator it = m_mapTick.begin();
	for (; it!=m_mapTick.end(); it++)
	{
		it->second = GetTickCount();
	}

	return emRun;
}

EnumStatus CLogicBelt::OnStop()
{
	m_pBelt->BeltOff();

	m_pBelt->NextStationTrackRequestOff();

	return emRun;
}

CStatus CLogicBelt::OnRun()
{
	PARAM("自检完成标志") = false;

	PARAM("允许治具下盖上料标志") = false;
	
	PARAM("治具下盖上料完成标志") = false;

	PARAM("治具提前取上盖标志") = false;

	PARAM("允许治具合盖标志") = false;

	PARAM("治具合盖完成标志") = false;

	PARAM("允许机械手装配主板标志") = false;

	PARAM("机械手装配主板完成标志") = false;

	PARAM("皮带有料标志") = false;

	RETURN_STATE(&CLogicBelt::OnSelfCheck00, true);
}

CStatus CLogicBelt::OnSelfCheck00()
{
	RUN_STOP_IF_ERROR(m_pBelt->FixtureAssembleStopCylinderOff());

	RUN_STOP_IF_ERROR(m_pBelt->FixtureAssembleStopCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具合盖位阻挡气缸缩回", true);
	}

	RUN_STOP_IF_ERROR(m_pBelt->FixtureAssembleSetPosCylinderOff());

	RUN_STOP_IF_ERROR(m_pBelt->FixtureAssembleSetPosCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具合盖位阻挡侧定位气缸缩回", true);
	}

	RUN_STOP_IF_ERROR(m_pBelt->MainBoardSetPosCylinderOff());

	RUN_STOP_IF_ERROR(m_pBelt->MainBoardSetPosCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待主板装配位阻挡气缸缩回", true);
	}

	m_mapTick["自检皮带计时"] = GetTickCount();

	RETURN_STATE(&CLogicBelt::OnSelfCheck01, true);
}

CStatus CLogicBelt::OnSelfCheck01()
{
	RUN_STOP_IF_ERROR(m_pBelt->BeltOn(!PARAM_BOOL("皮带正转方向")));

	RUN_STOP_IF_ERROR(m_pBelt->MainBoardInPosStatus());

	if (m_sRet == "On") {
		RUN_STOP_IF_ERROR(m_pBelt->BeltOff());
		REPORT("传送带上自检有料，请取走!", emLogLevelWarn);
		MESSAGEBOX("传送带上自检有料，请取走!", "", false);
		m_mapTick["自检皮带计时"] = GetTickCount();
		RETURN_SELF("", false);
	}

	if (GetTickCount() - m_mapTick["自检皮带计时"] < 5000) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(m_pBelt->BeltOff());

	RETURN_STATE(&CLogicBelt::OnSelfCheck02, true);
}

CStatus CLogicBelt::OnSelfCheck02()
{
	PARAM("自检完成标志") = true;

	PARAM("允许治具下盖上料标志") = true;

	RETURN_STATE(&CLogicBelt::OnBelt00, true);
}

CStatus CLogicBelt::OnBelt00()
{
	if (VAR_MACHINE_B("直通模式")) {
		RUN_STOP_IF_ERROR(m_pBelt->MainBoardInPosStatus());
		if (m_sRet == "On") {
			RETURN_STATE(&CLogicBelt::OnBelt09, true);
		}
		else {
			RETURN_SELF("直通模式等待放板", false);
		}
	}

	if (!PARAM_BOOL("治具下盖上料完成标志")) {
		RETURN_SELF("等待治具下盖上料", false);
	}

	PARAM("治具下盖上料完成标志") = false;

	PARAM("皮带有料标志") = true;

	RETURN_STATE(&CLogicBelt::OnBelt01, true);
}

CStatus CLogicBelt::OnBelt01()
{
	RUN_STOP_IF_ERROR(m_pBelt->BeltOn(!PARAM_BOOL("皮带正转方向")));

	RUN_STOP_IF_ERROR(m_pBelt->MainBoardInPosStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待传送带将治具下盖运输到主板装配位", true);
	}

	m_mapTick["皮带A运输计时"] = GetTickCount();

	RETURN_STATE(&CLogicBelt::OnBelt02, true);
}

CStatus CLogicBelt::OnBelt02()
{
	RUN_STOP_IF_ERROR(m_pBelt->BeltOn(!PARAM_BOOL("皮带正转方向")));

	if (GetTickCount() - m_mapTick["皮带A运输计时"] < (DWORD)PARAM_INT("治具到主板装配位皮带转动延时")) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(m_pBelt->BeltOff());

//	RUN_STOP_IF_ERROR(m_pBelt->MainBoardSetPosCylinderOn());
	
	m_mapTick["MARK拍照延时计时"] = GetTickCount();
	
	RETURN_STATE(&CLogicBelt::OnBelt02_0, true);
}

CStatus CLogicBelt::OnBelt02_0()
{
	if (GetTickCount() - m_mapTick["MARK拍照延时计时"] < PARAM_INT("MARK拍照延时")) {
		RETURN_SELF("", false);
	}
	
	PARAM("允许机械手装配主板标志") = true;

	PARAM("皮带有料标志") = false;

	RETURN_STATE(&CLogicBelt::OnBelt03, true);
}

CStatus CLogicBelt::OnBelt03()
{
	if (!PARAM_BOOL("机械手装配主板完成标志")) {
		RETURN_SELF("等待机械手装配完成", false);
	}

	PARAM("机械手装配主板完成标志") = false;

	RETURN_STATE(&CLogicBelt::OnBelt04, true);
}

CStatus CLogicBelt::OnBelt04()
{
	RUN_STOP_IF_ERROR(m_pBelt->MainBoardSetPosCylinderOff());

	RUN_STOP_IF_ERROR(m_pBelt->FixtureAssembleStopCylinderOn());

	RUN_STOP_IF_ERROR(m_pBelt->MainBoardSetPosCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待主板装配位侧定位气缸缩回", true);
	}

	RUN_STOP_IF_ERROR(m_pBelt->FixtureAssembleStopCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待治具合盖位阻挡气缸伸出", true);
	}

	RETURN_STATE(&CLogicBelt::OnBelt05, true);
}

CStatus CLogicBelt::OnBelt05()
{
	RUN_STOP_IF_ERROR(m_pBelt->BeltOn(PARAM_BOOL("皮带正转方向")));
	
	RUN_STOP_IF_ERROR(m_pBelt->FixtureAssembleInPosStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待治具运动到治具装配位", true);
	}

	m_mapTick["皮带A运输计时"] = GetTickCount();

	RETURN_STATE(&CLogicBelt::OnBelt06, true);
}

CStatus CLogicBelt::OnBelt06()
{
	RUN_STOP_IF_ERROR(m_pBelt->BeltOn(PARAM_BOOL("皮带正转方向")));

	if (GetTickCount() - m_mapTick["皮带A运输计时"] < (DWORD)PARAM_INT("治具到治具合盖位皮带转动延时")) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(m_pBelt->BeltOff());

	RETURN_STATE(&CLogicBelt::OnBelt07, true);
}

CStatus CLogicBelt::OnBelt07()
{
	PARAM("允许治具合盖标志") = true;

	RETURN_STATE(&CLogicBelt::OnBelt08, true);
}

CStatus CLogicBelt::OnBelt08()
{
	if (!PARAM_BOOL("治具合盖完成标志")) {
		RETURN_SELF("等待治具合盖完成", false);
	}

	PARAM("治具合盖完成标志") = false;

	RETURN_STATE(&CLogicBelt::OnBelt09, true);
}

CStatus CLogicBelt::OnBelt09()
{
	RUN_STOP_IF_ERROR(m_pBelt->FixtureAssembleSetPosCylinderOff());
 
// 	RUN_STOP_IF_ERROR(m_pBelt->FixtureAssembleSetPosCylinderStatus());
// 
// 	if (m_sRet != "Off") {
// 		RETURN_SELF("等待治具装配位侧定位气缸缩回", true);
// 	}

	RETURN_STATE(&CLogicBelt::OnBelt10, true);
}

CStatus CLogicBelt::OnBelt10()
{
	RUN_STOP_IF_ERROR(m_pBelt->FixtureAssembleStopCylinderOff());

	RUN_STOP_IF_ERROR(m_pBelt->FixtureAssembleStopCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具合盖位阻挡气缸缩回", true);
	}

	RETURN_STATE(&CLogicBelt::OnBelt11, true);
}

CStatus CLogicBelt::OnBelt11()
{
	RUN_STOP_IF_ERROR(m_pBelt->BeltOn(PARAM_BOOL("皮带正转方向")));

	RUN_STOP_IF_ERROR(m_pBelt->FixtureOutInPosStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待治具运动到治具出料位", true);
	}

	RUN_STOP_IF_ERROR(m_pBelt->BeltOff());

	if (VAR_MACHINE_B("直通模式")) {
		RUN_STOP_IF_ERROR(m_pBelt->NextStationTrackRequestOn());
		RETURN_STATE(&CLogicBelt::OnBelt13, true);
	}
	
	CString sValue, sRes, sErr;
	sValue.Format("%.06f", VAR_FIXTURE_D("合盖高度差正常范围"));

	bool bRes = false;
	if (VAR_MACHINE_B("MES参数上传功能启用")) {
		bRes = CMes::HttpPostUploadParam(VAR_MACHINE_S("MES参数上传服务器地址"), 80, "F-Height1", sValue, VAR_MACHINE_S("MES参数上传用户名"), VAR_MACHINE_S("MES参数上传密码"), VAR_MACHINE_S("MES参数上传工厂"), &sRes);
	}
	else {
		bRes = true;
	}

	if (!bRes) {
		MESSAGEBOX(sRes + ",请取走治具【" + m_sTrack + "】", "", false);
		RETURN_STATE(&CLogicBelt::OnBelt11_0, true);
	}
	else {
		REPORT(CString("MES参数上传功能回传数据: ") + sRes, emLogLevelNormal);
	}

	RETURN_STATE(&CLogicBelt::OnBelt12, true);
}

CStatus CLogicBelt::OnBelt11_0()
{
	CString sRet;
	
	RUN_STOP_IF_ERROR(m_pBelt->FixtureOutInPosStatus());
	if (m_sRet != "Off") {
		RETURN_SELF("等待治具被取走", true);
	}
	
	RETURN_STATE(&CLogicBelt::OnBelt12, true);
}

CStatus CLogicBelt::OnBelt12()
{
	CString sRet;
	
	if (m_sTrack.Find("A") >= 0) {
		if (VAR_BELTA_B("治具合盖异常标志")) {
			sRet = "A轨（2轨）测高异常，请检查合盖是否异常或主板是否放好,请取走治具后点击确定!";
			REPORT(sRet, emLogLevelError);
			MESSAGEBOX(sRet, "A轨测高", false);
			RUN_STOP_IF_ERROR(m_pBelt->FixtureOutInPosStatus());
			if (m_sRet != "Off") {
				RETURN_SELF("等待治具被取走", true);
			}
			VAR_BELTA("治具合盖异常标志") = false;
			SendMessage(g_hMainWnd, UM_UPDATE_PRO, 1, 0);
			RETURN_STATE(&CLogicBelt::OnBelt14, true);
		}
		else {
			SendMessage(g_hMainWnd, UM_UPDATE_PRO, 0, 0);
		}
	}
	else {
		if (VAR_BELTB_B("治具合盖异常标志")) {
			sRet = "B轨（1轨）测高异常，请检查合盖是否异常或主板是否放好,请取走治具后点击确定!";
			REPORT(sRet, emLogLevelError);
			MESSAGEBOX(sRet, "B轨测高", false);
			RUN_STOP_IF_ERROR(m_pBelt->FixtureOutInPosStatus());
			if (m_sRet != "Off") {
				RETURN_SELF("等待治具被取走", true);
			}
			VAR_BELTB("治具合盖异常标志") = false;
			SendMessage(g_hMainWnd, UM_UPDATE_PRO, 1, 1);
			RETURN_STATE(&CLogicBelt::OnBelt14, true);
		}
		else {
			SendMessage(g_hMainWnd, UM_UPDATE_PRO, 0, 1);
		}
	}

	RUN_STOP_IF_ERROR(m_pBelt->NextStationTrackRequestOn());

	RETURN_STATE(&CLogicBelt::OnBelt13, true);
}

CStatus CLogicBelt::OnBelt13()
{
	CString sRet1, sRet2;

	RUN_STOP_IF_ERROR(m_pBelt->NextStationTrackRequestStatus());
	sRet1 = m_sRet;

	RUN_STOP_IF_ERROR(m_pBelt->FixtureOutInPosStatus());
	sRet2 = m_sRet;

	if (sRet2 != "On") {
		PARAM("允许治具下盖上料标志") = true;
		RETURN_STATE(&CLogicBelt::OnBelt00, true);
	}

	if (sRet1 != "On") {
		RETURN_SELF("等待下游请求放料信号", false);
	}

	RETURN_STATE(&CLogicBelt::OnBelt14, true);
}

CStatus CLogicBelt::OnBelt14()
{
	RUN_STOP_IF_ERROR(m_pBelt->BeltOn(PARAM_BOOL("皮带正转方向")));

	RUN_STOP_IF_ERROR(m_pBelt->FixtureOutInPosStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具离开治具出料位", true);
	}

	m_mapTick["治具离开治具出料位皮带转动计时"] = GetTickCount();

	RETURN_STATE(&CLogicBelt::OnBelt15, true);
}

CStatus CLogicBelt::OnBelt15()
{
	if (GetTickCount() - m_mapTick["治具离开治具出料位皮带转动计时"] < (DWORD)PARAM_INT("治具离开治具出料位皮带转动延时")) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(m_pBelt->BeltOff());

	PARAM("允许治具下盖上料标志") = true;

	m_mapTick["请求下料信号关闭计时"] = GetTickCount();

	RETURN_STATE(&CLogicBelt::OnBelt16, true);
}

CStatus CLogicBelt::OnBelt16()
{
	if (GetTickCount() - m_mapTick["请求下料信号关闭计时"] < 3000) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(m_pBelt->NextStationTrackRequestOff());

	RETURN_STATE(&CLogicBelt::OnBelt00, true);
}
