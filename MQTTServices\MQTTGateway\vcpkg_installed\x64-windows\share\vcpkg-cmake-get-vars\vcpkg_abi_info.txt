cmake 3.30.1
cmake-get-vars.cmake.in 2c9f7cdefba85c7a6df2e881888681e85fb4ab7593f1647ea9897d5c1df3ce66
cmake_get_vars\CMakeLists.txt 33c9cdd0a0945e590ea1dd6b9744c86783e6a6ca1c070943b297bb383d15f134
features core
portfile.cmake 0a6c61926f6d1af5ed6e02b8df4dd8df81d3988a263c2849a38cef364e290e81
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
powershell 7.2.24
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
vcpkg-cmake 8112fdacac7427b4feea02a5a660bd2fb97e5b898a970d195ff6370d43a794fb
vcpkg-port-config.cmake 4fd4c2e909bbdf069eb3c59b4b847b0b386cdb41840714e12b34b7eff41f9e22
vcpkg.json c63654032f0c0f79ffc9932cfade8a13d68fc607e823d3ea17cc4d24b4e76bac
vcpkg_cmake_get_vars.cmake f80132758a6a7d0930697814c0fd2b719a06ee092bf60fab96d3d9cfef0e4c2b
