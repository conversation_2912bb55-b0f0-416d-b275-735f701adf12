﻿#include "stdafx.h"
#include "FStation.h"
#include "DialogProduct.h"
#include "afxdialogex.h"

#include "Sys.h"
#include "LogicMgr.h"

// CDialogProductSwitch 对话框

IMPLEMENT_DYNAMIC(CDialogProduct, CDialogEx)

CDialogProduct::CDialogProduct(CWnd* pParent /*=NULL*/)
	: CDialogEx(CDialogProduct::IDD, pParent)
{

}

CDialogProduct::~CDialogProduct()
{
}

void CDialogProduct::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_LIST1, m_list1);
}

BEGIN_MESSAGE_MAP(CDialogProduct, CDialogEx)
	ON_NOTIFY(NM_CLICK, IDC_LIST1, &CDialogProduct::OnNMClickList1)
	ON_BN_CLICKED(IDC_BUTTON1, &CDialogProduct::OnBnClickedButton1)
	ON_BN_CLICKED(IDC_BUTTON2, &CDialogProduct::OnBnClickedButton2)
	ON_BN_CLICKED(IDC_BUTTON3, &CDialogProduct::OnBnClickedButton3)
	ON_BN_CLICKED(IDC_BUTTON4, &CDialogProduct::OnBnClickedButton4)
	ON_BN_CLICKED(IDC_BUTTON5, &CDialogProduct::OnBnClickedButton5)
END_MESSAGE_MAP()

// CDialogProductSwitch 消息处理程序

BOOL CDialogProduct::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	m_imageList.Create(24, 24, TRUE, 2, 2);

	//Node
	LONG lStyle = 0;
	lStyle = GetWindowLong(m_list1.m_hWnd, GWL_STYLE);//获取当前窗口样式
	lStyle &= ~LVS_TYPEMASK;//清除显位方式位
	lStyle |= LVS_REPORT;
	SetWindowLong(m_list1.m_hWnd, GWL_STYLE, lStyle);

	DWORD dwStyle = 0;
	dwStyle = m_list1.GetExtendedStyle();//获取当前窗口扩展样式
	dwStyle |= LVS_EX_FULLROWSELECT;//选中某行使整行高亮
	dwStyle |= LVS_EX_GRIDLINES;//网格线
	m_list1.SetExtendedStyle(dwStyle);

	m_list1.SetImageList(&m_imageList, LVSIL_SMALL);

	CString sParam[] = { "", "序号", "产品" };
	int		nParamLen[] = { 1, 60, 350 };

	for (int i=0; i<sizeof(nParamLen) / sizeof(int); i++)
	{
		m_list1.InsertColumn(i, sParam[i], i == 1 ? LVCFMT_CENTER : LVCFMT_LEFT, nParamLen[i]);
	}
	
	CString strPath = CString(GetModulePath().c_str());
	CString strProPath = strPath + "\\Pro\\";

	vector<string> vStrPro = ListSubDir(strProPath.GetBuffer());

	for (unsigned int i=0; i<vStrPro.size(); i++)
	{
		m_list1.InsertItem(i, "");
		m_list1.SetItemText(i, 1, (int)(i + 1));
		m_list1.SetItemText(i, 2, vStrPro[i].c_str());
	}

	return TRUE;
}

void CDialogProduct::OnNMClickList1(NMHDR *pNMHDR, LRESULT *pResult)
{
	LPNMITEMACTIVATE pNMItemActivate = reinterpret_cast<LPNMITEMACTIVATE>(pNMHDR);
	
	do 
	{
		m_sPro.Empty();

		if (pNMItemActivate->iItem < 0) {
			break;
		}

		CString sPro;
		sPro = m_list1.GetItemText(pNMItemActivate->iItem, 2);

		GetDlgItem(IDC_STATIC1)->SetWindowText(CString("当前产品：") + sPro);

		int nIndex = 0;
		nIndex = sPro.Find("-");

		CString sProject, sFixture;
		if (nIndex >= 0) {
			sProject = sPro.Left(nIndex);
			sFixture = sPro.Mid(nIndex + 1);
		}
		else {
			sProject = sPro;
			sFixture.Empty();
		}

		SetDlgItemText(IDC_EDIT1, sProject);
		SetDlgItemText(IDC_EDIT2, sFixture);

		m_nRowIndex = pNMItemActivate->iItem;
		m_sPro = sPro;
	} while (false);

	*pResult = 0;
}

void CDialogProduct::OnBnClickedButton1()
{
	if (m_sPro.IsEmpty()) {
		return;
	}

	UpdateData();

	CString sProject, sFixture;
	GetDlgItemText(IDC_EDIT1, sProject);
	GetDlgItemText(IDC_EDIT2, sFixture);

	CString sPro = sProject + "-" + sFixture;

	if (sPro == m_sPro) {
		return;
	}

	bool bFlag = false;

	for (int i=0; i<m_list1.GetItemCount(); i++)
	{
		if (m_list1.GetItemText(i, 2) == sPro) {
			bFlag = true;
			break;
		}
	}

	if (bFlag) {
		AfxMessageBox("产品已存在");
		return;
	}

	CString strPath = CString(GetModulePath().c_str());
	CString strProPath = strPath + "\\Pro\\";

	rename(strProPath + m_sPro, strProPath + sPro);

	m_sPro = sPro;

	m_list1.SetItemText(m_nRowIndex, 2, sPro);
}

void CDialogProduct::OnBnClickedButton2()
{
	if (m_sPro.IsEmpty()) {
		return;
	}

	CString strPath = CString(GetModulePath().c_str());
	CString strProPath = strPath + "\\Pro\\";
	CString strCompress = strPath + "\\Upload\\";

	CreatePath(strCompress.GetBuffer());
	strCompress.ReleaseBuffer();

	if (CompressDirectory(strProPath + m_sPro, strCompress + m_sPro + ".zip")) {
		AfxMessageBox("压缩成功,路径为: " + strCompress + m_sPro + ".zip");
	}
	else {
		AfxMessageBox("压缩失败!");
	}
}

void CDialogProduct::OnBnClickedButton3()
{
	if (m_sPro.IsEmpty()) {
		return;
	}

	CString sUpload;
	GetDlgItemText(IDC_EDIT3, sUpload);

	if (sUpload.IsEmpty()) {
		AfxMessageBox("请输入FTP服务器上传相对路径(例：\"/yanbanxian/1608/)!");
		return;
	}

	CString strPath = CString(GetModulePath().c_str());
	CString strUpload = strPath + "\\Upload\\";

	CreatePath(strUpload.GetBuffer());
	strUpload.ReleaseBuffer();

	sUpload += m_sPro + ".zip";
	strUpload += m_sPro + ".zip";

	CFileFind ff;
	if (!ff.FindFile(strUpload)) {
		AfxMessageBox("请先压缩产品数据!");
		return;
	}

	if (AfxMessageBox(CString("是否上传产品[") + m_sPro + "]?") == IDNO) {
		return;
	}

	if (FTP_Upload((LPSTR)(LPCSTR)VAR_MACHINE_S("MES产品资料存储地址"), (LPSTR)(LPCSTR)VAR_MACHINE_S("MES产品资料访问用户名"), (LPSTR)(LPCSTR)VAR_MACHINE_S("MES产品资料访问密码"), (LPSTR)(LPCSTR)sUpload, (LPSTR)(LPCSTR)strUpload)) {
		AfxMessageBox("上传成功");
	}
	else {
		AfxMessageBox("上传失败!");
	}
}

void CDialogProduct::OnBnClickedButton4()
{
	UpdateData();

	CString sProject, sFixture;
	GetDlgItemText(IDC_EDIT1, sProject);
	GetDlgItemText(IDC_EDIT2, sFixture);

	CString sPro = sProject + "-" + sFixture;

	bool bFlag = false;

	for (int i=0; i<m_list1.GetItemCount(); i++)
	{
		if (m_list1.GetItemText(i, 2) == sPro) {
			bFlag = true;
			break;
		}
	}

	if (bFlag) {
		AfxMessageBox("产品已存在");
		return;
	}

	int nIndex = 0;
	nIndex = m_list1.GetItemCount();

	m_list1.InsertItem(nIndex, "");
	m_list1.SetItemText(nIndex, 1, nIndex + 1);
	m_list1.SetItemText(nIndex, 2, sPro);

	//增加文件夹
	CreatePath(GetModulePath() + "\\Pro\\" + sPro.GetBuffer());
	sPro.ReleaseBuffer();

	//复制当前机型配置文件信息
	CString strCur = CString(GetModulePath().c_str()) + "\\Pro\\" + CSys::m_strPro.c_str();
	CString strNew = CString(GetModulePath().c_str()) + "\\Pro\\" + sPro;
	CopyDirectory(strCur.GetBuffer(), strNew.GetBuffer());

	AfxMessageBox(CString("创建新产品[") + sPro + "]成功");
}

void CDialogProduct::OnBnClickedButton5()
{
	if (m_sPro.IsEmpty()) {
		return;
	}

	if (m_sPro == CSys::m_strPro.c_str()) {
		return;
	}

	if (CLogicMgr::isRunning() || CLogicMgr::isPause()) {
		AfxMessageBox("设备正在生产中，禁止切换！");
		return;
	}

	CSys::m_strPro = m_sPro.GetBuffer();
	m_sPro.ReleaseBuffer();
	::SendMessage(g_hMainWnd, UM_PRODUCT_SWITCH, 0, 0);
}
