# FStationMFC项目MQTT集成说明

## 概述

本文档专门针对FStationMFC项目的MQTT集成，提供详细的集成步骤和代码示例。

## 文件位置

```
FStationMFC/
└── FStation/
    ├── SocketInterface.h              # MQTT Socket接口头文件
    ├── SocketInterface.cpp            # MQTT Socket接口实现
    ├── FStationMQTTIntegration_Example.cpp  # 集成示例代码
    └── config/
        └── socket_config.json         # Socket配置文件
```

## 集成步骤

### 1. 添加文件到VS2010项目

在Visual Studio 2010中打开FStation.vcxproj项目：

1. **添加头文件**
   - 右键点击"头文件"过滤器
   - 选择"添加" -> "现有项"
   - 添加 `SocketInterface.h`

2. **添加源文件**
   - 右键点击"源文件"过滤器
   - 选择"添加" -> "现有项"
   - 添加 `SocketInterface.cpp`

3. **添加协议文件**
   - 添加 `../../Common/Protocol/SocketProtocol.h` 到项目中

### 2. 配置项目设置

#### 2.1 包含目录
在项目属性中设置包含目录：
```
配置属性 -> C/C++ -> 常规 -> 附加包含目录
```
添加：
```
$(ProjectDir)
$(SolutionDir)..\..\Common\Protocol
```

#### 2.2 库依赖
在项目属性中添加库依赖：
```
配置属性 -> 链接器 -> 输入 -> 附加依赖项
```
添加：
```
ws2_32.lib
```

#### 2.3 预编译头设置
如果项目使用预编译头，在 `stdafx.h` 中添加：
```cpp
#include <winsock2.h>
#include <ws2tcpip.h>
#include <string>
```

### 3. 在现有代码中集成

#### 3.1 应用程序初始化

在 `FStation.cpp` 的 `CFStationApp::InitInstance()` 中添加：

```cpp
#include "SocketInterface.h"

BOOL CFStationApp::InitInstance()
{
    // ... 现有初始化代码 ...
    
    // 初始化MQTT接口
    try {
        // 使用默认配置或自定义配置
        SocketConfig config;
        config.serverIP = "127.0.0.1";
        config.serverPort = 8888;
        CSocketInterface::GetInstance()->SetConfig(config);
        
        TRACE(_T("MQTT接口初始化成功\n"));
    }
    catch (...) {
        TRACE(_T("MQTT接口初始化失败\n"));
    }
    
    // ... 继续现有代码 ...
    return TRUE;
}
```

#### 3.2 应用程序清理

在 `FStation.cpp` 的 `CFStationApp::ExitInstance()` 中添加：

```cpp
int CFStationApp::ExitInstance()
{
    // 清理MQTT接口
    CSocketInterface::DestroyInstance();
    
    // ... 现有清理代码 ...
    return CWinApp::ExitInstance();
}
```

#### 3.3 设备状态上报

在设备状态变化的地方添加上报代码。例如，在 `FStationDlg.cpp` 中：

```cpp
void CFStationDlg::OnDeviceStatusChanged()
{
    // ... 现有状态处理代码 ...
    
    // 上报设备状态
    CString status;
    switch (m_deviceStatus) {
        case DEVICE_RUNNING:
            status = _T("RUNNING");
            break;
        case DEVICE_IDLE:
            status = _T("IDLE");
            break;
        case DEVICE_ERROR:
            status = _T("ERROR");
            break;
        default:
            status = _T("UNKNOWN");
            break;
    }
    
    // 使用便捷宏
    MQTT_REPORT_STATUS(status, m_productionCount, m_errorCount);
}
```

#### 3.4 生产数据上报

在生产完成的地方添加：

```cpp
void CFStationDlg::OnProductionCompleted()
{
    // ... 现有生产完成处理 ...
    
    // 生成产品ID
    CString productId;
    productId.Format(_T("PROD_%08d"), ++m_productionCount);
    
    // 获取批次号
    CString batchNo = GetCurrentBatchNumber();
    
    // 质量结果
    CString quality = (m_lastQualityResult == PASS) ? _T("PASS") : _T("FAIL");
    
    // 上报生产数据
    MQTT_REPORT_PRODUCTION(productId, batchNo, quality);
}
```

#### 3.5 报警事件上报

在报警处理函数中添加：

```cpp
void CFStationDlg::OnAlarmOccurred(int alarmCode, const CString& description)
{
    // ... 现有报警处理 ...
    
    // 转换报警类型
    CString alarmType;
    int severity = 2; // 默认一般严重程度
    
    switch (alarmCode) {
        case ALARM_SENSOR_FAULT:
            alarmType = _T("SENSOR_ERROR");
            severity = 2;
            break;
        case ALARM_COMMUNICATION_FAULT:
            alarmType = _T("COMMUNICATION_ERROR");
            severity = 3;
            break;
        case ALARM_SAFETY_FAULT:
            alarmType = _T("SAFETY_ERROR");
            severity = 4;
            break;
        default:
            alarmType = _T("UNKNOWN_ERROR");
            break;
    }
    
    // 上报报警事件
    MQTT_REPORT_ALARM(alarmType, description, severity);
}
```

### 4. 配置文件设置

编辑 `config/socket_config.json` 文件：

```json
{
  "gateway": {
    "server_ip": "127.0.0.1",
    "server_port": 8888,
    "enabled": true
  },
  "connection": {
    "connect_timeout": 5000,
    "send_timeout": 3000,
    "retry_interval": 10000,
    "auto_reconnect": true
  }
}
```

### 5. 编译和测试

#### 5.1 编译项目
1. 在VS2010中编译FStation项目
2. 确保没有编译错误
3. 检查是否正确链接了ws2_32.lib

#### 5.2 测试MQTT功能
1. 启动MQTT网关程序：`MQTTGateway.exe`
2. 启动FStation程序
3. 检查日志输出，确认MQTT接口初始化成功
4. 触发设备状态变化，观察MQTT网关是否收到消息

### 6. 调试和故障排除

#### 6.1 常见编译错误

**错误：找不到SocketInterface.h**
- 检查文件是否正确添加到项目
- 验证包含目录设置

**错误：无法解析的外部符号 socket**
- 确认已添加ws2_32.lib到链接器依赖

**错误：重定义 SOCKET**
- 确保winsock2.h在windows.h之前包含

#### 6.2 运行时问题

**MQTT接口初始化失败**
- 检查Winsock是否正确初始化
- 验证网络权限设置

**无法连接到MQTT网关**
- 确认MQTT网关程序正在运行
- 检查端口8888是否被占用
- 验证防火墙设置

**消息发送失败**
- 检查网络连接
- 查看MQTT网关日志
- 验证消息格式是否正确

### 7. 性能优化建议

#### 7.1 减少对主线程的影响
```cpp
// 使用异步发送避免阻塞主线程
void AsyncReportStatus(const CString& status, int prodCount, int errCount)
{
    AfxBeginThread([](LPVOID pParam) -> UINT {
        SocketInterface::SendDeviceStatus(
            ((StatusData*)pParam)->status,
            ((StatusData*)pParam)->prodCount,
            ((StatusData*)pParam)->errCount
        );
        delete (StatusData*)pParam;
        return 0;
    }, new StatusData{status, prodCount, errCount});
}
```

#### 7.2 批量发送
```cpp
// 对于高频数据，考虑批量发送
class MQTTBatcher {
private:
    std::vector<CString> m_messages;
    DWORD m_lastFlush;
    
public:
    void AddMessage(const CString& msg) {
        m_messages.push_back(msg);
        if (m_messages.size() >= 10 || GetTickCount() - m_lastFlush > 5000) {
            Flush();
        }
    }
    
    void Flush() {
        // 发送所有缓存的消息
        for (const auto& msg : m_messages) {
            CSocketInterface::SendCustomMessage(_T("BATCH"), msg);
        }
        m_messages.clear();
        m_lastFlush = GetTickCount();
    }
};
```

### 8. 部署注意事项

1. **配置文件**：确保config目录和socket_config.json文件正确部署
2. **MQTT网关**：确保MQTT网关程序在FStation启动前运行
3. **网络配置**：确认本地网络和防火墙设置允许Socket通信
4. **日志监控**：定期检查MQTT相关日志，及时发现问题

### 9. 维护和升级

1. **版本兼容性**：升级时注意接口版本兼容性
2. **配置备份**：升级前备份配置文件
3. **测试验证**：升级后进行完整的功能测试
4. **性能监控**：持续监控MQTT通信性能和稳定性

## 联系支持

如果在集成过程中遇到问题，请：
1. 检查本文档的故障排除部分
2. 查看项目日志文件
3. 参考FStationMQTTIntegration_Example.cpp示例代码
4. 联系技术支持团队 