﻿// FStationDlg.h : 头文件
//

#pragma once

#include "ThreadBase.h"

#include "ColorButton.h"
#include "ColorEdit.h"
#include "ColorList.h"
using namespace yzBase;

#include "Sys.h"

#include "DialogAgv.h"


#define KEY_DOWN(VK_NONAME) ((GetAsyncKeyState(VK_NONAME) & 0x8000) ? 1 : 0)

// Socket命令处理回调函数声明
int OnSocketCommandReceived(const CString& command, const CString& params, const CString& requestId);

class CInBeltMonitor : public CParallel
{
public:
	CInBeltMonitor() {}
	virtual ~CInBeltMonitor() {}

public:
	virtual bool Excute();
};

class CWarnStatus : public CParallel
{
public:
	CWarnStatus() {}
	virtual ~CWarnStatus() {}

public:
	virtual bool Excute();
};

class CLoginStatus;

// CFStationDlg 对话框
class CFStationDlg : public CDialogEx, public CParallel
{
// 构造
public:
	CFStationDlg(CWnd* pParent = NULL);	// 标准构造函数
	virtual ~CFStationDlg();

// 对话框数据
	enum { IDD = IDD_FSTATION_DIALOG };

	enum
	{
		emTimerDoorEnable = 1,
		emTimerLoginTime,
		emTimerDelayLoad,
		emTimerDelayInit,
		emTimerEveryTenMinutes, 
		emTimerDelay,
		emTimerStatus,
		emTimerUpdateRight,
		emTimerEverySencond
	};

protected:
	virtual void DoDataExchange(CDataExchange* pDX);	// DDX/DDV 支持
	virtual BOOL PreTranslateMessage(MSG* pMsg);
	virtual BOOL OnInitDialog();
	virtual void OnOK() {}
	virtual bool Excute();

// 实现
protected:
	// 生成的消息映射函数
	DECLARE_MESSAGE_MAP()
	afx_msg void OnPaint();
	afx_msg void OnTimer(UINT_PTR nIDEvent);
	afx_msg void OnHotKey(UINT nHotKeyId, UINT nKey1, UINT nKey2);
	afx_msg LRESULT OnUmTimeLogin(WPARAM wParam, LPARAM lParam);
	afx_msg LRESULT OnUmUpdateLog(WPARAM wParam, LPARAM lParam);
	afx_msg LRESULT OnUmAxisOff(WPARAM wParam, LPARAM lParam);
	afx_msg LRESULT OnUmUpdatePro(WPARAM wParam, LPARAM lParam);
	afx_msg LRESULT OnUmTimeOut(WPARAM wParam, LPARAM lParam);
	afx_msg LRESULT OnUmProductSwitch(WPARAM wParam, LPARAM lParam);
	afx_msg HCURSOR OnQueryDragIcon();
	afx_msg void OnBnClickedButton1();
	afx_msg void OnBnClickedButton2();
	afx_msg void OnBnClickedButton3();
	afx_msg void OnBnClickedButton4();
	afx_msg void OnBnClickedButton5();
	afx_msg void OnBnClickedButton6();
	afx_msg void OnBnClickedButton7();
	afx_msg void OnBnClickedButton8();
	afx_msg void OnBnClickedButton9();
	afx_msg void OnBnClickedButton10();
	afx_msg void OnBnClickedButton11();
	afx_msg void OnBnClickedButton12();
	afx_msg void OnBnClickedButton13();
	afx_msg void OnBnClickedButton14();

public:
	void FlowCmd(EnumLogLevel emLevel, CString strCmd);

	void UpdateRunStatus();
	void UpdateRightCtrl();
	void UpdateLog(EnumLogLevel emLevel, CString strLog);

	void UpdateDat();

	void UpdateButtonState();
	
	// Socket接口相关函数
	void InitializeSocketInterface();
	void CleanupSocketInterface();
	void OnProductionStatusChanged();

	// MQTT集成相关函数
	void InitMQTTInterface();
	void ProcessMQTTCommands();
	void SendPeriodicStatus();
	void CleanupMQTTInterface();

	// 静态成员
	static int m_nMQTTStatusCounter;

protected:
	HICON				m_hIcon;
	CStatusBar			m_wndStatusBar;

	CColorButton		m_btnLogin;
	CColorButton		m_btnReset;
	CColorButton		m_btnDebug;
	CColorButton		m_btnMaterial;
	CColorButton		m_btnProduction;
	CColorButton		m_btnCheck;
	CColorButton		m_btnHelp;
	CColorButton		m_btnExit;

	CColorButton		m_btnMesEnable;
	CColorButton		m_btnMannual;

	CColorButton		m_btnStart;
	CColorButton		m_btnPause;
	CColorButton		m_btnStop;

	CColorEdit			m_editStatus;

	CColorList			m_listLog;
	CColorList			m_listDat;

	COLORREF			m_BkColor;

	CImageList			m_imgList;

	CDialogAgv*			m_pDlgAgv;

	CWarnStatus*		m_pWarnStatus;

	CInBeltMonitor*		m_pInBeltMonitor;

	CLoginStatus*		m_pLoginStatus;
	

};

class CLoginStatus :public CParallel
{
public:
	CLoginStatus(CFStationDlg* pMainDlg) { m_pMainDlg = pMainDlg; }
	virtual ~CLoginStatus() {}

public:
	virtual bool Excute()
	{
		while (!m_bExit)
		{
			if (KEY_DOWN(VK_LBUTTON)) {
				if (CSys::m_nRight > 1 && CSys::m_nRight < 4) {
					TRACE("检测到鼠标消息!\n");
					m_pMainDlg->SetTimer(m_pMainDlg->emTimerLoginTime, 1800 * 1000, NULL);
				}
			}

			Sleep(100);
		}

		return true;
	}

private:
	CFStationDlg *m_pMainDlg;
};
