#pragma once

#include <string>
#include <unordered_map>
#include <mutex>
#include <chrono>
#include <QTimer>
#include <QObject>
#include <QJsonObject>

/**
 * 命令管理器
 * 
 * 功能：
 * - 管理平台下发的命令执行状态
 * - 处理命令超时
 * - 验证命令参数
 * - 跟踪命令执行结果
 */
class CommandManager : public QObject {
    Q_OBJECT

public:
    /**
     * 命令执行状态
     */
    enum class CommandStatus {
        PENDING,    // 等待执行
        EXECUTING,  // 执行中
        SUCCESS,    // 执行成功
        FAILED,     // 执行失败
        TIMEOUT     // 执行超时
    };

    /**
     * 命令信息结构
     */
    struct CommandInfo {
        std::string requestId;
        std::string commandName;
        std::string commandType;
        QJsonObject commandData;
        CommandStatus status;
        std::chrono::system_clock::time_point startTime;
        std::chrono::system_clock::time_point endTime;
        int timeoutMs;
        std::string resultMessage;
        int resultCode;
        
        CommandInfo() : status(CommandStatus::PENDING), resultCode(-1) {}
    };

private:
    // 正在执行的命令映射 (requestId -> CommandInfo)
    std::unordered_map<std::string, CommandInfo> m_activeCommands;
    
    // 线程安全锁
    mutable std::mutex m_mutex;
    
    // 超时检查定时器
    QTimer* m_timeoutTimer;
    
    // 支持的命令配置
    std::unordered_map<std::string, QJsonObject> m_commandConfigs;

public:
    explicit CommandManager(QObject* parent = nullptr);
    ~CommandManager();
    
    /**
     * 加载命令配置
     */
    bool LoadCommandConfigs(const std::string& configPath);
    
    /**
     * 验证命令格式和参数
     * @param commandData 命令数据
     * @param errorMessage 错误信息输出
     * @return true=验证通过，false=验证失败
     */
    bool ValidateCommand(const QJsonObject& commandData, std::string& errorMessage);
    
    /**
     * 注册新命令
     * @param requestId 请求ID
     * @param commandData 命令数据
     * @return true=注册成功，false=注册失败
     */
    bool RegisterCommand(const std::string& requestId, const QJsonObject& commandData);
    
    /**
     * 标记命令开始执行
     * @param requestId 请求ID
     */
    void MarkCommandExecuting(const std::string& requestId);
    
    /**
     * 标记命令执行完成
     * @param requestId 请求ID
     * @param success 是否成功
     * @param resultCode 结果码
     * @param resultMessage 结果消息
     */
    void MarkCommandCompleted(const std::string& requestId, bool success, 
                             int resultCode, const std::string& resultMessage);
    
    /**
     * 获取命令信息
     * @param requestId 请求ID
     * @param commandInfo 输出参数
     * @return true=找到，false=未找到
     */
    bool GetCommandInfo(const std::string& requestId, CommandInfo& commandInfo) const;
    
    /**
     * 检查命令是否存在
     * @param requestId 请求ID
     * @return true=存在，false=不存在
     */
    bool HasCommand(const std::string& requestId) const;
    
    /**
     * 移除命令
     * @param requestId 请求ID
     */
    void RemoveCommand(const std::string& requestId);
    
    /**
     * 获取统计信息
     */
    struct Statistics {
        size_t activeCommands;      // 活跃命令数
        size_t completedCommands;   // 已完成命令数
        size_t timeoutCommands;     // 超时命令数
        size_t failedCommands;      // 失败命令数
    };
    
    Statistics GetStatistics() const;

private slots:
    /**
     * 超时检查槽函数
     */
    void OnTimeoutCheck();

private:
    /**
     * 检查命令是否超时
     */
    void CheckCommandTimeouts();
    
    /**
     * 验证命令参数
     */
    bool ValidateCommandProperties(const std::string& commandType, 
                                  const QJsonObject& properties, 
                                  std::string& errorMessage);

signals:
    /**
     * 命令超时信号
     */
    void CommandTimeout(const QString& requestId, const QString& commandName);
    
    /**
     * 命令完成信号
     */
    void CommandCompleted(const QString& requestId, bool success, int resultCode, const QString& resultMessage);
};
