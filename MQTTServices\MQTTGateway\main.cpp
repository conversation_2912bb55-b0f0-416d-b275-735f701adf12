#include <QCoreApplication>
#include <QDateTime>
#include <QDebug>
#include <QLoggingCategory>
#include <iostream>
#include <memory>
#include <signal.h>
#include <thread>

#ifdef _WIN32
#include <fcntl.h>
#include <io.h>
#include <windows.h>
#endif

// MQTT库头文件
#include <MQTTAsync.h>

#include "SimpleMQTTGateway.h"

using namespace std;

// 全局网关对象
unique_ptr<SimpleMQTTGateway> g_gateway;

// 自定义消息处理器，确保中文正确输出
void myMessageOutput(QtMsgType type, const QMessageLogContext &context,
                     const QString &msg) {
  // 获取当前时间戳
  QString timestamp =
      QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");

#ifdef _WIN32
  // 在Windows下，直接输出到控制台，确保UTF-8编码
  QString txt;
  switch (type) {
  case QtDebugMsg:
    txt = QString("[%1] [调试] %2").arg(timestamp, msg);
    break;
  case QtWarningMsg:
    txt = QString("[%1] [警告] %2").arg(timestamp, msg);
    break;
  case QtCriticalMsg:
    txt = QString("[%1] [错误] %2").arg(timestamp, msg);
    break;
  case QtFatalMsg:
    txt = QString("[%1] [致命] %2").arg(timestamp, msg);
    break;
  case QtInfoMsg:
    txt = QString("[%1] [信息] %2").arg(timestamp, msg);
    break;
  }

  // 转换为UTF-8字节并输出到控制台
  QByteArray utf8Data = txt.toUtf8();
  WriteConsoleA(GetStdHandle(STD_OUTPUT_HANDLE), utf8Data.constData(),
                utf8Data.length(), NULL, NULL);
  WriteConsoleA(GetStdHandle(STD_OUTPUT_HANDLE), "\n", 1, NULL, NULL);
#else
  // 非Windows系统使用标准输出
  QString txt;
  switch (type) {
  case QtDebugMsg:
    txt = QString("[%1] [调试] %2").arg(timestamp, msg);
    break;
  case QtWarningMsg:
    txt = QString("[%1] [警告] %2").arg(timestamp, msg);
    break;
  case QtCriticalMsg:
    txt = QString("[%1] [错误] %2").arg(timestamp, msg);
    break;
  case QtFatalMsg:
    txt = QString("[%1] [致命] %2").arg(timestamp, msg);
    break;
  case QtInfoMsg:
    txt = QString("[%1] [信息] %2").arg(timestamp, msg);
    break;
  }
  fprintf(stderr, "%s\n", txt.toLocal8Bit().constData());
#endif
}

// 信号处理函数
void signalHandler(int signal) {
  qInfo() << "收到停止信号，正在关闭...";

  if (g_gateway) {
    g_gateway->Stop();
  }

  QCoreApplication::quit();
}

#ifdef _WIN32
BOOL WINAPI ConsoleHandler(DWORD signal) {
  if (signal == CTRL_C_EVENT || signal == CTRL_CLOSE_EVENT) {
    signalHandler(SIGINT);
    return TRUE;
  }
  return FALSE;
}
#endif

int main(int argc, char *argv[]) {

#ifdef _WIN32
  // 设置控制台输出为UTF-8编码
  SetConsoleOutputCP(CP_UTF8);
  SetConsoleCP(CP_UTF8);

  // 设置控制台模式以支持UTF-8
  HANDLE hOut = GetStdHandle(STD_OUTPUT_HANDLE);
  DWORD dwMode = 0;
  GetConsoleMode(hOut, &dwMode);
  dwMode |= ENABLE_PROCESSED_OUTPUT | ENABLE_WRAP_AT_EOL_OUTPUT |
            ENABLE_VIRTUAL_TERMINAL_PROCESSING;
  SetConsoleMode(hOut, dwMode);

  // 设置locale为UTF-8
  setlocale(LC_ALL, ".UTF8");

  // 设置C++流的locale
  std::locale::global(std::locale(".UTF8"));
  std::cout.imbue(std::locale(".UTF8"));
  std::cerr.imbue(std::locale(".UTF8"));
#endif

      // 🔧 初始化MQTT库 - 必须在创建任何MQTT客户端之前调用
    MQTTAsync_init_options mqttInitOpts = MQTTAsync_init_options_initializer;
    mqttInitOpts.do_openssl_init = 1; // 让MQTT库处理OpenSSL初始化
    MQTTAsync_global_init(&mqttInitOpts);
    std::cout << "[INFO] MQTT库全局初始化完成" << std::endl;

    QCoreApplication app(argc, argv);

  // 设置Qt应用程序编码
  QCoreApplication::setApplicationName("MQTTGateway");
  QCoreApplication::setApplicationVersion("1.0.0");

  // 安装自定义消息处理器
  qInstallMessageHandler(myMessageOutput);

  // 输出启动信息
  qInfo() << "=== 极简MQTT网关 v2.0.0 ===";
  qInfo() << "配置驱动架构，专为FStation与SCADA平台通信设计";

  // 设置信号处理
  signal(SIGINT, signalHandler);
  signal(SIGTERM, signalHandler);

#ifdef _WIN32
  SetConsoleCtrlHandler(ConsoleHandler, TRUE);
#endif

  try {
    // 创建并初始化极简网关
    g_gateway = make_unique<SimpleMQTTGateway>();

    if (!g_gateway->Initialize()) {
      qCritical() << "极简网关初始化失败";
      return -1;
    }

    qInfo() << "极简网关启动成功，按Ctrl+C退出...";

    // 在后台线程中运行网关业务逻辑
    thread gatewayThread([&]() { g_gateway->Run(); });

    // 启动Qt事件循环
    int result = app.exec();

    // 等待网关线程结束
    if (gatewayThread.joinable()) {
      gatewayThread.join();
    }

    return result;

  } catch (const exception &e) {
    qCritical() << "程序异常退出:" << e.what();
    return -1;
  }
}