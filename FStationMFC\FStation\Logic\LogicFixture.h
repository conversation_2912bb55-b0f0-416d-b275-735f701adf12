﻿#pragma once

#include "ThreadBase.h"
using namespace yzBase;

#include "Fixture.h"

class CLogicFixture : public CThreadBase
{
public:
	CLogicFixture();
	virtual ~CLogicFixture();

public:
	// 基础控制函数
	EnumStatus OnSafeCheck();       // 安全检查：检查治具系统安全状态
	EnumStatus OnStart();           // 启动控制：初始化治具系统
	EnumStatus OnPause();           // 暂停控制：暂停治具操作
	EnumStatus OnResume();          // 恢复控制：从暂停状态恢复治具操作
	EnumStatus OnStop();            // 停止控制：停止治具系统

public:
	// 主运行函数
	CStatus OnRun();                // 主状态机运行函数：协调治具系统工作流程

	// ========== 自检流程 (OnSelfCheck00-OnSelfCheck03) ==========
	CStatus OnSelfCheck00();        // 自检准备：准备治具系统自检
	CStatus OnSelfCheck01();        // 自检执行：执行治具系统自检
	CStatus OnSelfCheck02();        // 自检验证：验证治具系统自检结果
	CStatus OnSelfCheck03();        // 自检完成：完成治具系统自检

	// ========== 治具主流程 (OnFixture00-OnFixture02) ==========
	CStatus OnFixture00();          // 治具流程控制：控制治具主流程
	CStatus OnFixture01();          // 治具状态检查：检查治具状态
	CStatus OnFixture02();          // 治具流程分配：分配治具流程

	// ========== 治具上料流程 (OnFixtureUpload00-OnFixtureUpload20) ==========
	CStatus OnFixtureUpload00();    // 治具上料准备：准备治具上料
	CStatus OnFixtureUpload01();    // 治具上料检测：检测治具上料状态
	CStatus OnFixtureUpload02();    // 治具上料定位：定位治具上料位置
	CStatus OnFixtureUpload03();    // 治具上料夹紧：夹紧治具
	CStatus OnFixtureUpload04();    // 治具上料抬升：抬升治具到工作位置
	CStatus OnFixtureUpload05();    // 治具上料验证：验证治具上料状态
	CStatus OnFixtureUpload06();    // 治具上料完成：完成治具上料
	CStatus OnFixtureUpload07();    // 治具上料后处理：治具上料后处理
	CStatus OnFixtureUpload08();    // 治具上料清理：清理治具上料区域
	CStatus OnFixtureUpload09();    // 治具上料记录：记录治具上料数据
	CStatus OnFixtureUpload10();    // 治具上料状态更新：更新治具上料状态
	CStatus OnFixtureUpload11();    // 治具上料流程验证：验证治具上料流程
	CStatus OnFixtureUpload12();    // 治具上料流程完成：完成治具上料流程
	CStatus OnFixtureUpload13();    // 治具上料流程结束：结束治具上料流程
	CStatus OnFixtureUpload14();    // 治具上料最终检查：最终检查治具上料状态
	CStatus OnFixtureUpload15();    // 治具上料最终验证：最终验证治具上料结果
	CStatus OnFixtureUpload16();    // 治具上料最终完成：最终完成治具上料
	CStatus OnFixtureUpload17();    // 治具上料最终后处理：治具上料最终后处理
	CStatus OnFixtureUpload18();    // 治具上料最终清理：治具上料最终清理
	CStatus OnFixtureUpload19();    // 治具上料最终记录：治具上料最终记录
	CStatus OnFixtureUpload20();    // 治具上料最终结束：最终结束治具上料

	// ========== 治具分离流程 (OnFixtureSeparate00-OnFixtureSeparate11) ==========
	CStatus OnFixtureSeparate00();  // 治具分离准备：准备治具分离
	CStatus OnFixtureSeparate00_0(); // 治具分离准备子流程：执行治具分离准备详细操作
	CStatus OnFixtureSeparate00_1(); // 治具分离准备验证：验证治具分离准备状态
	CStatus OnFixtureSeparate01();  // 治具分离检测：检测治具分离条件
	CStatus OnFixtureSeparate02();  // 治具分离定位：定位治具分离位置
	CStatus OnFixtureSeparate03();  // 治具分离下降：下降治具到分离位置
	CStatus OnFixtureSeparate04();  // 治具分离松开：松开治具夹紧
	CStatus OnFixtureSeparate05();  // 治具分离推出：推出治具
	CStatus OnFixtureSeparate06();  // 治具分离验证：验证治具分离状态
	CStatus OnFixtureSeparate07();  // 治具分离完成：完成治具分离
	CStatus OnFixtureSeparate08();  // 治具分离后处理：治具分离后处理
	CStatus OnFixtureSeparate09();  // 治具分离清理：清理治具分离区域
	CStatus OnFixtureSeparate10();  // 治具分离记录：记录治具分离数据
	CStatus OnFixtureSeparate11();  // 治具分离流程结束：结束治具分离流程
	
	// ========== 治具AGV流程 (OnFixtureAgv00-OnFixtureAgv04) ==========
	CStatus OnFixtureAgv00();       // 治具AGV流程控制：控制治具AGV流程
	CStatus OnFixtureAgv01();       // 治具AGV准备：准备治具AGV操作
	CStatus OnFixtureAgv02();       // 治具AGV执行：执行治具AGV操作
	CStatus OnFixtureAgv03();       // 治具AGV验证：验证治具AGV结果
	CStatus OnFixtureAgv04();       // 治具AGV完成：完成治具AGV流程
	
private:
	CFixture*				m_pFixture;         // 治具设备对象指针

	CString					m_sRet;             // 函数返回字符串结果

	map<CString, bool>		m_mapFlag;          // 标志映射表，记录各种状态标志
	map<CString, DWORD>		m_mapTick;          // 时间计时器映射表，用于超时控制
	map<CString, double>	m_mapPos;           // 位置映射表，记录各种位置信息

	CThreadFunc*			m_pFuncUpload;      // 治具上料线程函数指针
	CThreadFunc*			m_pFuncSeparate;    // 治具分离线程函数指针
	CThreadFunc*			m_pFuncAgv;         // 治具AGV线程函数指针
};
