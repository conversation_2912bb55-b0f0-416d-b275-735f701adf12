#pragma once

#include <QObject>
#include <QTimer>
#include <QLoggingCategory>
#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <memory>
#include <atomic>
#include <map>
#include <mutex>
#include <thread>

#include "SocketServer.h"

Q_DECLARE_LOGGING_CATEGORY(testSocketGateway)

// 增强版测试Socket网关 - Socket通信 + HTTP模拟MQTT发布
class TestSocketGateway : public QObject {
    Q_OBJECT

public:
    explicit TestSocketGateway(QObject* parent = nullptr);
    virtual ~TestSocketGateway();
    
    // 核心接口
    bool Initialize(int port = 8888);
    void Run();
    void Stop();
    
    // 状态查询
    bool IsSocketConnected() const;
    bool IsMQTTConnected() const;

signals:
    void connectionStatusChanged(bool socketConnected);
    void messageReceived(const QString& messageType, const QString& data);

private slots:
    void OnPeriodicReport();

private:
    // 基础组件
    std::unique_ptr<SocketServer> m_socketServer;
    QNetworkAccessManager* m_networkManager;

    // 运行状态
    std::atomic<bool> m_running{false};
    std::atomic<bool> m_mqttConnected{false};

    // 定时器
    QTimer* m_reportTimer;

    // MQTT配置
    QString m_mqttBroker;
    int m_mqttPort;
    QString m_deviceId;
    
    // Socket消息处理
    void ProcessSocketMessages();
    void HandleDeviceStatusMessage(const QJsonObject& data);
    void HandleEventMessage(const std::string& eventType, const QJsonObject& data);

    // MQTT模拟发布（通过HTTP API）
    void PublishToMQTT(const QString& topic, const QJsonObject& payload, int qos = 1);
    void TestMQTTConnection();

    // 工具方法
    std::string GetCurrentTimestamp();
    QString BuildMQTTTopic(const QString& topicType, const QString& requestId = "");
};
