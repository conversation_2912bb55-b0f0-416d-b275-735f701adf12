# 三色灯显示逻辑优化功能说明

## 概述

本次更新优化了FStation系统的三色灯显示逻辑，改进了机台运行状态的视觉反馈，使三色灯能更准确地反映机台的实际工作状态。

## 问题背景

### 原有逻辑的问题

在原有系统中，三色灯的显示逻辑存在以下问题：

1. **绿灯显示条件过于严格**：
   - 只有在tray盘有料的情况下运行才显示绿灯
   - 满tray无料时显示黄灯，即使皮带有料正常工作
   - 只有皮带来料工作时也显示黄灯

2. **待机时间固定**：
   - 机台待机90秒后变为黄灯，时间无法调节
   - 不同生产环境对待机时间的需求不同

3. **状态反馈不准确**：
   - 皮带有料属于正常工作状态，但显示黄灯容易误导操作员
   - 无法准确反映机械手的实际工作状态

## 解决方案

### 1. 三色灯显示逻辑优化

#### 1.1 新的显示规则

| 机台状态 | 三色灯显示 | 说明 |
|----------|------------|------|
| 机械手正常工作 | 🟢 绿灯常亮 | 包括皮带来料、tray盘作业等所有正常工作状态 |
| 机台待机 | 🟡 黄灯闪烁 | 超过配置的待机时间后显示 |
| 治具预警 | 🟡 黄灯闪烁 | 治具相关预警状态 |
| 系统未运行 | 🟡 黄灯常亮 | 系统停止或暂停状态 |
| 报警状态 | 🔴 红灯闪烁 | 系统报警或错误状态 |

#### 1.2 关键优化点

**绿灯显示优化：**
- ✅ 只要机械手在工作状态下就显示绿灯
- ✅ 皮带有料工作时显示绿灯（原来显示黄灯）
- ✅ 满tray无料但皮带正常工作时显示绿灯
- ✅ 不再区分tray盘有料和皮带来料的工作状态

**待机逻辑优化：**
- ✅ 待机变灯时间可配置（原来固定90秒）
- ✅ 待机状态下黄灯闪烁提醒
- ✅ 机械手开始工作立即恢复绿灯

### 2. 新增配置参数

在系统参数中新增可配置项：

| 配置项名称 | 默认值 | 取值范围 | 单位 | 说明 |
|------------|--------|----------|------|------|
| 机台待机变灯时间 | 90000 | 30000-300000 | 毫秒 | 机台待机多长时间后变为黄灯 |

**配置说明：**
- 默认值：90000毫秒（90秒），保持原有时间
- 最小值：30000毫秒（30秒），避免过于频繁的状态切换
- 最大值：300000毫秒（300秒），避免过长的待机时间

### 3. 代码修改详情

#### 3.1 配置参数添加 (Machine.cpp)
```cpp
// 新增机台待机变灯时间配置项
MakePair("机台待机变灯时间", new CData(90000, 30000, 300000, true, 4));
```

#### 3.2 待机逻辑优化 (LogicRobot.cpp)
```cpp
// 使用配置的待机时间替代硬编码的90000毫秒
if (GetTickCount() - m_mapTick["待料计时"] > (DWORD)VAR_MACHINE_I("机台待机变灯时间")) {
    g_bWaitFlag = true;
}
```

#### 3.3 三色灯控制逻辑优化 (FStationDlg.cpp)
```cpp
if (CLogicMgr::isRunning()) {
    // 检查是否需要显示黄灯（待机状态或治具预警）
    if (g_bWaitFlag || VAR_FIXTURE_B("治具预警标志")) {
        // 黄灯闪烁提醒
        // ...闪烁逻辑
    }
    else {
        // 机械手正常工作，显示绿灯
        g_pMachine->GreenLightOn();
    }
}
```

## 功能特性

### ✨ 主要改进

1. **状态反馈更准确**：
   - 绿灯真正反映机械手的工作状态
   - 黄灯专门用于待机和预警提醒
   - 消除了皮带工作时的误导性黄灯显示

2. **配置更灵活**：
   - 待机变灯时间可根据生产需求调整
   - 支持30秒到300秒的灵活配置
   - 参数修改后立即生效

3. **操作体验优化**：
   - 减少不必要的黄灯显示
   - 提高状态识别的准确性
   - 降低操作员的困惑和误判

### 🔧 技术实现

1. **待机检测机制**：
   - 基于待料计数和时间判断
   - 配置化的超时时间设置
   - 自动重置机制

2. **状态切换逻辑**：
   - 实时检测机械手工作状态
   - 平滑的状态转换
   - 优先级明确的显示规则

3. **闪烁控制**：
   - 独立的闪烁状态管理
   - 500ms亮/300ms灭的闪烁节奏
   - 状态变化时的即时响应

## 使用指南

### 📋 配置步骤

1. **设置待机时间**：
   - 进入系统参数设置
   - 找到"机台待机变灯时间"配置项
   - 根据生产节拍设置合适的时间（单位：毫秒）
   - 建议设置为生产节拍的2-3倍

2. **观察三色灯状态**：
   - 🟢 绿灯：机械手正常工作，生产进行中
   - 🟡 黄灯闪烁：机台待机或治具预警
   - 🟡 黄灯常亮：系统停止或暂停
   - 🔴 红灯闪烁：系统报警

### ⚙️ 推荐配置

| 生产类型 | 建议待机时间 | 说明 |
|----------|--------------|------|
| 高频生产 | 60000ms (60秒) | 生产节拍快，待机时间短 |
| 标准生产 | 90000ms (90秒) | 标准配置，适用于大多数场景 |
| 低频生产 | 120000ms (120秒) | 生产节拍慢，允许更长待机 |

## 测试验证

### 🧪 测试场景

1. **皮带来料测试**：
   - ✅ 皮带有料时显示绿灯
   - ✅ 满tray无料但皮带工作时显示绿灯
   - ✅ 待机超时后显示黄灯闪烁

2. **tray盘作业测试**：
   - ✅ tray盘有料作业时显示绿灯
   - ✅ tray盘作业完成后正常切换

3. **待机时间测试**：
   - ✅ 配置30秒待机时间，30秒后变黄灯
   - ✅ 配置120秒待机时间，120秒后变黄灯
   - ✅ 机械手开始工作立即变绿灯

## 总结

此次三色灯优化显著改善了系统状态反馈的准确性和用户体验：

### 🎯 解决的问题
- ✅ 消除了皮带工作时的误导性黄灯显示
- ✅ 提供了灵活的待机时间配置
- ✅ 优化了状态切换逻辑

### 🚀 带来的价值
- 📈 提高操作员对机台状态的准确判断
- ⚡ 减少不必要的状态检查和干预
- 🔧 支持不同生产环境的个性化配置
- 💡 提升整体生产效率和用户体验

通过这次优化，三色灯真正成为了机台运行状态的准确指示器，为生产管理提供了更可靠的视觉反馈。 