Package: vcpkg-cmake
Version: 2024-04-23
Architecture: x64-windows
Multi-Arch: same
Abi: 8112fdacac7427b4feea02a5a660bd2fb97e5b898a970d195ff6370d43a794fb
Status: install ok installed

Package: vcpkg-cmake-get-vars
Version: 2025-05-29
Depends: vcpkg-cmake
Architecture: x64-windows
Multi-Arch: same
Abi: 6fc3112bb2922bda535f50cc6b50bea5ed7333a064b036e2edcc1e25bb0afce9
Status: install ok installed

Package: vcpkg-cmake-config
Version: 2024-05-23
Architecture: x64-windows
Multi-Arch: same
Abi: 8b1ae8f4be6cff022eadef50d38cffca5e75f23d30fc7e280b912b164b7e8ea1
Status: install ok installed

Package: openssl
Version: 3.5.0
Port-Version: 1
Depends: vcpkg-cmake:x64-windows, vcpkg-cmake-config:x64-windows, vcpkg-cmake-get-vars:x64-windows
Architecture: x64-windows-static-md
Multi-Arch: same
Abi: d7791f0fdf61fd6ccba9c7b41fc33760c2590cbe080a3ba2487c8442e2fc3a41
Description: OpenSSL is an open source project that provides a robust, commercial-grade, and full-featured toolkit for the Transport Layer Security (TLS) and Secure Sockets Layer (SSL) protocols. It is also a general-purpose cryptography library.
Status: install ok installed

Package: paho-mqtt
Version: 1.3.14
Depends: openssl, vcpkg-cmake:x64-windows, vcpkg-cmake-config:x64-windows
Architecture: x64-windows-static-md
Multi-Arch: same
Abi: 737af9ed4088d83ee6d9a3148cf0baac1fc23767537f87bafabdfc560f8c394a
Description: Paho project provides open-source client implementations of MQTT and MQTT-SN messaging protocols aimed at new, existing, and emerging applications for the Internet of Things
Status: install ok installed

Package: paho-mqttpp3
Version: 1.5.2
Depends: paho-mqtt, vcpkg-cmake:x64-windows, vcpkg-cmake-config:x64-windows
Architecture: x64-windows-static-md
Multi-Arch: same
Abi: 05a58f3fa238d038107f2caf5f90a6a4bbcac2b6827fef2fe5aeab117700106f
Description: Paho project provides open-source C++ wrapper for Paho C library
Default-Features: ssl
Status: install ok installed

Package: paho-mqttpp3
Feature: ssl
Depends: openssl
Architecture: x64-windows-static-md
Multi-Arch: same
Description: Build with SSL support
Status: install ok installed

