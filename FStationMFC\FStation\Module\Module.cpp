#include "stdafx.h"
#include "Module.h"

#include "Pro.h"

CModule::CModule(int nModule)
{
	m_nModule = nModule;
}

CModule::~CModule()
{
	Save();

	map<CString, CData*>::iterator it;
	for (it = m_mapParam.begin(); it != m_mapParam.end(); it++)
	{
		delete it->second;
	}
	m_mapParam.clear();
}

void CModule::Save()
{
	CString sPath = CString(GetModulePath().c_str());
	CString sSysPath, sProPath;
	sSysPath.Format("%s\\Sys\\Sys.ini", sPath.GetBuffer());
	sProPath.Format("%s\\Pro\\%s\\Pro.ini", sPath.GetBuffer(), CPro::m_strPro.c_str());
	
	CString sSection;
	sSection.Format("Module_%d", m_nModule);
	
	map<CString, CData*>::iterator it;
	for (it = m_mapParam.begin(); it != m_mapParam.end(); it++)
	{
		if (!it->second->NeedSave()) {
			continue;
		}

		CString sKey = it->first;
		if (it->second->IsValid(3)) {
			WriteIni(sSection.GetBuffer(), sKey.GetBuffer(), it->second->Pack().GetBuffer(), sProPath.GetBuffer());
		}
		else {
			WriteIni(sSection.GetBuffer(), sKey.GetBuffer(), it->second->Pack().GetBuffer(), sSysPath.GetBuffer());
		}
	}
}

void CModule::Load()
{
	if (CPro::m_strPro.size() <= 0) {
		return;
	}

	CString sPath = CString(GetModulePath().c_str());
	CString sSysPath, sProPath;
	sSysPath.Format("%s\\Sys\\Sys.ini", sPath.GetBuffer());
	sProPath.Format("%s\\Pro\\%s\\Pro.ini", sPath.GetBuffer(), CPro::m_strPro.c_str());

	CString sSection;
	sSection.Format("Module_%d", m_nModule);

	int nFlag = ReadIni(sSection, "InitFlag", 0, sProPath);

	map<CString, CData*>::iterator it = m_mapParam.begin();
	for (; it != m_mapParam.end(); it++)
	{
		if (!it->second->NeedSave()) {
			continue;
		}

		CString sKey = it->first;
		if (it->second->IsValid(3) && nFlag == 0) {
			it->second->UnPack(ReadIni(sSection, sKey, it->second->Pack(), sSysPath));
			WriteIni(sSection.GetBuffer(), sKey.GetBuffer(), it->second->Pack().GetBuffer(), sProPath.GetBuffer());
			sSection.ReleaseBuffer();
			sProPath.ReleaseBuffer();
			continue;
		}

		if (it->second->IsValid(3)) {
			it->second->UnPack(ReadIni(sSection, sKey, it->second->Pack(), sProPath));
		}
		else {
			it->second->UnPack(ReadIni(sSection, sKey, it->second->Pack(), sSysPath));
		}
	}

	WriteIni(sSection.GetBuffer(), "InitFlag", 1, sProPath.GetBuffer());
}

void CModule::MakePair(CString sName, CData* pData)
{
	m_mapParam[sName] = pData;

	m_vecParam.push_back(sName);
}
