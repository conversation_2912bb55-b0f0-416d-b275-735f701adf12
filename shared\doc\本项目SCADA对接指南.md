# 本项目SCADA对接指南

## 概述

本指南描述FStation项目与SCADA平台的对接实现，基于官方SCADA协议进行本地化适配。

**协议版本**: SCADA v1.0
**最后更新**: 2024-12-24
**变更说明**: 移除旧命令格式，统一使用SCADA标准协议

## 最新变更

### 2024-12-25 SCADA协议规范修复和暂停/恢复功能实现

1. **修复重复响应和空格处理问题**：
   - 问题1：同一命令收到两个响应（"命令处理器处理失败"和"命令执行失败"）
   - 问题2：Topic中的空格导致request_id提取失败
   - 问题3：命令格式缺少必需的`lane_no`和`side`字段
   - 解决：
     - ✅ 修复重复响应：移除`CommandProcessor`失败时的额外响应调用
     - ✅ 增强空格处理：支持`request_id=xxx`和`request_id = xxx`格式
     - ✅ 完善命令格式：所有测试命令添加`lane_no`和`side`字段
   - 结果：现在只返回一个正确的SCADA标准响应，支持带空格的Topic格式

2. **实现完整的暂停/恢复生产功能**：
   - 功能：完全集成FStationMFC的暂停和恢复生产机制
   - 实现：
     - ✅ `COMMAND_PAUSE`：调用`CLogicMgr::Pause()`暂停所有生产线程
     - ✅ `COMMAND_PRODUCTION`：调用`CLogicMgr::Run()`恢复生产线程
     - ✅ 状态同步：自动触发`OnProductionStatusChanged()`通知状态变化
     - ✅ 手动操作记录：更新`CDat::UpdateValueManualOperation()`记录操作类型
   - 效果：SCADA平台可以远程控制FStation的暂停和恢复，与UI按钮功能完全一致

2. **修复命令响应格式不符合SCADA协议规范问题**：
   - 问题：命令响应格式不符合SCADA平台MQTT协议设备接入指南要求
   - 解决：修改`PublishCommandResponse`方法，使用标准的SCADA响应格式
   - 结果：命令响应现在完全符合SCADA协议规范

3. **修复幂等性功能失效问题**：
   - 问题：命令在处理前就被标记为已处理，导致重复命令被错误拒绝
   - 解决：调整幂等性标记时机，只有在命令成功处理后才标记为已处理
   - 结果：幂等性功能现在正确工作，失败的命令可以重试

4. **修复时间格式不一致问题**：
   - 问题：返回的时间戳格式是ISO 8601格式 `"2025-07-24T16:42:10Z"`
   - 解决：统一所有时间戳格式为 `"2025-01-25 10:30:00.000"` 格式
   - 结果：所有MQTT消息和响应使用一致的时间格式

5. **修复命令处理器初始化失败问题**：
   - 问题：Socket服务器在命令处理器初始化之后才创建，导致空指针错误
   - 解决：调整初始化顺序，先创建Socket服务器，再初始化命令处理器
   - 结果：MQTT网关现在可以正常启动，所有组件初始化成功

### 2024-12-24 SCADA标准协议统一

1. **移除旧命令格式**：
   - 完全移除对旧命令格式的支持（`START_PRODUCTION`, `STOP_PRODUCTION`等）
   - 统一使用SCADA协议标准格式，确保协议一致性
   - 仅支持5种标准命令：`COMMAND_SN_DELIVER`, `COMMAND_BOP_DELIVER`, `COMMAND_PAUSE`, `COMMAND_PRODUCTION`, `COMMAND_MQTT_CONFIG_DELIVER`

2. **命令处理器架构**：
   - 新增`CommandProcessor`类，专门处理SCADA标准命令
   - 实现配置文件下载和验证功能
   - 支持命令参数验证和幂等性保证
   - 提供完整的错误处理和日志记录

3. **协议处理增强**：
   - 扩展`ScadaProtocolHandler`，支持命令解析和验证
   - 实现配置文件信息解析和验证
   - 添加命令参数提取和响应构建方法

4. **配置文件支持**：
   - 支持从SCADA平台下载配置文件
   - 实现文件哈希验证和缓存管理
   - 支持并发下载和超时控制

5. **FStation端标准化**：
   - 使用JsonCpp库优化JSON解析，提升可靠性和性能
   - 移除所有旧命令格式的兼容性代码
   - 统一使用SCADA标准协议，拒绝非标准命令
   - 简化命令处理逻辑，提高代码维护性
   - 将SCADA命令处理器文件正确添加到Visual Studio项目中
   - 修正接口使用：使用SocketInterface替代MQTTInterface
   - 修复编译问题：解决头文件依赖和编码问题
   - 删除示例集成文件，将功能直接集成到FStationDlg.cpp中

6. **MQTT网关修复**：
   - 修复命令处理器初始化失败的关键问题
   - 调整组件初始化顺序：Socket服务器 → 命令处理器
   - 验证MQTT连接和SCADA主题订阅功能正常
   - 确保周期性状态报告和系统监控正常运行

### 2025-01-27 数据点访问权限控制

1. **数据点配置增强**：
   - 新增`readonly`字段，标识数据点访问权限
   - 基于科瑞F站数采清单，设置32个只读属性
   - A系列（设备状态）和C系列（生产追溯）全部设为只读
   - B系列（设备参数）设为可读写，支持平台设置

2. **属性设置权限检查**：
   - 在`HandlePropertySetRequest`中添加只读检查
   - 拒绝对只读属性的设置请求
   - 返回明确的权限错误信息给平台
   - 提高系统安全性和数据完整性

3. **配置文件更新**：
   - 更新`data_points_config.json`，添加readonly字段文档
   - 批量设置数据点访问权限
   - 提供权限配置示例和说明

### 2024-01-15 协议简化

1. **FStation端修改**：
   - 修改`SendMessage`函数，直接发送services数组格式
   - 更新所有事件构建函数，生成符合SCADA协议的services数组
   - 移除外层包裹字段（version、type、messageId、source、target等）

2. **网关端修改**：
   - 修改`ProcessSocketMessages`函数，直接处理services数组
   - 移除外层包裹字段解析逻辑
   - 简化消息处理流程

3. **协议优势**：
   - 减少数据传输量
   - 简化解析逻辑
   - 提高处理效率
   - 降低维护成本

## 核心文档

- **官方协议**: [doc/重要/SCADA平台MQTT协议设备接入指南.md](mdc:doc/重要/SCADA平台MQTT协议设备接入指南.md)
- **数采清单**: [doc/重要/科瑞F站--数采清单.md](mdc:doc/重要/科瑞F站--数采清单.md)

## 数据点访问权限

### 权限分类

基于科瑞F站数采清单中的访问权限定义，数据点分为两类：

#### 只读属性（32个）
**A系列 - 设备运行状态**：
- A00002（正常生产）、A00003（运行暂停）、A00004（设备故障）
- A00006（待机状态）、A00010（机型程序名）、A00014（运行周期）、A00015（累计产能）

**A系列 - 硬件监控**：
- A00020-A00030（内存、磁盘、CPU等系统监控数据）

**C系列 - 生产追溯**：
- C00001-C00014（主板SN、设备编码、轨道号、面别、程序信息、时间参数、故障信息等）

#### 可读写属性（16个）
**B系列 - 设备参数**：
- B40001-B40016（机器人连接状态、气源状态、测高数据等设备参数）

### 权限检查机制

1. **平台属性设置请求**：网关会检查请求中的所有属性ID
2. **只读检查**：如果包含任何只读属性，整个请求被拒绝
3. **错误响应**：返回明确的权限错误信息
4. **安全保护**：防止平台误操作影响设备状态和生产数据

## 消息格式

### Socket消息格式（FStation → 网关）

**更新说明**：FStation端已修改为直接发送services数组，无需外层包裹字段。

FStation直接发送services数组，无需外层包裹：

```json
{
  "services": [
    {
      "service_id": "EventService",
      "event_type": "EVENT_SN_IN",
      "properties": {
        "sn": "2001000324005454107303211",
        "production_model": "A121000185",
        "profiles": [
          {
            "name": "3AB849-0   BOT  V1.0"
          }
        ]
      },
      "event_time": "2025-07-11 16:22:07.902"
    },
    {
      "service_id": "DefaultService",
      "event_time": "2025-07-11 16:22:07.902",
      "properties": {
        "C00001": "2001000324005454107303211",
        "C00002": "A121000185",
        "C00003": 2,
        "C00004": "T",
        "C00005": "3AB849-0   BOT  V1.0"
      }
    }
  ]
}
```

### MQTT上报格式（网关 → SCADA）

网关将Socket消息转换为标准SCADA格式上报：

```json
{
  "services": [
    {
      "service_id": "EventService",
      "event_type": "EVENT_SN_IN",
      "properties": {
        "sn": "2001000324005454107303211",
        "production_model": "A121000185"
      },
      "event_time": "2025-07-11 16:22:07.902"
    },
    {
      "service_id": "DefaultService",
      "properties": {
        "A00001": "设备运行状态",
        "A00002": true,
        "A00003": false,
        "A00004": false
      },
      "event_time": "2025-07-11 16:22:07.902"
    }
  ]
}
```

## 支持的消息类型

### 事件类型
- `EVENT_SN_IN`: SN入站事件
- `EVENT_SN_OUT`: SN出站事件
- `EVENT_FAULT`: 故障事件
- `PRODUCTION_EVENT`: 通用生产事件

### 服务类型
- `EventService`: 事件服务
- `DefaultService`: 默认服务（设备状态）
- `StatusService`: 状态服务
- `SnService`: SN服务

### 命令类型（新增）
- `COMMAND_SN_DELIVER`: SN下发命令
- `COMMAND_BOP_DELIVER`: 转产/下发生产参数命令
- `COMMAND_PAUSE`: 暂停生产命令
- `COMMAND_PRODUCTION`: 恢复生产命令
- `COMMAND_MQTT_CONFIG_DELIVER`: MQTT配置下发命令

## SCADA标准命令处理

### 命令格式

**下行命令格式（SCADA → 设备）**：
```json
{
  "service_id": "CommandService",
  "command_name": "COMMAND_SN_DELIVER",
  "lane_no": 1,
  "side": "T",
  "properties": {
    "sn": "00232430452003F200003171",
    "production_model": "C380_B75"
  }
}
```

**命令响应格式（设备 → SCADA）**：
```json
{
  "response_name": "COMMAND_RESPONSE",
  "result_code": 0,
  "properties": {
    "result": "success",
    "command_name": "COMMAND_SN_DELIVER",
    "timestamp": "2024-12-24 10:30:45.123"
  }
}
```

### 命令处理流程

1. **命令接收**：网关订阅 `$oc/devices/{deviceId}/sys/commands/+` 主题
2. **命令验证**：检查命令格式、参数完整性和权限
3. **幂等性检查**：基于 `request_id` 避免重复处理
4. **配置文件下载**：如果命令包含配置文件，先下载并验证
5. **转发到FStation**：将标准SCADA命令转发给生产设备
6. **响应返回**：向SCADA平台返回命令执行结果

### 配置文件处理

**转产命令中的配置文件格式**：
```json
{
  "profiles": [
    {
      "name": "5380495513.xml",
      "down_url": "https://ocs-cn-south3.oppoer.me/scada-device-file/5380495513.xml",
      "hash": "040d85d08b0ad6e57ce649cf573747d8",
      "fsize": 3235
    }
  ]
}
```

**配置文件处理特性**：
- 支持HTTP/HTTPS下载
- MD5哈希验证确保文件完整性
- 本地缓存避免重复下载
- 并发下载控制和超时保护

## FStation端SCADA命令处理

### VS2010 MFC集成

**ScadaCommandHandler类特性**：
- 适配VS2010编译环境，无需C++11特性
- 单例模式设计，全局访问方便
- 使用JsonCpp库进行JSON解析，确保可靠性和性能
- 完整的错误处理和日志记录

**命令处理流程**：
1. **命令接收**：通过SocketInterface接收Socket消息
2. **回调触发**：调用OnSocketCommandReceived回调函数
3. **格式转换**：将Socket命令转换为SCADA标准格式
4. **JSON解析**：使用JsonCpp库解析命令，支持完整的JSON特性
5. **参数验证**：验证命令格式和必需参数
6. **业务处理**：调用FStation相应的业务逻辑
7. **结果记录**：记录命令执行结果和日志

**集成示例**：
```cpp
// Socket命令处理回调函数（全局函数）
int OnSocketCommandReceived(const CString& command, const CString& params, const CString& requestId)
{
    CScadaCommandHandler* pScadaHandler = GetScadaCommandHandler();
    if (pScadaHandler && pScadaHandler->IsInitialized())
    {
        // 构建SCADA标准命令JSON格式
        CString commandJson;
        commandJson.Format(_T("{\"command_name\":\"%s\",\"service_id\":\"CommandService\",\"request_id\":\"%s\",\"properties\":%s}"),
                          command, requestId, params);

        return pScadaHandler->ProcessCommand(commandJson, requestId) ? 0 : 1;
    }
    return 1; // 失败
}

// 在FStationDlg初始化中
void CFStationDlg::InitializeSocketInterface()
{
    SimpleSocketInterface* pSocket = SimpleSocketInterface::GetInstance();

    if (pSocket->Connect()) {
        // 初始化SCADA命令处理器
        CScadaCommandHandler* pScadaHandler = GetScadaCommandHandler();
        pScadaHandler->Initialize(this);
    }
}

// 定期状态发送
void CFStationDlg::SendPeriodicStatus()
{
    if (++m_nMQTTStatusCounter >= 60) // 每60秒发送一次
    {
        m_nMQTTStatusCounter = 0;

        SimpleSocketInterface::SendDeviceStatus();
    }
}
```

### 标准化支持

**SCADA标准协议**：
- 仅支持SCADA协议规范中定义的5种标准命令
- 拒绝非标准格式的命令，确保协议一致性
- 使用JsonCpp库确保JSON解析的可靠性

**错误处理**：
- 详细的错误日志记录
- 命令执行状态跟踪
- 异常情况的优雅处理
- 对非标准命令的明确拒绝和提示

## 关键代码文件

- **MQTT网关**: [MQTTGateway/MQTTGateway.cpp](mdc:MQTTGateway/MQTTGateway.cpp)
- **配置管理**: [MQTTGateway/ConfigManager.cpp](mdc:MQTTGateway/ConfigManager.cpp)
- **数据点管理**: [MQTTGateway/DataPointManager.cpp](mdc:MQTTGateway/DataPointManager.cpp)

## 数据点映射

### A系列 - 设备状态
- `A00001`: 设备名称
- `A00002`: 正常生产状态
- `A00003`: 运行暂停状态
- `A00004`: 设备故障状态
- `A00006`: 待机状态
- `A00010`: 程序名称
- `A00014`: 运行周期
- `A00015`: 累计产量

### B系列 - 工艺参数
- `B00001`: 温度设定值
- `B00002`: 压力设定值
- `B00003`: 速度设定值

### C系列 - 时间参数
- `C00001`: 当前SN
- `C00002`: 产品型号
- `C00003`: 生产批次
- `C00004`: 生产状态标识
- `C00005`: 程序版本
- `C00010`: 故障代码

## 常见问题

### Q: 为什么移除了外层包裹字段？
A: 简化协议，减少冗余，提高传输效率。SCADA平台只关心services数组内容。

### Q: 如何处理多service场景？
A: 在services数组中包含多个service对象，每个service有独立的service_id和properties。

### Q: 数据点配置在哪里？
A: 配置文件 `config/data_points_config.json`，支持动态配置和映射。

### Q: 如何处理协议变更？
A: 1. 更新本指南的协议版本
   2. 更新代码中的协议注释
   3. 记录变更历史

## 变更记录

- **2024-01-15**: 移除外层包裹字段，简化消息格式
- **2024-01-10**: 添加EVENT_SN_IN消息类型支持
- **2024-01-05**: 初始实现，支持DEVICE_STATUS和PRODUCTION_EVENT

## 测试验证

### 消息格式验证
```bash
# 验证JSON格式
echo '{"services":[...]}' | jq .

# 验证必需字段
jq '.services[0] | has("service_id") and has("properties")' message.json
```

### 连接测试
1. 启动MQTT网关
2. 连接FStation
3. 发送测试消息
4. 验证SCADA平台接收

## 故障排查

### Socket连接问题
- 检查端口配置
- 验证网络连接
- 查看网关日志

### MQTT发布问题
- 检查认证信息
- 验证主题格式
- 查看发布队列状态

### 数据点映射问题
- 检查配置文件
- 验证数据点ID
- 查看映射日志

## 命令响应格式

### 标准SCADA协议响应格式

根据SCADA平台MQTT协议设备接入指南，命令响应必须使用以下标准格式：

```json
{
  "response_name": "COMMAND_RESPONSE",
  "result_code": 0,
  "properties": {
    "result": "success"
  }
}
```

### 响应格式说明

| 字段名 | 类型 | 是否必填 | 描述 |
|--------|------|----------|------|
| `response_name` | String | 是 | 固定为 `"COMMAND_RESPONSE"` |
| `result_code` | Integer | 是 | 命令执行结果码。`0` 表示成功，非 `0` 表示失败 |
| `properties` | Object | 是 | 包含命令执行结果的详细信息 |
| `properties.result` | String | 是 | 成功时为 `"success"`，失败时为具体错误信息 |

### 响应示例

**成功响应**：
```json
{
  "response_name": "COMMAND_RESPONSE",
  "result_code": 0,
  "properties": {
    "result": "success"
  }
}
```

**失败响应**：
```json
{
  "response_name": "COMMAND_RESPONSE",
  "result_code": 500,
  "properties": {
    "result": "命令处理器处理失败"
  }
}
```

### result字段逻辑说明

根据SCADA协议规范，`properties.result`字段的值由`result_code`决定：

| result_code | result字段值 | 说明 |
|-------------|-------------|------|
| `0` | `"success"` | 命令执行成功 |
| `非0` | 具体错误信息 | 命令执行失败，显示具体原因 |

**代码实现**：
```cpp
properties["result"] = (resultCode == 0) ? "success" : QString::fromStdString(resultDesc);
```

**常见错误信息**：
- `"命令处理器处理失败"` - CommandProcessor初始化或处理失败
- `"命令执行失败"` - FStation执行命令失败
- `"命令解析失败"` - JSON格式错误
- `"命令参数验证失败"` - 缺少必需参数

### 修复前后对比

**修复前（不符合规范）**：
```json
{
  "command_name": "COMMAND_SN_DELIVER",
  "request_id": "1753383005875",
  "result_code": 0,
  "result_desc": "命令执行成功",
  "source": "MQTTGateway",
  "timestamp": "2025-07-25 02:50:05.882"
}
```

**修复后（符合SCADA规范）**：
```json
{
  "response_name": "COMMAND_RESPONSE",
  "result_code": 0,
  "properties": {
    "result": "success"
  }
}
```

### 主要修复点

1. ✅ **添加必需字段** `response_name`（固定为 `"COMMAND_RESPONSE"`）
2. ✅ **添加必需字段** `properties`（包含 `result` 字段）
3. ✅ **移除非标准字段** `command_name`、`request_id`、`source`、`timestamp`
4. ✅ **统一result字段** 成功时使用 `"success"`，失败时使用具体错误信息

## SCADA协议Topic格式规范

### 标准Topic格式

根据SCADA平台MQTT协议设备接入指南，所有Topic必须严格遵循以下格式：

| 用途 | Topic格式 | 示例 |
|------|-----------|------|
| **平台下发设备命令** | `$oc/devices/{deviceId}/sys/commands/request_id={requestId}` | `$oc/devices/A320021760/sys/commands/request_id=123456789` |
| **设备响应平台命令** | `$oc/devices/{deviceId}/sys/commands/response/request_id={requestId}` | `$oc/devices/A320021760/sys/commands/response/request_id=123456789` |

## 暂停/恢复生产功能详细说明

### 功能概述

本项目已完全实现SCADA平台对FStationMFC的远程暂停和恢复生产控制功能，与设备UI界面的暂停/开始按钮功能完全一致。

### 支持的命令

#### 1. COMMAND_PAUSE - 暂停生产命令

**命令格式**：
```json
{
  "command_name": "COMMAND_PAUSE",
  "service_id": "CommandService",
  "lane_no": 1,
  "side": "T",
  "properties": {
    "pause_msg": "设备维护，暂停生产30分钟"
  }
}
```

**执行效果**：
- ✅ 调用`CLogicMgr::Pause()`暂停所有生产线程
- ✅ 更新手动操作状态为暂停(1)
- ✅ 触发生产状态变化通知
- ✅ UI界面暂停按钮变为禁用，开始按钮变为可用
- ✅ 系统状态变为"PAUSED"

#### 2. COMMAND_PRODUCTION - 恢复生产命令

**命令格式**：
```json
{
  "command_name": "COMMAND_PRODUCTION",
  "service_id": "CommandService",
  "lane_no": 1,
  "side": "T",
  "properties": {}
}
```

**执行效果**：
- ✅ 重置线程监控器`CLogicMgr::m_pThreadMonitor->Reset()`
- ✅ 调用`CLogicMgr::Run()`启动所有生产线程
- ✅ 更新手动操作状态为运行(0)
- ✅ 触发生产状态变化通知
- ✅ UI界面开始按钮变为禁用，暂停按钮变为可用
- ✅ 系统状态变为"RUNNING"

### 状态同步机制

系统通过`OnProductionStatusChanged()`方法自动同步状态变化：

```cpp
void CFStationDlg::OnProductionStatusChanged()
{
    // 获取当前状态
    CString currentStatus;
    if (CLogicMgr::isRunning()) {
        currentStatus = _T("RUNNING");
    } else if (CLogicMgr::isPause()) {
        currentStatus = _T("PAUSED");
    } else {
        currentStatus = _T("STOPPED");
    }

    // 通过Socket接口上报状态变化
    // ...
}
```

### 测试方法

使用测试脚本验证功能：
```bash
# 运行暂停/恢复测试
shared/test/test_pause_resume.bat
```

### 预期响应

**成功响应**：
```json
{
  "response_name": "COMMAND_RESPONSE",
  "result_code": 0,
  "properties": {
    "result": "success"
  }
}
```

**失败响应**：
```json
{
  "response_name": "COMMAND_RESPONSE",
  "result_code": 500,
  "properties": {
    "result": "命令执行失败"
  }
}
```
| **平台查询设备属性** | `$oc/devices/{deviceId}/sys/properties/get/request_id={requestId}` | `$oc/devices/A320021760/sys/properties/get/request_id=123456789` |
| **设备响应属性查询** | `$oc/devices/{deviceId}/sys/properties/get/response/request_id={requestId}` | `$oc/devices/A320021760/sys/properties/get/response/request_id=123456789` |

### 重要说明

- ✅ **正确格式**：`request_id={requestId}`（等号连接，无空格）
- ❌ **错误格式**：`request_id_123`、`request_id = 123`、`requestId=123`
- 🔧 **系统行为**：只有符合标准格式的Topic才能正确提取request_id
- 📋 **幂等性保证**：相同request_id的命令会被幂等性机制处理