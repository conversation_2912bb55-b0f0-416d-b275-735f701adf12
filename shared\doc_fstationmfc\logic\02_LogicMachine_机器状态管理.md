# LogicMachine 机器状态管理线程分析

## 概述

LogicMachine是FStation系统中负责机器状态管理和安全监控的核心线程，虽然代码量相对较少（6.4KB），但承担着系统安全的重要职责。该线程作为常驻线程（emThreadAlwaysExist）持续运行，确保系统的安全性和稳定性。

## 类结构分析

### 头文件结构
```cpp
class CLogicMachine : public CThreadBase
{
private:
    CMachine*        m_pMachine;     // 机器控制对象
    CString          m_sRet;         // 返回信息
    CThreadFunc*     m_pFuncNGBelt;  // NG皮带子线程
    CThreadFunc*     m_pFuncButton;  // 按钮监控子线程
    map<CString, bool>  m_mapFlag;   // 标志位映射表
    map<CString, DWORD> m_mapTick;   // 时间戳映射表
};
```

### 核心特点
1. **双子线程架构**: 使用两个子线程并发处理NG皮带和按钮监控
2. **安全优先**: 作为系统安全监控的核心组件
3. **状态映射**: 通过映射表管理各种标志和时间信息
4. **常驻运行**: 作为emThreadAlwaysExist类型持续监控系统

## 功能模块分析

### 1. 机器状态监控
```cpp
CStatus OnMachine00();  // 机器状态检查
CStatus OnMachine01();  // 机器状态处理
CStatus OnMachine02();  // 机器状态响应
```

**功能职责**:
- 监控机器整体运行状态
- 检查系统初始化状态
- 处理机器级别的异常情况
- 维护机器运行的基本条件

### 2. NG皮带控制系统
```cpp
CStatus OnNGBelt00();   // NG皮带初始状态
CStatus OnNGBelt01();   // NG皮带启动控制
CStatus OnNGBelt02();   // NG皮带运行监控
CStatus OnNGBelt03();   // NG皮带停止控制
CStatus OnNGBelt04();   // NG皮带异常处理
CStatus OnNGBelt05();   // NG皮带复位操作
```

**设计特点**:
- **专用子线程**: 通过`m_pFuncNGBelt`独立处理
- **状态机驱动**: 6个状态完整覆盖NG皮带操作流程
- **异常处理**: 专门的异常检测和恢复机制
- **自动化操作**: 自动启动、监控、停止的完整流程

**业务价值**:
- 自动清理不合格产品
- 防止NG产品进入下一工序
- 保证产品质量和生产效率
- 减少人工干预和错误

### 3. 按钮监控系统
```cpp
CStatus OnButton00();   // 按钮状态初始化
CStatus OnButton01();   // 启动按钮监控
CStatus OnButton02();   // 停止按钮监控
CStatus OnButton03();   // 复位按钮监控
CStatus OnButton04();   // 急停按钮监控
CStatus OnButton05();   // 安全门监控
CStatus OnButton06();   // 模式切换按钮
CStatus OnButton07();   // 手动操作按钮
CStatus OnButton08();   // 其他功能按钮
```

**设计特点**:
- **专用子线程**: 通过`m_pFuncButton`独立处理
- **多按钮支持**: 覆盖9种不同类型的按钮
- **实时响应**: 快速响应操作员的按钮操作
- **安全优先**: 急停和安全门具有最高优先级

**安全机制**:
- **急停按钮**: 立即停止所有运动，确保人员安全
- **安全门监控**: 检测安全门状态，防止误入危险区域
- **启停控制**: 安全的系统启动和停止流程
- **模式切换**: 手动/自动模式的安全切换

## 子线程协作机制

### 1. 子线程创建
```cpp
CLogicMachine::CLogicMachine()
{
    m_pFuncNGBelt = CLogicMgr::m_ThreadFactory.CreateThreadFunc("NG皮带");
    m_pFuncButton = CLogicMgr::m_ThreadFactory.CreateThreadFunc("按钮监控");
}
```

### 2. 子线程执行
```cpp
// NG皮带子线程执行
m_pFuncNGBelt->SetAction(static_cast<THREAD_FUNC>(&CLogicMachine::OnNGBelt00));
m_pFuncNGBelt->Run(this);

// 按钮监控子线程执行
m_pFuncButton->SetAction(static_cast<THREAD_FUNC>(&CLogicMachine::OnButton00));
m_pFuncButton->Run(this);
```

### 3. 并发优势
- **提高响应性**: 两个关键功能并发处理，提高系统响应速度
- **独立控制**: 每个子线程可以独立暂停、恢复、停止
- **状态隔离**: 不同功能的状态独立管理，避免相互干扰
- **资源优化**: 合理分配CPU资源，提高整体效率

## 安全检查机制

### 1. 系统级安全检查
```cpp
EnumStatus CLogicMachine::OnSafeCheck()
{
    // 检查系统初始化状态
    if (!CSys::m_bInit) {
        return emStop;
    }
    
    // 检查安全光栅状态
    if (安全光栅被遮挡) {
        return emPause;
    }
    
    // 检查急停按钮状态
    if (急停按钮被按下) {
        return emStop;
    }
    
    // 检查安全门状态
    if (安全门打开) {
        return emPause;
    }
    
    return emRun;
}
```

### 2. 分级响应机制
- **emStop**: 立即停止系统，用于严重安全问题
- **emPause**: 暂停系统，用于可恢复的安全问题
- **emRun**: 正常运行，所有安全检查通过

### 3. 安全优先级
1. **最高级**: 急停按钮、系统故障
2. **高级**: 安全光栅、安全门
3. **中级**: 设备异常、温度异常
4. **低级**: 一般性警告、提示信息

## 状态管理

### 1. 标志位管理
```cpp
map<CString, bool> m_mapFlag;
// 示例标志位
m_mapFlag["NGBeltRunning"] = true;     // NG皮带运行标志
m_mapFlag["EmergencyStop"] = false;    // 急停标志
m_mapFlag["SafetyGate"] = true;        // 安全门状态
m_mapFlag["ManualMode"] = false;       // 手动模式标志
```

### 2. 时间戳管理
```cpp
map<CString, DWORD> m_mapTick;
// 示例时间戳
m_mapTick["NGBeltStartTick"] = GetTickCount();     // NG皮带启动时间
m_mapTick["ButtonPressTick"] = GetTickCount();     // 按钮按下时间
m_mapTick["SafetyCheckTick"] = GetTickCount();     // 安全检查时间
```

### 3. 状态持久化
- **内存状态**: 运行时状态保存在映射表中
- **配置文件**: 关键设置保存到配置文件
- **日志记录**: 重要状态变化记录到日志
- **异常恢复**: 系统重启后能恢复到安全状态

## 异常处理策略

### 1. 预防性措施
- **多重检查**: 关键安全信号多重验证
- **冗余设计**: 关键安全功能有备用方案
- **定时检查**: 定期执行安全检查
- **状态监控**: 持续监控系统关键状态

### 2. 响应策略
```cpp
// 异常响应流程
if (检测到异常) {
    记录异常信息();
    评估异常级别();
    
    if (严重异常) {
        立即停止系统();
        触发报警();
        通知维护人员();
    } else if (一般异常) {
        暂停相关功能();
        尝试自动恢复();
        记录恢复结果();
    }
}
```

### 3. 恢复机制
- **自动恢复**: 对于临时性问题自动重试
- **手动干预**: 严重问题需要人工处理
- **渐进恢复**: 分步骤恢复系统功能
- **安全确认**: 恢复前确认安全条件

## 与其他线程的交互

### 1. 向其他线程发送控制信号
```cpp
// 发送系统暂停信号
VAR_SYSTEM_B("SystemPause") = true;

// 发送急停信号
VAR_SYSTEM_B("EmergencyStop") = true;

// 发送安全门状态
VAR_SAFETY_B("SafetyGate") = bSafetyGateStatus;
```

### 2. 接收其他线程的状态信息
```cpp
// 读取机器人状态
bool bRobotBusy = VAR_ROBOT_B("Busy");

// 读取托盘状态
bool bTrayReady = VAR_TRAY_B("Ready");

// 读取传送带状态
bool bBeltRunning = VAR_BELT_B("Running");
```

### 3. 系统级协调
- **统一控制**: 作为系统安全总控，可以控制所有其他线程
- **状态同步**: 与其他线程同步关键状态信息
- **优先级管理**: 安全相关的控制具有最高优先级
- **异常传播**: 将安全异常信息传播到相关线程

## 性能优化

### 1. 响应时间优化
- **高频检查**: 安全相关信号高频率检查
- **中断驱动**: 使用硬件中断提高响应速度
- **优先级调度**: 安全检查具有最高优先级
- **并发处理**: 多个子线程并发处理不同任务

### 2. 资源使用优化
- **内存映射**: 使用映射表高效管理状态
- **缓存机制**: 缓存频繁访问的状态信息
- **批量处理**: 批量处理多个按钮状态
- **定时器优化**: 优化定时器使用减少系统开销

## 维护和调试

### 1. 调试接口
```cpp
// 状态查询接口
CString GetMachineStatus();
CString GetNGBeltStatus();
CString GetButtonStatus();

// 手动控制接口
void ManualStartNGBelt();
void ManualStopNGBelt();
void ResetSafetyAlarm();
```

### 2. 日志记录
- **操作日志**: 记录所有操作员操作
- **状态日志**: 记录关键状态变化
- **异常日志**: 详细记录异常信息
- **性能日志**: 记录响应时间等性能数据

### 3. 测试支持
- **单元测试**: 各功能模块的独立测试
- **集成测试**: 与其他线程的协作测试
- **安全测试**: 各种安全场景的测试
- **压力测试**: 高负载下的性能测试

## 总结

LogicMachine作为FStation系统的安全守护者，具有以下核心价值：

### 技术特点
1. **双子线程架构**: 提高并发处理能力
2. **状态机驱动**: 清晰的状态管理和转换
3. **映射表通信**: 高效的数据管理和共享
4. **分级响应**: 根据问题严重程度分级处理

### 业务价值
1. **安全保障**: 全面的安全监控和保护机制
2. **质量控制**: 通过NG皮带确保产品质量
3. **操作友好**: 直观的按钮操作和状态反馈
4. **系统稳定**: 异常检测和自动恢复机制

### 设计优势
1. **模块化**: 清晰的功能模块划分
2. **可扩展**: 易于添加新的监控功能
3. **可维护**: 标准化的代码结构和接口
4. **高可靠**: 多重安全检查和冗余设计

LogicMachine虽然代码量不大，但在整个系统中起到了至关重要的作用，是保证系统安全运行的重要基石。其设计理念和实现方式为工控软件的安全设计提供了良好的参考。 