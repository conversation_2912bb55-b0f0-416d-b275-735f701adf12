# MQTT功能使用说明

## 概述

FStationMFC 项目现在支持通过配置项控制 MQTT 功能的启用和禁用。这个功能允许用户在不需要 MQTT 通信时完全禁用相关功能，避免不必要的网络连接和资源消耗。

## 配置项说明

### 配置项名称
- **参数名**: "MQTT功能启用"
- **类型**: 布尔值 (true/false)
- **默认值**: false (禁用)
- **权限级别**: 4 (管理员级别)

### 配置位置
配置项位于 Machine 模块中，会自动保存到配置文件：
- 系统级配置: `Sys/Sys.ini`
- 产品级配置: `Pro/{产品名}/Pro.ini`

## 功能影响范围

当 MQTT 功能被禁用时，以下功能将被跳过：

### 1. 初始化阶段
- Socket 接口初始化被跳过
- 不会尝试连接到 MQTT 网关
- 相关日志会显示 "MQTT功能已禁用，跳过Socket接口初始化"

### 2. 运行时功能
- 定期状态发送被跳过
- MQTT 命令处理被跳过
- 生产状态变化上报被跳过

### 3. 事件上报
- SN入站事件 (SendSnInEvent)
- SN出站事件 (SendSnOutEvent)
- SN出站询问事件 (SendSnOutReqEvent)
- BOP交付完成事件 (SendBopDeliverCompletedEvent)
- 暂停事件 (SendPauseEvent)
- 故障事件 (SendFaultEvent)
- 设备状态上报 (SendDeviceStatus)

### 4. 清理阶段
- Socket 接口清理被跳过

## 使用方法

### 方法1: 通过配置文件修改
1. 打开配置文件 `Sys/Sys.ini` 或 `Pro/{产品名}/Pro.ini`
2. 找到 `[Module_1]` 段落
3. 修改或添加: `MQTT功能启用=1` (启用) 或 `MQTT功能启用=0` (禁用)
4. 重启 FStation 程序

### 方法2: 通过程序界面修改
1. 启动 FStation 程序
2. 进入模块参数设置界面
3. 找到 Machine 模块
4. 修改 "MQTT功能启用" 参数
5. 保存配置

### 方法3: 通过代码修改
```cpp
// 启用 MQTT 功能
VAR_MACHINE("MQTT功能启用") = true;
g_pMachine->Save();

// 禁用 MQTT 功能
VAR_MACHINE("MQTT功能启用") = false;
g_pMachine->Save();

// 检查 MQTT 功能状态
bool mqttEnabled = VAR_MACHINE_B("MQTT功能启用");
```

## 日志信息

### 启用状态日志
```
Socket接口初始化成功，已连接到MQTT网关
已发送设备状态到SCADA平台
Socket接口已清理
```

### 禁用状态日志
```
MQTT功能已禁用，跳过Socket接口初始化
```

## 注意事项

1. **权限要求**: 修改此配置需要管理员级别权限 (权限级别4)
2. **重启生效**: 配置修改后需要重启程序才能完全生效
3. **向后兼容**: 默认值为 false，确保现有系统不会意外启用 MQTT 功能
4. **安全返回**: 当功能禁用时，所有 MQTT 相关函数返回 true，避免调用方认为是错误
5. **资源节约**: 禁用时不会创建网络连接，节约系统资源

## 故障排除

### 问题1: 配置修改后没有生效
**解决方案**: 确保重启了 FStation 程序，配置在程序启动时加载

### 问题2: 无法修改配置
**解决方案**: 检查用户权限，确保具有管理员级别权限

### 问题3: MQTT 功能启用但连接失败
**解决方案**: 
1. 检查 MQTT 网关是否运行
2. 检查网络连接
3. 检查配置文件 `config/socket_config.json`

## 技术实现

### 代码修改位置
1. **FStationDlg.cpp**: 主要的 MQTT 功能控制逻辑
2. **SimpleSocketInterface.cpp**: 静态方法中的配置检查
3. **Machine.cpp**: 配置项定义

### 检查机制
所有 MQTT 相关函数在执行前都会检查配置：
```cpp
if (!VAR_MACHINE_B("MQTT功能启用")) {
    return; // 或 return true;
}
```

这确保了功能的完全隔离和安全性。
