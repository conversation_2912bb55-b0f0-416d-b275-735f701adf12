
# CMake 构建系统指南

本项目的C++部分（如 `MQTTGateway` 和 `MQTTGatewayUI`）使用 CMake 进行构建管理。

## vcpkg 集成

- 项目通过 `vcpkg` 来管理第三方依赖库（如 `paho-mqtt`, `n<PERSON><PERSON>-json`）。
- 在配置CMake时，必须通过 `CMAKE_TOOLCHAIN_FILE` 参数指定 `vcpkg.cmake` 文件的路径，以便CMake能找到由vcpkg安装的库。

**示例命令:**
```cmake
cmake -S . -B build -DCMAKE_TOOLCHAIN_FILE=[path/to/vcpkg]/scripts/buildsystems/vcpkg.cmake
```

## 项目结构

- **根 [CMakeLists.txt](mdc:CMakeLists.txt)**: 定义顶层项目，并使用 `add_subdirectory()` 将 `MQTTGateway` 和 `MQTTGatewayUI` 等子项目包含进来。
- **子项目 CMakeLists.txt**: 分别定义每个子项目的源文件、依赖项 (`find_package`, `target_link_libraries`) 和可执行文件目标。

## 完整的安装配置系统

项目现在包含完整的安装和打包功能：

### 安装配置模块
- **[cmake/InstallConfig.cmake](mdc:cmake/InstallConfig.cmake)**: 统一的安装配置模块
- **[cmake/FStationConfig.cmake.in](mdc:cmake/FStationConfig.cmake.in)**: 包配置文件模板

### 组件化安装
支持以下安装组件：
- `Runtime`: 运行时文件（可执行文件、DLL、配置文件）
- `Development`: 开发文件（头文件、静态库、CMake配置）
- `Documentation`: 文档文件

### 自动DLL管理
安装系统自动包含以下依赖库：
- **vcpkg依赖**: paho-mqtt, OpenSSL, zlib等
- **Visual C++运行时库**: vcruntime140.dll, msvcp140.dll, ucrtbase.dll
- **Windows CRT API**: api-ms-win-crt-*.dll系列
- **Qt6库**: 所有必要的Qt DLL和插件

### 跨Windows系统支持
- 支持Windows 7/8.1/10/11
- 智能选择Debug/Release版本的DLL
- 自动处理运行时库依赖

## 构建和安装命令

### 标准构建流程
```bash
# 1. 配置构建
cmake -B build -S . -DCMAKE_TOOLCHAIN_FILE=D:/vcpkg/scripts/buildsystems/vcpkg.cmake

# 2. 构建项目
cmake --build build --config Release

# 3. 安装（包含所有运行时库）
cmake --install build --prefix ./install --component Runtime
```

### 组件化安装
```bash
# 仅安装运行时文件
cmake --install build --prefix ./install --component Runtime

# 仅安装开发文件
cmake --install build --prefix ./install --component Development

# 完整安装
cmake --install build --prefix ./install
```

### 打包分发
```bash
cd build
cpack -G ZIP    # 生成ZIP包
cpack -G WIX    # 生成MSI安装包
```

## 验证和部署工具

### 安装验证
- **[verify_installation.bat](mdc:verify_installation.bat)**: 验证安装完整性
- **[check_runtime_environment.bat](mdc:check_runtime_environment.bat)**: 检查目标系统运行时环境

### 使用方法
```bash
# 验证安装
.\verify_installation.bat install_directory

# 检查运行时环境
.\check_runtime_environment.bat
```

## 重要注意事项

1. **vcpkg工具链**: 必须在配置时指定vcpkg工具链文件
2. **构建类型**: Debug和Release版本会自动选择对应的DLL
3. **运行时库**: 现在自动包含，无需手动安装Visual C++ Redistributable
4. **跨系统部署**: 安装包包含所有必要的运行时库，可在不同Windows系统上运行
5. **Qt6路径**: 系统会自动检测Qt6安装路径，也可手动指定

## 故障排除

- 如果找不到vcpkg依赖，检查`CMAKE_TOOLCHAIN_FILE`设置
- 如果在目标系统运行失败，运行`check_runtime_environment.bat`检查环境
- 如果DLL缺失，确保使用了正确的安装组件
- 详细的故障排除指南请参考 [install_guide.md](mdc:install_guide.md)
