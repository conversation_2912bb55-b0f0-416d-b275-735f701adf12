﻿#include "stdafx.h"
#include "LogicProc.h"

#include "Sys.h"

#include "Robot.h"
#include "Mes.h"

CLogicProc::CLogicProc(int nIndex)
{
	m_nIndex = nIndex;
}

CLogicProc::~CLogicProc()
{

}

EnumStatus CLogicProc::OnSafeCheck()
{
	return emRun;
}

EnumStatus CLogicProc::OnStart()
{
	return emRun;
}

EnumStatus CLogicProc::OnPause()
{
	return emRun;
}

EnumStatus CLogicProc::OnResume()
{
	return emRun;
}

EnumStatus CLogicProc::OnStop()
{
	return emRun;
}

CStatus CLogicProc::OnRun()
{
	RETURN_STATE(&CLogicProc::OnProc00, true);
}

CStatus CLogicProc::OnProc00()
{
	DWORD nWaitRes = 0;

	nWaitRes = WaitForSingleObject(g_hEventProcStart[m_nIndex], 1);

	if (nWaitRes != WAIT_OBJECT_0) {
		RETURN_SELF("等待图像处理开始事件", false);
	}

	m_mapIndex["图像处理"] = 0;

	RETURN_STATE(&CLogicProc::OnProc01, true);
}

CStatus CLogicProc::OnProc01()
{
	CString sRet;

	if (m_mapIndex["图像处理"] >= g_pRobot->m_vImageFlow[m_nIndex].size()) {
		RETURN_STATE(&CLogicProc::OnProc00, true);
	}

	Sleep(1);

	DWORD nWaitRes = 0;

	nWaitRes = WaitForSingleObject(g_hEventProcStop[m_nIndex], 0);

	if (nWaitRes == WAIT_OBJECT_0) {
		RETURN_STATE(&CLogicProc::OnProc00, true);
	}

	if (g_pRobot->m_vImage[m_nIndex].size() <= m_mapIndex["图像处理"]) {
		sRet.Format("等待第%d张图像拍照完成", m_mapIndex["图像处理"] + 1);
		RETURN_SELF(sRet, false);
	}

	RETURN_STATE(&CLogicProc::OnProc02, true);
}

CStatus CLogicProc::OnProc02()
{
// 	if (VAR_MACHINE_B("弹片检测功能启用")) {
// 		IMAGINFO stImageInfo;
// 		stImageInfo = g_pRobot->m_vImage[m_mapIndex["图像处理"]];
// 
// 		CCvImage* pImage = CCvImage::CreateInstance();
// 
// 		pImage->SetImage(stImageInfo.pBuffer, stImageInfo.nWidth, stImageInfo.nHeight, stImageInfo.bTranspose, stImageInfo.bFlipX, stImageInfo.bFlipY);
// 
// 		CString strFile = CString(GetModulePath().c_str()) + "\\oppoFstation\\origin.bmp";
// 		DeleteFile(strFile);
// 		pImage->SaveImage(strFile, 1.0, true);
// 		// 删除旧文件
// 		CFileFind ff;
// 		BOOL bFlag = ff.FindFile("C:\\AlgoResult\\*.*");
// 		while(bFlag)
// 		{
// 			bFlag = (BOOL)ff.FindNextFile();
// 			if (ff.IsDots()) {
// 				continue;
// 			}
// 
// 			if (ff.IsDirectory()) {
// 				continue;
// 			}
// 
// 			CString strFileName = ff.GetFilePath();
// 
// 			DeleteFile(strFileName);
// 		}
// 		ff.Close();
// 
// 		delete pImage;
// 
// 		RETURN_STATE(&CLogicProc::OnProc02_0, true);
// 	}

	RETURN_STATE(&CLogicProc::OnProc03, true);
}

CStatus CLogicProc::OnProc02_0()
{
	CString strFile = CString(GetModulePath().c_str()) + "\\oppoFstation\\origin.bmp";

	CFileFind ff;
	BOOL bFlag = ff.FindFile(strFile);	
	ff.Close();

	if (!bFlag) {
		RETURN_SELF("等待图片保存完成", false);
	}

	CString strPath = CString(GetModulePath().c_str()) + "\\oppoFstation\\";

	CTime t = CTime::GetCurrentTime();

	CString strTime;
	strTime.Format("%02d-%02d-%02", t.GetHour(), t.GetMinute(), t.GetSecond());

	CString strCmd;
	strCmd.Format("/c oppoFAlgo.exe %s 1 oppoFStation.onnx origin.bmp C:\\AlgoResult\\ true", strTime);

	ShellExecute(NULL, "open", "cmd", strCmd, strPath, SW_HIDE);

	RETURN_STATE(&CLogicProc::OnProc02_1, true);
}

CStatus CLogicProc::OnProc02_1()
{
	bool bCheckFlag = false;

	CString strFileName;

	CFileFind ff;
	BOOL bFlag = ff.FindFile("C:\\AlgoResult\\*.*");
	while(bFlag)
	{
		bFlag = (BOOL)ff.FindNextFile();
		if (ff.IsDots()) {
			continue;
		}

		if (ff.IsDirectory()) {
			continue;
		}

		strFileName = ff.GetFilePath();

		if (strFileName.Find(".json") >= 0) {
			bCheckFlag = true;
			break;
		}
	}
	ff.Close();

	if (!bCheckFlag) {
		RETURN_SELF("等待图片检测完成", false);
	}

	int nShrapnelSum = 0, nScratchSum = 0, nBreakageSum = 0;

	CStdioFile sf;
	sf.Open(strFileName, CFile::modeRead | CFile::shareDenyNone);
	sf.SeekToBegin();
	CString sValue;
	do 
	{
		sf.ReadString(sValue);

		if (sValue.Find("shrapnel") > 0) {
			nShrapnelSum++;
			continue;
		}

		if (sValue.Find("scratch") > 0) {
			nScratchSum++;
			continue;
		}

		if (sValue.Find("breakage") > 0) {
			nBreakageSum++;
			continue;
		}
	} while (!sValue.IsEmpty());

	sf.Close();

	sValue.Format("Shrapnel num : %d; Scratch num : %d; Breakage num : %d", nShrapnelSum, nScratchSum, nBreakageSum);

	if (nBreakageSum > 0) {
		g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].bBadCheckResult = false;
		REPORT(sValue, emLogLevelError);
	}
	else {
		g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].bBadCheckResult = true;
		REPORT(sValue, emLogLevelNormal);
	}

	RETURN_STATE(&CLogicProc::OnProc03, true);
}

CStatus CLogicProc::OnProc03()
{
	IMAGINFO stImageInfo;
	stImageInfo = g_pRobot->m_vImage[m_nIndex][m_mapIndex["图像处理"]];

	CImageFlow *pFlow = NULL, *pFlowScanCode = NULL;
	pFlow = g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].pImageFlow;
	pFlowScanCode = g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].pImageFlowScanCode;

	bool bScanAll = false;
	bScanAll = g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].bScanAll;

	CImageWindow* pWnd = NULL;
	pWnd = g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].pImageWnd;

	CCvImage* pImage = CCvImage::CreateInstance();

	pImage->SetImage(stImageInfo.pBuffer, stImageInfo.nWidth, stImageInfo.nHeight, stImageInfo.bTranspose, stImageInfo.bFlipX, stImageInfo.bFlipY);

	CString sCode2D;
	CString ssCode2DforImage="";
	if (g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].bScanCode2D && pFlowScanCode != NULL) {
		pFlowScanCode->ScanCode(pImage, bScanAll);
		vector<string> vCode;
		pFlowScanCode->GetCode(vCode);
		g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].sCode2D.Empty();
		for (int i=0;i<vCode.size();i++)
		{
			ssCode2DforImage += CString("_") + vCode[i].c_str();
		}
		if (vCode.size() > 0) {
			g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].sCode2D = vCode[0].c_str();
			sCode2D=vCode[0].c_str();
		}
	}

	CString sName;

	if (!sCode2D.IsEmpty()) {
		g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].sCode2D = sCode2D;
	}

	if (!g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].sCode2D.IsEmpty()) {
		//sName.Format("%s_%s", g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].sName, sCode2D);
		sName.Format("%s_%s", g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].sName, ssCode2DforImage);
	}
	else {
		sName = g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].sName;
	}

	if (pFlow != NULL) {
		pFlow->Excute(pImage, CSys::m_strPro.c_str(), g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].sCode2D, g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].nHeadIndex, pWnd == g_pImageWndDn ? false : true, g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].bBadCheck);

		if (VAR_MACHINE_B("弹片检测功能启用") && g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].bBadCheck && pFlow->GetNccResult() != "OK") {
			g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].bBadCheckResult = true;
		}
		else {
			g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].bBadCheckResult = false;
		}

		bool bExistFlag = false;
		double nResultX = 0, nResultY = 0, nResultR = 0;

		if (g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].bBadCheck && pFlow->GetResult(nResultX, nResultY, nResultR, bExistFlag) == "OK") {
			g_pDatLog->UpdateSpecialSum(pFlow->GetNccResult() != "OK");
		}
	}

	g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].bProcOk = true;

	if (pFlow != NULL) {
		g_pRobot->PushImageResult(pImage, pWnd, g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].sDir, sName, pFlow->GetNccResult() == "OK" ? true : false);
	}
	else {
		g_pRobot->PushImageResult(pImage, pWnd, g_pRobot->m_vImageFlow[m_nIndex][m_mapIndex["图像处理"]].sDir, sName, true);
	}

	m_mapIndex["图像处理"]++;

	RETURN_STATE(&CLogicProc::OnProc01, true);
}
