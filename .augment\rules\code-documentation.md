---
type: "always_apply"
---

# 代码注释规范 - 协议文档同步

## 核心原则

在代码中直接说明协议细节，减少对外部文档的依赖，确保协议信息与代码实现同步。

## 注释规范

### 1. 协议版本标注
```cpp
// 协议版本：SCADA v1.0
// 最后更新：2024-01-15
// 变更说明：移除外层包裹字段，直接使用services数组
```

### 2. 消息格式说明
```cpp
// 消息格式示例：
// {
//   "services": [
//     {
//       "service_id": "EventService",
//       "event_type": "EVENT_SN_IN",
//       "properties": {
//         "sn": "2001000324005454107303211",
//         "production_model": "A121000185"
//       },
//       "event_time": "2025-07-11 16:22:07.902"
//     }
//   ]
// }
```

### 3. 函数级注释
```cpp
/**
 * 处理FStation发送的Socket消息
 * 
 * 支持的消息类型：
 * - DEVICE_STATUS: 设备状态消息
 * - PRODUCTION_EVENT: 生产事件消息  
 * - EVENT_SN_IN: SN入站事件
 * - EVENT_SN_OUT: SN出站事件
 * - EVENT_FAULT: 故障事件
 * - ALARM_EVENT: 告警事件
 * 
 * 消息格式：直接发送services数组，无需外层包裹
 * 参考文档：[doc/本项目SCADA对接指南.md](mdc:doc/本项目SCADA对接指南.md)
 */
void ProcessSocketMessages() {
    // 实现代码...
}
```

## 关键文件注释要求

### MQTTGateway.cpp
- [MQTTGateway/MQTTGateway.cpp](mdc:MQTTGateway/MQTTGateway.cpp) 中的关键函数必须包含协议说明
- ProcessSocketMessages() 函数需要详细的消息类型说明
- 所有MQTT发布函数需要包含主题格式说明

### ConfigManager.cpp  
- [MQTTGateway/ConfigManager.cpp](mdc:MQTTGateway/ConfigManager.cpp) 中的配置项需要说明用途
- 主题配置需要包含SCADA协议规范引用

### DataPointManager.cpp
- [MQTTGateway/DataPointManager.cpp](mdc:MQTTGateway/DataPointManager.cpp) 中的数据点需要说明来源
- 属性映射需要引用数采清单文档

## 文档引用规范

### 在代码中引用文档
```cpp
// 参考文档：[doc/重要/SCADA平台MQTT协议设备接入指南.md](mdc:doc/重要/SCADA平台MQTT协议设备接入指南.md)
// 数采清单：[doc/重要/科瑞F站--数采清单.md](mdc:doc/重要/科瑞F站--数采清单.md)
// 项目对接：[doc/本项目SCADA对接指南.md](mdc:doc/本项目SCADA对接指南.md)
```

### 在文档中引用代码
```markdown
相关代码：[MQTTGateway/MQTTGateway.cpp](mdc:MQTTGateway/MQTTGateway.cpp) 中的 `ProcessSocketMessages()` 函数
```

## 变更记录规范

### 代码变更时
```cpp
// 变更记录：
// 2024-01-15: 移除外层包裹字段，直接使用services数组
// 2024-01-10: 添加EVENT_SN_IN消息类型支持
// 2024-01-05: 初始实现，支持DEVICE_STATUS和PRODUCTION_EVENT
```

### 协议变更时
```cpp
// 协议变更：
// 2024-01-15: 升级到SCADA v1.0，简化消息格式
// 2024-01-10: 添加新的事件类型定义
```

## 质量检查

### 注释完整性检查
- [ ] 是否标注了协议版本
- [ ] 是否包含了消息格式示例
- [ ] 是否引用了相关文档
- [ ] 是否记录了变更历史
- [ ] 是否说明了函数用途

### 同步性检查
- [ ] 代码注释是否与文档一致
- [ ] 消息格式是否与官方协议一致
- [ ] 变更记录是否完整
- [ ] 文档引用是否正确

## 常见问题

### Q: 如何处理协议变更？
A: 1. 更新代码中的协议版本标注
   2. 更新消息格式示例
   3. 记录变更历史
   4. 同步更新项目对接指南

### Q: 如何处理代码重构？
A: 1. 保持注释的完整性
   2. 更新函数说明
   3. 确保文档引用正确
   4. 验证协议说明的准确性

### Q: 如何处理新增功能？
A: 1. 添加详细的功能说明
   2. 包含消息格式示例
   3. 引用相关文档
   4. 记录实现日期
description:
globs:
alwaysApply: false
---
