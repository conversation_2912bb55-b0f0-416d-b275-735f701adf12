﻿#pragma once

#include "ThreadBase.h"
using namespace yzBase;

#include <string>
#include <vector>
#include <map>
using namespace std;

enum enThreadType
{
	emThreadSpecial = 0,
	emThreadNormal,
	emThreadAlwaysExist
};

typedef struct THREAD_MSG
{
	CThreadMgr*		pThread;
	enThreadType	type;
	bool			bEnable;
} THREADMSG, *PTHREADMSG;

class CLogicMgr
{
public:
	CLogicMgr() {}
	virtual ~CLogicMgr() {}

public:
	static void InitProc();		// 初始化所有线程
	static void ReleaseProc();	// 释放所有线程

	static void Run();
	static void Pause();
	static void Stop();			// 停止所有线程

	static bool isRunning();
	static bool isPause();

public:
	static void RunAlways();
	static void Reset();
	static void RunSingle(CString sThreadName);
	static void RunAny(vector<CString> vThreadName);

public:
	static CThreadFactory			m_ThreadFactory;	// 创建线程
	static map<CString, THREADMSG>	m_mapThread;		// 包含所有线程

	static CThreadMonitor*			m_pThreadMonitor;

};