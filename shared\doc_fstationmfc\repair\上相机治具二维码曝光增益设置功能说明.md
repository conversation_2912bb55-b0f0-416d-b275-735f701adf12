# 上相机治具二维码曝光增益设置功能说明

## 概述

本次更新为FStation系统新增了专门针对治具二维码识别的曝光增益设置功能，将治具二维码识别的相机参数与普通治具识别参数分开设置，以增强治具二维码的识别能力。

## 功能背景

在原有系统中，治具二维码扫描使用的是"上相机(皮带)"的曝光增益设置，这可能导致：
- 二维码识别效果不佳
- 无法针对二维码特性进行专门优化
- 与其他识别功能的参数冲突

## 解决方案

### 1. 新增配置参数

在Robot模块中新增了两个专用配置参数：

| 配置项名称 | 默认值 | 说明 |
|------------|--------|------|
| 上相机(治具二维码)曝光时间 | 15000 | 治具二维码识别专用曝光时间(微秒) |
| 上相机(治具二维码)增益 | 5 | 治具二维码识别专用增益值 |

**参数说明：**
- 曝光时间默认15000微秒，比普通治具识别增加5000微秒，提高二维码清晰度
- 增益默认为5，适当提高对比度，增强二维码边缘识别

### 2. CDialogCamera界面增强

#### 2.1 新增第6个相机选项
- 添加了"上相机(治具二维码)"选项（Radio6）
- 用户可以单独调节治具二维码识别的曝光增益参数
- 界面布局与其他相机选项保持一致

#### 2.2 鼠标右键移动功能完善
修复了CDialogCamera中鼠标右键点动移动相机功能的遗漏：

**修复内容：**
- **OnUmRightMove函数增强：**
  - 添加了case 4（下相机参考位）和case 5（上相机治具二维码）的支持
  - 完善了图像尺寸计算逻辑
  - 修复了图像标定对象选择
  - 补充了相机移动坐标计算
  - 完善了触发模式设置

**修复前的问题：**
- case 2（TRAY相机）缺少图像标定对象处理
- case 4（下相机参考位）完全缺失
- case 5（新增的治具二维码相机）未实现

**修复后的改进：**
- 所有6个相机选项都支持鼠标右键移动功能
- 正确的图像标定对象映射：
  - case 0,3,5 → 上相机标定对象
  - case 1,4 → 下相机标定对象  
  - case 2 → TRAY相机标定对象

### 3. 逻辑控制更新

#### 3.1 治具二维码扫描优化
在`LogicRobot.cpp`的`OnRobot34`函数中：
- **修改前：** 使用"上相机(皮带)曝光时间"和"上相机(皮带)增益"
- **修改后：** 使用"上相机(治具二维码)曝光时间"和"上相机(治具二维码)增益"

这样确保治具二维码扫描使用专门优化的参数，提高识别成功率。

## 使用指南

### 1. 参数配置
1. 打开相机调试界面
2. 选择"上相机(治具二维码)"选项（Radio6）
3. 调节曝光时间和增益滑块
4. 实时观察二维码识别效果
5. 保存最优参数设置

### 2. 参数调节建议
- **曝光时间：** 
  - 起始值：15000微秒
  - 二维码模糊时增加曝光时间
  - 过度曝光时减少曝光时间
  
- **增益：**
  - 起始值：5
  - 对比度不足时增加增益
  - 噪点过多时减少增益

### 3. 鼠标右键移动功能
- 在任意相机视图中，右键点击图像区域
- 相机会自动移动到点击位置
- 支持所有6种相机模式：
  - 上相机(治具)
  - 下相机
  - TRAY相机
  - 上相机(皮带)
  - 下相机(参考位)
  - 上相机(治具二维码) ← 新增

## 技术实现

### 1. 代码修改文件
- `FStationMFC/FStation/Module/Robot.cpp` - 添加配置参数
- `FStationMFC/FStation/DialogCamera.h` - 添加函数声明
- `FStationMFC/FStation/DialogCamera.cpp` - 界面逻辑实现
- `FStationMFC/FStation/Logic/LogicRobot.cpp` - 业务逻辑更新

### 2. 关键函数
- `OnBnClickedRadio6()` - 第6个相机选项处理
- `OnUmRightMove()` - 鼠标右键移动功能
- `OnHScroll()` - 滑块调节处理
- `OnRobot34()` - 治具二维码扫描逻辑

## 预期效果

1. **识别精度提升：** 专用参数优化提高二维码识别成功率
2. **参数独立性：** 治具二维码参数不影响其他识别功能
3. **操作便利性：** 界面统一，参数调节方便
4. **功能完整性：** 鼠标右键移动功能覆盖所有相机
5. **系统稳定性：** 减少参数冲突，提高系统可靠性

## 注意事项

1. **参数范围：** 曝光时间建议在5000-30000微秒范围内
2. **增益设置：** 增益值过高可能引入噪点，建议0-20范围内
3. **环境光照：** 不同光照条件下需要重新调节参数
4. **二维码质量：** 确保治具二维码清晰、无损坏
5. **保存设置：** 调节完成后及时保存参数避免丢失 