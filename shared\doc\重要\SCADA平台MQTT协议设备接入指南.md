# SCADA 平台 MQTT 协议设备接入指南 v1.13

## 1. 文档概述

### 1.1. 目的

本文档旨在为设备开发人员提供一个清晰、全面的指南，用于将各类工业设备快速、稳定地接入 SCADA 监控平台。通过遵循本文档中定义的 MQTT 通信协议和数据格式规范，开发者可以实现设备与平台之间的顺利连接、实时数据传输和远程控制。

### 1.2. 适用范围

本指南适用于所有需要接入 SCADA 平台的、支持 TCP/IP 协议并能够实现 MQTT 客户端功能的智能设备、网关设备或边缘计算单元。

### 1.3. 版本历史

| 版本号 | 修订日期   | 修订内容                                                                                                                                                                                       |
| :----- | :--------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| v1.10  | 2024-10-25 | - 增加设备订阅 Topic 说明，明确通配符 '+' 的使用场景。`<br>`- 定义基础数据交互的统一 JSON 结构。`<br>`- 强调设备侧的幂等性及重试逻辑实现要求。`<br>`- 明确设备事件上报后，平台必须响应。 |
| v1.11  | 2024-10-30 | - 阐明设备接入时，协议中固定部分与可变部分的界限。`<br>`- 增加设备端需要进行的软件改造通用要求。                                                                                             |
| v1.12  | 2024-12-08 | -**设备事件上报**：`eventType` 增加 `EVENT_SN_OUT_REQ` (过站询问) 和 `EVENT_BOP_DELIVER_COMPLETED` (转产配置下发完成)。                                                            |
| v1.13  | 2024-12-24 | -**初始版本**：基于 v1.13 PDF 文档进行专业化重写和内容增强。                                                                                                                             |

---

## 2. 核心概念与设计原则

### 2.1. 通信协议

平台与设备之间所有的数据交互均基于 **MQTT v3.1.1** 协议。

- **测试环境 Broker 地址**: `172.16.247.78:1883`
- **网络连通性测试**: 在尝试连接前，请务必确保设备网络可访问目标地址和端口。可使用 `telnet 172.16.247.78 1883` 命令进行测试。

### 2.2. Topic 命名规范

Topic 是 MQTT 协议的核心，用于路由消息。本协议中的 Topic 遵循层级结构，并使用 `/` 作为分隔符。

- **`$oc/devices/{deviceId}`**: 这是所有设备相关通信的根路径。
- **`{deviceId}`**: 设备的唯一标识符。在建立连接和发布/订阅时，必须替换为具体的设备 ID。

#### 2.2.1. Topic 方向

- **上行 (Uplink)**: 设备向平台发送消息。用于设备上报数据或响应平台的请求。
- **下行 (Downlink)**: 平台向设备发送消息。用于平台下发指令或响应设备的请求。

> **重要**: 设备成功连接到 MQTT Broker 后，**必须立即订阅所有相关的下行 Topic**，否则将无法接收来自平台的任何指令和响应。

#### 2.2.2. Topic 总览

| 用途                       | Topic                                                                         | 方向 | QoS |
| :------------------------- | :---------------------------------------------------------------------------- | :--- | :-- |
| **设备属性周期上报** | `$oc/devices/{deviceId}/sys/properties/report`                              | 上行 | 1   |
| **平台查询设备属性** | `$oc/devices/{deviceId}/sys/properties/get/request_id={requestId}`          | 下行 | 2   |
| **设备响应属性查询** | `$oc/devices/{deviceId}/sys/properties/get/response/request_id={requestId}` | 上行 | 1   |
| **平台设置设备属性** | `$oc/devices/{deviceId}/sys/properties/set/request_id={requestId}`          | 下行 | 2   |
| **设备响应属性设置** | `$oc/devices/{deviceId}/sys/properties/set/response/request_id={requestId}` | 上行 | 1   |
| **平台下发设备命令** | `$oc/devices/{deviceId}/sys/commands/request_id={requestId}`                | 下行 | 2   |
| **设备响应平台命令** | `$oc/devices/{deviceId}/sys/commands/response/request_id={requestId}`       | 上行 | 1   |
| **设备主动上报事件** | `$oc/devices/{deviceId}/sys/events/up/request_id={requestId}`               | 上行 | 2   |
| **平台响应设备事件** | `$oc/devices/{deviceId}/sys/events/up/response/request_id={requestId}`      | 下行 | 2   |

### 2.3. 服务质量 (QoS)

为保证消息的可靠传输，本协议根据不同的交互类型，定义了两个服务质量 (QoS) 等级：

- **QoS 1 (At Least Once)**: 用于设备单向的、无需业务层确认的数据上报（如属性周期上报）。消息至少会送达一次，网络层面的确认可以保证消息到达 Broker，但设备端和平台端仍需处理可能出现的重复消息。
- **QoS 2 (Exactly Once)**: 用于所有需要请求-响应模式的交互（如命令、查询、设置、事件上报等）。这是最高的保证级别，确保消息只被接收和处理一次。设备端必须实现相应的逻辑来处理 MQTT 协议的 QoS 2 流程。

### 2.4. 幂等性 (Idempotency)

由于网络波动和重试机制，平台或设备可能会重发消息，导致接收方收到重复的请求。为避免重复处理指令（例如，对同一个 SN 重复入站），所有 **QoS 为 2 的请求都必须实现幂等性**。

- **实现方式**: 通过 `request_id` 实现。`request_id` 是一个由请求发起方生成的、在一定时间内唯一的标识符。
- **处理逻辑**: 接收方必须缓存近期已处理过的 `request_id`。在收到新请求时，首先检查其 `request_id` 是否已被处理。
  - 如果是，则直接丢弃该请求或仅返回上一次的处理结果，而不重复执行业务逻辑。
  - 如果否，则正常处理该请求，并记录其 `request_id`。

### 2.2.3. 设备订阅 Topic 指南

设备与平台建立连接后，必须使用 `+` 单层通配符订阅以下 Topic，以接收来自平台的指令和响应。

| 订阅 Topic                                          | QoS 等级 | 说明                         |
| :-------------------------------------------------- | :------- | :--------------------------- |
| `$oc/devices/{deviceId}/sys/properties/get/+`     | 2        | 接收平台属性查询请求         |
| `$oc/devices/{deviceId}/sys/properties/set/+`     | 2        | 接收平台属性设置请求         |
| `$oc/devices/{deviceId}/sys/commands/+`           | 2        | 接收平台命令下发请求         |
| `$oc/devices/{deviceId}/sys/events/up/response/+` | 2        | 接收平台对设备事件上报的响应 |

> **警告**: 请勿使用多层通配符 `#` 进行订阅，这可能导致接收过多非必要消息或被 ACL 策略拒绝。

### 2.5. 设备端改造要求

#### 2.5.1. 配置文件

为便于统一管理和维护，设备端应用程序应包含一个配置文件，推荐命名为 `scada_ctrl_conf.json`，存放于程序主目录。

**配置示例**:

```json
{
  "device": {
    "deviceId": "YOUR_UNIQUE_DEVICE_ID",
    "secret": "YOUR_DEVICE_SECRET"
  },
  "mqtt": {
    "broker": "172.16.247.78",
    "port": 1883,
    "autoReconnect": true,
    "cleanSession": true,
    "keepaliveInterval": 60,
    "connectionTimeout": 30
  },
  "log": {
    "level": "warn",
    "filePath": "logs/device.log",
    "maxSizeMB": 20,
    "maxFiles": 5
  },
  "features": {
    "defaultReportCycleS": 300
  }
}
```

#### 2.5.2. 日志记录

设备端必须实现完善的日志记录功能，覆盖关键操作和异常情况，包括但不限于：

- 程序启动与退出
- MQTT 连接、断开与重连
- 消息的发布与接收 (关键信息，如 Topic 和 `request_id`)
- 业务逻辑的执行结果 (成功/失败)
- 错误与异常详情

#### 2.5.3. 重试逻辑

设备在发送请求失败（无论是网络传输层面还是业务层面未收到预期响应）时，应具备自动重试机制。

- **重试次数**: 设置合理的重试次数（如 1-3 次），避免无限重试。
- **重试间隔**: 采用指数退避等策略设置合理的重试间隔（如首次 2s，第二次 4s），避免过于频繁的重试给平台造成压力。
- **错误处理**: 如果多次重试后仍然失败，应将该事件记录到错误日志中，并根据业务需求触发相应的报警或提示，以便人工干预。

#### 2.5.4. 降级与自主运行

在极端情况下（如与平台长时间失联），设备应具备服务降级能力，切换至**自主运行模式**，以保障核心生产功能不中断。当与平台的连接恢复后，应能自动或手动切换回**中控模式**。

### 2.6. 设备认证

#### 2.6.1. 功能说明

平台通过 MQTT `CONNECT` 消息接口对设备进行身份验证。认证通过后，设备与平台间的 MQTT 连接才能成功建立。设备认证失败时，平台会返回相应的错误码并主动断开连接。

您可以点击使用 [SCADA MQTT 客户端连接参数生成工具](https://www.example.com) 填写您获取的设备 ID 和密钥，来生成连接所需的认证参数。

#### 2.6.2. 详细设计

##### MQTT 连接参数

| 字段名       | 是否必填 | 类型       | 描述                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :----------- | :------- | :--------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `clientId` | 是       | `String` | **一机一密的设备 ClientId**，由 `设备ID`、`身份标识`、`签名类型`、`时间戳` 四个部分通过下划线 `_` 分隔组成。 **格式**: `{deviceId}_{identityType}_{signatureType}_{timestamp}` **示例**: `A049998285_0_0_2024080506`  **组成部分说明**: - **设备ID**: 在平台成功注册后生成的唯一设备标识。- **身份标识**: 固定为 `0`，表示设备ID。- **签名类型**: `0` = HMACSHA256不校验时间戳; `1` = HMACSHA256校验时间戳。 - **时间戳**: 设备连接平台的 UTC+8 时间，格式为 `YYYYMMDDHH`。 |
| `username` | 是       | `String` | 此处即为设备的唯一标识 `deviceId`。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| `password` | 是       | `String` | 使用 `HMACSHA256` 算法，以 `clientId` 中的**时间戳**作为密钥，对设备的 `secret` 进行加密后的值。                                                                                                                                                                                                                                                                                                                                                                                                                                         |

##### MQTT 连接返回码

| 返回码   | 描述                         | 可能原因                                          |
| :------- | :--------------------------- | :------------------------------------------------ |
| `0x00` | 连接成功                     | -                                                 |
| `0x01` | 请求拒绝，协议版本错误       | 服务器不支持客户端请求的 MQTT 协议版本。          |
| `0x02` | 请求拒绝，无效的客户端标识符 | `clientId` 格式不正确或心跳间隔不满足平台要求。 |
| `0x03` | 请求拒绝，服务器不可用       | 平台服务不可用。                                  |
| `0x04` | 请求拒绝，用户名或密码错误   | 用户名或密码错误。                                |
| `0x05` | 请求拒绝，没有授权           | 客户端没有权限连接。                              |

### 2.7. 设备授权

设备认证成功后，平台将通过 ACL (Access Control List) 验证其 Topic 操作权限。这用于对设备的发布/订阅行为进行隔离，确保设备只能访问授权的 Topic。

当设备尝试订阅或发布未授权的 Topic 时，操作将失败，并可能在设备日志中看到类似如下的错误：

```log
[2024-10-30 17:32:04] [ERROR] Failed to subscribe: $oc/devices/A005/sys/#, Error: Not authorized (Code: 128).
```

### 2.8. 基础数据结构定义

所有上行的数据交互（如属性上报、事件上报）均遵循以下基础的 JSON 结构。它以一个 `services` 数组为根，其中包含一个或多个服务对象。

```json
{
  "services": [
    {
      "service_id": "DefaultService",
      "properties": {},
      "event_time": "2024-09-04 16:02:21.165"
    },
    {
      "service_id": "SnService",
      "properties": {},
      "event_time": "2024-09-04 16:02:21.165"
    },
    {
      "service_id": "StatusService",
      "properties": {},
      "event_time": "2024-09-04 16:02:21.165"
    }
  ]
}
```

**核心服务说明**:

- **`DefaultService`**: 用于上报设备类型的通用参数和生产数据。
- **`SnService`**: 用于上报与产品 SN 码相关的上下文数据。
- **`StatusService`**: 用于上报设备当前的状态信息。

在具体的接口定义中，每个服务的 `properties` 对象需要根据实际情况填充具体的属性数据。

---

## 3. 交互接口详解

本章节详细定义了设备与平台之间的各类数据交互接口。

### 3.1. 设备属性周期上报

#### 3.1.1. 功能说明

用于设备按产品模型中定义的格式将属性数据主动上报给平台。需要设备软件支持可动态配置上报频率（单位：毫秒），当配置为0时则不主动上报。动态配置上报频率由平台命令进行下发, 具体请查看 **3.4. 平台命令下发** 章节。

#### 3.1.2. 详细设计

**使用限制**

- 单个消息内容不大于 512KB。
- **服务质量 QoS：1 - 至少一次（At least once）**。
- 无需等待业务层面的回复，只需保证数据在传输层面成功投递给 MQTT broker。
- 需要设置产品模型，数据内容需要与产品模型中定义的属性相匹配。

#### 3.1.3. 时序图

```mermaid
sequenceDiagram
    participant Device
    participant Platform

    loop Periodic Reporting
        Device->>Device: 采集设备数据
        Device->>Platform: 发布属性数据 (properties/report)
    end
```

#### 3.1.4. Topic

- **方向**: 上行 (Uplink)
- **Topic**: `$oc/devices/{deviceId}/sys/properties/report`

#### 3.1.5. 数据格式 (Payload)

Payload 为一个 JSON 对象，其中包含一个或多个服务（Service）的数据。

**Payload 结构**:

```json
{
  "services": [
    {
      "service_id": "DefaultService",
      "properties": {
        "C00001": "2001000168005843127309215",
        "C00002": "A049991234",
        "C00003": 1
      },
      "event_time": "2024-09-04 16:02:21.165"
    },
    {
      "service_id": "SnService",
      "properties": {
        "sn": "2001000168005843127309215",
        "production_model": "C380_B75",
        "profiles": [
          { "name": "5380495513.xml" },
          { "name": "5382000005.xml" }
        ]
      },
      "event_time": "2024-09-04 16:02:21.165"
    },
    {
      "service_id": "StatusService",
      "properties": {
        "device_status_enum": "fault_error",
        "device_status_code": "E001",
        "device_status_desc": "Robot检测段差C"
      },
      "event_time": "2024-09-04 16:02:21.165"
    }
  ]
}
```

**Payload 参数说明**:

| 字段名         | 父字段         | 类型       | 是否必填 | 描述                                                                                                  |
| :------------- | :------------- | :--------- | :------- | :---------------------------------------------------------------------------------------------------- |
| `services`   | (root)         | `Array`  | 是       | 服务列表，每个元素代表一个服务的数据集合。                                                            |
| `service_id` | `services[]` | `String` | 是       | 设备的服务ID，由创建的产品模型确定。默认服务 `DefaultService` 需要上传当前设备所有生产数据。        |
| `properties` | `services[]` | `Object` | 是       | 设备关联的产品模型中定义的属性键值对集合。                                                            |
| `event_time` | `services[]` | `String` | 否       | 事件时间，标准日期时间格式，精确到毫秒:`YYYY-MM-DD hh:mm:ss.SSS`。例: `2024-01-11 16:02:21.165`。 |

---

### 3.2. 平台查询设备属性

#### 3.2.1. 功能说明

平台向指定设备发起请求，用于实时获取一个或多个服务（Service）的属性数据。设备在收到请求后，必须立即上报所请求服务的当前数据。此接口用于替代轮询，实现按需数据拉取。

#### 3.2.2. 使用限制

- **服务质量 QoS：2 - 仅一次（Exactly once）**。
- **幂等性要求**: 平台下发消息后如果在设定超时时间内（如 10s）没有收到设备响应，会上报超时并可能重发。设备端必须基于 `request_id` 实现去重，避免重复处理。

#### 3.2.3. 时序图

```mermaid
sequenceDiagram
    participant Platform
    participant Device

    Platform->>Device: 发起属性查询请求 (properties/get)
    Note right of Device: 设备处理请求...
    Device-->>Platform: 响应查询结果 (properties/get/response)
```

#### 3.2.4. Topic

- **下行 (Downlink - 请求)**: `$oc/devices/{deviceId}/sys/properties/get/request_id={requestId}`
- **上行 (Uplink - 响应)**: `$oc/devices/{deviceId}/sys/properties/get/response/request_id={requestId}`

> **注意**: 响应的 Topic 必须包含与请求完全相同的 `requestId`。

#### 3.2.5. 下行请求 (Platform -> Device)

**请求 Payload 结构**:

```json
{
  "lane_no": 1,
  "side": "T",
  "service_id": "DefaultService"
}
```

**请求 Payload 参数说明**:

| 字段名         | 类型        | 是否必填 | 描述                                                                                                                                    |
| :------------- | :---------- | :------- | :-------------------------------------------------------------------------------------------------------------------------------------- |
| `lane_no`    | `Integer` | 否       | 轨道号。默认为 0。                                                                                                                      |
| `side`       | `Enum`    | 否       | 面别。可选值为 `T` (Top) 或 `B` (Bottom)。                                                                                          |
| `service_id` | `String`  | 是       | 需要查询的服务 ID。如果需要查询所有核心服务，可以约定一个特殊值（如 `all`）或省略此字段（需前后端约定）。目前定义为查询指定单个服务。 |

#### 3.2.6. 上行响应 (Device -> Platform)

响应的数据格式与 **3.1. 设备属性周期上报** 的格式基本一致。设备应根据请求中的 `service_id`，上报对应服务的数据。如果请求中未指定 `service_id` 或指定为 `all`，则默认上报所有核心服务的数据。

**响应 Payload 示例 (请求 `DefaultService` 或 `all`)**:

```json
{
  "services": [
    {
      "service_id": "DefaultService",
      "properties": {
        "C00001": "00232430452003F200003171",
        "C00002": "A049991234"
      },
      "event_time": "2024-09-04 16:05:30.482"
    },
    {
      "service_id": "SnService",
      "properties": {
        "sn": "00232430452003F200003171"
      },
      "event_time": "2024-09-04 16:05:30.482"
    },
    {
      "service_id": "StatusService",
      "properties": {
        "device_status_enum": "production"
      },
      "event_time": "2024-09-04 16:05:30.482"
    }
  ]
}
```

---

### 3.3. 平台设置设备属性

#### 3.3.1. 功能说明

平台向指定设备下发请求，用于修改设备的可写属性（参数）。设备收到后需要应用这些设置，并向上行返回一个执行结果的响应。

#### 3.3.2. 使用限制

- **服务质量 QoS：2 - 仅一次（Exactly once）**。
- **幂等性要求**: 平台下发消息后如果在设定超时时间内（如 10s）没有收到设备响应，会上报超时并可能重发。设备端必须基于 `request_id` 实现去重，避免重复处理。

#### 3.3.3. 时序图

```mermaid
sequenceDiagram
    participant Platform
    participant Device

    Platform->>Device: 发起属性设置请求 (properties/set)
    Note right of Device: 设备应用参数...
    Device-->>Platform: 响应设置结果 (properties/set/response)
```

#### 3.3.4. Topic

- **下行 (Downlink - 请求)**: `$oc/devices/{deviceId}/sys/properties/set/request_id={requestId}`
- **上行 (Uplink - 响应)**: `$oc/devices/{deviceId}/sys/properties/set/response/request_id={requestId}`

#### 3.3.5. 下行请求 (Platform -> Device)

**请求 Payload 结构**:
Payload 包含一个服务列表，每个服务中定义了需要设置的属性键值对。

```json
{
  "lane_no": 1,
  "side": "T",
  "services": [
    {
      "service_id": "DefaultService",
      "properties": {
        "C00001": "new_value_for_C00001",
        "C00003": 25.5
      }
    }
  ]
}
```

**请求 Payload 参数说明**:

| 字段名         | 父字段         | 类型        | 是否必填 | 描述                                                                     |
| :------------- | :------------- | :---------- | :------- | :----------------------------------------------------------------------- |
| `lane_no`    | (root)         | `Integer` | 否       | 轨道号。                                                                 |
| `side`       | (root)         | `Enum`    | 否       | 面别 (`T`/`B`)。                                                     |
| `services`   | (root)         | `Array`   | 是       | 服务列表。                                                               |
| `service_id` | `services[]` | `String`  | 是       | 要设置的服务 ID。                                                        |
| `properties` | `services[]` | `Object`  | 是       | 属性键值对集合。Key为物模型中定义的可写属性标识符，Value为要设置的新值。 |

#### 3.3.6. 上行响应 (Device -> Platform)

**响应 Payload 结构**:

```json
{
  "result_code": 0,
  "result_desc": "success"
}
```

**响应 Payload 参数说明**:

| 字段名          | 类型        | 是否必填 | 描述                                                  |
| :-------------- | :---------- | :------- | :---------------------------------------------------- |
| `result_code` | `Integer` | 是       | 命令执行结果码。`0` 表示成功，非 `0` 表示失败。   |
| `result_desc` | `String`  | 是       | 对执行结果的文字描述，如 "success" 或具体的错误信息。 |

---

### 3.4. 平台命令下发

#### 3.4.1. 功能说明

平台向设备下发一个指定的命令，以触发设备执行某个特定的动作，例如转产、暂停、下发SN、下发远程配置等。设备执行命令后，需要返回执行结果。

#### 3.4.2. 使用限制

- 单个消息内容不大于 512KB。
- **服务质量 QoS：2 - 仅一次（Exactly once）**。
- **幂等性要求**: 平台下发消息后如果在设定超时时间内（如 10s）没有收到设备响应，会上报超时并可能重发。设备端必须基于 `request_id` 实现去重，避免重复处理。
- 需要设置产品模型，数据内容需要与产品模型中定义的属性相匹配。

#### 3.4.3. 时序图

```mermaid
sequenceDiagram
    participant Platform
    participant Device

    Platform->>Device: 下发命令 (commands/request)
    Note right of Device: 设备执行命令...
    Device-->>Platform: 响应命令执行结果 (commands/response)
```

#### 3.4.4. Topic

- **下行 (Downlink - 请求)**: `$oc/devices/{deviceId}/sys/commands/request_id={requestId}`
- **上行 (Uplink - 响应)**: `$oc/devices/{deviceId}/sys/commands/response/request_id={requestId}`

#### 3.4.5. 平台命令清单

| 命令名称           | 命令 Key                        | 命令参数 (`properties`)          | 说明                                                                                |
| :----------------- | :------------------------------ | :--------------------------------- | :---------------------------------------------------------------------------------- |
| 下发 SN            | `COMMAND_SN_DELIVER`          | `sn`, `production_model`       | 向设备下发当前工序需要处理的产品 SN。                                               |
| 转产/下发生产参数  | `COMMAND_BOP_DELIVER`         | `production_model`, `profiles` | 设备自主清空旧配置，并应用新的转产生产参数。`profiles` 包含工艺参数配置文件列表。 |
| 暂停生产           | `COMMAND_PAUSE`               | (无)                               | 指示设备暂停当前生产活动。                                                          |
| 恢复生产           | `COMMAND_PRODUCTION`          | (无)                               | 将设备从暂停状态恢复到生产状态或待机状态。                                          |
| 下发 MQTT 配置参数 | `COMMAND_MQTT_CONFIG_DELIVER` | `prop_report_cycle`              | 设置设备属性上报频率（毫秒），为 `0` 则不主动上报。设备需将此配置持久化到本地。   |

#### 3.4.6. 下行请求 (Platform -> Device)

##### 请求 Payload 结构

```json
{
  "service_id": "CommandService",
  "command_name": "COMMAND_BOP_DELIVER",
  "lane_no": 1,
  "side": "T",
  "properties": {
    "production_model": "C380_B75",
    "profiles": [
      {
        "name": "5380495513.xml",
        "down_url": "https://ocs-cn-south3.oppoer.me/scada-device-file/5380495513.xml",
        "hash": "040d85d08b0ad6e57ce649cf573747d8",
        "fsize": 3235
      }
    ]
  }
}
```

##### 请求 Payload 参数说明

| 字段名           | 类型        | 是否必填 | 描述                                                                                                                                                                                                                                                                          |
| :--------------- | :---------- | :------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `service_id`   | `String`  | 是       | 固定为 `CommandService`。                                                                                                                                                                                                                                                   |
| `command_name` | `String`  | 是       | 命令名称。具体名称在物模型中定义。示例：`<br>`- `COMMAND_SN_DELIVER`: 下发 SN `<br>`- `COMMAND_BOP_DELIVER`: 转产/下发生产参数 `<br>`- `COMMAND_PAUSE`: 暂停 `<br>`- `COMMAND_PRODUCTION`: 恢复生产 `<br>`- `COMMAND_MQTT_CONFIG_DELIVER`: 下发 MQTT 配置 |
| `lane_no`      | `Integer` | 否       | 轨道号。                                                                                                                                                                                                                                                                      |
| `side`         | `Enum`    | 否       | 面别 (`T`/`B`)。                                                                                                                                                                                                                                                          |
| `properties`   | `Object`  | 是       | 执行该命令所需的参数列表，以键值对形式提供。具体内容由 `command_name` 决定。                                                                                                                                                                                                |

##### 请求示例 2: 下发 SN

```json
{
  "service_id": "CommandService",
  "command_name": "COMMAND_SN_DELIVER",
  "lane_no": 1,
  "side": "T",
  "properties": {
    "sn": "00232430452003F200003171",
    "production_model": "C380_B75"
  }
}
```

#### 3.4.7. 上行响应 (Device -> Platform)

**响应 Payload 结构**:

```json
{
  "response_name": "COMMAND_RESPONSE",
  "result_code": 0,
  "properties": {
    "result": "success"
  }
}
```

**响应 Payload 参数说明**:

| 字段名            | 类型        | 是否必填 | 描述                                                                                              |
| :---------------- | :---------- | :------- | :------------------------------------------------------------------------------------------------ |
| `response_name` | `String`  | 是       | 固定为 `COMMAND_RESPONSE`。                                                                     |
| `result_code`   | `Integer` | 是       | 命令执行结果码。`0` 表示成功，非 `0` 表示失败。                                               |
| `properties`    | `Object`  | 是       | 包含命令执行结果的详细信息。通常至少包含一个 `result` 字段，内容为 "success" 或具体的错误信息。 |

---

### 3.5. 设备事件上报

#### 3.5.1. 功能说明

设备在生产过程中发生特定事件时（例如：产品入站、出站、发生故障、转产完成等），应主动向平台发送事件通知。这使得平台能够实时响应产线的变化。与周期性上报不同，事件上报是触发式的。

#### 3.5.2. 使用限制

- 单个消息内容不大于 512KB。
- **服务质量 QoS：2 - 仅一次（Exactly once）**。
- **重试与幂等性要求**: 设备每次上报事件必须使用唯一的 `request_id`（如13位毫秒时间戳）。如果在 10 秒内未收到平台的响应 `ACK`，设备需要重发一次（使用相同的 `request_id`）。如果重发后仍未收到响应，则应记录日志并报警，通知人工处理。

#### 3.5.3. 时序图

```mermaid
sequenceDiagram
    participant Device
    participant Platform

    Device->>Device: 检测到特定事件 (如 SN 入站)
    Device->>Platform: 上报事件 (events/up)
    Note right of Platform: 平台处理事件...
    Platform-->>Device: 响应事件接收结果 (events/up/response)
```

#### 3.5.4. Topic

- **上行 (Uplink - 请求)**: `$oc/devices/{deviceId}/sys/events/up/request_id={requestId}`
- **下行 (Downlink - 响应)**: `$oc/devices/{deviceId}/sys/events/up/response/request_id={requestId}`

> **注意**: 设备每次上报事件时，都必须生成一个唯一的 `requestId`。平台会使用此 `requestId` 进行响应。

#### 3.5.5. 事件类型列表

| 事件名称       | 事件 Key                        | 携带参数 (`properties`)                                                                                        | 需要携带<br />实时数据? | 说明                                                                                 |
| :------------- | :------------------------------ | :--------------------------------------------------------------------------------------------------------------- | :---------------------- | :----------------------------------------------------------------------------------- |
| 入站           | `EVENT_SN_IN`                 | `sn`, `production_model`, `profiles`                                                                       | 是                      | 产品进入本工位。                                                                     |
| 过站(出站)     | `EVENT_SN_OUT`                | `sn`, `production_model`, `profiles`                                                                       | 是                      | 产品完成本工位作业并离开。                                                           |
| 过站(出站)询问 | `EVENT_SN_OUT_REQ`            | `sn`, `production_model`, `profiles`                                                                       | 是                      | 在出站前向平台询问是否可以出站。                                                     |
| 转产完成       | `EVENT_BOP_DELIVER_COMPLETED` | (无)                                                                                                             | 是                      | 设备已成功应用平台下发的转产参数。`request_id` 为平台下发指令时的 `request_id`。 |
| 暂停           | `EVENT_PAUSE`                 | `sn`, `pause_msg`, `production_model`, `profiles`                                                        | 是                      | 设备进入暂停状态。                                                                   |
| 故障           | `EVENT_FAULT`                 | `sn`, `production_model`, `profiles`, `fault_code`, `fault_type` (`warn` / `error`), `fault_msg` | 是                      | 设备发生故障。                                                                       |

#### 3.5.6. 上行请求 (Device -> Platform)

##### 请求 Payload 结构

Payload 包含一个 `EventService`，用于描述事件本身；同时，为了提供事件发生时的完整现场快照，通常会一并上报 `DefaultService`（设备通用属性）。

##### 请求示例 1: SN 入站事件

```json
{
  "services": [
    {
      "service_id": "EventService",
      "event_type": "EVENT_SN_IN",
      "properties": {
        "sn": "00232430452003F200003171",
        "production_model": "C380_B75",
        "profiles": [
          { "name": "5380495513.xml" },
          { "name": "5382000005.xml" }
        ]
      },
      "event_time": "2024-09-04 16:10:00.123"
    },
    {
      "service_id": "DefaultService",
      "properties": {
        "C00001": "00232430452003F200003171",
        "C00002": "A049991234"
      },
      "event_time": "2024-09-04 16:10:00.123"
    }
  ]
}
```

##### 请求示例 2: 故障事件

```json
{
  "services": [
    {
      "service_id": "EventService",
      "event_type": "EVENT_FAULT",
      "properties": {
        "sn": "00232430452003F200003171",
        "production_model": "C380_B75",
        "fault_code": "E03",
        "fault_type": "error",
        "fault_msg": "RAM错误"
      },
      "event_time": "2024-09-04 16:12:00.456"
    },
    {
      "service_id": "DefaultService",
      "properties": {
        "C00001": "00232430452003F200003171",
        "C00002": "A049991234"
      },
      "event_time": "2024-09-04 16:12:00.456"
    }
  ]
}
```

##### 请求 Payload 参数说明

| 字段名         | 父字段         | 类型       | 是否必填 | 描述                                                                                                                                                                                                                                                                  |
| :------------- | :------------- | :--------- | :------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `services`   | (root)         | `Array`  | 是       | 服务列表。                                                                                                                                                                                                                                                            |
| `service_id` | `services[]` | `String` | 是       | 服务 ID。对于事件上报，必须包含 `EventService`。                                                                                                                                                                                                                    |
| `event_type` | `services[]` | `String` | 是       | **仅在 `service_id` 为 `EventService` 时出现**。表示事件类型。示例：- `EVENT_SN_IN`: SN 入站 - `EVENT_SN_OUT`: SN 出站 - `EVENT_SN_OUT_REQ`: 出站询问 - `EVENT_PAUSE`: 暂停 - `EVENT_FAULT`: 发生故障 - `EVENT_BOP_DELIVER_COMPLETED`: 转产完成 |
| `properties` | `services[]` | `Object` | 是       | 与该服务/事件相关的属性。对于 `EventService`，它包含了触发事件的上下文信息。                                                                                                                                                                                        |
| `event_time` | `services[]` | `String` | 是       | 事件发生的精确时间。                                                                                                                                                                                                                                                  |

#### 3.5.7. 下行响应 (Platform -> Device)

**响应 Payload 结构**:
平台收到事件后，回复一个简单的确认消息。

```json
{
  "result_code": 0,
  "result_desc": "success"
}
```

**响应 Payload 参数说明**:

| 字段名          | 类型        | 是否必填 | 描述                                                              |
| :-------------- | :---------- | :------- | :---------------------------------------------------------------- |
| `result_code` | `Integer` | 是       | 平台接收结果码。`0` 表示成功收到，非 `0` 表示平台侧处理失败。 |
| `result_desc` | `String`  | 是       | 对接收结果的文字描述。                                            |

---

## 4. 附录

### 4.1. 数据类型列表

| 数据类型       | 说明       | 示例                          |
| :------------- | :--------- | :---------------------------- |
| `int`        | 整型       | `100`                       |
| `long`       | 长整型     | `1678886400000`             |
| `decimal`    | 浮点型     | `123.45`                    |
| `string`     | 字符串     | `"hello"`                   |
| `boolean`    | 布尔值     | `true`                      |
| `dateTime`   | 日期时间   | `"2024-09-04 16:02:21.165"` |
| `enum`       | 枚举       | `"OPEN"`, `"CLOSE"`       |
| `jsonObject` | JSON 对象  | `{"result": "success"}`     |
| `stringList` | 字符串数组 | `["str1", "str2", "str3"]`  |

### 4.2. 设备状态定义

`StatusService` 用于上报设备当前的工作状态。设备需按情况上传当前状态。

| 状态     | 状态 Key        | 携带数据 (可选)                                | 说明                                 |
| :------- | :-------------- | :--------------------------------------------- | :----------------------------------- |
| 未开机   | `shutdown`    | `device_status_desc`                         | 设备未上电，或检测不到设备。         |
| 待机状态 | `ready`       | `device_status_desc`                         | 设备已上电，但尚未开始生产作业。     |
| 正常生产 | `production`  | `device_status_desc`                         | 设备正在按照预设的程序进行生产作业。 |
| 运行暂停 | `pause`       | `device_status_desc`                         | 设备在生产间隙或计划停机期间暂停。   |
| 故障状态 | `fault_warn`  | `device_status_desc`                         | 提示故障，非停机故障，不会造成停机。 |
| 故障状态 | `fault_error` | `device_status_code`, `device_status_desc` | 停机故障，会造成停机。               |

### 4.3. 辅助工具与资源

- **MQTT 客户端示例**:
  - **.NET**: [MQTTnet Client Samples](https://github.com/dotnet/MQTTnet/tree/master/Samples/Client)
  - **Multi-language**: [EMQX MQTT Client Examples](https://github.com/emqx/MQTT-Client-Examples)
- **在线 HMAC 计算工具**: [Online HMAC Generator](https://www.lddgo.net/en/crypto/hmac)
