﻿#pragma once

// 基础类型声明
#include <string>
#include <map>
#include "json/json.h"

// Windows网络API - 确保正确的包含顺序
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif

#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <process.h>

#pragma comment(lib, "ws2_32.lib")

// SOCKET类型已在winsock2.h中定义，无需重新定义

// 简化的Socket接口 - 专为FStation与MQTT网关通信设计
class SimpleSocketInterface {
public:
    // 单例模式
    static SimpleSocketInterface* GetInstance();
    static void DestroyInstance();
    
    // 基础连接管理
    bool Connect(const std::string& serverAddress = "127.0.0.1", int serverPort = 8888);
    void Disconnect();
    bool IsConnected() const;
    
    // 核心功能：6种固定事件上报（带答复等待）
    static bool SendSnInEvent(const CString& sn, const CString& productionModel, const CString& profilesJson = _T("[]"));
    static bool SendSnOutEvent(const CString& sn, const CString& productionModel, const CString& profilesJson = _T("[]"));
    static bool SendSnOutReqEvent(const CString& sn, const CString& productionModel, const CString& profilesJson = _T("[]"));
    static bool SendBopDeliverCompletedEvent(const CString& requestId = _T(""));
    static bool SendPauseEvent(const CString& sn, const CString& pauseMsg, const CString& productionModel = _T(""), const CString& profilesJson = _T("[]"));
    static bool SendFaultEvent(const CString& sn, const CString& faultCode, const CString& faultMsg, const CString& faultType = _T("error"), const CString& productionModel = _T(""), const CString& profilesJson = _T("[]"));
    
    // 核心功能：设备状态上报
    static bool SendDeviceStatus();

    // 核心功能：发送命令响应
    static bool SendCommandResponse(const std::string& requestId, const std::string& responseJson);

    // 数据收集（基于配置的44个数据点）
    std::string CollectAllDataPoints();
    
private:
    // 单例实例
    static SimpleSocketInterface* m_instance;
    static CRITICAL_SECTION m_instanceMutex;
    static bool m_instanceMutexInitialized;
    
    // 连接状态 - 使用int避免SOCKET类型冲突
    int m_socket;
    volatile bool m_connected;
    std::string m_serverAddress;
    int m_serverPort;

    // 事件答复等待（简化版，不使用chrono）
    struct PendingResponse {
        DWORD timestamp;
        bool received;
        bool success;
        std::string message;
    };
    std::map<std::string, PendingResponse> m_pendingResponses;
    CRITICAL_SECTION m_responseMutex;

    // 接收线程
    HANDLE m_receiveThread;
    volatile bool m_receiveRunning;
    
    // 构造函数私有
    SimpleSocketInterface();
    ~SimpleSocketInterface();
    
    // 禁用拷贝 (VS2010兼容写法)
    SimpleSocketInterface(const SimpleSocketInterface&);
    SimpleSocketInterface& operator=(const SimpleSocketInterface&);
    
    // 内部方法
    bool SendMessage(const std::string& messageType, const std::string& jsonData, const std::string& requestId = "");
    bool SendEventWithResponse(const std::string& eventType, const std::string& properties, int timeoutMs = 10000);
    bool WaitForResponse(const std::string& requestId, int timeoutMs);

    // 接收处理
    static unsigned __stdcall ReceiveThreadFunc(void* param);
    void ProcessReceivedMessage(const std::string& message);

    // 工具方法
    std::string GenerateRequestId();
    std::string GetCurrentTimestamp();
    std::string SafeCStringToUTF8(const CString& str);
    std::string BuildEventMessage(const std::string& eventType, const std::string& properties);

    // 硬件监控方法
    std::string GetHardwareMonitorData();
    void AddHardwareMonitorData(Json::Value& root);
    void AddMeasurementData(Json::Value& root);
    DWORD GetMemoryCapacityMB();
    DWORD GetDiskCapacityGB(const CString& drive);
    DWORD GetDiskFreeSpaceGB(const CString& drive);
    float GetCpuUsage();
    float GetMemoryUsage();

    // 日志方法
    void LogInfo(const CString& message);
    void LogError(const CString& message);
};
