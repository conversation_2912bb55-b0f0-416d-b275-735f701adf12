﻿// DialogCheck.cpp : 实现文件
//

#include "stdafx.h"
#include "FStation.h"
#include "DialogCheck.h"
#include "afxdialogex.h"


// CDialogCheck 对话框

IMPLEMENT_DYNAMIC(CDialogCheck, CDialogEx)

CDialogCheck::CDialogCheck(CWnd* pParent /*=NULL*/)
	: CDialogEx(CDialogCheck::IDD, pParent)
{
}

CDialogCheck::~CDialogCheck()
{
}

void CDialogCheck::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_LIST1, m_list1);
	DDX_Control(pDX, IDC_LIST2, m_list2);
	DDX_Control(pDX, IDC_LIST3, m_list3);
}

BEGIN_MESSAGE_MAP(CDialogCheck, CDialogEx)
	ON_WM_PAINT()
	ON_WM_DRAWITEM()
	ON_BN_CLICKED(IDC_BUTTON1, &CDialogCheck::OnBnClickedButton1)
	ON_BN_CLICKED(IDC_BUTTON2, &CDialogCheck::OnBnClickedButton2)
	ON_BN_CLICKED(IDC_BUTTON3, &CDialogCheck::OnBnClickedButton3)
	ON_BN_CLICKED(IDC_BUTTON4, &CDialogCheck::OnBnClickedButton4)
	ON_BN_CLICKED(IDC_BUTTON5, &CDialogCheck::OnBnClickedButton5)
	ON_BN_CLICKED(IDC_BUTTON6, &CDialogCheck::OnBnClickedButton6)
	ON_BN_CLICKED(IDC_BUTTON7, &CDialogCheck::OnBnClickedButton7)
	ON_BN_CLICKED(IDC_BUTTON8, &CDialogCheck::OnBnClickedButton8)
	ON_BN_CLICKED(IDC_BUTTON9, &CDialogCheck::OnBnClickedButton9)
	ON_BN_CLICKED(IDC_BUTTON10, &CDialogCheck::OnBnClickedButton10)
	ON_BN_CLICKED(IDC_BUTTON11, &CDialogCheck::OnBnClickedButton11)
	ON_BN_CLICKED(IDC_BUTTON12, &CDialogCheck::OnBnClickedButton12)
	ON_BN_CLICKED(IDC_BUTTON13, &CDialogCheck::OnBnClickedButton13)
	ON_BN_CLICKED(IDC_BUTTON14, &CDialogCheck::OnBnClickedButton14)
	ON_BN_CLICKED(IDC_BUTTON15, &CDialogCheck::OnBnClickedButton15)
	ON_BN_CLICKED(IDC_BUTTON16, &CDialogCheck::OnBnClickedButton16)
	ON_BN_CLICKED(IDC_BUTTON17, &CDialogCheck::OnBnClickedButton17)
	ON_BN_CLICKED(IDC_BUTTON18, &CDialogCheck::OnBnClickedButton18)
	ON_BN_CLICKED(IDC_BUTTON19, &CDialogCheck::OnBnClickedButton19)
	ON_BN_CLICKED(IDC_BUTTON20, &CDialogCheck::OnBnClickedButton20)
	ON_BN_CLICKED(IDC_BUTTON21, &CDialogCheck::OnBnClickedButton21)
	ON_BN_CLICKED(IDC_BUTTON22, &CDialogCheck::OnBnClickedButton22)
	ON_BN_CLICKED(IDC_BUTTON23, &CDialogCheck::OnBnClickedButton23)
	ON_BN_CLICKED(IDC_BUTTON24, &CDialogCheck::OnBnClickedButton24)
	ON_BN_CLICKED(IDC_BUTTON25, &CDialogCheck::OnBnClickedButton25)
	ON_BN_CLICKED(IDC_BUTTON26, &CDialogCheck::OnBnClickedButton26)
	ON_BN_CLICKED(IDC_BUTTON27, &CDialogCheck::OnBnClickedButton27)
	ON_BN_CLICKED(IDC_BUTTON28, &CDialogCheck::OnBnClickedButton28)
	ON_BN_CLICKED(IDC_BUTTON29, &CDialogCheck::OnBnClickedButton29)
	ON_BN_CLICKED(IDC_BUTTON30, &CDialogCheck::OnBnClickedButton30)
	ON_BN_CLICKED(IDC_BUTTON31, &CDialogCheck::OnBnClickedButton31)
	ON_BN_CLICKED(IDC_BUTTON32, &CDialogCheck::OnBnClickedButton32)
	ON_BN_CLICKED(IDC_BUTTON33, &CDialogCheck::OnBnClickedButton33)
	ON_NOTIFY(NM_CLICK, IDC_LIST1, &CDialogCheck::OnNMClickList1)
	ON_NOTIFY(NM_CLICK, IDC_LIST2, &CDialogCheck::OnNMClickList2)
END_MESSAGE_MAP()

BOOL CDialogCheck::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	m_imgList.Create(20, 20, TRUE, 2, 2);

	GetDlgItem(IDC_STATIC1)->GetClientRect(&m_rDraw1);

	CColorList* myList = &m_list1;

	LONG lStyle = GetWindowLong(myList->m_hWnd, GWL_STYLE);//获取当前窗口样式
	lStyle &= ~LVS_TYPEMASK;//清除显位方式位
	lStyle |= LVS_REPORT;
	SetWindowLong(myList->m_hWnd, GWL_STYLE, lStyle);

	DWORD dwStyle = myList->GetExtendedStyle();//获取当前窗口扩展样式
	dwStyle |= LVS_EX_FULLROWSELECT;//选中某行使整行高亮
	dwStyle |= LVS_EX_GRIDLINES;//网格线
	myList->SetExtendedStyle(dwStyle);

	myList->SetImageList(&m_imgList, LVSIL_SMALL);

	myList->SetBkColor(RGB(200, 200, 200));

	CString sHourName[] = { "", "序号", "时间段" };
	int		nHourWidth[] = { 1, 45, 300 };

	for (int i=0; i<sizeof(nHourWidth)/sizeof(int); i++)
	{
		myList->InsertColumn(i, sHourName[i], LVCFMT_LEFT, nHourWidth[i]);
	}

	CString sTemp;

	for (int i=0; i<2; i++)
	{
		myList->InsertItem(i, "");
		sTemp.Format("%d", i + 1);
		myList->SetItemText(i, 1, sTemp);
		myList->SetItemText(i, 2, i == 0 ? "白班（8:00 - 20:00）" : "夜班（20:00 - 8:00）");
	}

	myList = &m_list2;

	lStyle = GetWindowLong(myList->m_hWnd, GWL_STYLE);//获取当前窗口样式
	lStyle &= ~LVS_TYPEMASK;//清除显位方式位
	lStyle |= LVS_REPORT;
	SetWindowLong(myList->m_hWnd, GWL_STYLE, lStyle);

	dwStyle = myList->GetExtendedStyle();//获取当前窗口扩展样式
	dwStyle |= LVS_EX_FULLROWSELECT;//选中某行使整行高亮
	dwStyle |= LVS_EX_GRIDLINES;//网格线
	myList->SetExtendedStyle(dwStyle);

	myList->SetImageList(&m_imgList, LVSIL_SMALL);

	myList->SetBkColor(RGB(200, 200, 200));

	CString sPointName[] = { "", "序号", "点位", "OK", "NG" };
	int		nPointWidth[] = { 1, 45, 45, 45, 45 };

	for (int i=0; i<sizeof(nPointWidth)/sizeof(int); i++)
	{
		myList->InsertColumn(i, sPointName[i], LVCFMT_CENTER, nPointWidth[i]);
	}

	myList = &m_list3;

	lStyle = GetWindowLong(myList->m_hWnd, GWL_STYLE);//获取当前窗口样式
	lStyle &= ~LVS_TYPEMASK;//清除显位方式位
	lStyle |= LVS_REPORT;
	SetWindowLong(myList->m_hWnd, GWL_STYLE, lStyle);

	dwStyle = myList->GetExtendedStyle();//获取当前窗口扩展样式
	dwStyle |= LVS_EX_FULLROWSELECT;//选中某行使整行高亮
	dwStyle |= LVS_EX_GRIDLINES;//网格线
	myList->SetExtendedStyle(dwStyle);

	myList->SetImageList(&m_imgList, LVSIL_SMALL);

	myList->SetBkColor(RGB(200, 200, 200));

	CString sDetailName[] = { "", "序号", "点位", "时间", "面积1", "分数1", "X偏移量1", "Y偏移量1", "面积2", "分数2", "X偏移量2", "Y偏移量2", "通过" };
	int		nDetailWidth[] = { 1, 45, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60 };

	for (int i=0; i<sizeof(nDetailWidth)/sizeof(int); i++)
	{
		myList->InsertColumn(i, sDetailName[i], LVCFMT_CENTER, nDetailWidth[i]);
	}

	SYSTEMTIME st;
	GetLocalTime(&st);

	m_nToday = st.wDay;

	for (unsigned int i=28; i<31; i++)
	{
		if (i > GetDaysInMonth(st.wYear, st.wMonth) - 1) {
			GetDlgItem(IDC_BUTTON1 + i)->ShowWindow(SW_HIDE);
		}
		else {
			GetDlgItem(IDC_BUTTON1 + i)->ShowWindow(SW_NORMAL);
		}
	}

	CString str;
	str.Format("%04d年%02d月", st.wYear, st.wMonth);

	SetDlgItemText(IDC_EDIT1, str);

	CTime t = CTime::GetCurrentTime();
	Update(t.GetYear(), t.GetMonth(), t.GetDay());

	return TRUE;
}

void CDialogCheck::OnPaint()
{
	CPaintDC dc(this);
}

void CDialogCheck::OnDrawItem(int nIDCtl, LPDRAWITEMSTRUCT lpDrawItemStruct)
{
	CDC dc;
	dc.Attach(lpDrawItemStruct ->hDC);   // Get the Button DC to CDC

	RECT rect;
	rect = lpDrawItemStruct->rcItem;     // Store the Button rect to our local rect.

	dc.Draw3dRect(&rect, RGB(255, 255, 255), RGB(0, 0, 0)); 

	if (nIDCtl == IDC_BUTTON1 + m_nToday - 1)   {
		dc.FillSolidRect(&rect, RGB(128, 128, 128));
		dc.SetBkColor(RGB(128, 128, 128));   // Setting the Text Background color
	}
	else if (nIDCtl == IDC_BUTTON32 || nIDCtl == IDC_BUTTON33) {
		dc.FillSolidRect(&rect, RGB(230, 230, 230));
		dc.SetBkColor(RGB(230, 230, 230));   // Setting the Text Background color
	}
	else {
		dc.FillSolidRect(&rect, RGB(240, 240, 240));// Here you can define the required color to appear on the Button.
		dc.SetBkColor(RGB(240, 240, 240));   // Setting the Text Background color
	}
	
	dc.SetTextColor(RGB(0, 0, 0));     // Setting the Text Color

	TCHAR buffer[MAX_PATH] = { 0 };      // To store the Caption of the button.

	::GetWindowText(lpDrawItemStruct->hwndItem,buffer, MAX_PATH); // Get the Caption of Button Window 

	dc.DrawText(buffer, &rect, DT_CENTER | DT_VCENTER | DT_SINGLELINE); // Redraw the Caption of Button Window 

	dc.Detach(); // Detach the Button DC

	CDialogEx::OnDrawItem(nIDCtl, lpDrawItemStruct);
}

// CDialogCheck 消息处理程序

void CDialogCheck::Update(unsigned int nYear, unsigned int nMonth, unsigned int nDay)
{
	m_mCheckResult.clear();

	m_mCheckResult = CDat::ReadCheckData(nYear, nMonth, nDay);

	m_list2.DeleteAllItems();
	m_list3.DeleteAllItems();

	map<CString, int> mCntOk, mCntNg;

	int nIndex = 0;

	map<int, MCHECKRESULT>::iterator itHour = m_mCheckResult.begin();
	for (; itHour != m_mCheckResult.end(); itHour++)
	{
		map<CString, vector<CHECKRESULT>>::iterator itResult = itHour->second.begin();

		mCntOk[itResult->first];
		mCntNg[itResult->first];

		for (; itResult != itHour->second.end(); itResult++)
		{
			vector<CHECKRESULT>::iterator it = itResult->second.begin();
			for (; it != itResult->second.end(); it++)
			{
				nIndex = m_list3.GetItemCount();
				m_list3.InsertItem(nIndex, "");
				m_list3.SetItemText(nIndex, 1, nIndex + 1);
				m_list3.SetItemText(nIndex, 2, itResult->first);
				m_list3.SetItemText(nIndex, 3, it->sTime);
				m_list3.SetItemText(nIndex, 4, it->nArea[0]);
				m_list3.SetItemText(nIndex, 5, it->nAreaScore[0]);
				m_list3.SetItemText(nIndex, 6, it->nOffX[0]);
				m_list3.SetItemText(nIndex, 7, it->nOffY[0]);
				m_list3.SetItemText(nIndex, 8, it->nArea[1]);
				m_list3.SetItemText(nIndex, 9, it->nAreaScore[1]);
				m_list3.SetItemText(nIndex, 10, it->nOffX[1]);
				m_list3.SetItemText(nIndex, 11, it->nOffY[1]);
				if (it->sResult == "OK") {
					mCntOk[itResult->first]++;
					m_list3.SetItemText(nIndex, 12, "是");
				}
				else {
					mCntNg[itResult->first]++;
					m_list3.SetItemText(nIndex, 12, "否");
				}
			}
		}
	}

	map<CString, int>::iterator it = mCntOk.begin();
	for (; it != mCntOk.end(); it++)
	{
		nIndex = m_list2.GetItemCount();
		m_list2.InsertItem(nIndex, "");
		m_list2.SetItemText(nIndex, 1, nIndex + 1);
		m_list2.SetItemText(nIndex, 2, it->first);
		m_list2.SetItemText(nIndex, 3, it->second);
		m_list2.SetItemText(nIndex, 4, mCntNg[it->first]);
	}

	InvalidateRect(m_rDraw1);
	InvalidateRect(m_rDraw2);
}

void CDialogCheck::Update(unsigned int nHour)
{
	m_list2.DeleteAllItems();
	m_list3.DeleteAllItems();

	map<CString, int> mCntOk, mCntNg;

	int nIndex = 0;

	map<CString, vector<CHECKRESULT>>::iterator itResult = m_mCheckResult[nHour].begin();
	for (; itResult != m_mCheckResult[nHour].end(); itResult++)
	{
		mCntOk[itResult->first];
		mCntNg[itResult->first];

		vector<CHECKRESULT>::iterator it = itResult->second.begin();
		for (; it != itResult->second.end(); it++)
		{
			nIndex = m_list3.GetItemCount();
			m_list3.InsertItem(nIndex, "");
			m_list3.SetItemText(nIndex, 1, nIndex + 1);
			m_list3.SetItemText(nIndex, 2, itResult->first);
			m_list3.SetItemText(nIndex, 3, it->sTime);
			m_list3.SetItemText(nIndex, 4, it->nArea[0]);
			m_list3.SetItemText(nIndex, 5, it->nAreaScore[0]);
			m_list3.SetItemText(nIndex, 6, it->nOffX[0]);
			m_list3.SetItemText(nIndex, 7, it->nOffY[0]);
			m_list3.SetItemText(nIndex, 8, it->nArea[1]);
			m_list3.SetItemText(nIndex, 9, it->nAreaScore[1]);
			m_list3.SetItemText(nIndex, 10, it->nOffX[1]);
			m_list3.SetItemText(nIndex, 11, it->nOffY[1]);
			if (it->sResult == "OK") {
				mCntOk[itResult->first]++;
				m_list3.SetItemText(nIndex, 12, "是");
			}
			else {
				mCntNg[itResult->first]++;
				m_list3.SetItemText(nIndex, 12, "否");
			}
		}
	}

	map<CString, int>::iterator it = mCntOk.begin();
	for (; it != mCntOk.end(); it++)
	{
		nIndex = m_list2.GetItemCount();
		m_list2.InsertItem(nIndex, "");
		m_list2.SetItemText(nIndex, 1, nIndex + 1);
		m_list2.SetItemText(nIndex, 2, it->first);
		m_list2.SetItemText(nIndex, 3, it->second);
		m_list2.SetItemText(nIndex, 4, mCntNg[it->first]);
	}
}

void CDialogCheck::Update(CString sPoint)
{
	m_list3.DeleteAllItems();

	map<CString, int> mCntOk, mCntNg;

	mCntOk[sPoint] = 0;
	mCntNg[sPoint] = 0;

	int nIndex = 0;

	map<int, MCHECKRESULT>::iterator itHour = m_mCheckResult.begin();
	for (; itHour != m_mCheckResult.end(); itHour++)
	{		
		vector<CHECKRESULT>::iterator it = itHour->second[sPoint].begin();

		for (; it != itHour->second[sPoint].end(); it++)
		{
			nIndex = m_list3.GetItemCount();
			m_list3.InsertItem(nIndex, "");
			m_list3.SetItemText(nIndex, 1, nIndex + 1);
			m_list3.SetItemText(nIndex, 2, sPoint);
			m_list3.SetItemText(nIndex, 3, it->sTime);
			m_list3.SetItemText(nIndex, 4, it->nArea[0]);
			m_list3.SetItemText(nIndex, 5, it->nAreaScore[0]);
			m_list3.SetItemText(nIndex, 6, it->nOffX[0]);
			m_list3.SetItemText(nIndex, 7, it->nOffY[0]);
			m_list3.SetItemText(nIndex, 8, it->nArea[1]);
			m_list3.SetItemText(nIndex, 9, it->nAreaScore[1]);
			m_list3.SetItemText(nIndex, 10, it->nOffX[1]);
			m_list3.SetItemText(nIndex, 11, it->nOffY[1]);
			if (it->sResult == "OK") {
				mCntOk[sPoint]++;
				m_list3.SetItemText(nIndex, 12, "是");
			}
			else {
				mCntNg[sPoint]++;
				m_list3.SetItemText(nIndex, 12, "否");
			}
		}
	}

	for (int i=0; i<m_list2.GetItemCount(); i++)
	{
		if (m_list2.GetItemText(i, 2) == sPoint) {
			m_list2.SetItemText(i, 3, mCntOk[sPoint]);
			m_list2.SetItemText(i, 4, mCntNg[sPoint]);
		}
	}
}

unsigned int CDialogCheck::GetDaysInMonth(unsigned int nYear, unsigned int nMonth)
{
	unsigned int nDays = 0;
	do 
	{
		if (nMonth == 2) {
			if (((nYear % 4 == 0) && (nYear % 100 != 0)) || nYear % 400 == 0) {
				nDays = 29;
			}
			else {
				nDays = 28;
			}
			break;
		}

		if (nMonth == 1 || nMonth == 3 || nMonth == 5 || nMonth == 7 || nMonth == 8 || nMonth == 10 || nMonth == 12) {
			nDays = 31;
		}
		else {
			nDays = 30;
		}
	} while (false);

	return nDays;
}

void CDialogCheck::OnNMClickList1(NMHDR *pNMHDR, LRESULT *pResult)
{
	LPNMITEMACTIVATE pNMItemActivate = reinterpret_cast<LPNMITEMACTIVATE>(pNMHDR);
	
	if (pNMItemActivate->iItem >= 0) {
		Update(pNMItemActivate->iItem);
	}

	*pResult = 0;
}

void CDialogCheck::OnNMClickList2(NMHDR *pNMHDR, LRESULT *pResult)
{
	LPNMITEMACTIVATE pNMItemActivate = reinterpret_cast<LPNMITEMACTIVATE>(pNMHDR);
	
	if (pNMItemActivate->iItem >= 0 && pNMItemActivate->iItem < m_list2.GetItemCount()) {
		Update(m_list2.GetItemText(pNMItemActivate->iItem, 2));
	}

	*pResult = 0;
}

void CDialogCheck::OnBnClickedButton1()
{
	m_nToday = 1;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton2()
{
	m_nToday = 2;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton3()
{
	m_nToday = 3;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton4()
{
	m_nToday = 4;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton5()
{
	m_nToday = 5;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton6()
{
	m_nToday = 6;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton7()
{
	m_nToday = 7;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton8()
{
	m_nToday = 8;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton9()
{
	m_nToday = 9;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton10()
{
	m_nToday = 10;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton11()
{
	m_nToday = 11;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton12()
{
	m_nToday = 12;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton13()
{
	m_nToday = 13;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton14()
{
	m_nToday = 14;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton15()
{
	m_nToday = 15;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton16()
{
	m_nToday = 16;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton17()
{
	m_nToday = 17;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton18()
{
	m_nToday = 18;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton19()
{
	m_nToday = 19;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton20()
{
	m_nToday = 20;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton21()
{
	m_nToday = 21;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton22()
{
	m_nToday = 22;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton23()
{
	m_nToday = 23;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton24()
{
	m_nToday = 24;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton25()
{
	m_nToday = 25;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton26()
{
	m_nToday = 36;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton27()
{
	m_nToday = 27;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton28()
{
	m_nToday = 28;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton29()
{
	m_nToday = 29;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton30()
{
	m_nToday = 30;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton31()
{
	m_nToday = 31;

	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton32()
{
	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	nMonth--;
	if (nMonth < 1) {
		nMonth = 12;
		nYear--;
	}

	for (unsigned int i=28; i<31; i++)
	{
		if (i > GetDaysInMonth(nYear, nMonth) - 1) {
			GetDlgItem(IDC_BUTTON1 + i)->ShowWindow(SW_HIDE);
		}
		else {
			GetDlgItem(IDC_BUTTON1 + i)->ShowWindow(SW_NORMAL);
		}
	}

	str.Format("%04d年%02d月", nYear, nMonth);

	SetDlgItemText(IDC_EDIT1, str);

	if (m_nToday > GetDaysInMonth(nYear, nMonth)) {
		m_nToday = 1;
	}

	Update(nYear, nMonth, m_nToday);
}

void CDialogCheck::OnBnClickedButton33()
{
	CString str;
	GetDlgItemText(IDC_EDIT1, str);

	if (str.GetLength() <= 9) {
		return;
	}

	int nYear = atoi(str.Left(4));
	int nMonth = atoi(str.Mid(6, 2));

	nMonth++;
	if (nMonth > 12) {
		nMonth = 1;
		nYear++;
	}

	for (unsigned int i=28; i<31; i++)
	{
		if (i > GetDaysInMonth(nYear, nMonth) - 1) {
			GetDlgItem(IDC_BUTTON1 + i)->ShowWindow(SW_HIDE);
		}
		else {
			GetDlgItem(IDC_BUTTON1 + i)->ShowWindow(SW_NORMAL);
		}
	}

	str.Format("%04d年%02d月", nYear, nMonth);

	SetDlgItemText(IDC_EDIT1, str);

	if (m_nToday > GetDaysInMonth(nYear, nMonth)) {
		m_nToday = 1;
	}

	Update(nYear, nMonth, m_nToday);
}
