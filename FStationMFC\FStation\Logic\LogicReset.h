﻿#pragma once

#include "ThreadBase.h"
using namespace yzBase;

class CLogicReset : public CThreadBase
{
public:
	CLogicReset();
	virtual ~CLogicReset();

public:
	// 基础控制函数
	EnumStatus OnSafeCheck();       // 安全检查：检查复位过程中的安全条件
	EnumStatus OnStart();           // 启动控制：开始复位流程
	EnumStatus OnPause();           // 暂停控制：暂停复位操作
	EnumStatus OnResume();          // 恢复控制：从暂停状态恢复复位
	EnumStatus OnStop();            // 停止控制：停止复位流程

public:
	// 复位流程函数
	CStatus OnRun();                // 主运行函数：启动复位流程

	// ========== 复位流程 (OnReset00-OnReset07) ==========
	CStatus OnReset00();            // 复位准备：关闭机械手气缸，准备复位
	CStatus OnReset01();            // 机械手复位：机械手移动到空闲等待位
	CStatus OnReset01_0();          // 机械手位置复位：机械手移动到空闲等待位置
	CStatus OnReset01_1();          // 机械手检查：检查机械手是否有吸料
	CStatus OnReset01_2();          // 机械手清理：清理机械手上的物料
	CStatus OnReset01_3();          // 机械手验证：验证机械手复位状态
	CStatus OnReset01_4();          // 机械手完成：完成机械手复位
	CStatus OnReset01_5();          // 机械手后处理：机械手复位后处理
	CStatus OnReset01_6();          // 机械手复位结束：结束机械手复位流程
	CStatus OnReset02();            // 皮带复位：皮带系统复位
	CStatus OnReset03();            // 托盘复位：托盘系统复位
	CStatus OnReset04();            // 治具复位：治具系统复位
	CStatus OnReset05();            // 相机复位：相机系统复位
	CStatus OnReset06();            // 系统复位：其他系统复位
	CStatus OnReset07();            // 复位完成：完成所有复位操作，系统就绪

private:
	CString				m_sRet;             // 函数返回字符串结果

	map<CString, DWORD> m_mapTick;          // 时间计时器映射表，用于超时控制
	map<CString, bool>	m_mapFlag;          // 标志映射表，记录各种状态标志
};
