﻿#include "stdafx.h"
#include "LogicTray.h"

#include "Sys.h"
#include "LogicMgr.h"

#define PARAM(NAME)			(*m_pTray->m_mapParam[NAME])

#define PARAM_BOOL(NAME)	(*m_pTray->m_mapParam[NAME]).B()
#define PARAM_INT(NAME)		(*m_pTray->m_mapParam[NAME]).I()
#define PARAM_DOUBLE(NAME)	(*m_pTray->m_mapParam[NAME]).D()
#define PARAM_STRING(NAME)	(*m_pTray->m_mapParam[NAME]).S()

CLogicTray::CLogicTray()
{
	m_pTray = g_pTray;

	m_mapFlag["满TRAY盘"] = true;
	m_mapFlag["空TRAY盘"] = false;

	m_pFuncFullTray = CLogicMgr::m_ThreadFactory.CreateThreadFunc("满TRAY盘");

	m_pFuncEmptyTray = CLogicMgr::m_ThreadFactory.CreateThreadFunc("空TRAY盘");
}

CLogicTray::~CLogicTray()
{
	delete m_pFuncFullTray;
	delete m_pFuncEmptyTray;
}

EnumStatus CLogicTray::OnSafeCheck()
{
	if (!CSys::m_bInit) {
		return emStop;
	}

	return emRun;
}

EnumStatus CLogicTray::OnStart()
{
	return emRun;
}

EnumStatus CLogicTray::OnPause()
{
	m_pTray->FullTrayBeltOff();

	m_pTray->FullTrayUpDnZStop();

	m_pTray->EmptyTrayBeltOff();

	m_pTray->EmptyTrayTransportYStop();

	m_pTray->EmptyTraySeparateZStop();

	return emRun;
}

EnumStatus CLogicTray::OnResume()
{
	return emRun;
}

EnumStatus CLogicTray::OnStop()
{
	m_pTray->FullTrayBeltOff();

	m_pTray->FullTrayUpDnZStop();

	m_pTray->EmptyTrayBeltOff();

	m_pTray->EmptyTrayTransportYStop();

	m_pTray->EmptyTraySeparateZStop();

	return emRun;
}

CStatus CLogicTray::OnRun()
{
	PARAM("空TRAY盘进料标志") = false;	
	PARAM("空TRAY盘退料标志") = false;	
	PARAM("满TRAY盘进料标志") = false;	
	PARAM("满TRAY盘退料标志") = false;

	m_pFuncFullTray->SetAction(static_cast<THREAD_FUNC>(&CLogicTray::OnFullTray00));

	m_pFuncEmptyTray->SetAction(static_cast<THREAD_FUNC>(&CLogicTray::OnEmptyTray00));

	RETURN_STATE(&CLogicTray::OnTray00, false);
}

CStatus CLogicTray::OnSelfCheck00()
{
	RETURN_STATE(&CLogicTray::OnSelfCheck01, false);
}

CStatus CLogicTray::OnSelfCheck01()
{
	RETURN_STATE(&CLogicTray::OnSelfCheck02, false);
}

CStatus CLogicTray::OnSelfCheck02()
{
	RETURN_STATE(&CLogicTray::OnSelfCheck03, false);
}

CStatus CLogicTray::OnSelfCheck03()
{
	RETURN_STATE(&CLogicTray::OnTray00, false);
}

CStatus CLogicTray::OnTray00()
{
	if (g_pRobot->m_mapParam["TRAY盘进料标志"]->B()) {
		*g_pRobot->m_mapParam["TRAY盘进料标志"] = false;
		PARAM("空TRAY盘退料标志") = true;
	}

	if (g_pRobot->m_mapParam["TRAY盘退料标志"]->B()) {
		*g_pRobot->m_mapParam["TRAY盘退料标志"] = false;
		PARAM("满TRAY盘退料标志") = true;
	}

	RETURN_STATE(&CLogicTray::OnTray01, false);
}

CStatus CLogicTray::OnTray01()
{
	EnumStatus  status = m_pFuncFullTray->Run(this);

	switch (status)
	{
	case emRun:
		break;
	case emStop:
		RETURN_STOP();
	case emPause:
		RETURN_PAUSE(NULL);
	default:
		break;
	}

	RETURN_STATE(&CLogicTray::OnTray02, false);
}

CStatus CLogicTray::OnTray02()
{
	EnumStatus  status = m_pFuncEmptyTray->Run(this);

	switch (status)
	{
	case emRun:
		break;
	case emStop:
		RETURN_STOP();
	case emPause:
		RETURN_PAUSE(NULL);
	default:
		break;
	}

	RETURN_STATE(&CLogicTray::OnTray00, false);
}

CStatus CLogicTray::OnEmptyTray00()
{
	if (PARAM_BOOL("空TRAY盘进料标志")) {
		PARAM("空TRAY盘进料标志") = false;
		RETURN_STATE(&CLogicTray::OnEmptyTrayFeed00, m_mapFlag["空TRAY盘"]);
	}
	
	RETURN_STATE(&CLogicTray::OnEmptyTray01, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTray01()
{
	if (PARAM_BOOL("空TRAY盘退料标志")) {
		PARAM("空TRAY盘退料标志") = false;
		RETURN_STATE(&CLogicTray::OnEmptyTrayBack00, m_mapFlag["空TRAY盘"]);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTray00, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayFeed00()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySeparateZMove(PARAM_DOUBLE("空TRAY盘分盘Z轴支撑位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTraySeparateZInPos(PARAM_DOUBLE("空TRAY盘分盘Z轴支撑位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘分盘Z轴运动到支撑位", false);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTrayFeed01, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayFeed01()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySupportCylinderOff());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySupportCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待空TRAY盘支撑气缸缩回", false);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTrayFeed02, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayFeed02()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySeparateZMove(PARAM_DOUBLE("空TRAY盘分盘Z轴分盘位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTraySeparateZInPos(PARAM_DOUBLE("空TRAY盘分盘Z轴分盘位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘分盘Z轴运动到分盘位", false);
	}

	m_mapTick["真空检测计时"] = GetTickCount();

	RETURN_STATE(&CLogicTray::OnEmptyTrayFeed03, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayFeed03()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySupportCylinderOn());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayVacuumOn());

	Sleep(100);

//	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayVacuumStatus());
//
// 	if (m_sRet != "On") {
// 		if (GetTickCount() - m_mapTick["真空检测计时"] > PARAM_INT("空TRAY盘真空检测超时")) {
// 			if (AfxMessageBox("请确认是否有空TRAY盘?如果没有，请上空TRAY盘！", MB_YESNO) == IDYES) {
// 				m_mapTick["真空检测计时"] = GetTickCount();
// 			}
// 		}
// 
// 		RETURN_SELF("等待空TRAY盘真空信号", false);
// 	}

	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySupportCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待空TRAY盘支撑气缸伸出", false);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTrayFeed04, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayFeed04()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySeparateZMove(PARAM_DOUBLE("空TRAY盘分盘Z轴破真空位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTraySeparateZInPos(PARAM_DOUBLE("空TRAY盘分盘Z轴破真空位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘分盘Z轴运动到破真空位", false);
	}

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBrokenVacuumOn());

	Sleep(100);

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayVacuumOff());

	RETURN_STATE(&CLogicTray::OnEmptyTrayFeed05, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayFeed05()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySeparateZMove(PARAM_DOUBLE("空TRAY盘分盘Z轴放料位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTraySeparateZInPos(PARAM_DOUBLE("空TRAY盘分盘Z轴放料位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘分盘Z轴运动到放料位", false);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTrayFeed06, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayFeed06()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBeltOn(PARAM_BOOL("空TRAY盘皮带正转方向")));

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayTransportYMove(PARAM_DOUBLE("空TRAY盘搬运Y轴进料起始位")));

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBeltInMaterialStatus());

	if (m_sRet == "On") {
		if (GetTickCount() - m_mapTick["空TRAY盘进料计时"] < (DWORD)PARAM_INT("空TRAY盘进料到位延时")) {
			RETURN_SELF("等待空TRAY盘进料到位", false);
		}
		else {
			RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBeltOff());
		}
	}
	else {
		RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBeltOn(PARAM_BOOL("空TRAY盘皮带正转方向")));
		m_mapTick["空TRAY盘进料计时"] = GetTickCount();
		RETURN_SELF("等待空TRAY盘进料信号", false);
	}

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTrayTransportYInPos(PARAM_DOUBLE("空TRAY盘搬运Y轴进料起始位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘搬运Y轴运动到进料起始位", false);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTrayFeed07, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayFeed07()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayPullMaterialCylinderOn());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayPullMaterialCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待空TRAY盘拨料气缸伸出", false);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTrayFeed08, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayFeed08()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayTransportYMove(PARAM_DOUBLE("空TRAY盘搬运Y轴进料结束位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTrayTransportYInPos(PARAM_DOUBLE("空TRAY盘搬运Y轴进料结束位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘搬运Y轴运动到进料结束位", false);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTrayFeed09, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayFeed09()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayTransportYMove(PARAM_DOUBLE("空TRAY盘搬运Y轴进料起始位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTrayTransportYInPos(PARAM_DOUBLE("空TRAY盘搬运Y轴进料起始位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘搬运Y轴运动到进料起始位", false);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTrayFeed10, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayFeed10()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayPullMaterialCylinderOff());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayPullMaterialCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待空TRAY盘拨料气缸缩回", false);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTray00, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayBack00()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayTransportYMove(PARAM_DOUBLE("空TRAY盘搬运Y轴退料起始位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTrayTransportYInPos(PARAM_DOUBLE("空TRAY盘搬运Y轴退料起始位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘搬运Y轴运动到退料起始位", false);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTrayBack01, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayBack01()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayPullMaterialCylinderOn());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayPullMaterialCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待空TRAY盘拨料气缸伸出", false);
	}

	m_mapFlag["拨料成功标志"] = false;

	RETURN_STATE(&CLogicTray::OnEmptyTrayBack02, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayBack02()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayTransportYMove(PARAM_DOUBLE("空TRAY盘搬运Y轴退料结束位")));

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBeltInMaterialStatus());

	if (m_sRet == "On") {
		m_mapFlag["拨料成功标志"] = true;
	}

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTrayTransportYInPos(PARAM_DOUBLE("空TRAY盘搬运Y轴退料结束位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘搬运Y轴运动到退料结束位", false);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTrayBack03, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayBack03()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayPullMaterialCylinderOff());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayPullMaterialCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待空TRAY盘拨料气缸缩回", false);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTrayBack04, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayBack04()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayEndStopCylinderOn());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayEndStopCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待空TRAY盘尾端阻挡气缸伸出", false);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTrayBack05, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayBack05()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBeltOn(!PARAM_BOOL("空TRAY盘皮带正转方向")));

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBeltInMaterialStatus());

	if (m_sRet == "Off" && m_mapFlag["拨料成功标志"]) {
		m_mapFlag["拨料成功标志"] = false;
		PARAM("满TRAY盘进料标志") = true;
	}

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBeltInPosStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待空TRAY盘到位信号", false);
	}
	
	m_mapTick["空TRAY盘退料到位计时"] = GetTickCount();

	RETURN_STATE(&CLogicTray::OnEmptyTrayBack06, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayBack06()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBeltOn(!PARAM_BOOL("空TRAY盘皮带正转方向")));

	if (GetTickCount() - m_mapTick["空TRAY盘退料到位计时"] < (DWORD)PARAM_INT("皮带传输到位延时")) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBeltOff());

	RETURN_STATE(&CLogicTray::OnEmptyTrayBack07, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayBack07()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySeparateZMove(PARAM_DOUBLE("空TRAY盘分盘Z轴分盘位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTraySeparateZInPos(PARAM_DOUBLE("空TRAY盘分盘Z轴分盘位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘分盘Z轴运动到分盘位", false);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTrayBack08, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayBack08()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySupportCylinderOff());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySupportCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待空TRAY盘支撑气缸缩回", false);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTrayBack09, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayBack09()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySeparateZMove(PARAM_DOUBLE("空TRAY盘分盘Z轴支撑位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTraySeparateZInPos(PARAM_DOUBLE("空TRAY盘分盘Z轴支撑位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘分盘Z轴运动到支撑位", false);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTrayBack10, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayBack10()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySupportCylinderOn());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySupportCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待空TRAY盘支撑气缸伸出", false);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTrayBack11, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayBack11()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySeparateZMove(PARAM_DOUBLE("空TRAY盘分盘Z轴放料位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTraySeparateZInPos(PARAM_DOUBLE("空TRAY盘分盘Z轴放料位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘分盘Z轴运动到放料位", false);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTrayBack12, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayBack12()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayOutOfRangeWarnStatus());

	if (m_sRet == "On") {
		REPORT("空TRAY盘数量报警，请立即清空TRAY盘", emLogLevelWarn);
		AfxMessageBox("空TRAY盘数量报警，请立即清空TRAY盘");
		RETURN_STATE(&CLogicTray::OnEmptyTray00, m_mapFlag["空TRAY盘"]);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTrayBack13, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnEmptyTrayBack13()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayOutOfRangePreWarnStatus());

	if (m_sRet == "On") {
		REPORT("空TRAY盘数量预警，请及时清空TRAY盘", emLogLevelWarn);
	}

	RETURN_STATE(&CLogicTray::OnEmptyTray00, m_mapFlag["空TRAY盘"]);
}

CStatus CLogicTray::OnFullTray00()
{
	if (PARAM_BOOL("满TRAY盘进料标志")) {
		PARAM("满TRAY盘进料标志") = false;
		RETURN_STATE(&CLogicTray::OnFullTrayFeed00, m_mapFlag["满TRAY盘"]);
	}

	RETURN_STATE(&CLogicTray::OnFullTray01, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTray01()
{
	if (PARAM_BOOL("满TRAY盘退料标志")) {
		PARAM("满TRAY盘退料标志") = false;
		RETURN_STATE(&CLogicTray::OnFullTrayBack00, m_mapFlag["满TRAY盘"]);
	}

	RETURN_STATE(&CLogicTray::OnFullTray00, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayFeed00()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());

	if (m_sRet == "On") {
		RETURN_STATE(&CLogicTray::OnFullTrayFeed02, m_mapFlag["满TRAY盘"]);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayFeed01, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayFeed01()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltInPosStatus());

	if (m_sRet != "On") { // 无TRAY盘
		RETURN_STATE(&CLogicTray::OnFullTrayFeed09, m_mapFlag["满TRAY盘"]);
	}

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(PARAM_DOUBLE("满TRAY盘升降Z轴放料位")));

	if (m_sRet == "No") {
		RETURN_STATE(&CLogicTray::OnFullTrayFeed08, m_mapFlag["满TRAY盘"]);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayFeed02, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayFeed02()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTraySupportCylinderOff());

	RUN_STOP_IF_ERROR(m_pTray->FullTraySupportCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待满TRAY盘支撑气缸缩回", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayFeed03, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayFeed03()
{
	CString sRet1, sRet2;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(PARAM_DOUBLE("满TRAY盘升降Z轴最大送料位")));

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(PARAM_DOUBLE("满TRAY盘升降Z轴最大送料位")));
	
	sRet1 = m_sRet;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayMaterialExistStatus());

	sRet2 = m_sRet;

	if (sRet1 != "Yes" && sRet2 != "On") {
		RETURN_SELF("等待满TRAY盘升降Z轴送料到位", false);
	}

	if (sRet1 == "Yes" && sRet2 != "On") { //无料
		RETURN_STATE(&CLogicTray::OnFullTrayFeed08, m_mapFlag["满TRAY盘"]);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZStop());

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴当前位置"]));

	RETURN_STATE(&CLogicTray::OnFullTrayFeed03_0, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayFeed03_0()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(m_mapPos["满TRAY盘升降Z轴当前位置"] + PARAM_DOUBLE("满TRAY盘升降Z轴送料偏移量")));

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(m_mapPos["满TRAY盘升降Z轴当前位置"] + PARAM_DOUBLE("满TRAY盘升降Z轴送料偏移量")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到位", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayFeed04, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayFeed04()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTraySupportCylinderOn());

	RUN_STOP_IF_ERROR(m_pTray->FullTraySupportCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待满TRAY盘支撑气缸伸出", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayFeed05, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayFeed05()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(m_mapPos["满TRAY盘升降Z轴放料位"]));

	double nCurPos = 0;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(nCurPos));

	if (nCurPos < m_mapPos["满TRAY盘升降Z轴当前位置"] + PARAM_DOUBLE("满TRAY盘升降Z轴分盘偏移量")) {
		RUN_STOP_IF_ERROR(m_pTray->FullTraySeparateCylinderOff());
	}

	if (nCurPos < PARAM_DOUBLE("满TRAY盘升降Z轴TRAY盘检测位")) {
		RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());
		if (m_sRet == "On") {
			RETURN_STATE(&CLogicTray::OnFullTrayFeed05_0, m_mapFlag["满TRAY盘"]);
		}
	}

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(m_mapPos["满TRAY盘升降Z轴放料位"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到位", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayFeed06, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayFeed05_0()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(m_mapPos["满TRAY盘升降Z轴放料位"]));

	double nCurPos = 0;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(nCurPos));

	if (nCurPos < m_mapPos["满TRAY盘升降Z轴当前位置"] + PARAM_DOUBLE("满TRAY盘升降Z轴分盘偏移量")) {
		RUN_STOP_IF_ERROR(m_pTray->FullTraySeparateCylinderOff());
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());

	if (m_sRet == "Off") {
		m_pTray->FullTrayUpDnZStop();
		RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴当前位置"]));
		RETURN_STATE(&CLogicTray::OnFullTrayFeed05_1, m_mapFlag["满TRAY盘"]);
	}

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(m_mapPos["满TRAY盘升降Z轴放料位"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到位", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayFeed06, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayFeed05_1()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(m_mapPos["满TRAY盘升降Z轴当前位置"] + 10));

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(m_mapPos["满TRAY盘升降Z轴当前位置"] + 10));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到位", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayFeed06, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayFeed06()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTraySeparateCylinderOff());

	RUN_STOP_IF_ERROR(m_pTray->FullTraySeparateCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待满TRAY盘分盘气缸缩回", false);
	}
	
	RETURN_STATE(&CLogicTray::OnFullTrayFeed07, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayFeed07()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTraySeparateCylinderOn());

	RUN_STOP_IF_ERROR(m_pTray->FullTraySeparateCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待满TRAY盘分盘气缸伸出", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTray00, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayFeed08()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(PARAM_DOUBLE("满TRAY盘升降Z轴放料位")));

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(PARAM_DOUBLE("满TRAY盘升降Z轴放料位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到位", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayFeed00, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayFeed09()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltWaitMaterialStatus());

	if (m_sRet != "On") {
		RETURN_STATE(&CLogicTray::OnFullTrayFeed12, m_mapFlag["满TRAY盘"]);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayFeed10, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayFeed10()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltOn(PARAM_BOOL("满TRAY盘皮带正转方向")));

	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltInPosStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待TRAY盘传送到位", false);
	}

	m_mapTick["TRAY盘传输到位计时"] = GetTickCount();

	RETURN_STATE(&CLogicTray::OnFullTrayFeed11, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayFeed11()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltOn(PARAM_BOOL("满TRAY盘皮带正转方向")));

	if (GetTickCount() - m_mapTick["TRAY盘传输到位计时"] < (DWORD)PARAM_INT("皮带传输到位延时")) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltOff());

	RETURN_STATE(&CLogicTray::OnFullTrayFeed00, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayFeed12()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltInMaterialStatus());

	if (m_sRet != "On") {
		REPORT("满TRAY盘无料，请及时上料!", emLogLevelWarn);
		AfxMessageBox("满TRAY盘无料，请及时上料!");
		RETURN_SELF("", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayFeed13, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayFeed13()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltOn(PARAM_BOOL("满TRAY盘皮带正转方向")));

	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltWaitMaterialStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待TRAY盘传送到待料位", false);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltOff());

	RETURN_STATE(&CLogicTray::OnFullTrayFeed09, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayBack00()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());

	if (m_sRet != "On") {
		RETURN_STATE(&CLogicTray::OnFullTrayBack11, m_mapFlag["满TRAY盘"]);
	}
	
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴当前位置"]));

	RETURN_STATE(&CLogicTray::OnFullTrayBack01, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayBack01()
{
	m_mapPos["满TRAY盘升降Z轴接料位"] = m_mapPos["满TRAY盘升降Z轴当前位置"] + PARAM_DOUBLE("满TRAY盘升降Z轴退料偏移量");
	
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(m_mapPos["满TRAY盘升降Z轴接料位"]));

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(m_mapPos["满TRAY盘升降Z轴接料位"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到位", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayBack02, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayBack02()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTraySupportCylinderOff());

	RUN_STOP_IF_ERROR(m_pTray->FullTraySupportCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待满TRAY盘支撑气缸缩回", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayBack03, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayBack03()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(m_mapPos["满TRAY盘升降Z轴放料位"]));

	double nCurPos = 0;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(nCurPos));

	if (nCurPos < PARAM_DOUBLE("满TRAY盘升降Z轴TRAY盘检测位")) {
		RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());
		if (m_sRet == "On") {
			RETURN_STATE(&CLogicTray::OnFullTrayBack03_0, m_mapFlag["满TRAY盘"]);
		}
	}

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(m_mapPos["满TRAY盘升降Z轴放料位"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到位", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayBack04, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayBack03_0()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(m_mapPos["满TRAY盘升降Z轴放料位"]));

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());

	if (m_sRet == "Off") {
		m_pTray->FullTrayUpDnZStop();
		RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴当前位置"]));
		RETURN_STATE(&CLogicTray::OnFullTrayBack03_1, m_mapFlag["满TRAY盘"]);
	}

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(m_mapPos["满TRAY盘升降Z轴放料位"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到位", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayBack04, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayBack03_1()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(m_mapPos["满TRAY盘升降Z轴当前位置"] + 10));

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(m_mapPos["满TRAY盘升降Z轴当前位置"] + 10));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到位", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayBack04, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayBack04()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTraySupportCylinderOn());

	RUN_STOP_IF_ERROR(m_pTray->FullTraySupportCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待满TRAY盘支撑气缸伸出", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayBack04_0, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayBack04_0()
{
	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(PARAM_DOUBLE("满TRAY盘升降Z轴放料位")));

	if (m_sRet == "No") {
		RETURN_STATE(&CLogicTray::OnFullTray00, m_mapFlag["满TRAY盘"]);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltInPosStatus());

	if (m_sRet != "On") {
		RETURN_STATE(&CLogicTray::OnFullTrayBack08, m_mapFlag["满TRAY盘"]);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayBack05, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayBack05()
{
	CString sRet1, sRet2;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltInMaterialStatus());

	sRet1 = m_sRet;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltWaitMaterialStatus());

	sRet2 = m_sRet;

	if (sRet1 == "On" || sRet2 == "On") {
		REPORT("满TRAY盘皮带上有料，无法退料！", emLogLevelWarn);
		AfxMessageBox("满TRAY盘皮带上有料，无法退料！");
		RETURN_SELF("", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayBack06, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayBack06()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayEndStopCylinderOn());

	RUN_STOP_IF_ERROR(m_pTray->FullTrayEndStopCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待满TRAY盘尾端阻挡气缸伸出", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayBack07, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayBack07()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltOn(!PARAM_BOOL("满TRAY盘皮带正转方向")));

	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltInMaterialStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待满TRAY盘传输到位", false);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltOff());

	RETURN_STATE(&CLogicTray::OnFullTray00, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayBack08()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayEndStopCylinderOff());

	RUN_STOP_IF_ERROR(m_pTray->FullTrayEndStopCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待满TRAY盘尾端阻挡气缸缩回", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayBack09, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayBack09()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(PARAM_DOUBLE("满TRAY盘升降Z轴最大待料位")));

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(PARAM_DOUBLE("满TRAY盘升降Z轴最大待料位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到最大待料位", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayBack10, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayBack10()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltInMaterialStatus());

	if (m_sRet == "On") {
		REPORT("请取走满TRAY盘!", emLogLevelWarn);
		AfxMessageBox("请取走满TRAY盘!");
		RETURN_SELF("等待满TRAY盘被取走", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTray00, m_mapFlag["满TRAY盘"]);
}

CStatus CLogicTray::OnFullTrayBack11()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(PARAM_DOUBLE("满TRAY盘升降Z轴最大待料位")));

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(PARAM_DOUBLE("满TRAY盘升降Z轴最大待料位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到最大待料位", false);
	}

	RETURN_STATE(&CLogicTray::OnFullTrayBack00, m_mapFlag["满TRAY盘"]);
}
