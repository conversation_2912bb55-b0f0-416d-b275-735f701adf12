#----------------------------------------------------------------
# Generated CMake target import file for configuration "Debug".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "eclipse-paho-mqtt-c::paho-mqtt3c" for configuration "Debug"
set_property(TARGET eclipse-paho-mqtt-c::paho-mqtt3c APPEND PROPERTY IMPORTED_CONFIGURATIONS DEBUG)
set_target_properties(eclipse-paho-mqtt-c::paho-mqtt3c PROPERTIES
  IMPORTED_IMPLIB_DEBUG "${_IMPORT_PREFIX}/debug/lib/paho-mqtt3c.lib"
  IMPORTED_LOCATION_DEBUG "${_IMPORT_PREFIX}/debug/bin/paho-mqtt3c.dll"
  )

list(APPEND _cmake_import_check_targets eclipse-paho-mqtt-c::paho-mqtt3c )
list(APPEND _cmake_import_check_files_for_eclipse-paho-mqtt-c::paho-mqtt3c "${_IMPORT_PREFIX}/debug/lib/paho-mqtt3c.lib" "${_IMPORT_PREFIX}/debug/bin/paho-mqtt3c.dll" )

# Import target "eclipse-paho-mqtt-c::paho-mqtt3a" for configuration "Debug"
set_property(TARGET eclipse-paho-mqtt-c::paho-mqtt3a APPEND PROPERTY IMPORTED_CONFIGURATIONS DEBUG)
set_target_properties(eclipse-paho-mqtt-c::paho-mqtt3a PROPERTIES
  IMPORTED_IMPLIB_DEBUG "${_IMPORT_PREFIX}/debug/lib/paho-mqtt3a.lib"
  IMPORTED_LOCATION_DEBUG "${_IMPORT_PREFIX}/debug/bin/paho-mqtt3a.dll"
  )

list(APPEND _cmake_import_check_targets eclipse-paho-mqtt-c::paho-mqtt3a )
list(APPEND _cmake_import_check_files_for_eclipse-paho-mqtt-c::paho-mqtt3a "${_IMPORT_PREFIX}/debug/lib/paho-mqtt3a.lib" "${_IMPORT_PREFIX}/debug/bin/paho-mqtt3a.dll" )

# Import target "eclipse-paho-mqtt-c::paho-mqtt3cs" for configuration "Debug"
set_property(TARGET eclipse-paho-mqtt-c::paho-mqtt3cs APPEND PROPERTY IMPORTED_CONFIGURATIONS DEBUG)
set_target_properties(eclipse-paho-mqtt-c::paho-mqtt3cs PROPERTIES
  IMPORTED_IMPLIB_DEBUG "${_IMPORT_PREFIX}/debug/lib/paho-mqtt3cs.lib"
  IMPORTED_LOCATION_DEBUG "${_IMPORT_PREFIX}/debug/bin/paho-mqtt3cs.dll"
  )

list(APPEND _cmake_import_check_targets eclipse-paho-mqtt-c::paho-mqtt3cs )
list(APPEND _cmake_import_check_files_for_eclipse-paho-mqtt-c::paho-mqtt3cs "${_IMPORT_PREFIX}/debug/lib/paho-mqtt3cs.lib" "${_IMPORT_PREFIX}/debug/bin/paho-mqtt3cs.dll" )

# Import target "eclipse-paho-mqtt-c::paho-mqtt3as" for configuration "Debug"
set_property(TARGET eclipse-paho-mqtt-c::paho-mqtt3as APPEND PROPERTY IMPORTED_CONFIGURATIONS DEBUG)
set_target_properties(eclipse-paho-mqtt-c::paho-mqtt3as PROPERTIES
  IMPORTED_IMPLIB_DEBUG "${_IMPORT_PREFIX}/debug/lib/paho-mqtt3as.lib"
  IMPORTED_LOCATION_DEBUG "${_IMPORT_PREFIX}/debug/bin/paho-mqtt3as.dll"
  )

list(APPEND _cmake_import_check_targets eclipse-paho-mqtt-c::paho-mqtt3as )
list(APPEND _cmake_import_check_files_for_eclipse-paho-mqtt-c::paho-mqtt3as "${_IMPORT_PREFIX}/debug/lib/paho-mqtt3as.lib" "${_IMPORT_PREFIX}/debug/bin/paho-mqtt3as.dll" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
