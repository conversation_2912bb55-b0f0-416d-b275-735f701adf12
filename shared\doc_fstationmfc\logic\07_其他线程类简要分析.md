# 其他线程类简要分析

## LogicBelt - 传送带控制

### 功能概述
控制A/B轨传送带的运行，支持双轨道独立控制。

### 关键特点
- 构造函数接受轨道参数 `CLogicBelt(CString sTrack)`
- 18个状态控制完整的传送带操作流程
- 支持自检和运行两种模式
- 时间戳管理确保运行超时保护

### 主要状态
```
OnSelfCheck00-03 → 自检流程
OnBelt00-18 → 传送带运行控制
```

---

## LogicFixtureTransport - 治具传输控制

### 功能概述
专门负责治具在工位间的传输操作，包含复杂的定位和传输逻辑。

### 关键特点
- 单线程处理，但状态复杂（55个子状态）
- 精密的位置控制和检测
- 多级安全检查机制
- 支持治具的精确定位和传输

### 状态分布
```
OnFixtureTransport00 → 初始化
OnFixtureTransport01_0-12 → 第一阶段传输
OnFixtureTransport02_0-13 → 第二阶段传输
```

---

## LogicProc - 工艺处理

### 功能概述
处理特定的工艺流程，支持多实例运行。

### 关键特点
- 构造函数接受索引参数 `CLogicProc(int nIndex)`
- 简单的6状态流程
- 支持多个工艺实例并行处理
- 索引映射管理多实例状态

### 状态流程
```
OnProc00 → 工艺准备
OnProc01 → 工艺执行
OnProc02_0-1 → 工艺完成处理
OnProc03 → 工艺结束
```

---

## LogicReset - 系统重置

### 功能概述
负责系统的初始化和重置操作。

### 关键特点
- 8个主状态，包含多个子状态
- 分步骤重置各个子系统
- 确保系统安全复位到初始状态
- 时间戳和标志管理重置进度

### 重置流程
```
OnReset00 → 重置准备
OnReset01_0-6 → 分步骤重置各模块
OnReset02-07 → 完成重置和验证
```

---

## LogicTrayEmpty/LogicTrayFull

### 功能概述
分别处理空托盘和满托盘的专门操作。

### LogicTrayEmpty特点
- 专注空托盘进出料
- 13个进料状态 + 13个退料状态
- 简化的自检流程（4个状态）

### LogicTrayFull特点  
- 专注满托盘处理
- 更复杂的定位和安全检查
- 多级子状态进行精确控制
- 包含回看和验证机制

---

## 线程类设计模式总结

### 1. 状态机模式
所有线程都采用状态机设计，通过`RETURN_STATE`宏进行状态跳转。

### 2. 子线程模式
复杂线程（如LogicMachine、LogicTray）使用CThreadFunc创建子线程处理并发任务。

### 3. 参数共享模式
通过全局设备对象的参数映射表进行数据交换和状态同步。

### 4. 安全生命周期
统一的OnSafeCheck、OnStart、OnPause、OnResume、OnStop接口确保安全控制。

### 5. 错误处理模式
使用`RUN_STOP_IF_ERROR`宏进行统一的错误检查和处理。

这些设计模式确保了系统的可靠性、可维护性和可扩展性。 