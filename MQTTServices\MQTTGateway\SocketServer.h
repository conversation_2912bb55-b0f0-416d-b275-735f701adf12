#pragma once

#include <string>
#include <atomic>
#include <QJsonObject>
#include <QJsonDocument>
#include <QLoggingCategory>

// Socket依赖
#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#define SOCKET int
#define INVALID_SOCKET -1
#define SOCKET_ERROR -1
#define closesocket close
#endif

Q_DECLARE_LOGGING_CATEGORY(socketServer)

// Socket消息结构
struct SocketMessage {
    std::string type;        // 消息类型：DEVICE_STATUS, EVENT_*, COMMAND, RESPONSE
    std::string data;        // JSON格式的数据内容
    std::string timestamp;   // 时间戳
    std::string requestId;   // 请求ID（用于事件答复匹配）
};

// 简化的Socket服务器类
class SocketServer {
private:
    SOCKET m_listenSocket = INVALID_SOCKET;
    SOCKET m_clientSocket = INVALID_SOCKET;
    std::atomic<bool> m_running{false};
    int m_port;

public:
    explicit SocketServer(int port = 8888);
    ~SocketServer();
    
    bool Initialize(int port = 8888);
    void Run();
    void Stop();
    bool SendMessage(const SocketMessage& msg);
    bool ReceiveMessage(SocketMessage& msg);
    bool IsConnected() const;

private:
    void WaitForClient();
    void HandleClient();
    bool ParseMessage(const std::string& rawData, SocketMessage& msg);
};
