# MQTT网关项目规范

## 🎯 项目概述

MQTT网关是一个现代化的工业物联网通信中间件，基于Qt6和CMake构建，负责FStation设备与SCADA平台的数据交换。

## 🏗️ 技术栈

- **语言**: C++17
- **框架**: Qt6 Core
- **构建系统**: CMake 3.16+
- **依赖管理**: vcpkg
- **MQTT库**: Eclipse Paho MQTT C/C++
- **加密**: OpenSSL
- **编译器**: MSVC 2019/2022

## 📁 项目结构

```
MQTTGateway/
├── main.cpp                    # 程序入口
├── MQTTGateway.h/.cpp         # 核心网关类
├── ConfigManager.h/.cpp       # 配置管理器
├── DataPointManager.h/.cpp    # 数据点管理器
├── ScadaProtocolHandler.h/.cpp # SCADA协议处理器
├── EventManager.h/.cpp        # 事件管理器
├── config/                    # 配置文件目录
│   ├── scada_ctrl_conf.json  # 主配置文件
│   └── data_points_config.json # 数据点配置
├── CMakeLists.txt             # CMake构建配置
└── vcpkg.json                 # 依赖包配置
```

## 💻 编码规范

### C++17 现代特性使用
```cpp
// ✅ 推荐：使用智能指针
std::unique_ptr<mqtt::async_client> m_mqttClient;
std::shared_ptr<ScadaProtocolHandler> m_scadaHandler;

// ✅ 推荐：使用auto和范围for循环
for (const auto& [id, dataPoint] : m_dataPoints) {
    // 处理数据点
}

// ✅ 推荐：使用结构化绑定
if (auto [success, result] = ProcessMessage(msg); success) {
    // 处理成功结果
}
```

### Qt6 最佳实践
```cpp
// ✅ 推荐：使用Qt6的信号槽语法
connect(m_timer, &QTimer::timeout, this, &MQTTGateway::onTimeout);

// ✅ 推荐：使用QLoggingCategory
Q_DECLARE_LOGGING_CATEGORY(mqttGateway)
qCInfo(mqttGateway) << "MQTT连接成功";

// ✅ 推荐：使用QJsonDocument处理JSON
QJsonDocument doc = QJsonDocument::fromJson(data);
QJsonObject obj = doc.object();
```

### 异步编程模式
```cpp
// ✅ 推荐：使用std::future和std::async
auto future = std::async(std::launch::async, [this]() {
    return ProcessLongRunningTask();
});

// ✅ 推荐：使用条件变量进行线程同步
std::unique_lock<std::mutex> lock(m_mutex);
m_condition.wait(lock, [this] { return m_dataReady; });
```

## 🔧 架构设计原则

### 1. 单一职责原则
- **MQTTGateway**: 负责MQTT连接和消息路由
- **ConfigManager**: 专门处理配置文件加载和验证
- **DataPointManager**: 管理332个数据点的状态和映射
- **ScadaProtocolHandler**: 处理SCADA协议转换

### 2. 依赖注入模式
```cpp
class MQTTGateway {
private:
    std::shared_ptr<ConfigManager> m_configManager;
    std::shared_ptr<DataPointManager> m_dataPointManager;

public:
    // 构造函数注入依赖
    MQTTGateway(std::shared_ptr<ConfigManager> config,
                std::shared_ptr<DataPointManager> dataManager);
};
```

### 3. 观察者模式
```cpp
// 事件通知机制
class EventManager {
public:
    void RegisterCallback(const std::string& event,
                         std::function<void(const QJsonObject&)> callback);
    void NotifyEvent(const std::string& event, const QJsonObject& data);
};
```

## 📊 性能优化指南

### 消息队列优化
```cpp
// ✅ 推荐：使用优先级队列
enum class MessagePriority { HIGH, NORMAL, LOW };
std::priority_queue<MQTTMessage> m_messageQueue;

// ✅ 推荐：批量处理消息
void ProcessMessageBatch(const std::vector<MQTTMessage>& messages);
```

### 内存管理
```cpp
// ✅ 推荐：使用对象池减少内存分配
class MessagePool {
    std::queue<std::unique_ptr<MQTTMessage>> m_pool;
public:
    std::unique_ptr<MQTTMessage> Acquire();
    void Release(std::unique_ptr<MQTTMessage> msg);
};
```

## 🔒 安全规范

### 1. 连接安全
```cpp
// ✅ 必须：使用TLS加密
mqtt::ssl_options sslOpts;
sslOpts.set_trust_store("ca-cert.pem");
sslOpts.set_key_store("client-cert.pem");
```

### 2. 数据验证
```cpp
// ✅ 必须：验证所有输入数据
bool ValidateDataPoint(const QJsonObject& data) {
    if (!data.contains("id") || !data.contains("value")) {
        return false;
    }
    // 进一步验证逻辑
    return true;
}
```

## 🧪 测试规范

### 单元测试
```cpp
// 使用Qt Test框架
class TestMQTTGateway : public QObject {
    Q_OBJECT
private slots:
    void testConnectionEstablishment();
    void testMessagePublishing();
    void testDataPointUpdate();
};
```

### 集成测试
- 测试与FStation的Socket连接
- 测试MQTT服务器连接
- 测试数据点映射和转换

## 📝 配置管理

### 配置文件结构
```json
{
    "device": {
        "id": "FStation_001",
        "name": "FStation工业贴装设备"
    },
    "mqtt": {
        "broker": "mqtt.example.com",
        "port": 8883,
        "username": "gateway_user",
        "password": "secure_password"
    },
    "socket": {
        "port": 8080,
        "timeout": 30000
    }
}
```

### 配置验证
```cpp
// ✅ 必须：验证所有配置项
bool ConfigManager::validate() const {
    QStringList errors;

    if (m_mqtt.broker.isEmpty()) {
        errors << "MQTT broker地址不能为空";
    }

    if (m_socket.port < 1024 || m_socket.port > 65535) {
        errors << "Socket端口范围无效";
    }

    return errors.isEmpty();
}
```

## 🚀 部署和运维

### CMake构建配置
```cmake
# 设置构建类型
set(CMAKE_BUILD_TYPE Release)

# 启用优化
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")

# 静态链接运行时库
set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded")
```

### 日志配置
```cpp
// 生产环境日志级别
QLoggingCategory::setFilterRules("*.debug=false\n"
                                 "*.info=true\n"
                                 "*.warning=true\n"
                                 "*.critical=true");
```

## 📋 代码审查清单

### 提交前检查
- [ ] 代码符合C++17标准
- [ ] 所有Qt对象正确管理内存
- [ ] MQTT消息处理有错误处理
- [ ] 配置文件验证完整
- [ ] 添加了适当的日志输出
- [ ] 单元测试覆盖新功能
- [ ] 文档已更新

### 性能检查
- [ ] 避免在主线程进行阻塞操作
- [ ] 大对象使用移动语义
- [ ] 合理使用缓存机制
- [ ] 内存泄漏检查通过

## 📊 SCADA平台MQTT数采功能规范

### 核心协议要求
基于 **SCADA平台MQTT协议设备接入指南 v1.13** 和 **科瑞F站数采清单** 开发MQTT数采功能。

#### 1. MQTT连接规范
```cpp
// ✅ 必须：使用MQTT v3.1.1协议
// 测试环境: 172.16.247.78:1883
struct MQTTConnectionConfig {
    std::string broker = "172.16.247.78";
    int port = 1883;
    std::string deviceId;           // 设备唯一标识
    std::string secret;             // 设备密钥
    bool autoReconnect = true;
    bool cleanSession = true;
    int keepaliveInterval = 60;
    int connectionTimeout = 30;
};

// ✅ 必须：实现设备认证
std::string generateClientId(const std::string& deviceId,
                            const std::string& timestamp) {
    // 格式: {deviceId}_{identityType}_{signatureType}_{timestamp}
    return deviceId + "_0_0_" + timestamp;
}

std::string generatePassword(const std::string& secret,
                           const std::string& timestamp) {
    // 使用HMACSHA256算法，以timestamp为密钥加密secret
    return hmacSha256(secret, timestamp);
}
```

#### 2. Topic订阅规范
```cpp
// ✅ 必须：订阅所有下行Topic
void MQTTGateway::subscribeToTopics() {
    std::vector<std::string> topics = {
        "$oc/devices/" + m_deviceId + "/sys/properties/get/+",
        "$oc/devices/" + m_deviceId + "/sys/properties/set/+",
        "$oc/devices/" + m_deviceId + "/sys/commands/+",
        "$oc/devices/" + m_deviceId + "/sys/events/up/response/+"
    };

    for (const auto& topic : topics) {
        m_mqttClient->subscribe(topic, 2); // QoS 2
    }
}
```

#### 3. 数据点映射规范
```cpp
// ✅ 必须：实现科瑞F站数采清单映射
class DataPointMapper {
public:
    struct DataPoint {
        std::string code;        // 数据代码 (如 A00002, C00001)
        std::string name;        // 参数名称
        std::string dataType;    // 数据类型 (bool, int, string, decimal, datetime)
        std::string unit;        // 单位
        bool isRequired;         // 是否必需
        std::string source;      // 数据来源 (设备获取/Agent获取/服务器)
    };

    // 设备运行状态数据点
    static const std::vector<DataPoint> DEVICE_STATUS_POINTS;
    // 硬件资源监控数据点
    static const std::vector<DataPoint> HARDWARE_MONITOR_POINTS;
    // 生产过程追溯数据点
    static const std::vector<DataPoint> PRODUCTION_TRACE_POINTS;
    // 故障报警数据点
    static const std::vector<DataPoint> FAULT_ALARM_POINTS;
    // 设备参数数据点
    static const std::vector<DataPoint> DEVICE_PARAM_POINTS;
};
```

#### 4. 服务结构规范
```cpp
// ✅ 必须：遵循SCADA平台服务结构
struct ServiceData {
    std::string service_id;      // DefaultService, SnService, StatusService
    QJsonObject properties;      // 属性键值对
    std::string event_time;      // 事件时间 YYYY-MM-DD hh:mm:ss.SSS
};

// 核心服务定义
enum class ServiceType {
    DEFAULT_SERVICE,    // 设备通用参数和生产数据
    SN_SERVICE,        // 产品SN码相关数据
    STATUS_SERVICE,    // 设备状态信息
    EVENT_SERVICE,     // 事件上报服务
    COMMAND_SERVICE    // 命令响应服务
};
```

#### 5. 事件上报规范
```cpp
// ✅ 必须：实现标准事件类型
enum class EventType {
    EVENT_SN_IN,                    // 入站
    EVENT_SN_OUT,                   // 出站
    EVENT_SN_OUT_REQ,              // 出站询问
    EVENT_BOP_DELIVER_COMPLETED,   // 转产完成
    EVENT_PAUSE,                   // 暂停
    EVENT_FAULT                    // 故障
};

// 事件上报接口
bool MQTTGateway::reportEvent(EventType eventType,
                             const QJsonObject& properties) {
    std::string requestId = generateRequestId();
    std::string topic = "$oc/devices/" + m_deviceId +
                       "/sys/events/up/request_id=" + requestId;

    QJsonObject payload = buildEventPayload(eventType, properties);
    return publishMessage(topic, payload, 2); // QoS 2
}
```

#### 6. 命令处理规范
```cpp
// ✅ 必须：实现标准命令处理
enum class CommandType {
    COMMAND_SN_DELIVER,           // 下发SN
    COMMAND_BOP_DELIVER,          // 转产/下发生产参数
    COMMAND_PAUSE,                // 暂停生产
    COMMAND_PRODUCTION,           // 恢复生产
    COMMAND_MQTT_CONFIG_DELIVER   // 下发MQTT配置参数
};

class CommandHandler {
public:
    virtual bool handleCommand(CommandType cmd,
                              const QJsonObject& properties) = 0;
    virtual QJsonObject buildResponse(int resultCode,
                                     const std::string& resultDesc) = 0;
};
```

#### 7. 幂等性实现规范
```cpp
// ✅ 必须：实现请求去重机制
class RequestIdManager {
private:
    std::unordered_set<std::string> m_processedRequests;
    std::mutex m_mutex;
    QTimer* m_cleanupTimer;

public:
    bool isRequestProcessed(const std::string& requestId) {
        std::lock_guard<std::mutex> lock(m_mutex);
        return m_processedRequests.find(requestId) != m_processedRequests.end();
    }

    void markRequestProcessed(const std::string& requestId) {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_processedRequests.insert(requestId);
    }

    void cleanupOldRequests() {
        // 定期清理超过10分钟的请求ID
    }
};
```

#### 8. 重试机制规范
```cpp
// ✅ 必须：实现指数退避重试
class RetryManager {
private:
    struct RetryInfo {
        int attempts = 0;
        std::chrono::steady_clock::time_point lastAttempt;
        std::string requestId;
        QJsonObject payload;
    };

public:
    bool shouldRetry(const std::string& requestId) {
        auto it = m_retryMap.find(requestId);
        if (it == m_retryMap.end()) return true;

        const auto& info = it->second;
        if (info.attempts >= 3) return false; // 最多重试3次

        auto elapsed = std::chrono::steady_clock::now() - info.lastAttempt;
        auto waitTime = std::chrono::seconds(2 << info.attempts); // 指数退避
        return elapsed >= waitTime;
    }
};
```

#### 9. 配置文件规范
```json
// ✅ 必须：使用标准配置文件 scada_ctrl_conf.json
{
  "device": {
    "deviceId": "F_STATION_001",
    "secret": "your_device_secret"
  },
  "mqtt": {
    "broker": "172.16.247.78",
    "port": 1883,
    "autoReconnect": true,
    "cleanSession": true,
    "keepaliveInterval": 60,
    "connectionTimeout": 30
  },
  "features": {
    "defaultReportCycleS": 300,
    "enableEventReporting": true,
    "enableCommandHandling": true
  },
  "dataPoints": {
    "configFile": "config/data_points_config.json",
    "enableMapping": true
  }
}
```

#### 10. 数据验证规范
```cpp
// ✅ 必须：验证所有SCADA数据格式
class SCADADataValidator {
public:
    static bool validateDeviceStatus(const QJsonObject& data) {
        // 验证设备状态数据 (A00002-A00015)
        return data.contains("device_status_enum") &&
               isValidStatusEnum(data["device_status_enum"].toString());
    }

    static bool validateProductionData(const QJsonObject& data) {
        // 验证生产数据 (C00001-C00014)
        return data.contains("sn") &&
               data.contains("production_model");
    }

    static bool validateEventData(EventType eventType,
                                 const QJsonObject& data) {
        // 根据事件类型验证必需字段
        switch (eventType) {
            case EventType::EVENT_SN_IN:
            case EventType::EVENT_SN_OUT:
                return data.contains("sn") &&
                       data.contains("production_model");
            default:
                return true;
        }
    }
};
```

### 科瑞F站数采清单实现

#### 数据点分类定义
```cpp
// 1. 设备运行状态与基础信息 (A00002-A00015)
const std::vector<DataPoint> DEVICE_STATUS_POINTS = {
    {"A00002", "正常生产", "bool", "", true, "设备获取"},
    {"A00003", "运行暂停", "bool", "", true, "设备获取"},
    {"A00004", "设备故障", "bool", "", true, "设备获取"},
    {"A00006", "待机状态", "bool", "", true, "设备获取"},
    {"A00010", "机型程序名", "string", "", true, "设备获取"},
    {"A00014", "运行周期", "decimal", "s", true, "设备获取"},
    {"A00015", "累计产能", "int", "PCS", true, "设备获取"}
};

// 2. 设备硬件资源监控 (A00020-A00030)
const std::vector<DataPoint> HARDWARE_MONITOR_POINTS = {
    {"A00020", "内存容量", "int", "MB", true, "Agent获取"},
    {"A00021", "磁盘容量", "int", "GB", true, "Agent获取"},
    {"A00029", "CPU利用率", "float", "%", true, "Agent获取"},
    {"A00030", "内存利用率", "float", "%", true, "Agent获取"}
};

// 3. 生产过程与主板追溯参数 (C00001-C00009)
const std::vector<DataPoint> PRODUCTION_TRACE_POINTS = {
    {"C00001", "主板SN号", "string", "", true, "设备获取"},
    {"C00002", "设备资产编码", "string", "", true, "设备获取"},
    {"C00003", "轨道号", "int", "", true, "设备获取"},
    {"C00004", "面别", "enum", "", true, "设备获取"},
    {"C00005", "程序名", "string", "", true, "设备获取"},
    {"C00007", "实际加工周期", "decimal", "s", false, "设备获取"}
};

// 4. 故障与报警信息 (C00010-C00014)
const std::vector<DataPoint> FAULT_ALARM_POINTS = {
    {"C00010", "故障代码", "string", "", false, "设备获取"},
    {"C00011", "故障信息", "string", "", false, "设备获取"},
    {"C00012", "故障开始时间", "datetime", "", false, "设备获取"},
    {"C00013", "故障结束时间", "datetime", "", false, "设备获取"},
    {"C00014", "故障时间", "decimal", "s", false, "设备获取"}
};

// 5. 设备参数与测高数据 (B40008-B40016)
const std::vector<DataPoint> DEVICE_PARAM_POINTS = {
    {"B40008", "1轨测高实际值1", "decimal", "", true, "设备获取"},
    {"B40009", "1轨测高实际值2", "decimal", "", true, "设备获取"},
    {"B40016", "测高设定值", "decimal", "", true, "设备获取"}
};
```

### SCADA集成检查清单

#### 开发阶段检查
- [ ] 实现MQTT v3.1.1协议连接
- [ ] 实现设备认证机制 (ClientId + HMACSHA256)
- [ ] 订阅所有必需的下行Topic
- [ ] 实现科瑞F站数采清单所有数据点
- [ ] 实现标准服务结构 (DefaultService, SnService, StatusService)
- [ ] 实现事件上报机制 (入站、出站、故障等)
- [ ] 实现命令处理机制 (SN下发、转产、暂停等)
- [ ] 实现幂等性和重试机制
- [ ] 配置文件符合SCADA标准格式

#### 测试阶段检查
- [ ] 连接测试环境 172.16.247.78:1883 成功
- [ ] 属性周期上报功能正常 (QoS 1)
- [ ] 属性查询响应功能正常 (QoS 2)
- [ ] 属性设置响应功能正常 (QoS 2)
- [ ] 命令处理响应功能正常 (QoS 2)
- [ ] 事件上报响应功能正常 (QoS 2)
- [ ] 重复请求去重功能正常
- [ ] 网络断线重连功能正常
- [ ] 所有数据点映射正确

#### 部署阶段检查
- [ ] 生产环境MQTT Broker配置正确
- [ ] 设备ID和密钥配置正确
- [ ] 日志记录功能完整
- [ ] 性能监控指标正常
- [ ] 故障恢复机制有效

## 🔄 版本管理

### 语义化版本控制
- **主版本号**: 不兼容的API修改
- **次版本号**: 向后兼容的功能性新增
- **修订号**: 向后兼容的问题修正

### 发布流程
1. 功能开发在feature分支
2. 代码审查通过后合并到develop
3. 集成测试通过后合并到main
4. 创建release标签并部署
```
