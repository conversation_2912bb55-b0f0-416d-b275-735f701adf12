#pragma once

#include "Module.h"

class CFixture : public CModule
{
public:
	CFixture();
	virtual ~CFixture();

public:
	CString SupportCylinderOn();
	CString SupportCylinderOff();
	CString SupportCylinderStatus();

	CString SeparateCylinderOn();
	CString SeparateCylinderOff();
	CString SeparateCylinderStatus();

	CString UpLidLiftCylinderOn();
	CString UpLidLiftCylinderOff();
	CString UpLidLiftCylinderStatus();

	CString UpLidStopCylinderOn();
	CString UpLidStopCylinderOff();
	CString UpLidStopCylinderStatus();

	CString UpLidSetPosCylinderOn();
	CString UpLidSetPosCylinderOff();
	CString UpLidSetPosCylinderStatus();

	CString DnLidSetPosCylinderOn();
	CString DnLidSetPosCylinderOff();
	CString DnLidSetPosCylinderStatus();

	CString RollCylinderOn();
	CString RollCylinderOff();
	CString RollCylinderStatus();

	CString VacuumOn();
	CString BrokenVacuumOn();
	CString VacuumOff();
	CString VacuumStatus();

	CString InStopCylinderOn();
	CString InStopCylinderOff();
	CString InStopCylinderStatus();

	CString InStatus();
	CString InPosStatus();
	CString ErrorCheckStatus();
	CString OutOfRangePreWarnStatus();
	CString OutOfRangeWarnStatus();

	CString UpLidInPosStatus();
	CString DnLidInPosStatus();

	CString UpLidUpLayerExistStatus();
	CString UpLidDnLayerExistStatus();

	CString UploadBeltOn(bool bDir);
	CString UploadBeltOff();

	CString TransportYHome();
	CString IsTransportYHomeOK();

	CString TransportZHome();
	CString IsTransportZHomeOK();

	CString SeparateZHome();
	CString IsSeparateZHomeOK();

	CString LidZHome();
	CString IsLidZHomeOK();

	CString TransportYMove(double nPos, bool bSafeCheckFlag = true, int nSpeedRate = 100);
	CString IsTransportYInPos(double nPos, double nTolerance = 0.05);
	CString TransportYStop();

	CString TransportZMove(double nPos, int nSpeedRate = 100);
	CString IsTransportZInPos(double nPos, double nTolerance = 0.05);
	CString TransportZStop();

	CString SeparateZMove(double nPos, int nSpeedRate = 100);
	CString IsSeparateZInPos(double nPos, double nTolerance = 0.5);
	CString SeparateZStop();

	CString LidZMove(double nPos, int nSpeedRate = 100);
	CString IsLidZInPos(double nPos, double nTolerance = 0.5);
	CString LidZStop();

	CString GetHeightL(double &nHeight);
	CString GetHeightR(double &nHeight);
};
