#include <QCoreApplication>
#include <QLoggingCategory>
#include <QDebug>
#include <iostream>
#include <memory>
#include <thread>
#include <csignal>

#include "TestSocketGateway.h"

using namespace std;

// 全局网关对象
unique_ptr<TestSocketGateway> g_gateway;

// 信号处理函数
void signalHandler(int signal) {
    qInfo() << "收到退出信号:" << signal;
    
    if (g_gateway) {
        g_gateway->Stop();
        g_gateway.reset();
    }
    
    QCoreApplication::quit();
}

int main(int argc, char* argv[]) {
    QCoreApplication app(argc, argv);
    
    // 设置应用程序信息
    app.setApplicationName("TestSocketGateway");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("FStation");
    
    // 注册信号处理器
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
    
    // 设置日志格式
    qSetMessagePattern("[%{time yyyy-MM-dd hh:mm:ss.zzz}] [%{if-debug}调试%{endif}%{if-info}信息%{endif}%{if-warning}警告%{endif}%{if-critical}错误%{endif}%{if-fatal}致命%{endif}] %{message}");
    
    // 输出启动信息
    qInfo() << "=== 测试Socket网关 v1.0.0 ===";
    qInfo() << "专用于测试FStation与MQTT网关的Socket通信";
    
    try {
        // 创建并初始化测试网关
        g_gateway = make_unique<TestSocketGateway>();
        
        if (!g_gateway->Initialize(8888)) {
            qCritical() << "测试网关初始化失败";
            return -1;
        }
        
        qInfo() << "测试网关启动成功，监听端口8888，按Ctrl+C退出...";
        
        // 在后台线程中运行网关业务逻辑
        thread gatewayThread([&]() { g_gateway->Run(); });
        
        // 启动Qt事件循环
        int result = app.exec();
        
        // 等待网关线程结束
        if (gatewayThread.joinable()) {
            gatewayThread.join();
        }
        
        return result;
        
    } catch (const exception& e) {
        qCritical() << "程序异常退出:" << e.what();
        return -1;
    }
}
