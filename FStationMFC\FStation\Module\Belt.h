#pragma once

#include "Module.h"

class CBelt : public CModule
{
public:
	CBelt(CString sTrack);
	virtual ~CBelt();

public:
	CString MainBoardSetPosCylinderOn();
	CString MainBoardSetPosCylinderOff();
	CString MainBoardSetPosCylinderStatus();

	CString FixtureAssembleStopCylinderOn();
	CString FixtureAssembleStopCylinderOff();
	CString FixtureAssembleStopCylinderStatus();

	CString FixtureAssembleSetPosCylinderOn();
	CString FixtureAssembleSetPosCylinderOff();
	CString FixtureAssembleSetPosCylinderStatus();

	CString BeltOn(bool bDir);
	CString BeltOff();

	CString MainBoardInPosStatus();
	CString FixtureAssembleInPosStatus();
	CString FixtureOutInPosStatus();

	CString NextStationTrackRequestOn();
	CString NextStationTrackRequestOff();
	CString NextStationTrackRequestStatus();

private:
	CString m_sTrack;
};
