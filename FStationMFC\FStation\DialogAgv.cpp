﻿// DialogAgv.cpp : 实现文件
//

#include "stdafx.h"
#include "FStation.h"
#include "DialogAgv.h"
#include "afxdialogex.h"

// CDialogAgv 对话框

IMPLEMENT_DYNAMIC(CDialogAgv, CDialogEx)

CDialogAgv::CDialogAgv(CWnd* pParent /*=NULL*/)
	: CDialogEx(CDialogAgv::IDD, pParent)
{

}

CDialogAgv::~CDialogAgv()
{
}

void CDialogAgv::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_LIST1, m_list);
	DDX_Control(pDX, IDC_IPADDRESS1, m_ip);
}


BEGIN_MESSAGE_MAP(CDialogAgv, CDialogEx)
	ON_MESSAGE(UM_AGV_RECV, &CDialogAgv::OnUmAgvRecv)
	ON_WM_TIMER()
	ON_WM_DESTROY()
END_MESSAGE_MAP()


BOOL CDialogAgv::PreTranslateMessage( MSG* pMsg )
{
	if ((WM_SYSKEYDOWN == pMsg->message && VK_F4 == pMsg->wParam) || (pMsg->message == WM_KEYDOWN && pMsg->wParam == VK_ESCAPE)) { //屏蔽Alt + F4
		return TRUE;
	}

	return CDialogEx::PreTranslateMessage(pMsg);
}

static HWND hWndAgv = NULL;

void __stdcall OnSockRecv(char *pRecvBuff, int nRecvLen, void* pSender)
{
	char buff[1024] = { 0 };
	memcpy(buff, pRecvBuff, nRecvLen);

	int nLen = nRecvLen;

	if (nRecvLen >= 12) {
		CString str, str1;
		for (int i=0; i<nRecvLen; i++)
		{
			if (i == 0) {
				str1.Format("%02X", (unsigned char)buff[i]);
			}
			else {
				str1.Format(" %02X", (unsigned char)buff[i]);
			}

			str += str1;
		}

		::SendMessage(hWndAgv, UM_AGV_RECV, (WPARAM)(&str), 0);

		if (buff[7] == 0x03) {
			buff[12] = 0;
			if (buff[8] == 0x00 && buff[9] == 0x00 && buff[10] == 0x00) {
				do 
				{
					CString sRet;
					sRet = g_pFixture->OutOfRangeWarnStatus();

					if (sRet != "On") {
						buff[10] = 0;
						break;
					}

					static int	 nStatus = -1;
					static DWORD nTick = -1;

					sRet = g_pFixture->OutOfRangePreWarnStatus();
					if (sRet == "On") {
						if (nStatus == 1) {
							if (nTick > 0) {
								if (GetTickCount() - nTick > 10000) {
									nTick = -1;
									nStatus = 2;
								}
							}
							else {
								nTick = GetTickCount();
							}
						}
						else {
							nTick = -1;
							nStatus = 2;
						}
					}
					else {
						nTick = -1;
						nStatus = 1;
					}

					buff[10] = nStatus;

				} while (false);

				if (VAR_FIXTURE_B("AGV送料标志") && VAR_FIXTURE_B("AGV允许上料标志")) {
					if (VAR_FIXTURE_B("AGV送料完成标志")) {
						buff[12] = 2;
					}
					else {
						buff[12] = 1;
					}
				}
				else {
					buff[12] = 0;
				}

				buff[5] = buff[5] + 1;
				buff[8] = 2 * buff[11];
				buff[9] = 0;
				buff[11] = 0;

				g_pSockAgv->SendCmd(buff, nLen + buff[8] - 3, pSender);
			}
		}

		if (buff[7] == 0x06) {
			if (buff[8] == 0 && buff[9] == 0x10) {
				if (buff[11] == 1) {
					VAR_FIXTURE("AGV送料标志") = true;
				}

				if (buff[11] == 2 || buff[11] == 0) {
					VAR_FIXTURE("AGV送料标志") = false;
				}
			}

			g_pSockAgv->SendCmd(buff, nLen, pSender);
		}
	}
}

BOOL CDialogAgv::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	CenterWindow();

	hWndAgv = GetSafeHwnd();

	g_pSockAgv->InitServer(VAR_MACHINE_I("AGV通讯端口"), OnSockRecv);

	m_imageList.Create(20, 20, TRUE, 2, 2);

	//Node
	LONG lStyle = 0;
	lStyle = GetWindowLong(m_list.m_hWnd, GWL_STYLE);//获取当前窗口样式
	lStyle &= ~LVS_TYPEMASK;//清除显位方式位
	lStyle |= LVS_REPORT;
	SetWindowLong(m_list.m_hWnd, GWL_STYLE, lStyle);

	DWORD dwStyle = 0;
	dwStyle = m_list.GetExtendedStyle();//获取当前窗口扩展样式
	dwStyle |= LVS_EX_FULLROWSELECT;//选中某行使整行高亮
	dwStyle |= LVS_EX_GRIDLINES;//网格线
	m_list.SetExtendedStyle(dwStyle);

	m_list.SetImageList(&m_imageList, LVSIL_SMALL);

	CString sParam[] = { "", "序号", "时间", "通讯数据"};
	int		nParamLen[] = { 1, 60, 100, 1000 };

	for (int i=0; i<sizeof(nParamLen) / sizeof(int); i++)
	{
		if (i == 3) {
			m_list.InsertColumn(i, sParam[i], LVCFMT_LEFT, nParamLen[i]);
		}
		else {
			m_list.InsertColumn(i, sParam[i], LVCFMT_CENTER, nParamLen[i]);
		}
	}

	CString sIp;

	GetIPAddress(sIp);

	DWORD dwAddr = inet_addr(sIp);

	unsigned char *p = (unsigned char *)&dwAddr;
	m_ip.SetAddress(*p, *(p + 1), *(p + 2), *(p + 3));

	SetDlgItemInt(IDC_EDIT1, VAR_MACHINE_I("AGV通讯端口"));

	return TRUE;
}

// CDialogAgv 消息处理程序

void CDialogAgv::OnTimer(UINT_PTR nIDEvent)
{
	CDialogEx::OnTimer(nIDEvent);
}

afx_msg LRESULT CDialogAgv::OnUmAgvRecv(WPARAM wParam, LPARAM lParam)
{
	CString *pStr = (CString *)wParam;

	CString str;
	str.Format("%s", *pStr);

	static unsigned int nLogCnt = 0;
	nLogCnt++;

	CString strNo;
	strNo.Format("%d", nLogCnt);

	SYSTEMTIME t = { 0 };
	GetLocalTime(&t);

	CString strTime;
	strTime.Format("%02d:%02d:%02d:%03d", t.wHour, t.wMinute, t.wSecond, t.wMilliseconds);

	int nRow = 0;

	nRow = m_list.GetItemCount();

	if (nRow > 999) {
		m_list.DeleteItem(0);
		nRow = 999;
	}

	m_list.InsertItem(nRow, "");
	m_list.SetItemText(nRow, 1, strNo);
	m_list.SetItemText(nRow, 2, strTime);
	m_list.SetItemText(nRow, 3, str);

	m_list.EnsureVisible(nRow, true);

	return 0;
}

void CDialogAgv::GetIPAddress(CString &sIp)
{	
	char szHostName[128] = { 0 };

	sIp = "0.0.0.0";

	if(gethostname(szHostName, 128) == 0)
	{
		struct hostent* pHost = NULL;

		pHost = gethostbyname(szHostName);

		for(int i = 0; pHost!= NULL && pHost->h_addr_list[i] != NULL; i++)
		{
			CString str;

			for(int j = 0; j < pHost->h_length; j++)
			{
				CString addr;

				if(j > 0)
					str += ".";

				addr.Format("%u", (unsigned int)((unsigned char*)pHost->h_addr_list[i])[j]);
				str += addr;
			}

			if (!str.IsEmpty()) {
				sIp = str;
			}
		}
	}
}

void CDialogAgv::OnDestroy()
{
	g_pSockAgv->CloseSocket();

	delete g_pSockAgv;

	CDialogEx::OnDestroy();
}
