#!/usr/bin/env python3
"""
简单的MQTT命令测试
"""

import paho.mqtt.client as mqtt
import json
import time

# MQTT 配置
MQTT_BROKER = "192.168.88.44"
MQTT_PORT = 1883
DEVICE_ID = "A320021760"
CLIENT_ID = f"simple_mqtt_test_{int(time.time())}"

def on_connect(client, userdata, flags, rc):
    if rc == 0:
        print(f"✅ 连接成功到 EMQX")
        # 订阅命令响应
        response_topic = f"$oc/devices/{DEVICE_ID}/sys/commands/response/+"
        client.subscribe(response_topic, qos=1)
        print(f"📥 订阅命令响应主题: {response_topic}")
    else:
        print(f"❌ 连接失败: {rc}")

def on_message(client, userdata, msg):
    print(f"\n🎉 收到响应!")
    print(f"   主题: {msg.topic}")
    print(f"   原始数据: {msg.payload.decode()}")

def test_simple_command():
    print("=" * 60)
    print("简单MQTT命令测试")
    print("=" * 60)
    
    # 创建MQTT客户端
    client = mqtt.Client(CLIENT_ID)
    client.on_connect = on_connect
    client.on_message = on_message
    
    try:
        # 连接到MQTT Broker
        print(f"🔗 连接到MQTT Broker: {MQTT_BROKER}:{MQTT_PORT}")
        client.connect(MQTT_BROKER, MQTT_PORT, 60)
        client.loop_start()
        
        time.sleep(3)  # 等待连接建立
        
        # 发送简单的暂停命令
        request_id = f"simple_mqtt_test_{int(time.time())}"
        command = {
            "service_id": "CommandService",
            "command_name": "COMMAND_PAUSE",
            "properties": {
                "reason": "简单MQTT测试"
            }
        }
        
        command_topic = f"$oc/devices/{DEVICE_ID}/sys/commands/request_id={request_id}"
        
        print(f"\n📤 发送命令:")
        print(f"   主题: {command_topic}")
        print(f"   RequestId: {request_id}")
        print(f"   命令: {json.dumps(command, ensure_ascii=False)}")
        
        result = client.publish(command_topic, json.dumps(command), qos=2)
        
        if result.rc == mqtt.MQTT_ERR_SUCCESS:
            print(f"✅ 命令发送成功，等待响应...")
        else:
            print(f"❌ 命令发送失败: {result.rc}")
        
        # 等待响应
        time.sleep(10)
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    finally:
        if client:
            client.loop_stop()
            client.disconnect()
            print("\n🔌 MQTT连接已断开")

if __name__ == "__main__":
    test_simple_command()
