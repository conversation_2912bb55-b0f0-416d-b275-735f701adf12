﻿// DialogRobotCalib.cpp : 实现文件
//

#include "stdafx.h"
#include "FStation.h"
#include "DialogRobotCalib.h"
#include "afxdialogex.h"

#include "LogicMgr.h"

// CDialogRobotCalib 对话框

IMPLEMENT_DYNAMIC(CDialogRobotCalib, CDialogEx)

CDialogRobotCalib::CDialogRobotCalib(CPoint pt, CImageWindow* pImageWindow, CWnd* pParent /*=NULL*/)
	: CDialogEx(CDialogRobotCalib::IDD, pParent)
{
	m_pt = pt;
	m_pImageWindow = pImageWindow;
	m_eCalib = emCalibUp;
}

CDialogRobotCalib::~CDialogRobotCalib()
{
}

void CDialogRobotCalib::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
}

BEGIN_MESSAGE_MAP(CDialogRobotCalib, CDialogEx)
	ON_BN_CLICKED(IDC_BUTTON1, &CDialogRobotCalib::OnBnClickedButton1)
	ON_BN_CLICKED(IDC_BUTTON2, &CDialogRobotCalib::OnBnClickedButton2)
	ON_BN_CLICKED(IDC_BUTTON3, &CDialogRobotCalib::OnBnClickedButton3)
	ON_BN_CLICKED(IDC_BUTTON4, &CDialogRobotCalib::OnBnClickedButton4)
	ON_BN_CLICKED(IDC_BUTTON5, &CDialogRobotCalib::OnBnClickedButton5)
	ON_BN_CLICKED(IDC_BUTTON6, &CDialogRobotCalib::OnBnClickedButton6)
	ON_BN_CLICKED(IDC_BUTTON7, &CDialogRobotCalib::OnBnClickedButton7)
	ON_BN_CLICKED(IDC_BUTTON8, &CDialogRobotCalib::OnBnClickedButton8)
	ON_BN_CLICKED(IDC_BUTTON9, &CDialogRobotCalib::OnBnClickedButton9)
	ON_BN_CLICKED(IDC_BUTTON12, &CDialogRobotCalib::OnBnClickedButton12)
	ON_BN_CLICKED(IDC_BUTTON13, &CDialogRobotCalib::OnBnClickedButton13)
	ON_BN_CLICKED(IDC_BUTTON10, &CDialogRobotCalib::OnBnClickedButton10)
	ON_BN_CLICKED(IDC_BUTTON11, &CDialogRobotCalib::OnBnClickedButton11)
	ON_BN_CLICKED(IDC_BUTTON14, &CDialogRobotCalib::OnBnClickedButton14)
	ON_BN_CLICKED(IDC_BUTTON15, &CDialogRobotCalib::OnBnClickedButton15)
END_MESSAGE_MAP()

BOOL CDialogRobotCalib::PreTranslateMessage( MSG* pMsg )
{
	if ((WM_SYSKEYDOWN == pMsg->message && VK_F4 == pMsg->wParam) || (pMsg->message == WM_KEYDOWN && pMsg->wParam == VK_ESCAPE)) { //屏蔽Alt + F4
		return TRUE;
	}

	return CDialogEx::PreTranslateMessage(pMsg);
}

BOOL CDialogRobotCalib::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	CRect rc;

	GetWindowRect(&rc);

	MoveWindow(m_pt.x, m_pt.y, rc.Width(), rc.Height());

	SetDlgItemInt(IDC_EDIT1, 20);

	SetDlgItemInt(IDC_EDIT2, 20);
	
	return TRUE;
}

bool CDialogRobotCalib::Excute()
{
	g_pRobot->RegisterCamera();

	switch (m_eCalib)
	{
	case emCalibUp:
		CalibrateUp();
		break;
	case emCalibDn:
		CalibrateDn();
		break;
	case emRobot:
		Robot();
		break;
	default:
		break;
	}

	return true;
}

bool CDialogRobotCalib::CalibrateDn()
{
	CString sRet;

	ROBOTPOINT stRobPnt;

	g_pRobot->DnCameraLightOn();

	double nOffX = 0, nOffY = 0;

	CImageFlow* pFlow = NULL;

	pFlow = g_pImageFlowCalibrateDn;

	CImageCalibrate* pCalib = NULL;

	pCalib = g_pImageCalibrateDn;

	// 坐标系标定 
	vector<double> vMx, vMy, vPx, vPy;

	for (int i = 0; i < 9; i++)
	{
		stRobPnt = *g_pRobot->m_mapRobotPoint["下相机标定位"];

		switch (i)
		{
		case 0:
			stRobPnt.x += 0;
			stRobPnt.y += 0;
			break;
		case 1:
			stRobPnt.x += -m_nStep;
			stRobPnt.y += 0;
			break;
		case 2:
			stRobPnt.x += -m_nStep;
			stRobPnt.y += m_nStep;
			break;
		case 3:
			stRobPnt.x += 0;
			stRobPnt.y += m_nStep;
			break;
		case 4:
			stRobPnt.x += m_nStep;
			stRobPnt.y += m_nStep;
			break;
		case 5:
			stRobPnt.x += m_nStep;
			stRobPnt.y += 0;
			break;
		case 6:
			stRobPnt.x += m_nStep;
			stRobPnt.y += -m_nStep;
			break;
		case 7:
			stRobPnt.x += 0;
			stRobPnt.y += -m_nStep;
			break;
		case 8:
			stRobPnt.x += -m_nStep;
			stRobPnt.y += -m_nStep;
			break;
		default:
			break;
		}

		g_pRobot->ProcPrepare(0);

		g_pRobot->PushImageFlow(pFlow, NULL, m_pImageWindow, "下相机标定", "", "", false, false, -1, false, 0);

		g_pRobot->ProcStart(0);

		ULONGLONG nTick = GetTickCount64();
		while (true)
		{
			sRet = g_pRobot->Move(stRobPnt);
			if (sRet != "OK") {
				AfxMessageBox("机械手运动失败");
				return false;
			}

			if (g_pRobot->IsInPos(stRobPnt) == "Yes") {
				break;
			}

			if (GetTickCount64() - nTick > 30000) {
				AfxMessageBox("机械手运动超时");
				return false;
			}

			Sleep(1);
		}

		Sleep(50);

		g_pCamera->Trigger("Dn");

		double nPx = 0, nPy = 0, nPr = 0;

		bool bStatus = false;

		nTick = GetTickCount64();
		while (true)
		{
			Sleep(1);

			if (GetTickCount64() - nTick > 5000) {
				AfxMessageBox("图像处理超时");
				return false;
			}

			sRet = pFlow->GetFinishFlag(bStatus);
			if (sRet != "OK") {
				AfxMessageBox("图像处理异常");
				return false;
			}

			bool bExistFlag = false;

			if (bStatus) {
				sRet = pFlow->GetResult(nPx, nPy, nPr, bExistFlag);
				if (sRet != "OK") {
					AfxMessageBox("图像处理异常");
					return false;
				}

				vMx.push_back(stRobPnt.x);
				vMy.push_back(stRobPnt.y);
				vPx.push_back(nPx);
				vPy.push_back(nPy);

				break;
			}
		}
	}

	pCalib->CalibrateCoordinate(vPx, vPy, vMx, vMy);

	vPx.clear();
	vPy.clear();

	for (int i = 0; i < 9; i++)
	{
		stRobPnt = *g_pRobot->m_mapRobotPoint["下相机标定位"];

		switch (i)
		{
		case 0:
			stRobPnt.r += -8;
			break;
		case 1:
			stRobPnt.r += -6;
			break;
		case 2:
			stRobPnt.r += -4;
			break;
		case 3:
			stRobPnt.r += -2;
			break;
		case 4:
			stRobPnt.r += 0;
			break;
		case 5:
			stRobPnt.r += 2;
			break;
		case 6:
			stRobPnt.r += 4;
			break;
		case 7:
			stRobPnt.r += 6;
			break;
		case 8:
			stRobPnt.r += 8;
			break;
		default:
			break;
		}

		g_pRobot->ProcPrepare(0);

		g_pRobot->PushImageFlow(pFlow, NULL, m_pImageWindow, "下相机标定", "", "", false, false, -1, false, 0);

		g_pRobot->ProcStart(0);

		ULONGLONG nTick = GetTickCount64();
		while (true)
		{
			sRet = g_pRobot->Move(stRobPnt);
			if (sRet != "OK") {
				AfxMessageBox("机械手运动失败");
				return false;
			}

			if (g_pRobot->IsInPos(stRobPnt) == "Yes") {
				break;
			}

			if (GetTickCount64() - nTick > 30000) {
				AfxMessageBox("机械手运动超时");
				return false;
			}

			Sleep(1);
		}

		Sleep(50);

		g_pCamera->Trigger("Dn");

		double nPx = 0, nPy = 0, nPr = 0;

		bool bStatus = false;

		nTick = GetTickCount64();
		while (true)
		{
			Sleep(1);

			if (GetTickCount64() - nTick > 5000) {
				AfxMessageBox("图像处理超时");
				return false;
			}

			sRet = pFlow->GetFinishFlag(bStatus);
			if (sRet != "OK") {
				AfxMessageBox("图像处理异常");
				return false;
			}

			bool bExistFlag = false;

			if (bStatus) {
				sRet = pFlow->GetResult(nPx, nPy, nPr, bExistFlag);
				if (sRet != "OK") {
					AfxMessageBox("图像处理异常");
					return false;
				}

				vPx.push_back(nPx);
				vPy.push_back(nPy);

				break;
			}
		}
	}

	g_pImageCalibrateDn->CalibrateRotate(vPx, vPy, nOffX, nOffY, g_pRobot->m_mapParam["下相机中心坐标X"]->D(), g_pRobot->m_mapParam["下相机中心坐标Y"]->D());

	AfxMessageBox("标定成功");

	return true;
}

bool CDialogRobotCalib::CalibrateUp()
{
	CString sRet;

	ROBOTPOINT stRobPnt;

	g_pRobot->UpCameraLightOn();

	double nOffX = 0, nOffY = 0;

	CImageFlow* pFlow = NULL;

	pFlow = g_pImageFlowCalibrateUp;

	CImageCalibrate* pCalib = NULL;

	pCalib = g_pImageCalibrateUp;

	// 坐标系标定 
	vector<double> vMx, vMy, vPx, vPy;

	for (int i = 0; i < 9; i++)
	{
		stRobPnt = *g_pRobot->m_mapRobotPoint["上相机标定位"];

		switch (i)
		{
		case 0:
			stRobPnt.x += 0;
			stRobPnt.y += 0;
			break;
		case 1:
			stRobPnt.x += -m_nStep;
			stRobPnt.y += 0;
			break;
		case 2:
			stRobPnt.x += -m_nStep;
			stRobPnt.y += m_nStep;
			break;
		case 3:
			stRobPnt.x += 0;
			stRobPnt.y += m_nStep;
			break;
		case 4:
			stRobPnt.x += m_nStep;
			stRobPnt.y += m_nStep;
			break;
		case 5:
			stRobPnt.x += m_nStep;
			stRobPnt.y += 0;
			break;
		case 6:
			stRobPnt.x += m_nStep;
			stRobPnt.y += -m_nStep;
			break;
		case 7:
			stRobPnt.x += 0;
			stRobPnt.y += -m_nStep;
			break;
		case 8:
			stRobPnt.x += -m_nStep;
			stRobPnt.y += -m_nStep;
			break;
		default:
			break;
		}

		g_pRobot->ProcPrepare(0);

		g_pRobot->PushImageFlow(pFlow, NULL, m_pImageWindow, "上相机标定", "", "", false, false, -1, false, 0);

		g_pRobot->ProcStart(0);

		ULONGLONG nTick = GetTickCount64();
		while (true)
		{
			sRet = g_pRobot->Move(stRobPnt);
			if (sRet != "OK") {
				AfxMessageBox("机械手运动失败");
				return false;
			}

			if (g_pRobot->IsInPos(stRobPnt) == "Yes") {
				break;
			}

			if (GetTickCount64() - nTick > 30000) {
				AfxMessageBox("机械手运动超时");
				return false;
			}

			Sleep(1);
		}

		Sleep(50);

		g_pCamera->Trigger("Up");

		double nPx = 0, nPy = 0, nPr = 0;

		bool bStatus = false;

		nTick = GetTickCount64();
		while (true)
		{
			Sleep(1);

			if (GetTickCount64() - nTick > 5000) {
				AfxMessageBox("图像处理超时");
				return false;
			}

			sRet = pFlow->GetFinishFlag(bStatus);
			if (sRet != "OK") {
				AfxMessageBox("图像处理异常");
				return false;
			}

			bool bExistFlag = false;

			if (bStatus) {
				sRet = pFlow->GetResult(nPx, nPy, nPr, bExistFlag);
				if (sRet != "OK") {
					AfxMessageBox("图像处理异常");
					return false;
				}

				vMx.push_back(stRobPnt.x);
				vMy.push_back(stRobPnt.y);
				vPx.push_back(nPx);
				vPy.push_back(nPy);

				break;
			}
		}
	}

	pCalib->CalibrateCoordinate(vPx, vPy, vMx, vMy);

	AfxMessageBox("标定成功");

	return true;
}

void WriteRobotResult(CString sResult)
{
	CString strDatePath;
	strDatePath = "D:\\LOG_ROBOT_DATA\\";

	CreatePath(strDatePath.GetBuffer());
	strDatePath.ReleaseBuffer();

	CString strFile;
	strFile = strDatePath + "data.csv";

	bool bFlag = false;

	CFileFind ff;
	if (!ff.FindFile(strFile)) {
		bFlag = true;
	}

	CStdioFile sf;
	sf.Open(strFile, CFile::modeCreate | CFile::modeNoTruncate | CFile::modeReadWrite | CFile::shareDenyNone);
	sf.SeekToEnd();

	if (bFlag) {
		sf.WriteString("时间,X,Y\n");
	}

	sf.WriteString(sResult);

	sf.Flush();
	sf.Close();
}

bool CDialogRobotCalib::Robot()
{
	CString sRet;

	ROBOTPOINT stRobPnt;

	g_pRobot->DnCameraLightOn();

	CImageFlow* pFlow = NULL;
	pFlow = g_pImageFlowRobot;

	for (int i=0; i<50; i++)
	{
		stRobPnt = *g_pRobot->m_mapRobotPoint["TRAY盘正取放位"];
		stRobPnt.z = g_pRobot->m_mapRobotPoint["空闲等待位"]->z;
		
		ULONGLONG nTick = GetTickCount64();
		while (true)
		{
			sRet = g_pRobot->Move(stRobPnt);
			if (sRet != "OK") {
				AfxMessageBox("机械手运动失败");
				return false;
			}

			if (g_pRobot->IsInPos(stRobPnt) == "Yes") {
				break;
			}

			if (GetTickCount64() - nTick > 30000) {
				AfxMessageBox("机械手运动超时");
				return false;
			}

			Sleep(1);
		}

		Sleep(500);

		stRobPnt = *g_pRobot->m_mapRobotPoint["下相机吸嘴1拍照位"];

		nTick = GetTickCount64();
		while (true)
		{
			sRet = g_pRobot->Move(stRobPnt);
			if (sRet != "OK") {
				AfxMessageBox("机械手运动失败");
				return false;
			}

			if (g_pRobot->IsInPos(stRobPnt) == "Yes") {
				break;
			}

			if (GetTickCount64() - nTick > 30000) {
				AfxMessageBox("机械手运动超时");
				return false;
			}

			Sleep(50);
		}

		g_pRobot->ProcPrepare(0);
		g_pRobot->PushImageFlow(pFlow, NULL, m_pImageWindow, "下相机标定", "", "", false, false, -1, false, 0);
		g_pRobot->ProcStart(0);
		g_pCamera->Trigger("Dn");

		double nPx = 0, nPy = 0, nPr = 0;

		bool bStatus = false;

		nTick = GetTickCount64();
		while (true)
		{
			Sleep(1);

			if (GetTickCount64() - nTick > 5000) {
				AfxMessageBox("图像处理超时");
				return false;
			}

			sRet = pFlow->GetFinishFlag(bStatus);
			if (sRet != "OK") {
				AfxMessageBox("图像处理异常");
				return false;
			}

			bool bExistFlag = false;

			if (bStatus) {
				sRet = pFlow->GetResult(nPx, nPy, nPr, bExistFlag);
				if (sRet != "OK") {
					AfxMessageBox("图像处理异常");
					return false;
				}
				
				CTime t = CTime::GetCurrentTime();
				sRet.Format("'%04d%02d%02d%02d%02d%02d,%.f,%.f\n", t.GetYear(), t.GetMonth(), t.GetDay(), t.GetHour(), t.GetMinute(), t.GetSecond(), nPx, nPy);
				WriteRobotResult(sRet);

				break;
			}
		}
	}

	return true;
}

// CDialogRobotCalib 消息处理程序

void CDialogRobotCalib::OnBnClickedButton1()
{
	PREMISSION_CTRL();

	g_pRobot->UpCameraLightOn();

	vector<CImageFlow*> vImageFlow;

	vImageFlow.push_back(g_pImageFlowCalibrateUp);		

	yzBase::CImageProcessWindow* pImageProcess = yzBase::CImageProcessWindow::CreateInstance(&vImageFlow);
	pImageProcess->Show(CLogicMgr::m_mapThread["Robot"].pThread->GetStatus() == emRun);

	delete pImageProcess;

	g_pRobot->UpCameraLightOff();

	g_pRobot->RegisterCamera();
}

void CDialogRobotCalib::OnBnClickedButton2()
{
	PREMISSION_CTRL();

	if (!isThreadFinish()) {
		AfxMessageBox("已存在流程运行！");
		return;
	}

	UpdateData();

	CString str;

	GetDlgItemText(IDC_EDIT1, str);

	m_nStep = atof(str);

	if (m_nStep < 0) {
		m_nStep = 5;
		SetDlgItemInt(IDC_EDIT1, 5);
	}

	m_eCalib = emCalibUp;

	Start();
}

void CDialogRobotCalib::OnBnClickedButton3()
{
	PREMISSION_CTRL();

	g_pRobot->DnCameraLightOn();

	vector<CImageFlow*> vImageFlow;

	vImageFlow.push_back(g_pImageFlowCalibrateDn);		

	yzBase::CImageProcessWindow* pImageProcess = yzBase::CImageProcessWindow::CreateInstance(&vImageFlow);
	pImageProcess->Show(CLogicMgr::m_mapThread["Robot"].pThread->GetStatus() == emRun);

	delete pImageProcess;

	g_pRobot->DnCameraLightOff();

	g_pRobot->RegisterCamera();
}

void CDialogRobotCalib::OnBnClickedButton4()
{
	PREMISSION_CTRL();

	if (!isThreadFinish()) {
		AfxMessageBox("已存在流程运行！");
		return;
	}

	UpdateData();

	CString str;

	GetDlgItemText(IDC_EDIT2, str);

	m_nStep = atof(str);

	if (m_nStep < 0) {
		m_nStep = 5;
		SetDlgItemInt(IDC_EDIT2, 5);
	}

	m_eCalib = emCalibDn;

	Start();
}

void CDialogRobotCalib::OnBnClickedButton5()
{
	PREMISSION_CTRL();

	g_pRobot->TrayCameraLightOn();

	vector<CImageFlow*> vImageFlow;

	vImageFlow.push_back(g_pImageFlowCalibrateTray);		

	yzBase::CImageProcessWindow* pImageProcess = yzBase::CImageProcessWindow::CreateInstance(&vImageFlow);
	pImageProcess->Show(CLogicMgr::m_mapThread["Robot"].pThread->GetStatus() == emRun);

	delete pImageProcess;

	g_pRobot->TrayCameraLightOff();

	g_pRobot->RegisterCamera();
}

void CDialogRobotCalib::OnBnClickedButton6()
{
	PREMISSION_CTRL();

	vector<double> vMx, vMy, vPx, vPy;

	g_pRobot->RegisterCamera();
	g_pRobot->TrayCameraLightOn();

	g_pRobot->ProcPrepare(0);
	g_pRobot->PushImageFlow(g_pImageFlowCalibrateTray, NULL, m_pImageWindow, "TRAY相机标定", "", "", false, false, -1, false, 0);
	g_pRobot->ProcStart(0);

	g_pCamera->Trigger("Tray");

	DWORD nTick = 0;
	nTick = GetTickCount();
	while (true)
	{
		if (GetTickCount() - nTick > 3000) {
			AfxMessageBox("图像处理超时");
			return;
		}

		bool bFlag = false;

		g_pImageFlowCalibrateTray->GetFinishFlag(bFlag);
		if (bFlag) {
			break;
		}
	}

	int nTestIndex = 2;

	double nPixelX = 0, nPixelY = 0, nPixelR = 0, nTestPntX = 0, nTestPntY = 0;

	ROBOTPOINT stRobPnt;

	CString sPosition;
	for (int i=0; i<4; i++)
	{
		sPosition.Format("TRAY相机九点标定位%d", i + 1);

		stRobPnt = *g_pRobot->m_mapRobotPoint[sPosition];

		vMx.push_back(stRobPnt.x);
		vMy.push_back(stRobPnt.y);

		bool bExistFlag = false;

		g_pImageFlowCalibrateTray->GetResult(nPixelX, nPixelY, nPixelR, bExistFlag, 0, i / 2, i % 2);

		vPx.push_back(nPixelX);
		vPy.push_back(nPixelY);

		if (i == nTestIndex) {
			nTestPntX = nPixelX;
			nTestPntY = nPixelY;
		}
	}

	g_pImageCalibrateTray->CalibrateCoordinate(vPx, vPy, vMx, vMy);

	g_pRobot->TrayCameraLightOff();

	double nMachineX = 0, nMachineY = 0;

	g_pImageCalibrateTray->TransToMachine(nTestPntX, nTestPntY, nMachineX, nMachineY);

	stRobPnt = *g_pRobot->m_mapRobotPoint["空闲等待位"];

	stRobPnt.x = nMachineX;
	stRobPnt.y = nMachineY;

	g_pRobot->Move(stRobPnt);

	AfxMessageBox("标定成功");
}

void CDialogRobotCalib::OnBnClickedButton7()
{
	PREMISSION_CTRL();

	double nCalibMachineX = 0, nCalibMachineY = 0, nCalibCenterPixelX = 0, nCalibCenterPixelY = 0;

	g_pImageCalibrateDn->GetCalibrateCoordinate(nCalibMachineX, nCalibMachineY);

	g_pImageCalibrateDn->GetCalibrateCenter(nCalibCenterPixelX, nCalibCenterPixelY);

	double nCenterOffX = 0, nCenterOffY = 0, nCalibCenterMachineX = 0, nCalibCenterMachineY = 0, nCamCenterMachineX = 0, nCamCenterMachineY = 0;

	g_pImageCalibrateDn->TransToMachine(nCalibCenterPixelX, nCalibCenterPixelY, nCalibCenterMachineX, nCalibCenterMachineY);
	g_pImageCalibrateDn->TransToMachine(g_pRobot->m_mapParam["下相机中心坐标X"]->D(), g_pRobot->m_mapParam["下相机中心坐标Y"]->D(), nCamCenterMachineX, nCamCenterMachineY);

	VAR_ROBOT("吸嘴1基于旋转中心坐标X") = nCamCenterMachineX - nCalibCenterMachineX + nCalibMachineX - g_pRobot->m_mapRobotPoint["下相机吸嘴1拍照位"]->x;
	VAR_ROBOT("吸嘴1基于旋转中心坐标Y") = nCamCenterMachineY - nCalibCenterMachineY + nCalibMachineY - g_pRobot->m_mapRobotPoint["下相机吸嘴1拍照位"]->y;

	VAR_ROBOT("吸嘴2基于旋转中心坐标X") = nCamCenterMachineX - nCalibCenterMachineX + nCalibMachineX - g_pRobot->m_mapRobotPoint["下相机吸嘴2拍照位"]->x;
	VAR_ROBOT("吸嘴2基于旋转中心坐标Y") = nCamCenterMachineY - nCalibCenterMachineY + nCalibMachineY - g_pRobot->m_mapRobotPoint["下相机吸嘴2拍照位"]->y;

	VAR_ROBOT("吸嘴3基于旋转中心坐标X") = nCamCenterMachineX - nCalibCenterMachineX + nCalibMachineX - g_pRobot->m_mapRobotPoint["下相机吸嘴3拍照位"]->x;
	VAR_ROBOT("吸嘴3基于旋转中心坐标Y") = nCamCenterMachineY - nCalibCenterMachineY + nCalibMachineY - g_pRobot->m_mapRobotPoint["下相机吸嘴3拍照位"]->y;

	VAR_ROBOT("吸嘴4基于旋转中心坐标X") = nCamCenterMachineX - nCalibCenterMachineX + nCalibMachineX - g_pRobot->m_mapRobotPoint["下相机吸嘴4拍照位"]->x;
	VAR_ROBOT("吸嘴4基于旋转中心坐标Y") = nCamCenterMachineY - nCalibCenterMachineY + nCalibMachineY - g_pRobot->m_mapRobotPoint["下相机吸嘴4拍照位"]->y;

	g_pRobot->Save();

	AfxMessageBox("标定成功");
}

void CDialogRobotCalib::OnBnClickedButton8()
{
	PREMISSION_CTRL();

	double nCalibMachineX = 0, nCalibMachineY = 0, nCalibCenterPixelX = 0, nCalibCenterPixelY = 0;

	g_pImageCalibrateDn->GetCalibrateCoordinate(nCalibMachineX, nCalibMachineY);

	g_pImageCalibrateDn->GetCalibrateCenter(nCalibCenterPixelX, nCalibCenterPixelY);

	double nCenterOffX = 0, nCenterOffY = 0, nCalibCenterMachineX = 0, nCalibCenterMachineY = 0, nCamCenterMachineX = 0, nCamCenterMachineY = 0;

	g_pImageCalibrateDn->TransToMachine(nCalibCenterPixelX, nCalibCenterPixelY, nCalibCenterMachineX, nCalibCenterMachineY);
	g_pImageCalibrateDn->TransToMachine(g_pRobot->m_mapParam["下相机中心坐标X"]->D(), g_pRobot->m_mapParam["下相机中心坐标Y"]->D(), nCamCenterMachineX, nCamCenterMachineY);

	VAR_ROBOT("上相机中心基于旋转中心坐标X") = nCamCenterMachineX - nCalibCenterMachineX + g_pRobot->m_mapRobotPoint["上相机相机距离标定标定块设置位"]->x - g_pRobot->m_mapRobotPoint["上相机相机距离标定相机中心位"]->x;
	VAR_ROBOT("上相机中心基于旋转中心坐标Y") = nCamCenterMachineY - nCalibCenterMachineY + g_pRobot->m_mapRobotPoint["上相机相机距离标定标定块设置位"]->y - g_pRobot->m_mapRobotPoint["上相机相机距离标定相机中心位"]->y;
	
	g_pRobot->Save();

	AfxMessageBox("标定成功");
}

void CDialogRobotCalib::OnBnClickedButton9()
{
	PREMISSION_CTRL();

	VAR_ROBOT("治具中心间距X") = g_pRobot->m_mapRobotPoint["B轨右上角上相机中心位"]->x - g_pRobot->m_mapRobotPoint["B轨左下角上相机中心位"]->x;

	VAR_ROBOT("治具中心间距Y") = g_pRobot->m_mapRobotPoint["B轨左下角上相机中心位"]->y - g_pRobot->m_mapRobotPoint["B轨右上角上相机中心位"]->y;

	g_pRobot->Save();

	AfxMessageBox("标定成功");
}

void CDialogRobotCalib::OnBnClickedButton12()
{
	PREMISSION_CTRL();

	g_pRobot->UpCameraLightOn();

	vector<CImageFlow*> vImageFlow;

	vImageFlow.push_back(g_pImageFlowCalibrateBoardUp);		

	yzBase::CImageProcessWindow* pImageProcess = yzBase::CImageProcessWindow::CreateInstance(&vImageFlow);
	pImageProcess->Show(CLogicMgr::m_mapThread["Robot"].pThread->GetStatus() == emRun);

	delete pImageProcess;

	g_pRobot->UpCameraLightOff();

	g_pRobot->RegisterCamera();
}

void CDialogRobotCalib::OnBnClickedButton13()
{
	PREMISSION_CTRL();

	g_pRobot->DnCameraLightOn();

	vector<CImageFlow*> vImageFlow;

	vImageFlow.push_back(g_pImageFlowCalibrateBoardDn);		

	yzBase::CImageProcessWindow* pImageProcess = yzBase::CImageProcessWindow::CreateInstance(&vImageFlow);
	pImageProcess->Show(CLogicMgr::m_mapThread["Robot"].pThread->GetStatus() == emRun);

	delete pImageProcess;

	g_pRobot->DnCameraLightOff();

	g_pRobot->RegisterCamera();
}

void CDialogRobotCalib::OnBnClickedButton10()
{
	PREMISSION_CTRL();

	if (!isThreadFinish()) {
		AfxMessageBox("已存在流程运行！");
		return;
	}

	UpdateData();

// 	CString str;
// 
// 	GetDlgItemText(IDC_EDIT1, str);
// 
// 	m_nStep = atof(str);
// 
// 	if (m_nStep < 0) {
// 		m_nStep = 5;
// 		SetDlgItemInt(IDC_EDIT1, 5);
// 	}
// 
// 	m_eCalib = emCalibBoardUp;
// 
// 	Start();
}

void CDialogRobotCalib::OnBnClickedButton11()
{
	PREMISSION_CTRL();

	if (!isThreadFinish()) {
		AfxMessageBox("已存在流程运行！");
		return;
	}

// 	UpdateData();
// 
// 	CString str;
// 
// 	GetDlgItemText(IDC_EDIT1, str);
// 
// 	m_nStep = atof(str);
// 
// 	if (m_nStep < 0) {
// 		m_nStep = 5;
// 		SetDlgItemInt(IDC_EDIT1, 5);
// 	}
// 
// 	m_eCalib = emCalibBoardDn;
// 
// 	Start();
}

void CDialogRobotCalib::OnBnClickedButton15()
{
	PREMISSION_CTRL();

	g_pRobot->DnCameraLightOn();

	vector<CImageFlow*> vImageFlow;

	vImageFlow.push_back(g_pImageFlowRobot);		

	yzBase::CImageProcessWindow* pImageProcess = yzBase::CImageProcessWindow::CreateInstance(&vImageFlow);
	pImageProcess->Show(CLogicMgr::m_mapThread["Robot"].pThread->GetStatus() == emRun);

	delete pImageProcess;

	g_pRobot->DnCameraLightOff();

	g_pRobot->RegisterCamera();
}

void CDialogRobotCalib::OnBnClickedButton14()
{
	PREMISSION_CTRL();

	if (!isThreadFinish()) {
		AfxMessageBox("已存在流程运行！");
		return;
	}

	m_eCalib = emRobot;

	Start();
}
