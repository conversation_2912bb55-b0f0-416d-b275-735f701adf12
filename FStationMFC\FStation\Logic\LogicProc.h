﻿#pragma once

#include "ThreadBase.h"
using namespace yzBase;

class CLogicProc : public CThreadBase
{
public:
	CLogicProc(int nIndex);         // 构造函数：初始化图像处理逻辑类，nIndex为处理索引
	virtual ~CLogicProc();          // 析构函数：清理图像处理逻辑类资源

public:
	// 基础控制函数
	EnumStatus OnSafeCheck();       // 安全检查：检查图像处理系统安全状态
	EnumStatus OnStart();           // 启动控制：初始化图像处理系统
	EnumStatus OnPause();           // 暂停控制：暂停图像处理操作
	EnumStatus OnResume();          // 恢复控制：从暂停状态恢复图像处理操作
	EnumStatus OnStop();            // 停止控制：停止图像处理系统

public:
	// 主运行函数
	CStatus OnRun();                // 主状态机运行函数：协调图像处理工作流程

	// ========== 图像处理流程 (OnProc00-OnProc03) ==========
	CStatus OnProc00();             // 图像处理等待：等待图像处理开始事件
	CStatus OnProc01();             // 图像处理检查：检查图像处理条件和状态
	CStatus OnProc02();             // 图像处理执行：执行图像处理算法
	CStatus OnProc02_0();           // 图像处理执行子流程：执行图像处理详细操作
	CStatus OnProc02_1();           // 图像处理执行验证：验证图像处理执行结果
	CStatus OnProc03();             // 图像处理完成：完成图像处理，返回结果

private:
	map<CString, int>	m_mapIndex;     // 索引映射表，记录各种处理索引
	int					m_nIndex;       // 处理索引号，标识当前处理通道
};