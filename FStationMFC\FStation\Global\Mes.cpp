﻿#include "stdafx.h"

#include "Mes.h"
#include "Sys.h"

#include "json/json.h"

#define MES_LOG() { \
	SYSTEMTIME t = { 0 }; \
	GetLocalTime(&t); \
	\
	CString strTime; \
	strTime.Format("%02d:%02d:%02d:%03d", t.wHour, t.wMinute, t.wSecond, t.wMilliseconds); \
	\
	CString strLogToRecord; \
	strLogToRecord.Format("%s - %s - %s\n", strTime, g_strLogLevel[emLogLevelNormal], sSendBuff); \
	\
	strLogToRecord.Replace("\r", " "); \
	strLogToRecord.Replace("\n", " "); \
	\
	g_pDatLog->UpdateLog(strLogToRecord); \
}


// 编解码转换表
unsigned char CBASE64::s_encTable[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
unsigned char CBASE64::s_decTable[] = {
	0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
	0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
	0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x3E,0xFF,0xFF,0xFF,0x3F,
	0x34,0x35,0x36,0x37,0x38,0x39,0x3A,0x3B,0x3C,0x3D,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
	0xFF,0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0A,0x0B,0x0C,0x0D,0x0E,
	0x0F,0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,0x18,0x19,0xFF,0xFF,0xFF,0xFF,0xFF,
	0xFF,0x1A,0x1B,0x1C,0x1D,0x1E,0x1F,0x20,0x21,0x22,0x23,0x24,0x25,0x26,0x27,0x28,
	0x29,0x2A,0x2B,0x2C,0x2D,0x2E,0x2F,0x30,0x31,0x32,0x33,0xFF,0xFF,0xFF,0xFF,0xFF
};

// 执行BASE64编码操作
std::string CBASE64::encode(const std::string& str)
{
	std::string strEncode;
	strEncode.resize( (str.size() / 3 + 1) * 4 );
	strEncode.resize( __encode((unsigned char*)strEncode.data(), (const unsigned char*)str.data(), str.size()) );
	return strEncode;
}

// 执行BASE64解码操作
std::string CBASE64::decode(const std::string& str)
{
	std::string strDecode;
	strDecode.resize( str.size() );
	strDecode.resize( __decode((unsigned char*)strDecode.data(), (const unsigned char*)str.data(), str.size()) );
	return strDecode;
}

// 分组编码
int CBASE64::__encode(unsigned char* pDest, const unsigned char* pSrc, size_t nSrcLen)
{
	unsigned char * dst = pDest;
	const unsigned char * src = pSrc;
	size_t len = nSrcLen;

	unsigned char * odst = dst;
	unsigned long l = 0, m = 0, n = 0;

	// 循环处理分组
	size_t off = 0;
	for ( ; off + 3 <= len; off += 3)
	{
		l = *src++;
		m = *src++;
		n = *src++;

		*dst++ = s_encTable[ (l >> 2) & 0x3F ];
		*dst++ = s_encTable[ ((l << 4) & 0x30) | ((m >> 4) & 0x0F) ];
		*dst++ = s_encTable[ ((m << 2) & 0x3C) | ((n >> 6) & 0x03) ];
		*dst++ = s_encTable[ n & 0x3F ];
	}

	// 处理余下的2个字节
	if (off + 2 <= len)
	{
		l = *src++;
		m = *src++;

		*dst++ = s_encTable[ (l >> 2) & 0x3F ];
		*dst++ = s_encTable[ ((l << 4) & 0x30) | ((m >> 4) & 0x0F) ];
		*dst++ = s_encTable[ ((m << 2) & 0x3C) ];
		*dst++ = '=';
	}

	// 处理余下的1个字节
	else if (off + 1 <= len)
	{
		l = *src++;

		*dst++ = s_encTable[ (l >> 2) & 0x3F ];
		*dst++ = s_encTable[ ((l << 4) & 0x30) ];
		*dst++ = '=';
		*dst++ = '=';
	}

	return (int)(dst - odst);
}

// 分组解码
int CBASE64::__decode(unsigned char* pDest, const unsigned char* pSrc, size_t nSrcLen)
{
	unsigned char * dst = pDest;
	const unsigned char * src = pSrc;
	size_t len = nSrcLen;

	unsigned char * odst = dst;
	unsigned long l = 0, m = 0, n = 0, o = 0;

	// 循环处理分组
	size_t off = 0;
	for (; off + 4 <= len; off += 4)
	{
		if ( (src[0] > 0x7F) || (src[1] > 0x7F) || (src[2] > 0x7F) || (src[3] > 0x7F) ) return (int)(dst - odst);
		if ( (src[0] == '=') || (src[1] == '=') || (src[2] == '=') || (src[3] == '=') ) break;

		l = s_decTable[*src++];
		m = s_decTable[*src++];
		n = s_decTable[*src++];
		o = s_decTable[*src++];

		*dst++ = (unsigned char)( ((l << 2) & 0xFC) | ((m >> 4) & 0x03) );
		*dst++ = (unsigned char)( ((m << 4) & 0xF0) | ((n >> 2) & 0x0F) );
		*dst++ = (unsigned char)( ((n << 6) & 0xC0) | (o & 0x3F));
	}

	// 处理余下的3个字节
	if (off + 3 <= len)
	{
		if ( (src[0] != '=') && (src[1] != '=') )
		{
			l = s_decTable[*src++];
			m = s_decTable[*src++];

			*dst++ = (unsigned char)( ((l << 2) & 0xFC) | ((m >> 4) & 0x03) );
		}

		if ( (src[2] != '=') )
		{
			n = s_decTable[*src++];

			*dst++ = (unsigned char)( ((m << 4) & 0xF0) | ((n >> 2) & 0x0F) );
		}
	}

	// 处理余下的两个字节
	else if (off + 2 <= len)
	{
		if ( (src[0] != '=') && (src[1] != '=') )
		{
			l = s_decTable[*src++];
			m = s_decTable[*src++];

			*dst++ = (unsigned char)( ((l << 2) & 0xFC) | ((m >> 4) & 0x03) );
		}
	}

	return (int)(dst - odst);
}

static bool IsJsonData(std::string strData)
{
	if (strData[0] != '{')
		return false;

	int num = 1;
	for (int i=1; i<strData.length(); ++i)
	{
		if (strData[i] == '{')
		{
			++num;
		}
		else if (strData[i] == '}')
		{
			--num;
		}

		if (num == 0)
		{
			return true;
		}
	}

	return false;
}

void UTF8ToGBK(const char* cUtf8, char* cGbk)
{
	int len = MultiByteToWideChar(CP_UTF8, 0, cUtf8, -1, NULL, 0);
	wchar_t* wstr = new wchar_t[len + 1];
	memset(wstr, 0, len+1);
	MultiByteToWideChar(CP_UTF8, 0, cUtf8, -1, wstr, len);

	len = WideCharToMultiByte(CP_ACP, 0, wstr, -1, NULL, 0, NULL, NULL);
	WideCharToMultiByte(CP_ACP, 0, wstr, -1, cGbk, len, NULL, NULL);

	delete[] wstr;
}

bool CMes::HttpPost(CString sIp, int nPort, CString sCode, CString sUserName, CString sPassword, CString sFactory, CString *pStrRecv)
{
	CSockBase* pSock = CSockBase::CreateClient();

	bool bFlag = false;
	for (int i=0; i<sIp.GetLength(); i++)
	{
		if (sIp.GetAt(i) != '.' && (sIp.GetAt(i) < '0' || sIp.GetAt(i) > '9')) {
			bFlag  = true;
			break;
		}
	}

	if (bFlag) {
		CString sHostName = sIp;
		sIp.Empty();

		struct hostent* pHost = NULL;
		pHost = gethostbyname(sHostName);

		for(int i = 0; pHost!= NULL && pHost->h_addr_list[i] != NULL; i++)
		{
			CString str;

			for(int j = 0; j < pHost->h_length; j++)
			{
				CString addr;

				if(j > 0)
					str += ".";

				addr.Format("%u", (unsigned int)((unsigned char*)pHost->h_addr_list[i])[j]);
				str += addr;
			}

			if (!str.IsEmpty()) {
				sIp = str;
			}
		}
	}

	if (sIp.IsEmpty()) {
		*pStrRecv = "IP地址解析失败";
		return false;
	}

	do 
	{
		if (pSock->InitClient(sIp.GetBuffer(), 80)) {
			break;
		}

		if (pSock->InitClient(sIp.GetBuffer(), 8080)) {
			break;
		}

		if (pSock->InitClient(sIp.GetBuffer(), 3128)) {
			break;
		}

		if (pSock->InitClient(sIp.GetBuffer(), 8081)) {
			break;
		}

		if (pSock->InitClient(sIp.GetBuffer(), 9098)) {
			break;
		}

		TRACE("连接服务器失败\n");
		
		*pStrRecv = "MES通讯异常";

		return false;

	} while (false);

	char sUri[1024] = { 0 };
	sprintf_s(sUri, 1024, "http://%s:%d/mesplus/service/CwipPcbaGlueCheck.json", sIp.GetBuffer(), nPort);

	char sContent[1024] = { 0 };
	sprintf_s(sContent, 1024, "{\n\"pcba\":\"%s\"\n}", sCode.GetBuffer());

	char sUser[1024] = { 0 };
	sprintf_s(sUser, 1024, "%s@%s:%s", sUserName.GetBuffer(), sFactory.GetBuffer(), sPassword.GetBuffer());

	string temp1, temp2(sUser);
	temp1 = CBASE64::encode(temp2);

	char sAuthorization[1024] = { 0 };
	sprintf_s(sAuthorization, 1024, "Authorization:Basic %s\r\n", temp1.c_str());

	char content_header[1024] = { 0 };
	sprintf_s(content_header, 1024, "Content-Length: %d\r\n", strlen(sContent));

	char send_str[1024 * 10] = { 0 };

	//头信息
	strcat_s(send_str, "POST ");
	strcat_s(send_str, sUri);
	strcat_s(send_str, " HTTP/1.1\r\n");
	strcat_s(send_str, "Host: ");
	strcat_s(send_str, sIp.GetBuffer());
	strcat_s(send_str, "\r\n");


	strcat_s(send_str, sAuthorization);
	strcat_s(send_str, "Content-Type:application/json;charset=UTF-8\r\n");
	strcat_s(send_str, "Accept-Language:zh_CN\r\n");
	strcat_s(send_str, "User-Agent:IE or Chrome\r\n");
	strcat_s(send_str, "Accept: text/html, application/xhtml+xml, image/jxr, */*\r\n");
	strcat_s(send_str, "Connection: keep-alive\r\n");
	strcat_s(send_str, content_header);
//  strcat_s(send_str, "Referer: http://www.colibri.com.cn");

    //内容信息
    strcat_s(send_str, "\r\n");
    strcat_s(send_str, sContent);

	CString sSendBuff(send_str);

	MES_LOG();

	pSock->SendCmd(send_str, (int)strlen(send_str));

	//获取返回信息
	char buffRecv[10240] = { 0 };
	char buffRecvUtf[10240] = { 0 };
	int  nRecvLen = 10240;

	pSock->GetRecv(buffRecvUtf, nRecvLen, VAR_MACHINE_I("MES响应超时时间"));

	bFlag = false;

	UTF8ToGBK(buffRecvUtf, buffRecv);
	
	sSendBuff.Empty();
	sSendBuff = buffRecv;
	MES_LOG();

	CString sRecv(buffRecv);

	int nIndex = sRecv.Find("\r\n\r\n");
	if (nIndex >= 0) {
		sRecv = sRecv.Mid(nIndex + strlen("\r\n\r\n"));
	}

	if (pStrRecv != NULL) {
		*pStrRecv = sRecv;
	}
	
	do
	{
		if (nRecvLen <= 0) {
			*pStrRecv = "MES回传数据为空";
			break;
		}
		
		if (!IsJsonData(sRecv.GetBuffer())) {
			*pStrRecv = "MES回传数据格式不正确";
			break;
		}
		
		if (nRecvLen > 0 && sRecv.Find(sCode) > 0 && sRecv.Find("检查通过") > 0) {
			bFlag = true;
		}
	}
	while (false);

	pSock->CloseSocket();

	delete pSock;

	return bFlag;
}

bool CMes::HttpPostPassStation(CString sIp, int nPort, CString sCode, CString sLineId, CString sOper, CString sUserName, CString sPassword, CString sFactory, CString *pStrRecv)
{
	CSockBase* pSock = CSockBase::CreateClient();

	bool bFlag = false;
	for (int i=0; i<sIp.GetLength(); i++)
	{
		if (sIp.GetAt(i) != '.' && (sIp.GetAt(i) < '0' || sIp.GetAt(i) > '9')) {
			bFlag  = true;
			break;
		}
	}

	if (bFlag) {
		CString sHostName = sIp;
		sIp.Empty();

		struct hostent* pHost = NULL;
		pHost = gethostbyname(sHostName);

		for(int i = 0; pHost!= NULL && pHost->h_addr_list[i] != NULL; i++)
		{
			CString str;

			for(int j = 0; j < pHost->h_length; j++)
			{
				CString addr;

				if(j > 0)
					str += ".";

				addr.Format("%u", (unsigned int)((unsigned char*)pHost->h_addr_list[i])[j]);
				str += addr;
			}

			if (!str.IsEmpty()) {
				sIp = str;
			}
		}
	}

	if (sIp.IsEmpty()) {
		*pStrRecv = "IP地址解析失败";
		return false;
	}

	do 
	{
		if (pSock->InitClient(sIp.GetBuffer(), 80)) {
			break;
		}

		if (pSock->InitClient(sIp.GetBuffer(), 8080)) {
			break;
		}

		if (pSock->InitClient(sIp.GetBuffer(), 3128)) {
			break;
		}

		if (pSock->InitClient(sIp.GetBuffer(), 8081)) {
			break;
		}

		if (pSock->InitClient(sIp.GetBuffer(), 9098)) {
			break;
		}

		REPORT("连接服务器失败", emLogLevelError);
		
		*pStrRecv = "MES通讯异常";

		return false;

	} while (false);


	char sUri[1024] = { 0 };
	sprintf_s(sUri, 1024, "http://%s/mesplus/service/CwipBakeAutoStation.json", sIp.GetBuffer());
	
	Json::Value root;
	root["pcbaId"] = Json::Value(sCode.GetBuffer());
	root["lineId"] = Json::Value(sLineId.GetBuffer());
	root["oper"] = Json::Value(sOper.GetBuffer());

	Json::FastWriter fastwriter;

	string sTemp;
	sTemp = fastwriter.write(root);

	char sContent[1024] = { 0 };
	sprintf_s(sContent, 1024, "%s", sTemp.c_str());

	char sUser[1024] = { 0 };
	sprintf_s(sUser, 1024, "%s@%s:%s", sUserName.GetBuffer(), sFactory.GetBuffer(), sPassword.GetBuffer());

	string temp1, temp2(sUser);
	temp1 = CBASE64::encode(temp2);

	char sAuthorization[1024] = { 0 };
	sprintf_s(sAuthorization, 1024, "Authorization:Basic %s\r\n", temp1.c_str());

	char content_header[1024] = { 0 };
	sprintf_s(content_header, 1024, "Content-Length: %d\r\n", strlen(sContent));

	char send_str[1024 * 10] = { 0 };

	//头信息
	strcat_s(send_str, "POST ");
	strcat_s(send_str, sUri);
	strcat_s(send_str, " HTTP/1.1\r\n");
	strcat_s(send_str, "Host: ");
	strcat_s(send_str, sIp.GetBuffer());
	strcat_s(send_str, "\r\n");


	strcat_s(send_str, sAuthorization);
	strcat_s(send_str, "Content-Type:application/json;charset=UTF-8\r\n");
	strcat_s(send_str, "Accept-Language:zh_CN\r\n");
	strcat_s(send_str, "User-Agent:IE or Chrome\r\n");
	strcat_s(send_str, "Accept: text/html, application/xhtml+xml, image/jxr, */*\r\n");
	strcat_s(send_str, "Connection: keep-alive\r\n");
	strcat_s(send_str, content_header);
//  strcat_s(send_str, "Referer: http://www.colibri.com.cn");

    //内容信息
    strcat_s(send_str, "\r\n");
    strcat_s(send_str, sContent);

	CString sSendBuff(send_str);

	MES_LOG();

	pSock->SendCmd(send_str, (int)strlen(send_str));

	//获取返回信息
	char buffRecv[10240] = { 0 };
	char buffRecvUtf[10240] = { 0 };
	int  nRecvLen = 10240;

	pSock->GetRecv(buffRecvUtf, nRecvLen, VAR_MACHINE_I("MES响应超时时间"));

	bFlag = false;

	UTF8ToGBK(buffRecvUtf, buffRecv);
	
	sSendBuff.Empty();
	sSendBuff = buffRecv;
	MES_LOG();

	CString sRecv(buffRecv);

	int nIndex = sRecv.Find("\r\n\r\n");
	if (nIndex >= 0) {
		sRecv = sRecv.Mid(nIndex + strlen("\r\n\r\n"));
	}

	if (pStrRecv != NULL) {
		*pStrRecv = sRecv;
	}
	
	do
	{
		if (nRecvLen <= 0) {
			*pStrRecv = "MES回传数据为空";
			break;
		}
		
		if (!IsJsonData(sRecv.GetBuffer())) {
			*pStrRecv = "MES回传数据格式不正确";
			break;
		}

		if (nRecvLen > 0 && sRecv.Find("成功") > 0) {
			bFlag = true;
		}
	}
	while (false);

	pSock->CloseSocket();

	delete pSock;

	return bFlag;
}

bool CMes::HttpPostFixture(CString sIp, int nPort, CString sFixtureCode, CString sPcbaCode, CString sLineId, CString sStation, CString sOper, CString sUserName, CString sPassword, CString sFactory, CString *pStrRecv)
{
	CSockBase* pSock = CSockBase::CreateClient();

	bool bFlag = false;
	for (int i=0; i<sIp.GetLength(); i++)
	{
		if (sIp.GetAt(i) != '.' && (sIp.GetAt(i) < '0' || sIp.GetAt(i) > '9')) {
			bFlag  = true;
			break;
		}
	}

	if (bFlag) {
		CString sHostName = sIp;
		sIp.Empty();

		struct hostent* pHost = NULL;
		pHost = gethostbyname(sHostName);

		for(int i = 0; pHost!= NULL && pHost->h_addr_list[i] != NULL; i++)
		{
			CString str;

			for(int j = 0; j < pHost->h_length; j++)
			{
				CString addr;

				if(j > 0)
					str += ".";

				addr.Format("%u", (unsigned int)((unsigned char*)pHost->h_addr_list[i])[j]);
				str += addr;
			}

			if (!str.IsEmpty()) {
				sIp = str;
			}
		}
	}

	if (sIp.IsEmpty()) {
		*pStrRecv = "IP地址解析失败";
		return false;
	}

	do
	{
		if (pSock->InitClient(sIp.GetBuffer(), 80)) {
			break;
		}

		if (pSock->InitClient(sIp.GetBuffer(), 8080)) {
			break;
		}

		if (pSock->InitClient(sIp.GetBuffer(), 3128)) {
			break;
		}

		if (pSock->InitClient(sIp.GetBuffer(), 8081)) {
			break;
		}

		if (pSock->InitClient(sIp.GetBuffer(), 9098)) {
			break;
		}

		REPORT("连接服务器失败", emLogLevelError);
		
		*pStrRecv = "MES通讯异常";

		return false;

	} while (false);


	char sUri[1024] = { 0 };
	sprintf_s(sUri, 1024, "http://%s/mesplus/service/CwipBindLotIdAndJigInterface.json", sIp.GetBuffer());
	
	Json::Value root;
	root["lotId"] = Json::Value(sPcbaCode.GetBuffer());
	root["toolId"] = Json::Value(sFixtureCode.GetBuffer());
	root["lineId"] = Json::Value(sLineId.GetBuffer());
	root["station"] = Json::Value(sStation.GetBuffer());
	root["oper"] = Json::Value(sOper.GetBuffer());

	Json::FastWriter fastwriter;

	string sTemp;
	sTemp = fastwriter.write(root);

	char sContent[1024] = { 0 };
	sprintf_s(sContent, 1024, "%s", sTemp.c_str());

	char sUser[1024] = { 0 };
	sprintf_s(sUser, 1024, "%s@%s:%s", sUserName.GetBuffer(), sFactory.GetBuffer(), sPassword.GetBuffer());

	string temp1, temp2(sUser);
	temp1 = CBASE64::encode(temp2);

	char sAuthorization[1024] = { 0 };
	sprintf_s(sAuthorization, 1024, "Authorization:Basic %s\r\n", temp1.c_str());

	char content_header[1024] = { 0 };
	sprintf_s(content_header, 1024, "Content-Length: %d\r\n", strlen(sContent));

	char send_str[1024 * 10] = { 0 };

	//头信息
	strcat_s(send_str, "POST ");
	strcat_s(send_str, sUri);
	strcat_s(send_str, " HTTP/1.1\r\n");
	strcat_s(send_str, "Host: ");
	strcat_s(send_str, sIp.GetBuffer());
	strcat_s(send_str, "\r\n");


	strcat_s(send_str, sAuthorization);
	strcat_s(send_str, "Content-Type:application/json;charset=UTF-8\r\n");
	strcat_s(send_str, "Accept-Language:zh_CN\r\n");
	strcat_s(send_str, "User-Agent:IE or Chrome\r\n");
	strcat_s(send_str, "Accept: text/html, application/xhtml+xml, image/jxr, */*\r\n");
	strcat_s(send_str, "Connection: keep-alive\r\n");
	strcat_s(send_str, content_header);
//  strcat_s(send_str, "Referer: http://www.colibri.com.cn");

    //内容信息
    strcat_s(send_str, "\r\n");
    strcat_s(send_str, sContent);

	CString sSendBuff(send_str);

	MES_LOG();

	pSock->SendCmd(send_str, (int)strlen(send_str));

	//获取返回信息
	char buffRecv[10240] = { 0 };
	char buffRecvUtf[10240] = { 0 };
	int  nRecvLen = 10240;

	pSock->GetRecv(buffRecvUtf, nRecvLen, VAR_MACHINE_I("MES响应超时时间"));

	bFlag = false;

	UTF8ToGBK(buffRecvUtf, buffRecv);
	
	sSendBuff.Empty();
	sSendBuff = buffRecv;
	MES_LOG();

	CString sRecv(buffRecv);

	int nIndex = sRecv.Find("\r\n\r\n");
	if (nIndex >= 0) {
		sRecv = sRecv.Mid(nIndex + strlen("\r\n\r\n"));
	}

	if (pStrRecv != NULL) {
		*pStrRecv = sRecv;
	}
	
	do
	{
		if (nRecvLen <= 0) {
			*pStrRecv = "MES回传数据为空";
			break;
		}
		
		if (!IsJsonData(sRecv.GetBuffer())) {
			*pStrRecv = "MES回传数据格式不正确";
			break;
		}

		if (nRecvLen > 0 && sRecv.Find("成功") > 0) {
			bFlag = true;
		}
	}
	while (false);

	pSock->CloseSocket();

	delete pSock;

	return bFlag;
}

bool CMes::HttpPostSwitchProduct( CString sIp, int nPort, CString sPcbaCode, CString sUserName, CString sPassword, CString sFactory, CString *pStrRecv )
{
	CSockBase* pSock = CSockBase::CreateClient();

	bool bFlag = false;
	for (int i=0; i<sIp.GetLength(); i++)
	{
		if (sIp.GetAt(i) != '.' && (sIp.GetAt(i) < '0' || sIp.GetAt(i) > '9')) {
			bFlag  = true;
			break;
		}
	}

	if (bFlag) {
		CString sHostName = sIp;
		sIp.Empty();

		struct hostent* pHost = NULL;
		pHost = gethostbyname(sHostName);

		for(int i = 0; pHost!= NULL && pHost->h_addr_list[i] != NULL; i++)
		{
			CString str;

			for(int j = 0; j < pHost->h_length; j++)
			{
				CString addr;

				if(j > 0)
					str += ".";

				addr.Format("%u", (unsigned int)((unsigned char*)pHost->h_addr_list[i])[j]);
				str += addr;
			}

			if (!str.IsEmpty()) {
				sIp = str;
			}
		}
	}

	if (sIp.IsEmpty()) {
		*pStrRecv = "IP地址解析失败";
		return false;
	}

	do 
	{
		if (pSock->InitClient(sIp.GetBuffer(), 80)) {
			break;
		}

		if (pSock->InitClient(sIp.GetBuffer(), 8080)) {
			break;
		}

		if (pSock->InitClient(sIp.GetBuffer(), 3128)) {
			break;
		}

		if (pSock->InitClient(sIp.GetBuffer(), 8081)) {
			break;
		}

		if (pSock->InitClient(sIp.GetBuffer(), 9098)) {
			break;
		}
		// 
		REPORT("连接服务器失败", emLogLevelError);
		
		*pStrRecv = "MES通讯异常";

		return false;

	} while (false);

	char sUri[1024] = { 0 };
	sprintf_s(sUri, 1024, "http://%s/mesplus/service/CinvLineTransferInteractiveApi.json", sIp.GetBuffer());
	
	Json::Value root;
	root["procstep"] = Json::Value("1");
	root["lotId"] = Json::Value(sPcbaCode.GetBuffer());
	root["equipmentType"] = Json::Value("F-STATION");
	root["machineVendor"] = Json::Value("EUP01");

	Json::FastWriter fastwriter;

	string sTemp;
	sTemp = fastwriter.write(root);

	char sContent[1024] = { 0 };
	sprintf_s(sContent, 1024, "%s", sTemp.c_str());

	char sUser[1024] = { 0 };
	sprintf_s(sUser, 1024, "%s@%s:%s", sUserName.GetBuffer(), sFactory.GetBuffer(), sPassword.GetBuffer());

	string temp1, temp2(sUser);
	temp1 = CBASE64::encode(temp2);

	char sAuthorization[1024] = { 0 };
	sprintf_s(sAuthorization, 1024, "Authorization:Basic %s\r\n", temp1.c_str());

	char content_header[1024] = { 0 };
	sprintf_s(content_header, 1024, "Content-Length: %d\r\n", strlen(sContent));

	char send_str[1024 * 10] = { 0 };

	//头信息
	strcat_s(send_str, "POST ");
	strcat_s(send_str, sUri);
	strcat_s(send_str, " HTTP/1.1\r\n");
	strcat_s(send_str, "Host: ");
	strcat_s(send_str, sIp.GetBuffer());
	strcat_s(send_str, "\r\n");


	strcat_s(send_str, sAuthorization);
	strcat_s(send_str, "Content-Type:application/json;charset=UTF-8\r\n");
	strcat_s(send_str, "Accept-Language:zh_CN\r\n");
	strcat_s(send_str, "User-Agent:IE or Chrome\r\n");
	strcat_s(send_str, "Accept: text/html, application/xhtml+xml, image/jxr, */*\r\n");
	strcat_s(send_str, "Connection: keep-alive\r\n");
	strcat_s(send_str, content_header);
//  strcat_s(send_str, "Referer: http://www.colibri.com.cn");

    //内容信息
    strcat_s(send_str, "\r\n");
    strcat_s(send_str, sContent);

	CString sSendBuff(send_str);

	MES_LOG();

	pSock->SendCmd(send_str, (int)strlen(send_str));

	//获取返回信息
	char buffRecv[10240] = { 0 };
	char buffRecvUtf[10240] = { 0 };
	int  nRecvLen = 10240;

	pSock->GetRecv(buffRecvUtf, nRecvLen, VAR_MACHINE_I("MES响应超时时间"));

	bFlag = false;

	UTF8ToGBK(buffRecvUtf, buffRecv);
	
	sSendBuff.Empty();
	sSendBuff = buffRecv;
	MES_LOG();

	CString sRecv(buffRecv);

	int nIndex = sRecv.Find("\r\n\r\n");
	if (nIndex >= 0) {
		sRecv = sRecv.Mid(nIndex + strlen("\r\n\r\n"));
	}

	if (pStrRecv != NULL) {
		*pStrRecv = sRecv;
	}
	
	do
	{
		if (nRecvLen <= 0) {
			*pStrRecv = "MES回传数据为空";
			break;
		}
		
		if (!IsJsonData(sRecv.GetBuffer())) {
			*pStrRecv = "MES回传数据格式不正确";
			break;
		}
		
		if (nRecvLen > 0 && sRecv.Find("成功") >= 0) {
			bFlag = true;
			break;
		}

		Json::Value root;
		root.clear();

		char* cJson = sRecv.GetBuffer(0);
		sRecv.ReleaseBuffer();

		Json::Reader reader;
		reader.parse(cJson, root);

		if (root["msg"].isNull()) {
			*pStrRecv = root["msg"].asCString();
		}
	}
	while (false);

	pSock->CloseSocket();

	delete pSock;

	return bFlag;
}

bool CMes::HttpPostUploadParam( CString sIp, int nPort, CString sParamName, CString sParamValue, CString sUserName, CString sPassword, CString sFactory, CString *pStrRecv )
{
	CSockBase* pSock = CSockBase::CreateClient();

	bool bFlag = false;
	for (int i=0; i<sIp.GetLength(); i++)
	{
		if (sIp.GetAt(i) != '.' && (sIp.GetAt(i) < '0' || sIp.GetAt(i) > '9')) {
			bFlag  = true;
			break;
		}
	}

	if (bFlag) {
		CString sHostName = sIp;
		sIp.Empty();

		struct hostent* pHost = NULL;
		pHost = gethostbyname(sHostName);

		for(int i = 0; pHost!= NULL && pHost->h_addr_list[i] != NULL; i++)
		{
			CString str;

			for(int j = 0; j < pHost->h_length; j++)
			{
				CString addr;

				if(j > 0)
					str += ".";

				addr.Format("%u", (unsigned int)((unsigned char*)pHost->h_addr_list[i])[j]);
				str += addr;
			}

			if (!str.IsEmpty()) {
				sIp = str;
			}
		}
	}

	if (sIp.IsEmpty()) {
		*pStrRecv = "IP地址解析失败";
		return false;
	}

	do 
	{
		if (pSock->InitClient(sIp.GetBuffer(), 80)) {
			break;
		}

		if (pSock->InitClient(sIp.GetBuffer(), 8080)) {
			break;
		}

		if (pSock->InitClient(sIp.GetBuffer(), 3128)) {
			break;
		}

		if (pSock->InitClient(sIp.GetBuffer(), 8081)) {
			break;
		}

		if (pSock->InitClient(sIp.GetBuffer(), 9098)) {
			break;
		}

		REPORT("连接服务器失败", emLogLevelError);
		
		*pStrRecv = "MES通讯异常";

		return false;

	} while (false);

	char sUri[1024] = { 0 };
	sprintf_s(sUri, 1024, "http://%s/mesplus/service/CateCheckMchineOperateParameters.json", sIp.GetBuffer());
	
	CString sPro;
	sPro = CSys::m_strPro.c_str();

	int nIndex = 0;
	nIndex = sPro.Find("-");

	CString sProject, sFixture;
	if (nIndex >= 0) {
		sProject = sPro.Left(nIndex);
		sFixture = sPro.Mid(nIndex + 1);
	}
	else {
		sProject = sPro;
		sFixture.Empty();
	}

	Json::Value root;
	root["deviceType"] = Json::Value("F-STATION");
	root["lineId"] = Json::Value(VAR_MACHINE_S("MES参数上传拉线"));
	root["projectId"] = Json::Value(VAR_MACHINE_S("MES参数上传专案号"));
	root["productId"] = Json::Value(VAR_MACHINE_S("MES参数上传产品代码"));
	root["deviceParameters"] = Json::Value(sParamName);
	root["parametersValue"] = Json::Value(sParamValue);

	Json::FastWriter fastwriter;

	string sTemp;
	sTemp = fastwriter.write(root);

	char sContent[1024] = { 0 };
	sprintf_s(sContent, 1024, "%s", sTemp.c_str());

	char sUser[1024] = { 0 };
	sprintf_s(sUser, 1024, "%s@%s:%s", sUserName.GetBuffer(), sFactory.GetBuffer(), sPassword.GetBuffer());

	string temp1, temp2(sUser);
	temp1 = CBASE64::encode(temp2);

	char sAuthorization[1024] = { 0 };
	sprintf_s(sAuthorization, 1024, "Authorization:Basic %s\r\n", temp1.c_str());

	char content_header[1024] = { 0 };
	sprintf_s(content_header, 1024, "Content-Length: %d\r\n", strlen(sContent));

	char send_str[1024 * 10] = { 0 };

	//头信息
	strcat_s(send_str, "POST ");
	strcat_s(send_str, sUri);
	strcat_s(send_str, " HTTP/1.1\r\n");
	strcat_s(send_str, "Host: ");
	strcat_s(send_str, sIp.GetBuffer());
	strcat_s(send_str, "\r\n");


	strcat_s(send_str, sAuthorization);
	strcat_s(send_str, "Content-Type:application/json;charset=UTF-8\r\n");
	strcat_s(send_str, "Accept-Language:zh_CN\r\n");
	strcat_s(send_str, "User-Agent:IE or Chrome\r\n");
	strcat_s(send_str, "Accept: text/html, application/xhtml+xml, image/jxr, */*\r\n");
	strcat_s(send_str, "Connection: keep-alive\r\n");
	strcat_s(send_str, content_header);
//  strcat_s(send_str, "Referer: http://www.colibri.com.cn");

    //内容信息
    strcat_s(send_str, "\r\n");
    strcat_s(send_str, sContent);

	CString sSendBuff(send_str);

	MES_LOG();

	pSock->SendCmd(send_str, (int)strlen(send_str));

	//获取返回信息
	char buffRecv[10240] = { 0 };
	char buffRecvUtf[10240] = { 0 };
	int  nRecvLen = 10240;

	pSock->GetRecv(buffRecvUtf, nRecvLen, VAR_MACHINE_I("MES响应超时时间"));

	bFlag = false;

	UTF8ToGBK(buffRecvUtf, buffRecv);

	sSendBuff.Empty();
	sSendBuff = buffRecv;
	MES_LOG();

	CString sRecv(buffRecv);

	nIndex = sRecv.Find("\r\n\r\n");
	if (nIndex >= 0) {
		sRecv = sRecv.Mid(nIndex + strlen("\r\n\r\n"));
	}

	if (pStrRecv != NULL) {
		*pStrRecv = sRecv;
	}
	
	do
	{
		if (nRecvLen <= 0) {
			*pStrRecv = "MES回传数据为空";
			break;
		}
		
		if (!IsJsonData(sRecv.GetBuffer())) {
			*pStrRecv = "MES回传数据格式不正确";
			break;
		}
		
		Json::Value root;
		root.clear();

		char* cJson = sRecv.GetBuffer(0);
		sRecv.ReleaseBuffer();

		Json::Reader reader;
		reader.parse(cJson, root);

		bool bSuccess;
		if (!root["success"].isNull()) {
			bSuccess = root["success"].asBool();
			if (bSuccess) {
				CString sResult;
				if (!root["result"].isNull()) {
					sResult = root["result"].asCString();
					sResult.MakeUpper();
					if (sResult.Find("OK") < 0) {
						if (!root["resultDecription"].isNull()) {
							*pStrRecv = root["resultDecription"].asCString();
						}
					}
					else {
						bFlag = true;
					}
				}
			}
			else {
				if (!root["msg"].isNull()) {
					*pStrRecv = root["msg"].asCString();
				}
			}
		}
	}
	while (false);

	pSock->CloseSocket();

	delete pSock;

	return bFlag;
}
