﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{DB4888A3-3FE8-468F-87F3-91493951EF69}</ProjectGuid>
    <RootNamespace>FStation</RootNamespace>
    <Keyword>MFCProj</Keyword>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(ProjectDir)$(Platform)\$(Configuration)\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_WINDOWS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_WIN64;_WINDOWS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(ProjectDir);$(ProjectDir)Global\;$(ProjectDir)Logic\;$(ProjectDir)Param\;$(ProjectDir)Module\;$(SolutionDir)build\inc\</AdditionalIncludeDirectories>
      <AssemblerOutput>All</AssemblerOutput>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>Base.lib;Database.lib;DMC3800.lib;CameraHK.lib;ImageProcess.lib;json.lib;</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(SolutionDir)build\lib\</AdditionalLibraryDirectories>
      <GenerateMapFile>true</GenerateMapFile>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <PostBuildEvent>
      <Command>xcopy /S /F /Y "$(TargetDir)$(TargetFileName)" "$(SolutionDir)build\bin\"</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="ClassDiagram1.cd" />
    <None Include="ReadMe.txt" />
    <None Include="res\FStation.ico" />
    <None Include="res\FStation.rc2" />
    <None Include="res\LedD.ico" />
    <None Include="res\LedP.ico" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="CApplication.h" />
    <ClInclude Include="CFont0.h" />
    <ClInclude Include="CRange.h" />
    <ClInclude Include="CSheets.h" />
    <ClInclude Include="CWorkbook.h" />
    <ClInclude Include="CWorkbooks.h" />
    <ClInclude Include="CWorksheet.h" />
    <ClInclude Include="CWorksheets.h" />
    <ClInclude Include="DialogAgv.h" />
    <ClInclude Include="DialogBelt.h" />
    <ClInclude Include="DialogCamera.h" />
    <ClInclude Include="DialogCheck.h" />
    <ClInclude Include="DialogFixture.h" />
    <ClInclude Include="DialogLogin.h" />
    <ClInclude Include="DialogMachine.h" />
    <ClInclude Include="DialogProduct.h" />
    <ClInclude Include="DialogRobot.h" />
    <ClInclude Include="DialogRobotCalib.h" />
    <ClInclude Include="DialogRobotTeach.h" />
    <ClInclude Include="DialogTray.h" />
    <ClInclude Include="DialogWarn.h" />
    <ClInclude Include="DialogWarnCount.h" />
    <ClInclude Include="FStation.h" />
    <ClInclude Include="FStationDlg.h" />
    <ClInclude Include="Global\Dat.h" />
    <ClInclude Include="Global\ExcelEx.h" />
    <ClInclude Include="Global\Global.h" />
    <ClInclude Include="Global\LogicMgr.h" />
    <ClInclude Include="Global\Mes.h" />
    <ClInclude Include="Global\Pro.h" />
    <ClInclude Include="Global\Sys.h" />
    <ClInclude Include="Logic\LogicBelt.h" />
    <ClInclude Include="Logic\LogicContinueRun.h" />
    <ClInclude Include="Logic\LogicFixture.h" />
    <ClInclude Include="Logic\LogicFixtureTransport.h" />
    <ClInclude Include="Logic\LogicMachine.h" />
    <ClInclude Include="Logic\LogicProc.h" />
    <ClInclude Include="Logic\LogicReset.h" />
    <ClInclude Include="Logic\LogicRobot.h" />
    <ClInclude Include="Logic\LogicTrayEmpty.h" />
    <ClInclude Include="Logic\LogicTrayFull.h" />
    <ClInclude Include="ModuleDialog.h" />
    <ClInclude Include="Module\Belt.h" />
    <ClInclude Include="Module\Fixture.h" />
    <ClInclude Include="Module\HeightSensor.h" />
    <ClInclude Include="Module\Machine.h" />
    <ClInclude Include="Module\Module.h" />
    <ClInclude Include="Module\Robot.h" />
    <ClInclude Include="Module\Tray.h" />
    <ClInclude Include="Resource.h" />
    <ClInclude Include="ScadaCommandHandler.h" />
    <ClInclude Include="SimpleSocketInterface.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="targetver.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="DialogAgv.cpp" />
    <ClCompile Include="DialogBelt.cpp" />
    <ClCompile Include="DialogCamera.cpp" />
    <ClCompile Include="DialogCheck.cpp" />
    <ClCompile Include="DialogFixture.cpp" />
    <ClCompile Include="DialogLogin.cpp" />
    <ClCompile Include="DialogMachine.cpp" />
    <ClCompile Include="DialogProduct.cpp" />
    <ClCompile Include="DialogRobot.cpp" />
    <ClCompile Include="DialogRobotCalib.cpp" />
    <ClCompile Include="DialogRobotTeach.cpp" />
    <ClCompile Include="DialogTray.cpp" />
    <ClCompile Include="DialogWarn.cpp" />
    <ClCompile Include="DialogWarnCount.cpp" />
    <ClCompile Include="FStation.cpp" />
    <ClCompile Include="FStationDlg.cpp" />
    <ClCompile Include="Global\Dat.cpp" />
    <ClCompile Include="Global\ExcelEx.cpp" />
    <ClCompile Include="Global\Global.cpp" />
    <ClCompile Include="Global\LogicMgr.cpp" />
    <ClCompile Include="Global\Mes.cpp" />
    <ClCompile Include="Global\Pro.cpp" />
    <ClCompile Include="Global\Sys.cpp" />
    <ClCompile Include="Logic\LogicBelt.cpp" />
    <ClCompile Include="Logic\LogicContinueRun.cpp" />
    <ClCompile Include="Logic\LogicFixture.cpp" />
    <ClCompile Include="Logic\LogicFixtureTransport.cpp" />
    <ClCompile Include="Logic\LogicMachine.cpp" />
    <ClCompile Include="Logic\LogicProc.cpp" />
    <ClCompile Include="Logic\LogicReset.cpp" />
    <ClCompile Include="Logic\LogicRobot.cpp" />
    <ClCompile Include="Logic\LogicTrayEmpty.cpp" />
    <ClCompile Include="Logic\LogicTrayFull.cpp" />
    <ClCompile Include="ModuleDialog.cpp" />
    <ClCompile Include="Module\Belt.cpp" />
    <ClCompile Include="Module\Fixture.cpp" />
    <ClCompile Include="Module\HeightSensor.cpp" />
    <ClCompile Include="Module\Machine.cpp" />
    <ClCompile Include="Module\Module.cpp" />
    <ClCompile Include="Module\Robot.cpp" />
    <ClCompile Include="Module\Tray.cpp" />
    <ClCompile Include="ScadaCommandHandler.cpp" />
    <ClCompile Include="SimpleSocketInterface.cpp" />
    <ClCompile Include="stdafx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="FStation.rc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
  <ProjectExtensions>
    <VisualStudio>
      <UserProperties RESOURCE_FILE="FStation.rc" />
    </VisualStudio>
  </ProjectExtensions>
</Project>