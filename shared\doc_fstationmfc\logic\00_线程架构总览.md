# FStation线程系统架构总览

## 概述

FStation是一个复杂的工业控制软件，采用多线程架构实现各个功能模块的并发控制。该系统在Logic目录下包含11个主要的线程类，负责处理机器人控制、托盘管理、治具操作、传送带控制等核心业务逻辑。

## 线程类概览

### 文件统计

| 线程类文件                  | 大小             | 行数                | 主要功能               |
| --------------------------- | ---------------- | ------------------- | ---------------------- |
| LogicMachine.cpp/h          | 6.4KB            | 330行               | 机器状态管理、安全监控 |
| LogicRobot.cpp/h            | 179KB            | 5914行              | 机器人控制核心逻辑     |
| LogicTray.cpp/h             | 29KB             | 1091行              | 托盘管理总控           |
| LogicTrayEmpty.cpp/h        | 17KB             | 633行               | 空托盘处理             |
| LogicTrayFull.cpp/h         | 26KB             | 972行               | 满托盘处理             |
| LogicFixture.cpp/h          | 21KB             | 891行               | 治具操作管理           |
| LogicFixtureTransport.cpp/h | 24KB             | 805行               | 治具传输控制           |
| LogicBelt.cpp/h             | 11KB             | 484行               | 传送带控制             |
| LogicContinueRun.cpp/h      | 27KB             | 1108行              | 连续运行协调           |
| LogicProc.cpp/h             | 7.6KB            | 307行               | 工艺处理               |
| LogicReset.cpp/h            | 9.9KB            | 407行               | 系统重置               |
| **总计**              | **~330KB** | **~12,960行** | **完整工控系统** |

## 线程分类

### 1. 核心控制线程

- **LogicMachine**: 机器状态管理和安全监控
- **LogicRobot**: 机器人控制核心（最复杂）
- **LogicContinueRun**: 连续运行总协调器

### 2. 设备控制线程

- **LogicTray**: 托盘管理总控
- **LogicTrayEmpty**: 空托盘专用处理
- **LogicTrayFull**: 满托盘专用处理
- **LogicFixture**: 治具操作管理
- **LogicFixtureTransport**: 治具传输控制
- **LogicBelt**: 传送带控制（支持多轨道）

### 3. 辅助功能线程

- **LogicProc**: 工艺处理（支持多个实例）
- **LogicReset**: 系统重置

## 继承结构

```
yzBase::CThreadBase (抽象基类)
├── CLogicMachine          // 机器状态管理
├── CLogicRobot            // 机器人控制
├── CLogicTray             // 托盘总控
├── CLogicTrayEmpty        // 空托盘处理
├── CLogicTrayFull         // 满托盘处理
├── CLogicFixture          // 治具管理
├── CLogicFixtureTransport // 治具传输
├── CLogicBelt             // 传送带控制
├── CLogicContinueRun      // 连续运行协调
├── CLogicProc             // 工艺处理
└── CLogicReset            // 系统重置
```

## 管理架构

### 三层管理结构

```
CLogicMgr (逻辑管理器)
├── CThreadMgr (线程管理器) 
│   ├── 线程生命周期管理
│   ├── 启动/暂停/停止控制
│   └── 状态监控
├── CThreadBase (线程基类)
│   ├── 标准化接口定义
│   ├── 状态机框架
│   └── 安全检查机制
└── CThreadFunc (子线程功能)
    ├── 具体业务逻辑执行
    ├── 状态传递
    └── 并发处理
```

### 线程类型分类

```cpp
enum enThreadType {
    emThreadSpecial = 0,    // 特殊线程（Reset、ContinueRun）
    emThreadNormal,         // 普通线程（业务逻辑线程）
    emThreadAlwaysExist     // 常驻线程（Machine、Proc）
};
```

## 状态机设计

每个线程类内部都采用状态机模式：

```cpp
// 标准状态机接口
virtual EnumStatus OnSafeCheck() = 0;  // 安全检查
virtual EnumStatus OnStart() = 0;      // 启动状态
virtual EnumStatus OnPause() = 0;      // 暂停状态
virtual EnumStatus OnResume() = 0;     // 恢复状态
virtual EnumStatus OnStop() = 0;       // 停止状态
virtual CStatus OnRun() = 0;           // 运行主逻辑

// 业务状态方法
CStatus OnXXX00();  // 主状态
CStatus OnXXX01();  // 子状态
CStatus OnXXX01_0(); // 子子状态
```

## 并发机制

### 主线程 + 子线程模式

许多线程类采用主线程+子线程的并发模式：

- **LogicMachine**: `m_pFuncNGBelt` + `m_pFuncButton`
- **LogicTray**: `m_pFuncFullTray` + `m_pFuncEmptyTray`
- **LogicFixture**: `m_pFuncUpload` + `m_pFuncSeparate` + `m_pFuncAgv`
- **LogicContinueRun**: 5个子线程管理不同设备

### 线程间通信

通过参数映射表实现间接通信：

```cpp
map<CString, bool>   m_mapFlag;   // 标志位映射
map<CString, DWORD>  m_mapTick;   // 时间戳映射
map<CString, double> m_mapPos;    // 位置参数映射
map<CString, int>    m_mapIndex;  // 索引映射
```

## 设计优势

### 1. 模块化设计

- 每个线程专注于特定功能域
- 降低模块间耦合度
- 便于独立调试和维护

### 2. 状态机驱动

- 复杂流程分解为简单状态
- 状态转换清晰可控
- 异常处理机制完善

### 3. 分层管理

- 统一的生命周期管理
- 标准化的控制接口
- 集中的状态监控

### 4. 并发处理

- 提高系统响应性能
- 支持复杂业务场景
- 保证实时性要求

### 5. 安全机制

- 多重安全检查
- 分级响应处理
- 自动恢复能力

## 典型运行场景

### 正常生产流程

1. **LogicMachine**: 执行安全检查，启动系统
2. **LogicTray**: 管理托盘供给和回收
3. **LogicRobot**: 执行抓取、加工、装配流程
4. **LogicFixture**: 管理治具上传、分离
5. **LogicBelt**: 控制产品输送

### 连续运行模式

**LogicContinueRun** 作为总协调器：

- 统一管理5个子线程
- 协调各设备的运行节拍
- 优化整体生产效率

### 异常处理流程

1. **LogicMachine** 检测到异常信号
2. 触发系统级暂停命令
3. 各线程接收暂停信号并安全停止
4. **LogicReset** 执行系统重置
5. 恢复正常运行状态

## 总结

FStation线程系统采用分层、模块化、状态驱动的设计理念，通过11个专业化线程类实现了复杂工业设备的精确控制。该架构具有良好的可扩展性、可维护性和安全性，为工业4.0智能制造提供了坚实的软件基础。

系统的核心优势在于：

- **专业分工**: 每个线程专注特定领域
- **协调统一**: 通过管理器实现全局协调
- **安全可靠**: 多重检查和异常处理机制
- **高效并发**: 合理的并发设计提升性能
- **易于维护**: 清晰的架构和标准化接口

这种设计模式为复杂工控软件的开发提供了很好的参考价值。
