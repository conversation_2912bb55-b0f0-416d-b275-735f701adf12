﻿#pragma once

#include "Resource.h"

#include "ThreadBase.h"
#include "ColorButton.h"
#include "afxcmn.h"

// CDialogRobot 对话框

class CDialogRobot : public CDialogEx
{
	DECLARE_DYNAMIC(CDialogRobot)

public:
	CDialogRobot(CPoint pt, CWnd* pParent = NULL);   // 标准构造函数
	virtual ~CDialogRobot();

// 对话框数据
	enum { IDD = IDD_DIALOG_ROBOT };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持
	virtual BOOL PreTranslateMessage(MSG* pMsg);
	virtual void OnOK() {};
	virtual BOOL OnInitDialog();

	DECLARE_MESSAGE_MAP()
	afx_msg void OnHScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar);
	afx_msg void OnTimer(UINT_PTR nIDEvent);
	afx_msg void OnEnChangeEdit1();
	afx_msg void OnBnClickedButton1();
	afx_msg void OnBnClickedButton2();
	afx_msg void OnBnClickedButton3();
	afx_msg void OnBnClickedButton4();
	afx_msg void OnBnClickedButton5();
	afx_msg void OnBnClickedButton6();
	afx_msg void OnBnClickedButton7();
	afx_msg void OnBnClickedButton8();
	afx_msg void OnBnClickedButton9();
	afx_msg void OnBnClickedButton10();
	afx_msg void OnBnClickedButton11();
	afx_msg void OnBnClickedButton12();
	afx_msg void OnBnClickedButton13();
	afx_msg void OnBnClickedButton14();
	afx_msg void OnBnClickedButton15();
	afx_msg void OnBnClickedButton16();
	afx_msg void OnBnClickedButton17();
	afx_msg void OnBnClickedButton18();
	afx_msg void OnBnClickedButton19();
	afx_msg void OnBnClickedButton20();
	afx_msg void OnBnClickedButton21();
	afx_msg void OnBnClickedButton22();
	afx_msg void OnBnClickedButton23();
	afx_msg void OnBnClickedButton24();

public:
	void UpdateButtonState();

protected:
	CColorButton	m_btnReset;
	CColorButton	m_btnStart;
	CColorButton	m_btnPause;
	CColorButton	m_btnStop;

	CSliderCtrl		m_slider1;

private:
	CPoint			m_pt;
};
