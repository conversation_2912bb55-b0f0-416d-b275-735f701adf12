{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/paho-mqtt-x64-windows-1.3.14-f17f8f4a-a15a-4cee-8b8d-4bad367865c3", "name": "paho-mqtt:x64-windows@1.3.14 e04391966b12706516130ee4c3638115c5aea5fff27cbc92a9901830db5167b9", "creationInfo": {"creators": ["Tool: vcpkg-2025-06-02-145689e84b7637525510e2c9b4ee603fda046b56"], "created": "2025-06-20T07:01:13Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-3"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-3", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-3", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "paho-mqtt", "SPDXID": "SPDXRef-port", "versionInfo": "1.3.14", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/paho-mqtt", "homepage": "https://github.com/eclipse/paho.mqtt.c", "licenseConcluded": "EPL-2.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Paho project provides open-source client implementations of MQTT and MQTT-SN messaging protocols aimed at new, existing, and emerging applications for the Internet of Things", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "paho-mqtt:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "e04391966b12706516130ee4c3638115c5aea5fff27cbc92a9901830db5167b9", "downloadLocation": "NONE", "licenseConcluded": "EPL-2.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "eclipse/paho.mqtt.c", "downloadLocation": "git+https://github.com/eclipse/paho.mqtt.c@v1.3.14", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "5576ac3531a5c707f92a02cbfb9d60710b42acd99f57bcde311aa224780267a5152e8b92a6b077afab4780ee236d5e0c2a0b8986453439bce4323758b3d4385b"}]}], "files": [{"fileName": "./fix-ODR-libuuid-linux.patch", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "ebaa47ddc8e5c0cd946b724dc55885756616c42e8ed5ed44c09a52c7611de646"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./fix-unresolvedsymbol-arm.patch", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "d99ebb089a988de02dd62e5accd1a1e2047790083da62a32b2f6e2b73ef8c943"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "023711d7b6ac4fe89374121f6521d49f842f9cec1a2f2bd2fa82b94f2f8b78f8"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-3", "checksums": [{"algorithm": "SHA256", "checksumValue": "c8a5f3df2f074ff946aebb7138397ee4d5531374c2a4b7023fc4104ff7992547"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}