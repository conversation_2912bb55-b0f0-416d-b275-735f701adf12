# FStation CMake 配置文件
# 供其他项目使用 find_package(FStation) 时使用

@PACKAGE_INIT@

# 设置版本信息
set(FStation_VERSION "@PROJECT_VERSION@")
set(FStation_VERSION_MAJOR "@PROJECT_VERSION_MAJOR@")
set(FStation_VERSION_MINOR "@PROJECT_VERSION_MINOR@")
set(FStation_VERSION_PATCH "@PROJECT_VERSION_PATCH@")

# 设置安装路径
set_and_check(FStation_INSTALL_PREFIX "@PACKAGE_CMAKE_INSTALL_PREFIX@")
set_and_check(FStation_INCLUDE_DIR "@PACKAGE_FSTATION_INSTALL_INCLUDEDIR@")
set_and_check(FStation_LIB_DIR "@PACKAGE_FSTATION_INSTALL_LIBDIR@")
set_and_check(FStation_BIN_DIR "@PACKAGE_FSTATION_INSTALL_BINDIR@")
set_and_check(FStation_CONFIG_DIR "@PACKAGE_FSTATION_INSTALL_CONFIGDIR@")

# 查找依赖项
include(CMakeFindDependencyMacro)

# 查找Qt6
find_dependency(Qt6 REQUIRED COMPONENTS Core Widgets Network Charts)

# 查找OpenSSL
find_dependency(OpenSSL REQUIRED)

# 查找MQTT库
find_dependency(PahoMqttCpp CONFIG REQUIRED)
find_dependency(eclipse-paho-mqtt-c CONFIG REQUIRED)

# 导入目标
if(NOT TARGET FStation::MQTTGatewayLib)
    include("${CMAKE_CURRENT_LIST_DIR}/FStationTargets.cmake")
endif()

# 设置便捷变量
set(FStation_LIBRARIES FStation::MQTTGatewayLib)
set(FStation_INCLUDE_DIRS ${FStation_INCLUDE_DIR})

# 检查所有组件是否找到
check_required_components(FStation)

# 提供一些有用的宏
macro(fstation_add_executable target_name)
    add_executable(${target_name} ${ARGN})
    target_link_libraries(${target_name} PRIVATE FStation::MQTTGatewayLib)
    target_include_directories(${target_name} PRIVATE ${FStation_INCLUDE_DIRS})
endmacro()

# 显示找到的信息
if(NOT FStation_FIND_QUIETLY)
    message(STATUS "Found FStation: ${FStation_INSTALL_PREFIX} (version ${FStation_VERSION})")
    message(STATUS "  Include directories: ${FStation_INCLUDE_DIRS}")
    message(STATUS "  Libraries: ${FStation_LIBRARIES}")
    message(STATUS "  Config directory: ${FStation_CONFIG_DIR}")
endif() 