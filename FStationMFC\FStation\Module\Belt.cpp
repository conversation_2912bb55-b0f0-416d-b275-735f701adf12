﻿#include "stdafx.h"
#include "Belt.h"

CBelt::CBelt(CString sTrack) : CModule(4)
{
	m_nModule = sTrack.Find("A") >= 0 ? 4 : 5;

	m_sTrack = sTrack;
	
	MakePair("皮带正转方向", new CData(false, false, 4));	// 往右为正

	MakePair("自检完成标志", new CData(false, false));	

	MakePair("皮带有料标志", new CData(false, false));	

	MakePair("轨道启用标志", new CData(true, true, 3));	
	
	MakePair("允许治具下盖上料标志", new CData(false, false));			

	MakePair("治具下盖上料完成标志", new CData(false, false));			

	MakePair("治具提前取上盖标志", new CData(false, false));	

	MakePair("允许治具合盖标志", new CData(false, false));				

	MakePair("治具合盖完成标志", new CData(false, false));	

	MakePair("治具合盖异常标志", new CData(false, false));				
	
	MakePair("允许机械手装配主板标志", new CData(false, false));		

	MakePair("机械手装配主板完成标志", new CData(false, false));

	MakePair("机械手主板1装配完成标志", new CData(false, false));	

	MakePair("机械手主板2装配完成标志", new CData(false, false));	

	MakePair("机械手主板3装配完成标志", new CData(false, false));	

	MakePair("机械手主板4装配完成标志", new CData(false, false));		

	MakePair("MARK拍照延时", new CData(300));	

	MakePair("治具到主板装配位皮带转动延时", new CData(1000));	
	
	MakePair("治具到治具合盖位皮带转动延时", new CData(1000));	
	
	MakePair("治具离开治具合盖位皮带转动延时", new CData(1000));	

	MakePair("治具离开治具出料位皮带转动延时", new CData(1000));	

	Load();
}

CBelt::~CBelt()
{
}

CString CBelt::MainBoardSetPosCylinderOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput(m_sTrack + "主板装配位侧定位气缸", true));

	return "OK";
}

CString CBelt::MainBoardSetPosCylinderOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput(m_sTrack + "主板装配位侧定位气缸", false));

	return "OK";
}

CString CBelt::MainBoardSetPosCylinderStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput(m_sTrack + "主板装配位侧定位气缸伸出信号", bStatus));

	if (bStatus) {
		return "On";
	}
	else {
		return "Off";
	}
}

CString CBelt::FixtureAssembleStopCylinderOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput(m_sTrack + "治具合盖位阻挡气缸", true));

	return "OK";
}

CString CBelt::FixtureAssembleStopCylinderOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput(m_sTrack + "治具合盖位阻挡气缸", false));

	return "OK";
}

CString CBelt::FixtureAssembleStopCylinderStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput(m_sTrack + "治具合盖位阻挡气缸伸出信号", bStatus));

	if (bStatus) {
		return "On";
	}
	else {
		return "Off";
	}
}

CString CBelt::FixtureAssembleSetPosCylinderOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput(m_sTrack + "治具合盖位侧定位气缸", true));

	return "OK";
}

CString CBelt::FixtureAssembleSetPosCylinderOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput(m_sTrack + "治具合盖位侧定位气缸", false));

	return "OK";
}

CString CBelt::FixtureAssembleSetPosCylinderStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput(m_sTrack + "治具合盖位侧定位气缸伸出信号", bStatus));

	if (bStatus) {
		return "On";
	}
	else {
		return "Off";
	}
}

CString CBelt::BeltOn(bool bDir)
{
	EXCUTE_RETURN(g_pControl->WriteOutput(m_sTrack + "皮带方向", bDir));

	EXCUTE_RETURN(g_pControl->WriteOutput(m_sTrack + "皮带启动", true));

	return "OK";
}

CString CBelt::BeltOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput(m_sTrack + "皮带启动", false));

	return "OK";
}

CString CBelt::MainBoardInPosStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput(m_sTrack + "主板装配位到位信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CBelt::FixtureAssembleInPosStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput(m_sTrack + "治具合盖位到位信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CBelt::FixtureOutInPosStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput(m_sTrack + "治具出料位到位信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CBelt::NextStationTrackRequestOn()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->WriteOutput(m_sTrack + "请求下料", true));

	return "OK";
}

CString CBelt::NextStationTrackRequestOff()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->WriteOutput(m_sTrack + "请求下料", false));

	return "OK";
}

CString CBelt::NextStationTrackRequestStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput(m_sTrack + "下游请求放料信号", bStatus));

	return bStatus ? "On" : "Off";
}
