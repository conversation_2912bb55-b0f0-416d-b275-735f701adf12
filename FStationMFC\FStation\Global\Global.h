﻿#pragma once

#include <map>
using namespace std;

#include "BaseDef.h"
#include "ThreadBase.h"
#include "Device.h"

#include "DbInfo.h"
#include "Control.h"

#include "ExportFunc.h"

#include "Dat.h"
#include "Camera.h"
#include "Image.h"
#include "ProductionBase.h"

#include "Belt.h"
#include "Fixture.h"
#include "Machine.h"
#include "Robot.h"
#include "Tray.h"
#include "HeightSensor.h"

#include "DialogWarn.h"

#define MESSAGEBOX(sMsg, sMsgExt, bWarn) \
	CDialogWarn dlg(sMsg, sMsgExt, bWarn); \
	dlg.DoModal(); \
	bool bMsgBoxResult = false; \
	bMsgBoxResult = dlg.GetResult();

#define APP_VER	"V2.2.5 验证程序"

#define UM_PRODUCT_SWITCH	WM_USER + 1101 // 切换产品
#define UM_UPDATE_LOG		WM_USER + 1 // 更新日志信息
#define UM_UPDATE_PRO		WM_USER + 2 // 更新产量信息
#define UM_TIME_CNT			WM_USER + 3 // 计时
#define UM_TIME_LOGIN		WM_USER + 4 // 登录计时
#define UM_AXIS_OFF			WM_USER + 5 // 轴使能关闭后要重新复位
#define UM_TIME_OUT			WM_USER + 6 // 底层计时报警 

#define VAR_MACHINE(sName) *g_pMachine->m_mapParam[sName]
#define VAR_MACHINE_B(sName) g_pMachine->m_mapParam[sName]->B()
#define VAR_MACHINE_I(sName) g_pMachine->m_mapParam[sName]->I()
#define VAR_MACHINE_D(sName) g_pMachine->m_mapParam[sName]->D()
#define VAR_MACHINE_S(sName) g_pMachine->m_mapParam[sName]->S()

#define VAR_ROBOT(sName) *g_pRobot->m_mapParam[sName]
#define VAR_ROBOT_B(sName) g_pRobot->m_mapParam[sName]->B()
#define VAR_ROBOT_I(sName) g_pRobot->m_mapParam[sName]->I()
#define VAR_ROBOT_D(sName) g_pRobot->m_mapParam[sName]->D()

#define VAR_TRAY(sName) *g_pTray->m_mapParam[sName]
#define VAR_TRAY_B(sName) g_pTray->m_mapParam[sName]->B()
#define VAR_TRAY_I(sName) g_pTray->m_mapParam[sName]->I()
#define VAR_TRAY_D(sName) g_pTray->m_mapParam[sName]->D()

#define VAR_FIXTURE(sName) *g_pFixture->m_mapParam[sName]
#define VAR_FIXTURE_B(sName) g_pFixture->m_mapParam[sName]->B()
#define VAR_FIXTURE_I(sName) g_pFixture->m_mapParam[sName]->I()
#define VAR_FIXTURE_D(sName) g_pFixture->m_mapParam[sName]->D()

#define VAR_BELTA(sName) *g_pBeltA->m_mapParam[sName]
#define VAR_BELTA_B(sName) g_pBeltA->m_mapParam[sName]->B()
#define VAR_BELTA_I(sName) g_pBeltA->m_mapParam[sName]->I()
#define VAR_BELTA_D(sName) g_pBeltA->m_mapParam[sName]->D()

#define VAR_BELTB(sName) *g_pBeltB->m_mapParam[sName]
#define VAR_BELTB_B(sName) g_pBeltB->m_mapParam[sName]->B()
#define VAR_BELTB_I(sName) g_pBeltB->m_mapParam[sName]->I()
#define VAR_BELTB_D(sName) g_pBeltB->m_mapParam[sName]->D()

#define REPORT(sRet, nLevel) \
{ \
	CString str1, str2 = sRet; \
	if (CSys::m_bEngineer) { \
		str1.Format("Line %d %s : %s", __LINE__, __FUNCTION__, str2.GetBuffer()); \
	} \
	else { \
		str1.Format("%s", str2.GetBuffer()); \
	} \
	::SendMessage(g_hMainWnd, UM_UPDATE_LOG, (WPARAM)nLevel, (LPARAM)str1.GetBuffer()); \
}

#define RUN_STOP_IF_ERROR(Func) \
{ \
	m_sRet = Func; \
	\
	if (m_sRet != "On" && m_sRet != "Off" && m_sRet != "UnKnow" && m_sRet != "OK" && m_sRet != "Yes" && m_sRet != "No") { \
		REPORT(m_sRet, emLogLevelError); \
		MESSAGEBOX(m_sRet + "流程已停止，请检查原因并复位相关模块后点击启动", "", false); \
		RETURN_STOP(); \
	} \
}

#define RUN_PAUSE_IF_WARN(Func) \
{ \
	m_sRet = Func; \
	\
	if (m_sRet != "On" && m_sRet != "Off" && m_sRet != "UnKnow" && m_sRet != "OK" && m_sRet != "Yes" && m_sRet != "No") { \
		REPORT(m_sRet, emLogLevelWarn); \
		RETURN_PAUSE(NULL); \
	} \
}

#define PREMISSION_CTRL() \
if (g_bAuto && !VAR_MACHINE_B("强制自动模式")) { \
	AfxMessageBox("对不起，自动模式下禁止操作此功能！"); \
	return; \
} \
\
if (g_nPermissionLevel < emPremissionLevelTechnician) { \
	AfxMessageBox("对不起，您没有权限操作此功能！"); \
	return; \
}

#define MANNUAL_MODE() \
if (!g_bAuto) { \
	REPORT("手动模式下运行一次后停止", emLogLevelWarn); \
	RETURN_STOP(); \
}

bool __stdcall OnSafeCheck(CString sName, CString* pMsg);

typedef void (*lpFunc1)(HWND hWnd, void *pInfo, void *pControl, int *pRight, string *pStrPro);
typedef void (*lpFunc2)(int *pRight, HWND hWnd);
typedef void (*lpFunc3)();
typedef void (*lpFunc4)(SAFE_MOVE_FUNC func);

typedef enum LogLevel
{
	 emLogLevelNormal,
	 emLogLevelWarn,
	 emLogLevelError,
	 emLogLevelSum
} EnumLogLevel;

typedef struct _USER_
{
	CString sName;
	CString sKey;
	int		nLevel;
}USER, *PUSER;

extern const COLORREF g_clrLogLevel[emLogLevelSum]; // 日志显示等级颜色
extern const CString  g_strLogLevel[emLogLevelSum]; // 日志显示等级

// 设备运行状态
typedef enum RunStatus
{
	emRunStatusPreReset,
	emRunStatusReset,
	emRunStatusIdle,
	emRunStatusRunning,
	emRunStatusSuspending,
	emRunStatusWarning,
	emRunStatusSum
} EnumRunStatus;

typedef enum PremissionLevel
{
	emPremissionLevelOperator = 1,
	emPremissionLevelTechnician,
	emPremissionLevelEngineer
}EnumPremissionLevel;

extern const CString		g_strRunStatus[emRunStatusSum]; // 设备状态显示字符串

extern bool					g_bWaitFlag;

extern bool					g_bWarn;

extern bool					g_bWarnFlag;

extern int					g_nPermissionLevel;

extern int					g_nWarnTimes;

extern int					g_nPicIndex;

extern HWND					g_hMainWnd;

extern HINSTANCE			g_hInstDebug;

extern CDbInfo*				g_pInfo;

extern CControlBase*		g_pControl;

extern CCameraBase*			g_pCamera;

extern CProductionBase*		g_pProduction;

extern CDat*				g_pDatLog;

extern CDat*				g_pDatImage;

extern CSockBase*			g_pSockRobot;

extern CSockBase*			g_pSockAgv;

extern CImageWindow*		g_pImageWndUp;

extern CImageWindow*		g_pImageWndDn;

extern CImageWindow*		g_pImageWndTray;

extern CBelt*				g_pBeltA;

extern CBelt*				g_pBeltB;

extern CFixture*			g_pFixture;

extern CMachine*			g_pMachine;

extern CRobot*				g_pRobot;

extern CTray*				g_pTray;

extern HANDLE				g_hEventProcStart[4];

extern HANDLE				g_hEventProcStop[4];

extern CImageCalibrate*		g_pImageCalibrateTray;

extern CImageFlow*			g_pImageFlowCalibrateTray;

extern CImageCalibrate*		g_pImageCalibrateUp;

extern CImageFlow*			g_pImageFlowCalibrateUp;

extern CImageFlow*			g_pImageFlowCalibrateBoardUp;

extern CImageCalibrate*		g_pImageCalibrateDn;

extern CImageFlow*			g_pImageFlowCalibrateDn;

extern CImageFlow*			g_pImageFlowCalibrateBoardDn;

extern CImageFlow*			g_pImageFlowUp[4];

extern CImageFlow*			g_pImageFlowTray;

extern CImageFlow*			g_pImageFlowDn[4];

extern CImageFlow*			g_pImageFlowMark[4];

extern CImageFlow*			g_pImageFlowRobot;

extern CImageFlow*			g_pImageFlowScanCodeMainBoard[4];

extern CImageFlow*			g_pImageFlowScanCodeFixture;

extern CHeightSensor*		g_pHeightSensorL;

extern CHeightSensor*		g_pHeightSensorR;

extern vector<USER>			g_vUser;

extern bool					g_bAuto;

extern bool					g_bExit;