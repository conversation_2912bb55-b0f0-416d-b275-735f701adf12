﻿#pragma once

#include "ThreadBase.h"
using namespace yzBase;

class CLogicContinueRun : public CThreadBase
{
public:
	CLogicContinueRun();
	virtual ~CLogicContinueRun();

public:
	// 基础控制函数
	EnumStatus OnSafeCheck();       // 安全检查：检查连续运行系统安全状态
	EnumStatus OnStart();           // 启动控制：初始化连续运行系统
	EnumStatus OnPause();           // 暂停控制：暂停连续运行操作
	EnumStatus OnResume();          // 恢复控制：从暂停状态恢复连续运行操作
	EnumStatus OnStop();            // 停止控制：停止连续运行系统

public:
	// 主运行函数
	CStatus OnRun();                // 主状态机运行函数：协调连续运行系统工作流程

	// ========== 连续运行主流程 (OnContinue00-OnContinue04) ==========
	CStatus OnContinue00();         // 连续运行流程控制：控制连续运行主流程
	CStatus OnContinue01();         // 连续运行状态检查：检查连续运行状态
	CStatus OnContinue02();         // 连续运行流程分配：分配连续运行流程
	CStatus OnContinue03();         // 连续运行监控：监控连续运行状态
	CStatus OnContinue04();         // 连续运行完成：完成连续运行流程

	// ========== 托盘连续运行流程 (OnTray00-OnTray06) ==========
	CStatus OnTray00();             // 托盘连续运行控制：控制托盘连续运行流程
	CStatus OnTray01();             // 托盘连续运行准备：准备托盘连续运行
	CStatus OnTray02();             // 托盘连续运行执行：执行托盘连续运行
	CStatus OnTray03();             // 托盘连续运行监控：监控托盘连续运行状态
	CStatus OnTray04();             // 托盘连续运行验证：验证托盘连续运行结果
	CStatus OnTray05();             // 托盘连续运行完成：完成托盘连续运行
	CStatus OnTray06();             // 托盘连续运行结束：结束托盘连续运行流程

	// ========== 治具连续运行流程 (OnFixture00-OnFixture13) ==========
	CStatus OnFixture00();          // 治具连续运行控制：控制治具连续运行流程
	CStatus OnFixture01();          // 治具连续运行准备：准备治具连续运行
	CStatus OnFixture02();          // 治具连续运行执行：执行治具连续运行
	CStatus OnFixture03();          // 治具连续运行监控：监控治具连续运行状态
	CStatus OnFixture04();          // 治具连续运行验证：验证治具连续运行结果
	CStatus OnFixture05();          // 治具连续运行完成：完成治具连续运行
	CStatus OnFixture06();          // 治具连续运行后处理：治具连续运行后处理
	CStatus OnFixture07();          // 治具连续运行清理：清理治具连续运行区域
	CStatus OnFixture08();          // 治具连续运行记录：记录治具连续运行数据
	CStatus OnFixture09();          // 治具连续运行状态更新：更新治具连续运行状态
	CStatus OnFixture10();          // 治具连续运行流程验证：验证治具连续运行流程
	CStatus OnFixture11();          // 治具连续运行流程完成：完成治具连续运行流程
	CStatus OnFixture12();          // 治具连续运行流程结束：结束治具连续运行流程
	CStatus OnFixture13();          // 治具连续运行最终完成：最终完成治具连续运行

	// ========== 治具搬运连续运行流程 (OnFixtureTransport00-OnFixtureTransport18) ==========
	CStatus OnFixtureTransport00(); // 治具搬运连续运行控制：控制治具搬运连续运行流程
	CStatus OnFixtureTransport01(); // 治具搬运连续运行准备：准备治具搬运连续运行
	CStatus OnFixtureTransport02(); // 治具搬运连续运行执行：执行治具搬运连续运行
	CStatus OnFixtureTransport03(); // 治具搬运连续运行监控：监控治具搬运连续运行状态
	CStatus OnFixtureTransport04(); // 治具搬运连续运行验证：验证治具搬运连续运行结果
	CStatus OnFixtureTransport05(); // 治具搬运连续运行完成：完成治具搬运连续运行
	CStatus OnFixtureTransport06(); // 治具搬运连续运行后处理：治具搬运连续运行后处理
	CStatus OnFixtureTransport07(); // 治具搬运连续运行清理：清理治具搬运连续运行区域
	CStatus OnFixtureTransport08(); // 治具搬运连续运行记录：记录治具搬运连续运行数据
	CStatus OnFixtureTransport09(); // 治具搬运连续运行状态更新：更新治具搬运连续运行状态
	CStatus OnFixtureTransport10(); // 治具搬运连续运行流程验证：验证治具搬运连续运行流程
	CStatus OnFixtureTransport11(); // 治具搬运连续运行流程完成：完成治具搬运连续运行流程
	CStatus OnFixtureTransport12(); // 治具搬运连续运行流程结束：结束治具搬运连续运行流程
	CStatus OnFixtureTransport13(); // 治具搬运连续运行最终检查：最终检查治具搬运连续运行状态
	CStatus OnFixtureTransport14(); // 治具搬运连续运行最终验证：最终验证治具搬运连续运行结果
	CStatus OnFixtureTransport15(); // 治具搬运连续运行最终完成：最终完成治具搬运连续运行
	CStatus OnFixtureTransport16(); // 治具搬运连续运行最终后处理：治具搬运连续运行最终后处理
	CStatus OnFixtureTransport17(); // 治具搬运连续运行最终清理：治具搬运连续运行最终清理
	CStatus OnFixtureTransport18(); // 治具搬运连续运行最终结束：最终结束治具搬运连续运行

	// ========== A轨皮带连续运行流程 (OnBeltA00-OnBeltA07) ==========
	CStatus OnBeltA00();            // A轨皮带连续运行控制：控制A轨皮带连续运行流程
	CStatus OnBeltA01();            // A轨皮带连续运行准备：准备A轨皮带连续运行
	CStatus OnBeltA02();            // A轨皮带连续运行执行：执行A轨皮带连续运行
	CStatus OnBeltA03();            // A轨皮带连续运行监控：监控A轨皮带连续运行状态
	CStatus OnBeltA04();            // A轨皮带连续运行验证：验证A轨皮带连续运行结果
	CStatus OnBeltA05();            // A轨皮带连续运行完成：完成A轨皮带连续运行
	CStatus OnBeltA06();            // A轨皮带连续运行后处理：A轨皮带连续运行后处理
	CStatus OnBeltA07();            // A轨皮带连续运行结束：结束A轨皮带连续运行流程

	// ========== B轨皮带连续运行流程 (OnBeltB00-OnBeltB07) ==========
	CStatus OnBeltB00();            // B轨皮带连续运行控制：控制B轨皮带连续运行流程
	CStatus OnBeltB01();            // B轨皮带连续运行准备：准备B轨皮带连续运行
	CStatus OnBeltB02();            // B轨皮带连续运行执行：执行B轨皮带连续运行
	CStatus OnBeltB03();            // B轨皮带连续运行监控：监控B轨皮带连续运行状态
	CStatus OnBeltB04();            // B轨皮带连续运行验证：验证B轨皮带连续运行结果
	CStatus OnBeltB05();            // B轨皮带连续运行完成：完成B轨皮带连续运行
	CStatus OnBeltB06();            // B轨皮带连续运行后处理：B轨皮带连续运行后处理
	CStatus OnBeltB07();            // B轨皮带连续运行结束：结束B轨皮带连续运行流程

private:
	CString				m_sRet;             // 函数返回字符串结果

	map<CString, DWORD> m_mapTick;          // 时间计时器映射表，用于超时控制
	map<CString, bool>	m_mapFlag;          // 标志映射表，记录各种状态标志
	map<CString, double>m_mapPos;           // 位置映射表，记录各种位置信息
	
	CThreadFunc*		m_pFuncTray;        // 托盘连续运行线程函数指针
	CThreadFunc*		m_pFuncFixture;     // 治具连续运行线程函数指针
	CThreadFunc*		m_pFuncFixtureThransport; // 治具搬运连续运行线程函数指针
	CThreadFunc*		m_pFuncBeltA;       // A轨皮带连续运行线程函数指针
	CThreadFunc*		m_pFuncBeltB;       // B轨皮带连续运行线程函数指针
};
