x64-windows/
x64-windows/bin/
x64-windows/bin/paho-mqtt3a.dll
x64-windows/bin/paho-mqtt3a.pdb
x64-windows/bin/paho-mqtt3as.dll
x64-windows/bin/paho-mqtt3as.pdb
x64-windows/bin/paho-mqtt3c.dll
x64-windows/bin/paho-mqtt3c.pdb
x64-windows/bin/paho-mqtt3cs.dll
x64-windows/bin/paho-mqtt3cs.pdb
x64-windows/debug/
x64-windows/debug/bin/
x64-windows/debug/bin/paho-mqtt3a.dll
x64-windows/debug/bin/paho-mqtt3a.pdb
x64-windows/debug/bin/paho-mqtt3as.dll
x64-windows/debug/bin/paho-mqtt3as.pdb
x64-windows/debug/bin/paho-mqtt3c.dll
x64-windows/debug/bin/paho-mqtt3c.pdb
x64-windows/debug/bin/paho-mqtt3cs.dll
x64-windows/debug/bin/paho-mqtt3cs.pdb
x64-windows/debug/lib/
x64-windows/debug/lib/paho-mqtt3a.lib
x64-windows/debug/lib/paho-mqtt3as.lib
x64-windows/debug/lib/paho-mqtt3c.lib
x64-windows/debug/lib/paho-mqtt3cs.lib
x64-windows/include/
x64-windows/include/MQTTAsync.h
x64-windows/include/MQTTClient.h
x64-windows/include/MQTTClientPersistence.h
x64-windows/include/MQTTExportDeclarations.h
x64-windows/include/MQTTProperties.h
x64-windows/include/MQTTReasonCodes.h
x64-windows/include/MQTTSubscribeOpts.h
x64-windows/lib/
x64-windows/lib/paho-mqtt3a.lib
x64-windows/lib/paho-mqtt3as.lib
x64-windows/lib/paho-mqtt3c.lib
x64-windows/lib/paho-mqtt3cs.lib
x64-windows/share/
x64-windows/share/eclipse-paho-mqtt-c/
x64-windows/share/eclipse-paho-mqtt-c/eclipse-paho-mqtt-cConfig-debug.cmake
x64-windows/share/eclipse-paho-mqtt-c/eclipse-paho-mqtt-cConfig-release.cmake
x64-windows/share/eclipse-paho-mqtt-c/eclipse-paho-mqtt-cConfig.cmake
x64-windows/share/eclipse-paho-mqtt-c/eclipse-paho-mqtt-cConfigVersion.cmake
x64-windows/share/paho-mqtt/
x64-windows/share/paho-mqtt/copyright
x64-windows/share/paho-mqtt/vcpkg.spdx.json
x64-windows/share/paho-mqtt/vcpkg_abi_info.txt
x64-windows/tools/
x64-windows/tools/paho-mqtt/
x64-windows/tools/paho-mqtt/MQTTVersion.exe
