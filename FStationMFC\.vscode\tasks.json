{"version": "2.0.0", "tasks": [{"label": "Open in Visual Studio", "type": "shell", "command": "start", "args": ["Frame.sln"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Build with MSBuild (Debug)", "type": "shell", "command": "msbuild", "args": ["Frame.sln", "/p:Configuration=Debug", "/p:Platform=x64", "/m"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile"}, {"label": "Build with MSBuild (Release)", "type": "shell", "command": "msbuild", "args": ["Frame.sln", "/p:Configuration=Release", "/p:Platform=Win32", "/m"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile"}, {"label": "Clean Solution", "type": "shell", "command": "msbuild", "args": ["Frame.sln", "/t:Clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}