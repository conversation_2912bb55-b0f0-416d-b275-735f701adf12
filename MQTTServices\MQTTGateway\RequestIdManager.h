#pragma once

#include <string>
#include <unordered_map>
#include <unordered_set>
#include <mutex>
#include <chrono>
#include <QTimer>
#include <QObject>
#include <QJsonObject>
#include <QJsonDocument>

/**
 * 幂等性管理器
 * 
 * 根据SCADA协议v1.13要求实现：
 * - 缓存已处理的request_id，避免重复处理
 * - 缓存历史响应结果，用于重复请求直接返回
 * - 定期清理过期数据，防止内存泄漏
 * 
 * 适用于所有QoS 2的请求：
 * - 属性查询请求 (properties/get)
 * - 属性设置请求 (properties/set)  
 * - 命令下发请求 (commands)
 * - 事件上报 (events/up)
 */
class RequestIdManager : public QObject {
    Q_OBJECT

public:
    /**
     * 缓存的响应数据结构
     */
    struct CachedResponse {
        QJsonObject responseData;                           // 响应数据
        std::chrono::system_clock::time_point timestamp;    // 缓存时间
        std::string requestType;                            // 请求类型 (get/set/command/event)
        bool success;                                       // 处理是否成功
        
        CachedResponse() : success(false) {}
        
        CachedResponse(const QJsonObject& data, const std::string& type, bool isSuccess)
            : responseData(data), requestType(type), success(isSuccess) {
            timestamp = std::chrono::system_clock::now();
        }
    };

private:
    // 已处理的请求ID集合（用于快速检查）
    std::unordered_set<std::string> m_processedRequestIds;
    
    // 缓存的响应数据（用于重复请求返回相同结果）
    std::unordered_map<std::string, CachedResponse> m_cachedResponses;
    
    // 线程安全锁
    mutable std::mutex m_mutex;
    
    // 定期清理定时器
    QTimer* m_cleanupTimer;
    
    // 配置参数
    int m_cacheExpirationMinutes;  // 缓存过期时间（分钟）
    int m_cleanupIntervalMinutes;  // 清理间隔（分钟）
    size_t m_maxCacheSize;         // 最大缓存数量

public:
    explicit RequestIdManager(QObject* parent = nullptr);
    ~RequestIdManager();
    
    /**
     * 检查请求ID是否已被处理
     * @param requestId 请求ID
     * @return true=已处理，false=未处理
     */
    bool IsRequestProcessed(const std::string& requestId) const;
    
    /**
     * 标记请求ID为已处理，并缓存响应结果
     * @param requestId 请求ID
     * @param requestType 请求类型 (property_get/property_set/command/event)
     * @param responseData 响应数据
     * @param success 处理是否成功
     */
    void MarkRequestProcessed(const std::string& requestId, 
                             const std::string& requestType,
                             const QJsonObject& responseData,
                             bool success = true);
    
    /**
     * 获取缓存的响应数据（用于重复请求）
     * @param requestId 请求ID
     * @param cachedResponse 输出参数，缓存的响应数据
     * @return true=找到缓存，false=未找到
     */
    bool GetCachedResponse(const std::string& requestId, CachedResponse& cachedResponse) const;
    
    /**
     * 手动清理过期数据
     */
    void CleanupExpiredData();
    
    /**
     * 获取统计信息
     */
    struct Statistics {
        size_t processedCount;      // 已处理请求数量
        size_t cachedResponseCount; // 缓存响应数量
        size_t duplicateCount;      // 重复请求数量
    };
    
    Statistics GetStatistics() const;
    
    /**
     * 配置参数设置
     */
    void SetCacheExpirationMinutes(int minutes) { m_cacheExpirationMinutes = minutes; }
    void SetCleanupIntervalMinutes(int minutes);
    void SetMaxCacheSize(size_t maxSize) { m_maxCacheSize = maxSize; }

private slots:
    /**
     * 定时清理槽函数
     */
    void OnCleanupTimer();

private:
    /**
     * 检查缓存是否过期
     */
    bool IsCacheExpired(const std::chrono::system_clock::time_point& timestamp) const;
    
    /**
     * 强制清理最旧的缓存（当达到最大缓存数量时）
     */
    void ForceCleanupOldest();
    
    // 统计计数器
    mutable size_t m_duplicateRequestCount;
};
