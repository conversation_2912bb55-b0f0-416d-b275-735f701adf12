﻿#include "stdafx.h"
#include "Robot.h"

#include "Pro.h"

void __stdcall OnCaptureUp( BYTE *pBuffer, void *pUserData1, void *pUserData2, void *pUserData3, void *pUserData4, void *pUserData5, void *pUserData6)
{
	unsigned short* pWidth = (unsigned short *)pUserData1;
	unsigned short* pHeight = (unsigned short *)pUserData2;

	bool* pTranspose = (bool*)pUserData4;
	bool* pFlipX = (bool*)pUserData5;
	bool* pFlipY = (bool*)pUserData6;

	g_pRobot->PushImage(pBuffer, (int)(*pWidth), (int)(*pHeight), *pTranspose, *pFlipX, *pFlipY, "Up", g_nPicIndex);
}

void __stdcall OnCaptureDn( BYTE *pBuffer, void *pUserData1, void *pUserData2, void *pUserData3, void *pUserData4, void *pUserData5, void *pUserData6)
{
	unsigned short* pWidth = (unsigned short *)pUserData1;
	unsigned short* pHeight = (unsigned short *)pUserData2;

	bool* pTranspose = (bool*)pUserData4;
	bool* pFlipX = (bool*)pUserData5;
	bool* pFlipY = (bool*)pUserData6;

	g_pRobot->PushImage(pBuffer, (int)(*pWidth), (int)(*pHeight), *pTranspose, *pFlipX, *pFlipY, "Dn", g_nPicIndex);
}

void __stdcall OnCaptureTray( BYTE *pBuffer, void *pUserData1, void *pUserData2, void *pUserData3, void *pUserData4, void *pUserData5, void *pUserData6)
{
	unsigned short* pWidth = (unsigned short *)pUserData1;
	unsigned short* pHeight = (unsigned short *)pUserData2;

	bool* pTranspose = (bool*)pUserData4;
	bool* pFlipX = (bool*)pUserData5;
	bool* pFlipY = (bool*)pUserData6;

	g_pRobot->PushImage(pBuffer, (int)(*pWidth), (int)(*pHeight), *pTranspose, *pFlipX, *pFlipY, "Tray", 0);
}

static CString sRobotPoint[] = {
	"空闲等待位", 
	"主板进料1拍照位", 
	"主板进料2拍照位", 
	"主板进料3拍照位", 
	"主板进料正取料位", 
	"主板进料反取料位", 
	"TRAY盘正取放位", 
	"TRAY盘反取放位", 
	"下相机吸嘴1拍照位", 
	"下相机吸嘴2拍照位", 
	"下相机吸嘴3拍照位", 
	"下相机吸嘴4拍照位", 
	"下相机吸嘴1取料参考位", 
	"下相机吸嘴2取料参考位", 
	"下相机吸嘴3取料参考位", 
	"下相机吸嘴4取料参考位", 
	"A轨左下角Mark拍照位",
	"A轨右下角Mark拍照位",  
	"A轨左上角Mark拍照位",
	"A轨右上角Mark拍照位",  
	"B轨左下角Mark拍照位",   
	"B轨右下角Mark拍照位",  
	"B轨左上角Mark拍照位",   
	"B轨右上角Mark拍照位",     
	"A轨主板装配位", 
	"B轨主板装配位",
	"NG物料动作起始位", 
	"吸嘴1NG物料放料位", 
	"吸嘴1MESNG物料放料位", 
	"下相机标定位",  
	"上相机标定位",
	"上相机相机距离标定相机中心位",
	"上相机相机距离标定标定块设置位",
	"TRAY相机九点标定位1",
	"TRAY相机九点标定位2",
	"TRAY相机九点标定位3",
	"TRAY相机九点标定位4"
};

CRobot::CRobot() : CModule(1)
{
	for (int i=0; i<4; i++)
	{
		m_pImageBufferUp[i] = new unsigned char[5472 * 3648];
		m_pImageBufferDn[i] = new unsigned char[5472 * 3648];
	}

	m_pImageBufferTray = new unsigned char[4024 * 3036];
	
	MakePair("调试模式", new CData(false, false, 3));
	MakePair("纯收板模式", new CData(false, false, 3));
	MakePair("双主板装配模式", new CData(false, true));
	MakePair("MARK全拍模式", new CData(false, true, 4));
	MakePair("A轨自动计算补偿", new CData(false, false, 3));
	MakePair("B轨自动计算补偿", new CData(false, false, 3));

	MakePair("重新加载模板", new CData(0, 0, 5, false, 5));

	MakePair("主板回放标志", new CData(false, false));

	MakePair("TRAY盘进料标志", new CData(false, false));
	MakePair("TRAY盘退料标志", new CData(false, false));

	MakePair("TRAY盘主板行数", new CData(3));
	MakePair("TRAY盘主板列数", new CData(5));

	MakePair("Tray取料高度偏移", new CData(0.0));
	MakePair("Tray放料高度偏移", new CData(0.0));
	MakePair("皮带取料高度偏移", new CData(0.0));
	MakePair("A轨贴装高度偏移", new CData(0.0));
	MakePair("B轨贴装高度偏移", new CData(0.0));
	MakePair("治具取主板高度偏移", new CData(0.0));
	MakePair("A轨贴装角度偏移", new CData(0.0));
	MakePair("B轨贴装角度偏移", new CData(0.0));

	MakePair("主板回拍位置偏移X", new CData(0.0, -100.0, 100.0, true, 2));
	MakePair("主板回拍位置偏移Y", new CData(18.0, -100.0, 100.0, true, 2));
	
	MakePair("治具二维码扫描功能启用", new CData(false, false));
	MakePair("治具二维码位置偏移X", new CData(32.0, -100.0, 100.0, true, 2));
	MakePair("治具二维码位置偏移Y", new CData(32.0, -100.0, 100.0, true, 2));

	MakePair("皮带来料吸嘴1取料补偿X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("皮带来料吸嘴1取料补偿Y", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("皮带来料吸嘴2取料补偿X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("皮带来料吸嘴2取料补偿Y", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("皮带来料吸嘴3取料补偿X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("皮带来料吸嘴3取料补偿Y", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("皮带来料吸嘴4取料补偿X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("皮带来料吸嘴4取料补偿Y", new CData(0.0, -9999.999, 9999.999, true, 3));

	MakePair("TRAY盘来料吸嘴1取料补偿X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("TRAY盘来料吸嘴1取料补偿Y", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("TRAY盘来料吸嘴2取料补偿X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("TRAY盘来料吸嘴2取料补偿Y", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("TRAY盘来料吸嘴3取料补偿X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("TRAY盘来料吸嘴3取料补偿Y", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("TRAY盘来料吸嘴4取料补偿X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("TRAY盘来料吸嘴4取料补偿Y", new CData(0.0, -9999.999, 9999.999, true, 3));

	MakePair("A轨位置1贴装补偿X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("A轨位置1贴装补偿Y", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("A轨位置1贴装补偿R", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("A轨位置2贴装补偿X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("A轨位置2贴装补偿Y", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("A轨位置2贴装补偿R", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("A轨位置3贴装补偿X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("A轨位置3贴装补偿Y", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("A轨位置3贴装补偿R", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("A轨位置4贴装补偿X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("A轨位置4贴装补偿Y", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("A轨位置4贴装补偿R", new CData(0.0, -9999.999, 9999.999, true, 3));

	MakePair("B轨位置1贴装补偿X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("B轨位置1贴装补偿Y", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("B轨位置1贴装补偿R", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("B轨位置2贴装补偿X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("B轨位置2贴装补偿Y", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("B轨位置2贴装补偿R", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("B轨位置3贴装补偿X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("B轨位置3贴装补偿Y", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("B轨位置3贴装补偿R", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("B轨位置4贴装补偿X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("B轨位置4贴装补偿Y", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("B轨位置4贴装补偿R", new CData(0.0, -9999.999, 9999.999, true, 3));

	MakePair("贴装位置补偿X", new CData(0.0, -10.0, 10.0, true, 4));
	MakePair("贴装位置补偿Y", new CData(0.0, -10.0, 10.0, true, 4));
	MakePair("贴装位置补偿R", new CData(0.0, -10.0, 10.0, true, 4));
	
	MakePair("吸嘴吸料延时", new CData(200));
	MakePair("吸嘴贴装延时", new CData(200));

	MakePair("机械手速度百分比", new CData(100, 1, 100));
	MakePair("机械手Z轴上升速度百分比", new CData(100, 1, 100));
	MakePair("机械手Z轴下降速度百分比", new CData(100, 1, 100));

	MakePair("上相机(治具)曝光时间", new CData(10000));
	MakePair("上相机(治具)增益", new CData(0));

	MakePair("上相机(治具二维码)曝光时间", new CData(10000));
	MakePair("上相机(治具二维码)增益", new CData(0));

	MakePair("上相机(皮带)曝光时间", new CData(10000));
	MakePair("上相机(皮带)增益", new CData(0));

	MakePair("下相机曝光时间", new CData(10000));
	MakePair("下相机增益", new CData(0));

	MakePair("下相机(参考位)曝光时间", new CData(10000));
	MakePair("下相机(参考位)增益", new CData(0));

	MakePair("TRAY相机曝光时间", new CData(10000));
	MakePair("TRAY相机增益", new CData(0));

	MakePair("A轨治具角度R", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("B轨治具角度R", new CData(0.0, -9999.999, 9999.999, true, 3));

	MakePair("A轨位置1贴装位置偏移X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("A轨位置1贴装位置偏移Y", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("A轨位置2贴装位置偏移X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("A轨位置2贴装位置偏移Y", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("A轨位置3贴装位置偏移X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("A轨位置3贴装位置偏移Y", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("A轨位置4贴装位置偏移X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("A轨位置4贴装位置偏移Y", new CData(0.0, -9999.999, 9999.999, true, 3));

	MakePair("B轨位置1贴装位置偏移X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("B轨位置1贴装位置偏移Y", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("B轨位置2贴装位置偏移X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("B轨位置2贴装位置偏移Y", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("B轨位置3贴装位置偏移X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("B轨位置3贴装位置偏移Y", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("B轨位置4贴装位置偏移X", new CData(0.0, -9999.999, 9999.999, true, 3));
	MakePair("B轨位置4贴装位置偏移Y", new CData(0.0, -9999.999, 9999.999, true, 3));

	MakePair("吸嘴1基于旋转中心坐标X", new CData(0.0, -200.0, 200.0, true, 3));
	MakePair("吸嘴1基于旋转中心坐标Y", new CData(0.0, -200.0, 200.0, true, 3));
	MakePair("吸嘴2基于旋转中心坐标X", new CData(0.0, -200.0, 200.0, true, 3));
	MakePair("吸嘴2基于旋转中心坐标Y", new CData(0.0, -200.0, 200.0, true, 3));
	MakePair("吸嘴3基于旋转中心坐标X", new CData(0.0, -200.0, 200.0, true, 3));
	MakePair("吸嘴3基于旋转中心坐标Y", new CData(0.0, -200.0, 200.0, true, 3));
	MakePair("吸嘴4基于旋转中心坐标X", new CData(0.0, -200.0, 200.0, true, 3));
	MakePair("吸嘴4基于旋转中心坐标Y", new CData(0.0, -200.0, 200.0, true, 3));

	MakePair("吸嘴中心间距X", new CData(80.0, 0.0, 9999.999, true, 4));
	MakePair("吸嘴中心间距Y", new CData(60.0, 0.0, 9999.999, true, 4));

	MakePair("上相机中心基于旋转中心坐标X", new CData(0.0, -200.0, 200.0, true, 4));
	MakePair("上相机中心基于旋转中心坐标Y", new CData(0.0, -200.0, 200.0, true, 4));

	MakePair("上相机中心坐标X", new CData(0.0, 0.0, 9999.999, true, 4));
	MakePair("上相机中心坐标Y", new CData(0.0, 0.0, 9999.999, true, 4));

	MakePair("下相机中心坐标X", new CData(0.0, 0.0, 9999.999, true, 4));
	MakePair("下相机中心坐标Y", new CData(0.0, 0.0, 9999.999, true, 4));

	MakePair("TRAY相机中心坐标X", new CData(0.0, 0.0, 9999.999, true, 4));
	MakePair("TRAY相机中心坐标Y", new CData(0.0, 0.0, 9999.999, true, 4));

	Load();

	for (int i=0; i<37; i++)
	{
		CreateRobotPoint(sRobotPoint[i]);
	}

	LoadRobotPoint();
	
	InitializeCriticalSection(&m_cs);

	m_bRobotMoveFlag = false;
}

CRobot::~CRobot()
{
	map<CString, PROBOTPOINT>::iterator it = m_mapRobotPoint.begin();
	for (; it != m_mapRobotPoint.end(); it++)
	{
		delete it->second;
	}

	for (int i=0; i<4; i++)
	{
		delete []m_pImageBufferUp[i];
		delete []m_pImageBufferDn[i];
	}

	delete []m_pImageBufferTray;
}

CString CRobot::PickCylinderVacuumOn(int nIndex)
{
	CString sRobot;

	sRobot.Format("机械手取料气缸%d", nIndex + 1);

	EXCUTE_RETURN(g_pControl->WriteOutput(sRobot + "破真空", false));

	EXCUTE_RETURN(g_pControl->WriteOutput(sRobot + "真空", true));

	return "OK";
}

CString CRobot::PickCylinderBrokenVacuumOn(int nIndex)
{
	CString sRobot;

	sRobot.Format("机械手取料气缸%d", nIndex + 1);

	EXCUTE_RETURN(g_pControl->WriteOutput(sRobot + "真空", false));

	EXCUTE_RETURN(g_pControl->WriteOutput(sRobot + "破真空", true));

	return "OK";
}

CString CRobot::PickCylinderVacuumOff(int nIndex)
{
	CString sRobot;

	sRobot.Format("机械手取料气缸%d", nIndex + 1);

	EXCUTE_RETURN(g_pControl->WriteOutput(sRobot + "真空", false));

	EXCUTE_RETURN(g_pControl->WriteOutput(sRobot + "破真空", false));

	return "OK";
}

CString CRobot::PickCylinderVacuumStatus(int nIndex)
{
	CString sRobot;

	sRobot.Format("机械手取料气缸%d", nIndex + 1);

	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput(sRobot + "真空信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CRobot::PickCylinderOn(int nIndex)
{
	CString sRobot;

	sRobot.Format("机械手取料气缸%d", nIndex + 1);

	EXCUTE_RETURN(g_pControl->WriteOutput(sRobot, true));

	return "OK";
}

CString CRobot::PickCylinderOff(int nIndex)
{
	CString sRobot;

	sRobot.Format("机械手取料气缸%d", nIndex + 1);

	EXCUTE_RETURN(g_pControl->WriteOutput(sRobot, false));

	return "OK";
}

CString CRobot::PickCylinderStatus(int nIndex)
{
	CString sRobot;

	sRobot.Format("机械手取料气缸%d", nIndex + 1);

	bool bStatus1 = false, bStatus2 = false;

	EXCUTE_RETURN(g_pControl->ReadInput(sRobot + "缩回信号", bStatus1));

	EXCUTE_RETURN(g_pControl->ReadInput(sRobot + "伸出信号", bStatus2));

	if (bStatus1 && !bStatus2) {
		return "Off";
	}
	else if (!bStatus1 && bStatus2) {
		return "On";
	}
	else {
		return "UnKnow";
	}
}

CString CRobot::InBeltAInStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("入料皮带A有料信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CRobot::InBeltBInStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("入料皮带B有料信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CRobot::InBeltCInStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("入料皮带C有料信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CRobot::UpCameraLightOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("上相机光源", true));

	return "OK";
}

CString CRobot::UpCameraLightOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("上相机光源", false));

	return "OK";
}

CString CRobot::DnCameraLightOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("下相机光源", true));

	return "OK";
}

CString CRobot::DnCameraLightOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("下相机光源", false));

	return "OK";
}

CString CRobot::TrayCameraLightOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("TRAY盘相机光源", true));

	return "OK";
}

CString CRobot::TrayCameraLightOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("TRAY盘相机光源", false));

	return "OK";
}

CString CRobot::RegisterCamera()
{
	EXCUTE_RETURN(g_pCamera->Register("Up", (CAMERACAPTUREFUNC)(&OnCaptureUp), NULL));

	EXCUTE_RETURN(g_pCamera->Register("Dn", (CAMERACAPTUREFUNC)(&OnCaptureDn), NULL));

	EXCUTE_RETURN(g_pCamera->Register("Tray", (CAMERACAPTUREFUNC)(&OnCaptureTray), NULL));

	return "OK";
}

CString CRobot::GetPos(ROBOTPOINT &rbPoint)
{
	bool bFlag = false;

	EnterCriticalSection(&m_cs);

	do 
	{
		memset(&rbPoint, 0, sizeof(ROBOTPOINT));

		char	sSendBuff[] = "?Where:!";

		g_pSockRobot->SendCmd(sSendBuff, 8);

		int		nRecvLen = 128;
		char	sRecvBuff[128] = { 0 };

		g_pSockRobot->GetRecv(sRecvBuff, nRecvLen, 50);
		if (nRecvLen < 0) {
			break;
		}

		if (sRecvBuff[0] != '?' || sRecvBuff[nRecvLen - 1] != '!') {
			break;
		}

		CString sRecv = sRecvBuff;

		int nIndex = 0;

		nIndex = sRecv.Find(",", 0);
		if (nIndex < 0) {
			break;
		}

		rbPoint.x = atof(sRecv.Mid(1, nIndex));

		sRecv = sRecv.Mid(nIndex + 1);

		nIndex = sRecv.Find(",", 0);
		if (nIndex < 0) {
			break;
		}

		rbPoint.y = atof(sRecv.Mid(0, nIndex));

		sRecv = sRecv.Mid(nIndex + 1);
		nIndex = sRecv.Find(",", 0);
		if (nIndex < 0) {
			break;
		}

		rbPoint.z = atof(sRecv.Mid(0, nIndex));

		sRecv = sRecv.Mid(nIndex + 1);
		nIndex = sRecv.Find(",", 0);
		if (nIndex < 0) {
			break;
		}

		rbPoint.r = atof(sRecv.Mid(0, nIndex));

		sRecv = sRecv.Mid(nIndex + 1);
		nIndex = sRecv.Find(",", 0);
		if (nIndex < 0) {
			break;
		}

		rbPoint.cf1 = (int)atof(sRecv.Mid(0, nIndex));

		sRecv = sRecv.Mid(nIndex + 1);
		nIndex = sRecv.Find(",", 0);
		if (nIndex < 0) {
			break;
		}

		rbPoint.cf4 = (int)atof(sRecv.Mid(0, nIndex));

		sRecv = sRecv.Mid(nIndex + 1);
		nIndex = sRecv.Find(",", 0);
		if (nIndex < 0) {
			break;
		}

		rbPoint.cf6 = (int)atof(sRecv.Mid(0, nIndex));

		sRecv = sRecv.Mid(nIndex + 1);
		nIndex = sRecv.Find(",", 0);
		if (nIndex < 0) {
			break;
		}

		rbPoint.cfx = (int)atof(sRecv.Mid(0, nIndex));
		
		m_stCurRobPnt = rbPoint;

		bFlag = true;
	} while (false);

	LeaveCriticalSection(&m_cs);

	if (!bFlag) {
		return "获取机械手坐标失败";
	}

	m_bRobotMoveFlag = false;

	return "OK";
}

CString CRobot::Move(ROBOTPOINT stRobPnt, bool bCtrlSpeed/* = true*/, bool bSafeCheck/* = true*/, CString *pStr/* = NULL*/, int nSpeedRate/* = -1*/)
{
	if (m_bRobotMoveFlag) {
		return "OK";
	}

	bool bFlag = false;

	CString sRet;

	for (int i=0; i<4; i++)
	{
		sRet = g_pRobot->PickCylinderStatus(i);
		if (sRet != "Off" && bSafeCheck) {
			g_pRobot->PickCylinderOff(i);
			bFlag = true;
		}
	}

	if (bFlag) {
		if (pStr != NULL) {
			*pStr = "吸嘴未缩回，禁止移动机械手，吸嘴自动缩回,请重新点击移动机械手！";
		}
		return "OK";
	}

	int nSpeed = 0;

	if (bCtrlSpeed) {
		nSpeed = (int)(stRobPnt.speed * VAR_ROBOT_I("机械手速度百分比") / 100);
	}
	else {
		nSpeed = stRobPnt.speed;
	}

	if (nSpeedRate >=1 && nSpeedRate <= 100) {
		nSpeed = (int)(stRobPnt.speed * nSpeedRate / 100);
	}

// 	if (fabs(stRobPnt.x) < 0.1 || fabs(stRobPnt.y) < 0.1 || fabs(stRobPnt.z) < 0.1) {
// 		return "机械手坐标错误";
// 	}

	char sSendBuff[128] = { 0 };
	
	sprintf_s(sSendBuff, "?Move:%.3f,%.3f,%.3f,%.3f,%d,%d,%d,%d,%d,%d,!", 
		stRobPnt.x,
		stRobPnt.y,
		stRobPnt.z,
		stRobPnt.r,
		stRobPnt.cf1,
		stRobPnt.cf4,
		stRobPnt.cf6,
		stRobPnt.cfx,
		nSpeed,
		stRobPnt.confj);

	g_pSockRobot->SendCmd(sSendBuff, (int)strlen(sSendBuff));

	m_bRobotMoveFlag = true;

	return "OK";
}

CString CRobot::Move( CString sName, double nX, double nY, double nZ, double nR, bool bCtrlSpeed/* = true*/, bool bSafeCheck/* = true*/, CString *pStr/* = NULL*/, int nSpeedRate/* = -1*/)
{
	m_mapRobotPoint[sName]->x = nX;
	m_mapRobotPoint[sName]->y = nY;
	m_mapRobotPoint[sName]->z = nR;
	m_mapRobotPoint[sName]->r = nR;

	return Move(sName, bCtrlSpeed, bSafeCheck, pStr, nSpeedRate);
}

CString CRobot::Move(CString sName, bool bCtrlSpeed/* = true*/, bool bSafeCheck/* = true*/, CString *pStr/* = NULL*/, int nSpeedRate/* = -1*/)
{
	return Move(*m_mapRobotPoint[sName], bCtrlSpeed, bSafeCheck, pStr, nSpeedRate);
}

CString CRobot::Stop()
{
	char sSendBuff[] = "?Stop:!";

	g_pSockRobot->SendCmd(sSendBuff, 8);

	return "OK";
}

CString CRobot::IsInPos( CString sName )
{
	return IsInPos(*m_mapRobotPoint[sName]);
}

CString CRobot::IsInPos(ROBOTPOINT stRobPnt)
{
	CString sRet;

	ROBOTPOINT curRobPnt;

	sRet = GetPos(curRobPnt);

	if (sRet != "OK") {
		return "No";
	}

	if (fabs(stRobPnt.x - curRobPnt.x) > 1) {
		return "No";
	}

	if (fabs(stRobPnt.y - curRobPnt.y) > 1) {
		return "No";
	}

	if (fabs(stRobPnt.z - curRobPnt.z) > 1) {
		return "No";
	}

// 	if (fabs(stRobPnt.r - curRobPnt.r) > 2) {
// 		return "No";
// 	}

	return "Yes";
}

CString CRobot::ProcPrepare(int nIndex)
{
	m_vImage[nIndex].clear();
	m_vImageFlow[nIndex].clear();

	return "OK";
}

CString CRobot::PushImageFlow(CImageFlow *pFlow, CImageFlow* pFlowScanCode, CImageWindow* pWnd, CString sDir, CString sName, CString sCode2D, bool bScanCode2D, bool bScanAll, int nHeadIndex, bool bBadCheck, int nIndex)
{
	if (pFlow != NULL) {
		pFlow->Reset();
	}

	PROCINFO stProcInfo;

	stProcInfo.pImageFlow = pFlow;
	stProcInfo.pImageFlowScanCode = pFlowScanCode;
	stProcInfo.pImageWnd = pWnd;
	stProcInfo.sDir.Format("%s", sDir);
	stProcInfo.sName.Format("%s", sName);
	stProcInfo.nHeadIndex = nHeadIndex;
	stProcInfo.bScanCode2D = bScanCode2D;
	stProcInfo.bScanAll = bScanAll;
	stProcInfo.bMesResult = false;
	stProcInfo.bBadCheck = bBadCheck;
	stProcInfo.bBadCheckResult = false;
	stProcInfo.bTakePicOk = false;
	stProcInfo.bProcOk = false;

	stProcInfo.sCode2D.Format("%s", sCode2D);

	m_vImageFlow[nIndex].push_back(stProcInfo);

	return "OK";
}

CString CRobot::ProcStart(int nIndex)
{
	g_nPicIndex = nIndex;

	SetEvent(g_hEventProcStart[nIndex]);

	return "OK";
}

CString CRobot::ProcStop(int nIndex)
{
	SetEvent(g_hEventProcStop[nIndex]);

	return "OK";
}

CString CRobot::PushImage(unsigned char *pBuff, int nWidth, int nHeight, bool bTranspose, bool bFlipX, bool bFlipY, CString sCamera, int nIndex)
{
	unsigned char *pImage = NULL;

	if (sCamera == "Up") {
		pImage = m_pImageBufferUp[nIndex];
	}

	if (sCamera == "Dn") {
		pImage = m_pImageBufferDn[nIndex];
	}

	if (sCamera == "Tray") {
		pImage = m_pImageBufferTray;
	}

	if (pImage == NULL) {
		return "未分配内存";
	}

	memcpy(pImage, pBuff, nWidth * nHeight);

	IMAGINFO stImageInfo;

	stImageInfo.pBuffer = pImage;
	stImageInfo.nWidth = nWidth;
	stImageInfo.nHeight = nHeight;
	stImageInfo.bTranspose = bTranspose;
	stImageInfo.bFlipX = bFlipX;
	stImageInfo.bFlipY = bFlipY;
	stImageInfo.sCamera.Format("%s", sCamera);

	m_vImageFlow[nIndex][m_vImage[nIndex].size()].bTakePicOk = true;

	m_vImage[nIndex].push_back(stImageInfo);

	return "OK";
}

CString CRobot::PushImageResult(CCvImage* pImage, CImageWindow* pWnd, CString sDir, CString sName, bool bFlagOK)
{
	PPROCRESULT pProcRes = new PROCRESULT;

	pProcRes->pWnd = pWnd;
	pProcRes->pImage = pImage;
	pProcRes->sDir.Format("%s", sDir);
	pProcRes->sName.Format("%s", sName);
	pProcRes->bFlagOK = bFlagOK;

	m_vImageResult.push_back(pProcRes);

	return "OK";
}

void CRobot::CreateRobotPoint(CString sName)
{
	PROBOTPOINT pRobPnt = NULL;

	pRobPnt = new ROBOTPOINT;

	memset(pRobPnt, 0, sizeof(ROBOTPOINT));

	m_mapRobotPoint[sName] = pRobPnt;

	m_vecRobotPoint.push_back(sName);
}

void CRobot::LoadRobotPoint()
{
	CString sPath = CString(GetModulePath().c_str());
	CString sSysPath = sPath + "\\Sys\\Sys.ini";

	map<CString, PROBOTPOINT>::iterator it = m_mapRobotPoint.begin();
	for (; it != m_mapRobotPoint.end(); it++)
	{
		it->second->x = ReadIni(it->first, "x", 0.0, sSysPath);
		it->second->y = ReadIni(it->first, "y", 0.0, sSysPath);
		it->second->z = ReadIni(it->first, "z", 0.0, sSysPath);
		it->second->r = ReadIni(it->first, "r", 0.0, sSysPath);
		it->second->cf1 = ReadIni(it->first, "cf1", 0, sSysPath);
		it->second->cf4 = ReadIni(it->first, "cf4", 0, sSysPath);
		it->second->cf6 = ReadIni(it->first, "cf6", 0, sSysPath);
		it->second->cfx = ReadIni(it->first, "cfx", 0, sSysPath);
		it->second->speed = ReadIni(it->first, "speed", 30, sSysPath);
		it->second->confj = ReadIni(it->first, "confj", 1, sSysPath);
	}

	CString strProPath;
	strProPath.Format("%s\\Pro\\%s\\Pro.ini",  sPath.GetBuffer(), CPro::m_strPro.c_str());

	for (it = m_mapRobotPoint.begin(); it != m_mapRobotPoint.end(); it++)
	{
		int nconfj = 0;
		nconfj = ReadIni(it->first, "confj", 0, strProPath);

		if (nconfj != 1) {
			continue;
		}

		it->second->x = ReadIni(it->first, "x", 0.0, strProPath);
		it->second->y = ReadIni(it->first, "y", 0.0, strProPath);
		it->second->z = ReadIni(it->first, "z", 0.0, strProPath);
		it->second->r = ReadIni(it->first, "r", 0.0, strProPath);
		it->second->cf1 = ReadIni(it->first, "cf1", 0, strProPath);
		it->second->cf4 = ReadIni(it->first, "cf4", 0, strProPath);
		it->second->cf6 = ReadIni(it->first, "cf6", 0, strProPath);
		it->second->cfx = ReadIni(it->first, "cfx", 0, strProPath);
		it->second->speed = ReadIni(it->first, "speed", 30, strProPath);
		it->second->confj = ReadIni(it->first, "confj", 1, strProPath);
	}
}

void CRobot::SaveRobotPoint()
{
	map<CString, PROBOTPOINT>::iterator it = m_mapRobotPoint.begin();
	for (; it != m_mapRobotPoint.end(); it++)
	{
		SaveRobotPoint(it->first);
	}
}

void CRobot::SaveRobotPoint(CString sName)
{
	CString sPath = CString(GetModulePath().c_str());
	CString sSysPath = sPath + "\\Sys\\Sys.ini";

	WriteIni(sName.GetBuffer(), "x", m_mapRobotPoint[sName]->x, sSysPath.GetBuffer());
	WriteIni(sName.GetBuffer(), "y", m_mapRobotPoint[sName]->y, sSysPath.GetBuffer());
	WriteIni(sName.GetBuffer(), "z", m_mapRobotPoint[sName]->z, sSysPath.GetBuffer());
	WriteIni(sName.GetBuffer(), "r", m_mapRobotPoint[sName]->r, sSysPath.GetBuffer());
	WriteIni(sName.GetBuffer(), "cf1", m_mapRobotPoint[sName]->cf1, sSysPath.GetBuffer());
	WriteIni(sName.GetBuffer(), "cf4", m_mapRobotPoint[sName]->cf4, sSysPath.GetBuffer());
	WriteIni(sName.GetBuffer(), "cf6", m_mapRobotPoint[sName]->cf6, sSysPath.GetBuffer());
	WriteIni(sName.GetBuffer(), "cfx", m_mapRobotPoint[sName]->cfx, sSysPath.GetBuffer());
	WriteIni(sName.GetBuffer(), "speed", m_mapRobotPoint[sName]->speed, sSysPath.GetBuffer());
	WriteIni(sName.GetBuffer(), "confj", m_mapRobotPoint[sName]->confj, sSysPath.GetBuffer());

	CString strProPath;
	strProPath.Format("%s\\Pro\\%s\\Pro.ini",  sPath.GetBuffer(), CPro::m_strPro.c_str());

	WriteIni(sName.GetBuffer(), "x", m_mapRobotPoint[sName]->x, strProPath.GetBuffer());
	WriteIni(sName.GetBuffer(), "y", m_mapRobotPoint[sName]->y, strProPath.GetBuffer());
	WriteIni(sName.GetBuffer(), "z", m_mapRobotPoint[sName]->z, strProPath.GetBuffer());
	WriteIni(sName.GetBuffer(), "r", m_mapRobotPoint[sName]->r, strProPath.GetBuffer());
	WriteIni(sName.GetBuffer(), "cf1", m_mapRobotPoint[sName]->cf1, strProPath.GetBuffer());
	WriteIni(sName.GetBuffer(), "cf4", m_mapRobotPoint[sName]->cf4, strProPath.GetBuffer());
	WriteIni(sName.GetBuffer(), "cf6", m_mapRobotPoint[sName]->cf6, strProPath.GetBuffer());
	WriteIni(sName.GetBuffer(), "cfx", m_mapRobotPoint[sName]->cfx, strProPath.GetBuffer());
	WriteIni(sName.GetBuffer(), "speed", m_mapRobotPoint[sName]->speed, strProPath.GetBuffer());
	WriteIni(sName.GetBuffer(), "confj", m_mapRobotPoint[sName]->confj, strProPath.GetBuffer());
}

void CRobot::LoadMainBoardBackPos()
{
	CString sPath = CString(GetModulePath().c_str());

	CString strProPath;
	strProPath.Format("%s\\Pro\\%s\\BackPos.ini",  sPath.GetBuffer(), CPro::m_strPro.c_str());

	m_vecMainBoardBackPos.clear();

	for (int i=0; i<VAR_ROBOT_I("TRAY盘主板行数"); i++)
	{
		for (int j=0; j<VAR_ROBOT_I("TRAY盘主板列数"); j++)
		{
			CString sSec;
			sSec.Format("%02d", i * VAR_ROBOT_I("TRAY盘主板列数") + j);

			MAINBOARDBACKPOS stMainBoardBackPos;

			stMainBoardBackPos.bExistFlag = ReadIni(sSec, "ExistFlag", 0, strProPath) == 1 ? true : false;
			stMainBoardBackPos.nPixelX = ReadIni(sSec, "PixelX", 0.0, strProPath);
			stMainBoardBackPos.nPixelY = ReadIni(sSec, "PixelY", 0.0, strProPath);

			m_vecMainBoardBackPos.push_back(stMainBoardBackPos);
		}
	}
}

void CRobot::SaveMainBoardBackPos()
{
	for (int i=0; i<VAR_ROBOT_I("TRAY盘主板行数"); i++)
	{
		for (int j=0; j<VAR_ROBOT_I("TRAY盘主板列数"); j++)
		{
			SaveMainBoardBackPos(i * VAR_ROBOT_I("TRAY盘主板列数") + j);
		}
	}
}

void CRobot::SaveMainBoardBackPos(int nIndex)
{
	CString sPath = CString(GetModulePath().c_str());

	CString strProPath;
	strProPath.Format("%s\\Pro\\%s\\BackPos.ini",  sPath.GetBuffer(), CPro::m_strPro.c_str());

	CString sSec;
	sSec.Format("%02d", nIndex);

	WriteIni(sSec.GetBuffer(), "ExistFlag", (m_vecMainBoardBackPos[nIndex].bExistFlag ? 1 : 0), strProPath.GetBuffer());
	WriteIni(sSec.GetBuffer(), "PixelX", m_vecMainBoardBackPos[nIndex].nPixelX, strProPath.GetBuffer());
	WriteIni(sSec.GetBuffer(), "PixelY", m_vecMainBoardBackPos[nIndex].nPixelY, strProPath.GetBuffer());
}

void CRobot::SaveMainBoardBackPos(int nIndex, double nPixelX, double nPixelY, bool bExistFlag)
{
	CString sPath = CString(GetModulePath().c_str());

	CString strProPath;
	strProPath.Format("%s\\Pro\\%s\\BackPos.ini",  sPath.GetBuffer(), CPro::m_strPro.c_str());

	CString sSec;
	sSec.Format("%02d", nIndex);

	WriteIni(sSec.GetBuffer(), "ExistFlag", bExistFlag, strProPath.GetBuffer());
	WriteIni(sSec.GetBuffer(), "PixelX", nPixelX, strProPath.GetBuffer());
	WriteIni(sSec.GetBuffer(), "PixelY", nPixelY, strProPath.GetBuffer());
}

void CRobot::SimulateTakePicturesUp()
{
	CCvImage *pImage = CCvImage::CreateInstance();

	pImage->LoadImage();

	delete pImage;
}

void CRobot::SimulateTakePicturesDn()
{

}

void CRobot::SimulateTakePicturesMark()
{

}

void CRobot::SimulateTakePicturesTray()
{

}


void CRobot::GetCurPos( ROBOTPOINT &stRobPnt )
{
	stRobPnt = m_stCurRobPnt;
}
