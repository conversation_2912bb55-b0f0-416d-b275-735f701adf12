cmake_minimum_required(VERSION 3.16)

# 设置vcpkg工具链文件，此设置必须在project()命令之前
if(DEFINED ENV{VCPKG_ROOT})
    set(CMAKE_TOOLCHAIN_FILE "$ENV{VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake" CACHE STRING "vcpkg toolchain file" FORCE)
elseif(EXISTS "${CMAKE_SOURCE_DIR}/vcpkg/scripts/buildsystems/vcpkg.cmake")
    # Fallback to a vcpkg submodule in the project root
    set(CMAKE_TOOLCHAIN_FILE "${CMAKE_SOURCE_DIR}/vcpkg/scripts/buildsystems/vcpkg.cmake" CACHE STRING "vcpkg toolchain file" FORCE)
endif()

project(FStation VERSION 1.0.0 LANGUAGES CXX)

# --- 手动查找外部安装的Qt6 ---
if(WIN32)
    # 尝试在D:/Qt6/目录下自动查找
    file(GLOB QT_VERSIONS "D:/Qt6/*/")
    set(QT_FOUND FALSE)
    foreach(QT_VERSION ${QT_VERSIONS})
        if(IS_DIRECTORY "${QT_VERSION}msvc2022_64")
            list(APPEND CMAKE_PREFIX_PATH "${QT_VERSION}msvc2022_64")
            message(STATUS "自动找到Qt6 MSVC2022: ${QT_VERSION}msvc2022_64")
            set(QT_FOUND TRUE)
            break()
        endif()
    endforeach()

    # 如果自动查找失败，则使用一个明确的备用路径
    if(NOT QT_FOUND)
        set(QT_DEFAULT_PATH "D:/Qt6/6.9.1/msvc2022_64") # 请根据您的实际情况调整此路径
        if(EXISTS "${QT_DEFAULT_PATH}")
            list(APPEND CMAKE_PREFIX_PATH "${QT_DEFAULT_PATH}")
            message(STATUS "使用默认Qt6路径: ${QT_DEFAULT_PATH}")
        else()
            message(WARNING "无法自动或在默认路径中找到Qt6。请设置Qt6_DIR或将其路径添加到CMAKE_PREFIX_PATH。")
        endif()
    endif()
endif()

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Debug )
    message(STATUS "未指定构建类型，默认使用Debug模式")
else()
    message(STATUS "使用构建类型: ${CMAKE_BUILD_TYPE}")
endif()

# 添加子项目
add_subdirectory(MQTTGateway)
# 暂时禁用UI构建，因为它依赖完整的MQTTGateway类，而我们现在使用SimpleMQTTGateway
# add_subdirectory(MQTTGatewayUI)
# add_subdirectory(Tests) # 暂时禁用，因为目录不存在或不完整

# 设置默认构建目标
set_property(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR} PROPERTY VS_STARTUP_PROJECT MQTTGateway)

# ============================================================================
# 安装配置
# ============================================================================

# 包含安装配置模块
include(cmake/InstallConfig.cmake)
include(CMakePackageConfigHelpers)

# 调用安装函数
install_shared_headers()
install_config_files()
install_documentation()
install_vcpkg_dlls()
install_vcrt_dlls()

# 创建包配置文件
create_package_config()

# 导出目标以供其他项目使用
install(EXPORT FStationTargets
    FILE FStationTargets.cmake
    NAMESPACE FStation::
    DESTINATION "${CMAKE_INSTALL_LIBDIR}/cmake/FStation"
    COMPONENT Development
)

# 安装README文件
if(EXISTS "${CMAKE_SOURCE_DIR}/README.md")
    install(FILES "${CMAKE_SOURCE_DIR}/README.md"
        DESTINATION "${CMAKE_INSTALL_DOCDIR}"
        COMPONENT Documentation
    )
endif()

# 安装许可证文件
if(EXISTS "${CMAKE_SOURCE_DIR}/LICENSE")
    install(FILES "${CMAKE_SOURCE_DIR}/LICENSE"
        DESTINATION "${CMAKE_INSTALL_DOCDIR}"
        COMPONENT Documentation
    )
endif()

# ============================================================================
# CPack 打包配置
# ============================================================================

# 配置CPack
configure_cpack()

# 包含CPack
include(CPack)

# 显示安装信息
message(STATUS "")
message(STATUS "=== FStation 安装配置 ===")
message(STATUS "项目版本: ${PROJECT_VERSION}")
message(STATUS "安装前缀: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "可执行文件目录: ${CMAKE_INSTALL_BINDIR}")
message(STATUS "库文件目录: ${CMAKE_INSTALL_LIBDIR}")
message(STATUS "头文件目录: ${CMAKE_INSTALL_INCLUDEDIR}")
message(STATUS "配置文件目录: ${FSTATION_INSTALL_CONFIGDIR}")
message(STATUS "文档目录: ${CMAKE_INSTALL_DOCDIR}")
message(STATUS "")
message(STATUS "安装组件:")
message(STATUS "  - Runtime: 运行时文件（可执行文件、DLL、配置文件）")
message(STATUS "  - Development: 开发文件（头文件、静态库、CMake配置）")
message(STATUS "  - Documentation: 文档文件")
message(STATUS "")
message(STATUS "安装命令:")
message(STATUS "  完整安装: cmake --install . --prefix <安装目录>")
message(STATUS "  仅运行时: cmake --install . --prefix <安装目录> --component Runtime")
message(STATUS "  仅开发文件: cmake --install . --prefix <安装目录> --component Development")
message(STATUS "  仅文档: cmake --install . --prefix <安装目录> --component Documentation")
message(STATUS "")
message(STATUS "打包命令:")
message(STATUS "  生成安装包: cpack")
message(STATUS "  生成ZIP包: cpack -G ZIP")
if(WIN32)
    message(STATUS "  生成MSI包: cpack -G WIX")
endif()
message(STATUS "========================")
message(STATUS "") 