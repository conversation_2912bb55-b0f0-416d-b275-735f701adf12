---
alwaysApply: true
---

# 终端使用指南

本项目包含不同类型的子项目，构建和操作方式有所不同。所有命令都应在项目根目录下的 **PowerShell** 终端中执行。

## MQTT 网关 (CMake + vcpkg)

**配置 (首次运行):**
```powershell
cmake -B build -S . -DCMAKE_TOOLCHAIN_FILE=D:/vcpkg/scripts/buildsystems/vcpkg.cmake
```
*请将 `D:/vcpkg/scripts/buildsystems/vcpkg.cmake` 替换为你的实际 vcpkg 路径。*

**构建:**
```powershell
cmake --build build
```

**运行:**
```powershell
# 运行后端
./build/MQTTGateway/Debug/MQTTGateway.exe

# 运行UI
./build/MQTTGatewayUI/Debug/MQTTGatewayUI.exe
```

## FStationMFC (Visual Studio)

此项目应直接在 Visual Studio IDE 中打开、构建和运行。
- **解决方案文件**: `FStationMFC/Frame.sln`
- 在Visual Studio中选择 `Debug` 或 `Release` 配置，然后点击 "本地Windows调试器" 启动。
