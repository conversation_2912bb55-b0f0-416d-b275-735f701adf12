#pragma once

#include "Module.h"
#include <list>
using namespace std;

typedef struct _IN_BELT_STATUS
{
	int		nIndex;

	bool	bInFlag;

	DWORD	tLastPick;
	DWORD	tNowIn;
	DWORD	tNowPick;
}INBELTSTATUS, *PINBELTSTATUS;

typedef struct _PRODUCTION_CYCLE_
{
	bool	bFree;
}PRODUCTIONCYCLE, *PPRODUCTIONCYCLE;

class CMachine : public CModule
{
public:
	CMachine();
	virtual ~CMachine();

public:
	CString BootLightOn();
	CString BootLightOff();

	CString NGBeltLightOn();
	CString NGBeltLightOff();

	CString RedLightOn();
	CString RedLightOff();

	CString GreenLightOn();
	CString GreenLightOff();

	CString YellowLightOn();
	CString YellowLightOff();

	CString BuzzerOn();
	CString BuzzerOff();

	CString NGBeltOn(bool bDir);
	CString NGBeltOff();

	CString EmergencyStatus();

	CString BootStatus();

	CString NGBeltBootStatus();

	CString LeftDoorStatus();
	CString RightDoorStatus();
	CString FrontDoorStatus();
	CString BackDoorStatus();

	CString SafetyGratingStatus();
	
	CString NGBeltSignal1Status();
	CString NGBeltSignal2Status();
	CString NGBeltSignal3Status();

public:
	INBELTSTATUS			m_stInBeltStatus[3];
	PRODUCTIONCYCLE			m_stProductionCircle;

	list<INBELTSTATUS>		m_listInBeltStatus;
	list<PRODUCTIONCYCLE>	m_listProductionCycle;
};
