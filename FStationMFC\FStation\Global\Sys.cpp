﻿#include "stdafx.h"
#include "Sys.h"

#include "BaseApi.h"
using namespace yzBase;

string CSys::m_strPro;

int  CSys::m_nRight = 4;

bool CSys::m_bInit = false;

bool CSys::m_bTryRun = false;

EnumRunStatus CSys::m_emRunStatus = emRunStatusPreReset;

bool CSys::m_bBeepEnable = false;
bool CSys::m_bDoorEnable = true;
bool CSys::m_bDoorNotUse = false;

int CSys::m_nRecordOutDate = 30;

int CSys::m_nTotalSum = 0;

int	CSys::m_nTodayOK = 0;
int	CSys::m_nTodayNG = 0;
CString	CSys::m_sToday = "";

bool CSys::m_bEngineer = false;

int	 CSys::m_nDevId = 4;

CString CSys::m_sFirstCode;

void CSys::InitParam()
{
	CString strPath = CString(GetModulePath().c_str());
	CString strSysPath = strPath + "\\Sys";

	m_bEngineer = ReadIni("Setting", "Debug", 0,  "C:\\Config.ini") == 1 ? true : false;

	g_pInfo = new CDbInfo;

	g_pInfo->InitSysInfo(strSysPath + "\\Sys.mdb", "123");

	g_pControl = CControl::CreateInstance();

	g_pControl->Init(g_pInfo);

	g_pCamera = CCamera::CreateInstance();

	g_pProduction = CProductionBase::CreateInstance();

	g_pDatLog = new CDat("Log");

	g_pDatImage = new CDat("Image");

	g_pSockRobot = CSockBase::CreateClient();

	g_pSockAgv = CSockBase::CreateServer();

	g_pHeightSensorL = new CHeightSensor("HeightSensorA");
	g_pHeightSensorR = new CHeightSensor("HeightSensorB");

	for (int i=0; i<4; i++)
	{
		g_hEventProcStart[i] = CreateEvent(NULL, FALSE, FALSE, NULL);
		g_hEventProcStop[i] = CreateEvent(NULL, FALSE, FALSE, NULL);
	}
	
	// 产量信息
	g_pProduction->Load();
	
	strSysPath += "\\sys.ini";

	m_sFirstCode = ReadIni("Machine", "FirstCode", "", strSysPath);

	m_sToday = ReadIni("Machine", "Today", "", strSysPath);

	m_nTodayOK = ReadIni("Machine", "TodayOK", 0, strSysPath);
	m_nTodayNG = ReadIni("Machine", "TodayNG", 0, strSysPath);

	// 选项
	m_bBeepEnable = atoi(ReadIni("Machine", "BeepEnable", strSysPath.GetBuffer()).c_str()) == 1;
	strSysPath.ReleaseBuffer();

	m_nTotalSum = ReadIni("SUM", "TotalSum", 0, strSysPath);
	
	m_nRecordOutDate = atoi(ReadIni("Parameter", "RecordOutDate ", strSysPath.GetBuffer()).c_str());
	if (m_nRecordOutDate < 3) {
		m_nRecordOutDate = 3;
	}
}

void CSys::InitMachine()
{
	g_pRobot	= new CRobot();

	g_pTray		= new CTray();

	g_pFixture	= new CFixture();

	g_pBeltA	= new CBelt("A轨");

	g_pBeltB	= new CBelt("B轨");

	g_pMachine	= new CMachine();
}

void CSys::DelayInit()
{
	CString sRet;

	if (m_bEngineer) {
		g_pSockRobot->InitClient("127.0.0.1", 8000);
	}
	else {
		g_pSockRobot->InitClient("192.168.125.1", 8000);
	}

	sRet = g_pHeightSensorL->Init();
	if (sRet != "OK") {
		REPORT(sRet, emLogLevelError);
	}

	sRet = g_pHeightSensorR->Init();
	if (sRet != "OK") {
		REPORT(sRet, emLogLevelError);
	}

	sRet = g_pCamera->Init();
	if (sRet != "OK") {
		REPORT(sRet, emLogLevelError);
	}

	sRet = g_pCamera->Open("Up", false, true, true);
	if (sRet != "OK") {
		REPORT(sRet, emLogLevelError);
	}

	sRet = g_pCamera->Open("Dn", false, true, false);
	if (sRet != "OK") {
		REPORT(sRet, emLogLevelError);
	}

	sRet = g_pCamera->Open("Tray", false, true, true);
	if (sRet != "OK") {
		REPORT(sRet, emLogLevelError);
	}

	sRet = g_pRobot->RegisterCamera();
	if (sRet != "OK") {
		REPORT(sRet, emLogLevelError);
	}

	sRet = g_pCamera->Start("Up");
	if (sRet != "OK") {
		REPORT(sRet, emLogLevelError);
	}

	sRet = g_pCamera->Start("Dn");
	if (sRet != "OK") {
		REPORT(sRet, emLogLevelError);
	}

	sRet = g_pCamera->Start("Tray");
	if (sRet != "OK") {
		REPORT(sRet, emLogLevelError);
	}

	ROBOTPOINT stRobPnt;

	if (g_pRobot->GetPos(stRobPnt) != "OK") {
		REPORT("机械手连接失败，请查看机械手软件处理异常！", emLogLevelError);
	}

	g_pCamera->SetTriggerMode("Up", CCameraBase::emTriggerModeSoftware);

	g_pCamera->SetTriggerMode("Dn", CCameraBase::emTriggerModeSoftware);
	
	g_pCamera->SetTriggerMode("Tray", CCameraBase::emTriggerModeSoftware);

	CString strPath = CString(GetModulePath().c_str());
	CString strSysPath = strPath + "\\Sys";

	g_pImageCalibrateTray = CImageCalibrate::CreateInstance("Tray相机标定", strSysPath + "\\Calibrate");

	g_pImageCalibrateUp = CImageCalibrate::CreateInstance("上相机标定", strSysPath + "\\Calibrate");

	g_pImageCalibrateDn = CImageCalibrate::CreateInstance("下相机标定", strSysPath + "\\Calibrate");

	g_pImageFlowCalibrateTray = CImageFlow::CreateInstance("Tray相机标定图像处理流程", strSysPath + "\\Calibrate", g_pCamera, "Tray");

	g_pImageFlowCalibrateUp = CImageFlow::CreateInstance("上相机标定图像处理流程", strSysPath + "\\Calibrate", g_pCamera, "Up");

	g_pImageFlowCalibrateBoardUp = CImageFlow::CreateInstance("上相机标定板标定图像处理流程", strSysPath + "\\Calibrate", g_pCamera, "Up");

	g_pImageFlowCalibrateDn = CImageFlow::CreateInstance("下相机标定图像处理流程", strSysPath + "\\Calibrate", g_pCamera, "Dn");

	g_pImageFlowCalibrateBoardDn = CImageFlow::CreateInstance("下相机标定板标定图像处理流程", strSysPath + "\\Calibrate", g_pCamera, "Dn");

	g_pImageFlowRobot = CImageFlow::CreateInstance("机械手重复性测试图像处理流程", strSysPath + "\\Calibrate", g_pCamera, "Dn");
}

void CSys::DeInit()
{
	SaveCurrentPro();

	for (int i=0; i<4; i++)
	{
		CloseHandle(g_hEventProcStart[i]);
		CloseHandle(g_hEventProcStop[i]);
	}

	delete g_pRobot;

	delete g_pTray;		

	delete g_pFixture;

	delete g_pBeltA;	

	delete g_pBeltB;	

	delete g_pMachine;

	delete g_pProduction;

	delete g_pDatLog;

	delete g_pDatImage;

	g_pSockRobot->CloseSocket();

	delete g_pSockRobot;

	delete g_pInfo;

	delete g_pImageCalibrateTray;

	delete g_pImageCalibrateUp;

	delete g_pImageFlowCalibrateUp;

	delete g_pImageCalibrateDn;

	delete g_pImageFlowCalibrateDn;

	delete g_pHeightSensorL;
	delete g_pHeightSensorR;

	g_pControl->Close();
	delete g_pControl;
}

string CSys::GetCurrentPro()
{
	CString strPath = CString(GetModulePath().c_str());
	CString strSysPath = strPath + "\\Sys\\Sys.ini";

	m_strPro = ReadIni("Product", "Current", strSysPath.GetBuffer());

	return m_strPro;
}

void CSys::SaveCurrentPro()
{
	CString strPath = CString(GetModulePath().c_str());
	CString strSysPath = strPath + "\\Sys\\Sys.ini";

	WriteIni("Product", "Current", m_strPro, strSysPath.GetBuffer());
}

void CSys::SaveSys()
{
	CString strPath = CString(GetModulePath().c_str());
	CString strSysPath = strPath + "\\Sys\\Sys.ini";
	// 选项
	WriteIni("Machine", "BeepEnable", m_bBeepEnable, strSysPath.GetBuffer());
	strSysPath.ReleaseBuffer();

	WriteIni("Parameter", "RecordOutDate", m_nRecordOutDate, strSysPath.GetBuffer());
	strSysPath.ReleaseBuffer();
}