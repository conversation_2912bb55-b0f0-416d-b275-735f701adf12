#pragma once
#include <iostream>
#include<vector>
using namespace std;
#include "zip.h"
#include "unzip.h"

class ZipHelper
{
public:
	ZipHelper();
	~ZipHelper();

private:
	HZIP m_hz;				 //Zip文件句柄 
	ZRESULT m_zr;			 //操作返回值 
	ZIPENTRY m_ze;			 //Zip文件入口

	CString m_FolderPath;	 //folder路径 
	CString  m_FolderName;   //folder将要被压缩的文件夹名

	int m_nZipCnt;
private:
	//实现遍历文件夹 
	void BrowseFile(CString &strFile);

	//获取相对路径 
	void GetRelativePath(CString& pFullPath, CString& pSubString);

	//创建路径 
	BOOL CreatedMultipleDirectory(wchar_t* direct);

	//压缩解压缩接口 
	BOOL Zip_PackFiles(vector<CString> arr, CString mZipFileFullPath);

	BOOL Zip_UnPackFiles(CString mZipFileFullPath, CString mUnPackPath);

	//检查文件(夹)存在
	BOOL IsFolderExist(CString& strPath);

	BOOL IsFileExist(CString& strPath);
public:
	void PressFolder(CString strTgt,CString strSrcFolder, vector<CString> arrSrcFloder);//指定压缩包  单一路径 路径集合
	
	void UnPressFolder(CString strTgt);//指定解压目标
private:
	vector<CString>  m_arrFileFullName;
};