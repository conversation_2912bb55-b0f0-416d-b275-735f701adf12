#include "stdafx.h"
#include "HeightSensor.h"

void OnRecv(BYTE *pBuff, int nCount, void *pSender)
{
	CHeightSensor* pSensor = (CHeightSensor*)pSender;

	if (!pSensor->m_bRecvFlag) {
		pSensor->m_sRecv.Empty();
		return;
	}

	if (nCount < 5) {
		pSensor->m_sRecv.Empty();
		return;
	}

	BYTE buffer[128] = { 0 };

	memcpy(buffer, pBuff + 1, (nCount - 2) * sizeof(BYTE));

	pSensor->m_sRecv = CString(buffer);

	pSensor->m_bRecvOkFlag = true;
}

CHeightSensor::CHeightSensor(CString sName)
{
	m_sName = sName;

	m_bRecvFlag = false;
	m_bRecvOkFlag = false;

	Load();
}

CHeightSensor::~CHeightSensor()
{

}

void CHeightSensor::Load()
{
	CString strPath = CString(GetModulePath().c_str());
	CString strSysPath = strPath + "\\Sys\\Sys.ini";

	m_nCom = ReadIni(m_sName, "Com", 5, strSysPath);
	m_nBaud = ReadIni(m_sName, "Baud", 9600, strSysPath);
}

CString CHeightSensor::Init()
{
	CString sRet;

	sRet = CSerialBase::Init(m_nCom, m_nBaud);

	if (sRet != "OK") {
		return sRet;
	}

	Register((SERIALRECVFUNC)OnRecv, this);

	return "OK";
}

CString CHeightSensor::IsConnected()
{
	char sBuff[10] = { 0 };

	sBuff[0] = 2;
	sBuff[1] = 'S';
	sBuff[2] = 'E';
	sBuff[3] = 'R';
	sBuff[4] = 'I';
	sBuff[5] = 'A';
	sBuff[6] = 'L';
	sBuff[7] = 'N';
	sBuff[8] = 'O';
	sBuff[9] = 3;

	m_bRecvOkFlag = false;
	m_bRecvFlag = true;

	CString sRet;

	sRet = Output(sBuff, 10);

	if (sRet != "OK") {
		return sRet;
	}

	DWORD nTick = GetTickCount();
	while (true)
	{
		if (GetTickCount() - nTick > 200) {
			return "No";
		}

		if (m_bRecvOkFlag) {
			break;
		}
	}

	if (m_sRecv == "255") {
		return "Yes";
	}
	else {
		return "No";
	}
}

CString CHeightSensor::GetHeight(double &nHeight)
{
	char sBuff[13] = { 0 };

	sBuff[0] = 2;
	sBuff[1] = 'M';
	sBuff[2] = 'E';
	sBuff[3] = 'A';
	sBuff[4] = 'S';
	sBuff[5] = 'U';
	sBuff[6] = 'R';
	sBuff[7] = 'E';
	sBuff[8] = ' ';
	sBuff[9] = '2';
	sBuff[10] = '5';
	sBuff[11] = '5';
	sBuff[12] = 3;

	nHeight = 0;

	m_bRecvOkFlag = false;
	m_bRecvFlag = true;

	CString sRet;

	sRet = Output(sBuff, 13);

	if (sRet != "OK") {
		return sRet;
	}

	DWORD nTick = GetTickCount();
	while (true)
	{
		if (GetTickCount() - nTick > 200) {
			return "Distance is out of range.";
		}

		if (m_bRecvOkFlag) {
			break;
		}
	}

	double nTemp = 0;
	nTemp = atof(m_sRecv);

	if (nTemp < 80 || nTemp > 100) {
		return "Distance is out of range.";
	}

	nHeight = nTemp;

	return "OK";
}
