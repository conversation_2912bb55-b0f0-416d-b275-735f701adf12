# FStation机器人控制系统详细分析

## 概述

FStation机器人控制系统是整个自动化贴装系统的核心执行单元，由LogicRobot.cpp（5914行代码）实现，是系统中最复杂和最关键的模块。该系统负责控制4轴机器人的精确运动、4个吸嘴的协调工作、以及与三个视觉系统的集成控制。

## 机器人硬件架构

### 1. 运动系统
```cpp
// 4轴运动控制
X轴：水平方向移动（±9999.999mm精度）
Y轴：垂直方向移动（±9999.999mm精度）  
Z轴：上下移动（带速度分级控制）
R轴：旋转运动（角度补偿±9999.999°）

// 速度控制系统
机械手速度百分比：1-100%可调
Z轴上升速度百分比：独立控制
Z轴下降速度百分比：独立控制
```

### 2. 吸嘴系统
```cpp
// 4个独立吸嘴头
struct HEADPROCRESULT {
    bool bFromTray;          // 是否来自TRAY盘
    bool bHeadPickOk;        // 吸嘴取料状态
    int nProcTimes;          // 处理次数
    bool bHeadThrowFlag;     // 丢料标志
    bool bMesNgFlag;         // MES不良标志
    bool bHeadProcOk;        // 处理完成标志
    double nHeadPixelX/Y/R;  // 像素坐标
    double nHeadMachineX/Y/R;// 机械坐标
};
```

### 3. 视觉系统
```cpp
// 三相机系统集成
上相机（Up）：  5472×3648分辨率，治具定位和二维码扫描
下相机（Dn）：  5472×3648分辨率，元器件检测和位置识别  
TRAY相机：    4024×3036分辨率，托盘定位和主板识别

// 视觉处理回调函数
void OnCaptureUp(BYTE *pBuffer, ...);   // 上相机采集
void OnCaptureDn(BYTE *pBuffer, ...);   // 下相机采集  
void OnCaptureTray(BYTE *pBuffer, ...); // TRAY相机采集
```

## 关键数据结构

### 1. 吸嘴状态管理
```cpp
HEADPROCRESULT m_stHeadProcResult[4];  // 4个吸嘴状态数组

// 吸嘴取料序列控制
int m_nDnCamCaptureSequence[4] = {3, 2, 1, 0};  // 拍照序列
```

### 2. TRAY盘主板信息
```cpp
struct TRAYMAINBOARDINFO {
    double nPixelX, nPixelY, nPixelR;    // 像素坐标
    bool bExistFlag;                     // 存在标志
    bool bProcOk;                        // 处理完成
    int nProcTimes;                      // 处理次数
    bool bMesNgFlag;                     // MES不良标志
    bool bBadCheckNgFlag;                // 坏料检测标志
    bool bHasMaterialFlag;               // 有料标志
    CString sCode2D;                     // 二维码信息
};

TRAYMAINBOARDINFO *m_pMainBoardInTray;   // 动态分配数组
```

### 3. 皮带来料信息
```cpp
TRAYMAINBOARDINFO m_stInMainBoardInBelt[3];  // 3个进料位置
```

### 4. Mark识别信息
```cpp
struct MARKINFO {
    double nPixelX, nPixelY;             // Mark像素坐标
    double nMachineX, nMachineY;         // Mark机械坐标
    bool bFindFlag;                      // 识别成功标志
};

MARKINFO m_stMarkInfo[8];                // 8个Mark点（A轨4个+B轨4个）
```

## 机器人控制状态机

### 1. 主状态机结构
```cpp
CStatus OnRun()                          // 主运行入口
├── 初始化和参数设置
├── 数据结构初始化  
├── 相机注册和参数配置
└── 进入工作循环

// 主要工作状态
OnRobot00()     // 主状态机入口
OnRobot01()     // 取料检测状态
OnRobot02()     // 贴装准备状态
OnRobot03()     // 执行贴装状态
```

### 2. 取料状态机
```cpp
// 皮带取料流程
OnRobot01()
├── 检测皮带来料信号
├── 视觉识别主板位置
├── 计算取料补偿
├── 执行吸嘴取料
└── 验证取料结果

// TRAY取料流程  
OnTrayPick()
├── TRAY相机定位
├── 计算主板阵列位置
├── 选择最优取料序列
├── 多吸嘴协调取料
└── 更新TRAY状态
```

### 3. 检测状态机
```cpp
// 下相机检测流程
OnDnCameraCheck()
├── 移动到检测位置
├── 设置相机参数
├── 执行图像采集
├── 视觉算法处理
├── 计算位置偏差
└── 更新补偿参数
```

### 4. 贴装状态机
```cpp
// 贴装执行流程
OnPlace()
├── Mark拍照定位
├── 计算贴装补偿
├── 移动到贴装位置
├── 执行贴装动作
├── 验证贴装结果
└── 处理异常情况
```

## 视觉处理系统

### 1. 图像采集管理
```cpp
// 图像缓存管理
unsigned char *m_pImageBufferUp[4];      // 上相机缓存（4个吸嘴）
unsigned char *m_pImageBufferDn[4];      // 下相机缓存（4个吸嘴）
unsigned char *m_pImageBufferTray;       // TRAY相机缓存

// 图像处理接口
CString PushImage(unsigned char *pBuff, int nWidth, int nHeight, 
                  bool bTranspose, bool bFlipX, bool bFlipY, 
                  CString sCamera, int nIndex);
```

### 2. 相机参数控制
```cpp
// 曝光时间和增益设置
上相机(治具)曝光时间：        10000μs
上相机(治具)增益：           0
上相机(治具二维码)曝光时间：  10000μs
上相机(治具二维码)增益：      0
上相机(皮带)曝光时间：        10000μs
上相机(皮带)增益：           0
下相机曝光时间：             10000μs
下相机增益：                 0
下相机(参考位)曝光时间：      10000μs
下相机(参考位)增益：          0
TRAY相机曝光时间：           10000μs
TRAY相机增益：               0
```

### 3. 视觉标定和补偿
```cpp
// 坐标转换和补偿计算
OnStart() 函数中的标定逻辑：
├── 获取下相机标定坐标
├── 计算中心偏移量
├── 更新4个吸嘴基于旋转中心的坐标
└── 保存标定参数

// 各种补偿参数
皮带来料吸嘴1-4取料补偿X/Y：  ±9999.999mm
TRAY盘来料吸嘴1-4取料补偿X/Y： ±9999.999mm
A轨位置1-4贴装补偿X/Y/R：     ±9999.999mm/°
B轨位置1-4贴装补偿X/Y/R：     ±9999.999mm/°
```

## 运动控制系统

### 1. 位置点管理
```cpp
// 36个标准位置点定义
static CString sRobotPoint[36] = {
    "空闲等待位",
    "主板进料1拍照位", "主板进料2拍照位", "主板进料3拍照位",
    "主板进料正取料位", "主板进料反取料位",
    "TRAY盘正取放位", "TRAY盘反取放位",
    "下相机吸嘴1-4拍照位",
    "下相机吸嘴1-4取料参考位",
    "A轨左下/右下/左上/右上角Mark拍照位",
    "B轨左下/右下/左上/右上角Mark拍照位",
    "A轨主板装配位", "B轨主板装配位",
    "NG物料动作起始位", "吸嘴1NG物料放料位", "吸嘴1MESNG物料放料位",
    "下相机标定位", "上相机标定位",
    "上相机相机距离标定相机中心位", "上相机相机距离标定标定块设置位",
    "TRAY相机九点标定位1-4"
};
```

### 2. 运动控制接口
```cpp
// 基础运动控制
CString Move(ROBOTPOINT stRobPnt, bool bCtrlSpeed = true, 
             bool bSafeCheck = true, CString *pStr = NULL, 
             int nSpeedRate = -1);

CString Move(CString sName, double nX, double nY, double nZ, double nR,
             bool bCtrlSpeed = true, bool bSafeCheck = true,
             CString *pStr = NULL, int nSpeedRate = -1);

CString Move(CString sName, bool bCtrlSpeed = true, 
             bool bSafeCheck = true, CString *pStr = NULL,
             int nSpeedRate = -1);

// 位置检测  
CString IsInPos(CString sName);
CString IsInPos(ROBOTPOINT stRobPnt);
```

### 3. 吸嘴控制接口
```cpp
// 真空控制
CString PickCylinderVacuumOn(int nIndex);       // 打开真空
CString PickCylinderBrokenVacuumOn(int nIndex); // 破真空
CString PickCylinderVacuumOff(int nIndex);      // 关闭真空
CString PickCylinderVacuumStatus(int nIndex);   // 真空状态

// 气缸控制
CString PickCylinderOn(int nIndex);             // 气缸下降
CString PickCylinderOff(int nIndex);            // 气缸上升
CString PickCylinderStatus(int nIndex);         // 气缸状态
```

## 精度补偿系统

### 1. 多重补偿机制
```cpp
// 1. 机械补偿
贴装位置补偿X/Y/R：全局机械偏差补偿

// 2. 视觉补偿  
各吸嘴取料补偿：视觉识别偏差补偿
各位置贴装补偿：Mark识别偏差补偿

// 3. 高度补偿
Tray取料高度偏移：    TRAY盘高度变化
Tray放料高度偏移：    TRAY盘放料高度
皮带取料高度偏移：    皮带来料高度变化
A轨贴装高度偏移：     A轨治具高度补偿  
B轨贴装高度偏移：     B轨治具高度补偿
治具取主板高度偏移：  治具主板厚度补偿

// 4. 角度补偿
A轨贴装角度偏移：     A轨角度校正
B轨贴装角度偏移：     B轨角度校正
A轨治具角度R：        A轨治具角度
B轨治具角度R：        B轨治具角度
```

### 2. 实时补偿计算
```cpp
// Mark识别补偿计算
计算A轨/B轨4个Mark点偏差 → 计算平移和旋转补偿 → 应用到贴装位置

// 视觉识别补偿计算  
主板位置识别偏差 → 计算取料位置补偿 → 应用到吸嘴取料
```

## 多模式运行

### 1. 正常生产模式
```cpp
// 标准贴装流程
双轨并行生产 + 4吸嘴协作 + 智能调度
```

### 2. 调试模式
```cpp
if (VAR_ROBOT_B("调试模式")) {
    // 单步调试功能
    // 参数实时调整
    // 详细日志输出
}
```

### 3. 纯收板模式
```cpp
if (VAR_ROBOT_B("纯收板模式")) {
    // 只收集主板，不进行贴装
}
```

### 4. 双主板装配模式
```cpp
if (VAR_ROBOT_B("双主板装配模式")) {
    // 同时处理两块主板
}
```

### 5. MARK全拍模式
```cpp
if (VAR_ROBOT_B("MARK全拍模式")) {
    // 拍摄所有Mark点进行精确定位
}
```

## 异常处理机制

### 1. 安全检查
```cpp
EnumStatus OnSafeCheck() {
    if (!CSys::m_bInit) {
        return emStop;
    }
    return emRun;
}
```

### 2. 运动安全
```cpp
// 每次运动都进行安全检查
Move(..., bool bSafeCheck = true, ...)
```

### 3. 取料失败处理
```cpp
// 重复取料计数和处理
m_mapIndex["重复取料计数"]++;
if (重复取料次数超限) {
    // 转入异常处理流程
}
```

### 4. 视觉识别失败处理
```cpp
// 重复拍照计数和处理  
m_mapIndex["重复拍照计数"]++;
if (拍照失败次数超限) {
    // 跳过或报警处理
}
```

## 性能优化

### 1. 多吸嘴并行控制
```cpp
// 4个吸嘴独立状态管理
for (int i = 0; i < 4; i++) {
    // 并行处理每个吸嘴的状态
}
```

### 2. 智能取料序列
```cpp
// 下相机拍照序列优化
int m_nDnCamCaptureSequence[4] = {3, 2, 1, 0};
```

### 3. 视觉处理优化
```cpp
// 图像缓存和异步处理
// 多线程图像处理
// 结果缓存机制
```

## 数据统计和监控

### 1. 生产统计
```cpp
// 各种计数器和统计信息
取料次数统计、贴装次数统计、异常次数统计
```

### 2. 精度监控
```cpp
// 位置偏差统计
Mark识别精度、取料精度、贴装精度监控
```

### 3. 效率分析
```cpp
// 时间统计
单次循环时间、等待时间、运动时间分析
```

## 集成接口

### 1. MES集成
```cpp
// MES数据上报
生产数据、质量数据、设备状态数据
```

### 2. 其他线程接口
```cpp
// 与其他线程的数据交换
托盘系统、治具系统、传送带系统协调
```

## 总结

FStation机器人控制系统通过精密的软硬件设计，实现了：

- **高精度控制**：多重补偿机制保证±0.1mm级别精度
- **高效协作**：4吸嘴并行工作，3相机协同定位
- **智能调度**：状态机驱动的智能任务调度
- **安全可靠**：多层次安全检查和异常处理
- **灵活扩展**：模块化设计支持功能扩展
- **实时监控**：完整的数据统计和性能监控

该系统代表了现代工业机器人控制技术的先进水平，为高精度自动化贴装提供了完整的解决方案。 