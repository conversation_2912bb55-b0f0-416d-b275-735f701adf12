﻿#pragma once

#include "Resource.h"
// CDialogRobotCalib 对话框

class CDialogRobotCalib : public CDialogEx, public CParallel
{
	DECLARE_DYNAMIC(CDialogRobotCalib)

public:
	CDialogRobotCalib(CPoint pt, CImageWindow* pImageWindow, CWnd* pParent = NULL);   // 标准构造函数
	virtual ~CDialogRobotCalib();

// 对话框数据
	enum { IDD = IDD_DIALOG_ROBOT_CALIB };

	enum eCalib 
	{
		emCalibUp = 0,
		emCalibDn,
		emRobot
	};

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持
	virtual BOOL PreTranslateMessage(MSG* pMsg);
	virtual void OnOK() {};
	virtual BOOL OnInitDialog();
	virtual bool Excute();

	DECLARE_MESSAGE_MAP()
	afx_msg void OnBnClickedButton1();
	afx_msg void OnBnClickedButton2();
	afx_msg void OnBnClickedButton3();
	afx_msg void OnBnClickedButton4();
	afx_msg void OnBnClickedButton5();
	afx_msg void OnBnClickedButton6();
	afx_msg void OnBnClickedButton7();
	afx_msg void OnBnClickedButton8();
	afx_msg void OnBnClickedButton9();
	afx_msg void OnBnClickedButton10();
	afx_msg void OnBnClickedButton11();
	afx_msg void OnBnClickedButton12();
	afx_msg void OnBnClickedButton13();
	afx_msg void OnBnClickedButton14();
	afx_msg void OnBnClickedButton15();

public:
	bool CalibrateUp();
	bool CalibrateDn();
	bool Robot();

private:
	eCalib			m_eCalib;
	CPoint			m_pt;

	double			m_nStep;

	CImageWindow*	m_pImageWindow;
};
