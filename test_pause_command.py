#!/usr/bin/env python3
"""
测试暂停命令的修复效果
"""

import paho.mqtt.client as mqtt
import json
import time

# MQTT 配置
MQTT_BROKER = "192.168.88.44"
MQTT_PORT = 1883
DEVICE_ID = "A320021760"
CLIENT_ID = f"pause_test_{int(time.time())}"

def on_connect(client, userdata, flags, rc):
    if rc == 0:
        print(f"✅ 连接成功到 EMQX")
        # 订阅命令响应
        response_topic = f"$oc/devices/{DEVICE_ID}/sys/commands/response/+"
        client.subscribe(response_topic, qos=1)
        print(f"📥 订阅命令响应主题")
    else:
        print(f"❌ 连接失败: {rc}")

def on_message(client, userdata, msg):
    print(f"\n🎉 收到FStationMFC响应!")
    print(f"   主题: {msg.topic}")
    
    # 提取request_id
    topic_parts = msg.topic.split('/')
    request_id = None
    for part in topic_parts:
        if part.startswith('request_id='):
            request_id = part.split('=', 1)[1]
            break
    
    try:
        response = json.loads(msg.payload.decode())
        print(f"   RequestId: {request_id}")
        print(f"   结果码: {response.get('result_code', 'N/A')}")
        print(f"   结果消息: {response.get('result_message', 'N/A')}")
        
        # 检查响应格式
        print(f"\n📋 响应格式检查:")
        print(f"   ✅ result_code: {response.get('result_code', 'N/A')}")
        print(f"   ✅ result_message: {response.get('result_message', 'N/A')}")
        print(f"   {'✅' if 'timestamp' in response else '❌'} timestamp: {response.get('timestamp', '缺失')}")
        print(f"   {'✅' if 'data' in response else '❌'} data: {response.get('data', '缺失')}")
        
        if 'data' in response and response['data']:
            print(f"   📊 数据内容: {json.dumps(response['data'], indent=4, ensure_ascii=False)}")
        
        # 完整响应
        print(f"\n📄 完整响应JSON:")
        print(json.dumps(response, indent=2, ensure_ascii=False))
        
    except Exception as e:
        print(f"   JSON解析失败: {e}")
        print(f"   原始数据: {msg.payload.decode()}")

def test_pause_command():
    print("=" * 60)
    print("暂停命令修复效果测试")
    print("=" * 60)
    
    # 创建MQTT客户端
    client = mqtt.Client(CLIENT_ID)
    client.on_connect = on_connect
    client.on_message = on_message
    
    try:
        # 连接到MQTT Broker
        print(f"🔗 连接到MQTT Broker: {MQTT_BROKER}:{MQTT_PORT}")
        client.connect(MQTT_BROKER, MQTT_PORT, 60)
        client.loop_start()
        
        time.sleep(3)  # 等待连接建立
        
        # 发送暂停命令
        request_id = f"pause_fix_test_{int(time.time())}"
        command = {
            "service_id": "CommandService",
            "command_name": "COMMAND_PAUSE",
            "properties": {
                "reason": "修复测试暂停"
            }
        }
        
        command_topic = f"$oc/devices/{DEVICE_ID}/sys/commands/request_id={request_id}"
        
        print(f"\n📤 发送暂停命令:")
        print(f"   主题: {command_topic}")
        print(f"   RequestId: {request_id}")
        print(f"   命令内容: {json.dumps(command, indent=2, ensure_ascii=False)}")
        
        result = client.publish(command_topic, json.dumps(command), qos=2)
        
        if result.rc == mqtt.MQTT_ERR_SUCCESS:
            print(f"✅ 命令发送成功")
        else:
            print(f"❌ 命令发送失败: {result.rc}")
        
        print(f"\n⏳ 等待FStationMFC响应...")
        time.sleep(15)  # 等待响应
        
        print(f"\n🔄 发送恢复命令...")
        
        # 发送恢复命令
        request_id2 = f"resume_fix_test_{int(time.time())}"
        command2 = {
            "service_id": "CommandService",
            "command_name": "COMMAND_PRODUCTION",
            "properties": {}
        }
        
        command_topic2 = f"$oc/devices/{DEVICE_ID}/sys/commands/request_id={request_id2}"
        
        print(f"   主题: {command_topic2}")
        print(f"   RequestId: {request_id2}")
        
        result2 = client.publish(command_topic2, json.dumps(command2), qos=2)
        
        if result2.rc == mqtt.MQTT_ERR_SUCCESS:
            print(f"✅ 恢复命令发送成功")
        else:
            print(f"❌ 恢复命令发送失败: {result2.rc}")
        
        print(f"⏳ 等待恢复命令响应...")
        time.sleep(10)  # 等待响应
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    finally:
        if client:
            client.loop_stop()
            client.disconnect()
            print("\n🔌 MQTT连接已断开")

if __name__ == "__main__":
    print("FStationMFC 暂停命令修复测试")
    print("=" * 40)
    print("测试目标:")
    print("1. 验证暂停命令不再超时")
    print("2. 验证响应包含完整的JSON格式")
    print("3. 验证timestamp和data字段存在")
    print("4. 验证恢复命令正常工作")
    print()
    
    test_pause_command()
