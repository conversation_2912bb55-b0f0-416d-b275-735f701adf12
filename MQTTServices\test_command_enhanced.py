#!/usr/bin/env python3
"""
增强的命令下发测试脚本
测试完善后的命令处理功能
"""

import paho.mqtt.client as mqtt
import json
import time
import threading

# MQTT 配置
MQTT_BROKER = "192.168.88.44"
MQTT_PORT = 1883
DEVICE_ID = "A320021760"
CLIENT_ID = f"command_test_{int(time.time())}"

class CommandTester:
    def __init__(self):
        self.client = None
        self.responses = {}
        self.lock = threading.Lock()
        
    def on_connect(self, client, userdata, flags, rc):
        if rc == 0:
            print(f"✅ 连接成功到 EMQX")
            # 订阅命令响应
            response_topic = f"$oc/devices/{DEVICE_ID}/sys/commands/response/+"
            client.subscribe(response_topic, qos=1)
            print(f"📥 订阅命令响应主题")
        else:
            print(f"❌ 连接失败: {rc}")
    
    def on_message(self, client, userdata, msg):
        print(f"\n📨 收到命令响应:")
        print(f"   主题: {msg.topic}")
        print(f"   大小: {len(msg.payload)} 字节")
        
        # 提取request_id
        topic_parts = msg.topic.split('/')
        request_id = None
        for part in topic_parts:
            if part.startswith('request_id='):
                request_id = part.split('=', 1)[1]
                break
        
        try:
            response = json.loads(msg.payload.decode())
            print(f"   RequestId: {request_id}")
            print(f"   结果码: {response.get('result_code', 'N/A')}")
            print(f"   结果消息: {response.get('result_message', 'N/A')}")
            
            with self.lock:
                self.responses[request_id] = response
                
        except Exception as e:
            print(f"   解析失败: {e}")
    
    def send_command(self, command_data, request_id):
        """发送命令"""
        command_topic = f"$oc/devices/{DEVICE_ID}/sys/commands/request_id={request_id}"
        
        print(f"\n📤 发送命令:")
        print(f"   主题: {command_topic}")
        print(f"   RequestId: {request_id}")
        print(f"   命令类型: {command_data.get('command_name', 'N/A')}")
        print(f"   消息体: {json.dumps(command_data, indent=2, ensure_ascii=False)}")
        
        result = self.client.publish(command_topic, json.dumps(command_data), qos=2)
        
        if result.rc == mqtt.MQTT_ERR_SUCCESS:
            print(f"✅ 命令发送成功")
            return True
        else:
            print(f"❌ 命令发送失败: {result.rc}")
            return False
    
    def test_valid_command(self):
        """测试有效命令"""
        print("\n" + "="*50)
        print("测试1: 有效的SN下发命令")
        print("="*50)
        
        request_id = f"test_sn_deliver_{int(time.time())}"
        command = {
            "service_id": "CommandService",
            "command_name": "COMMAND_SN_DELIVER",
            "lane_no": 1,
            "side": "T",
            "properties": {
                "sn": "00232430452003F200003171",
                "production_model": "A121000185"
            }
        }
        
        return self.send_command(command, request_id), request_id
    
    def test_invalid_service_id(self):
        """测试无效的service_id"""
        print("\n" + "="*50)
        print("测试2: 无效的service_id")
        print("="*50)
        
        request_id = f"test_invalid_service_{int(time.time())}"
        command = {
            "service_id": "InvalidService",  # 错误的service_id
            "command_name": "COMMAND_SN_DELIVER",
            "properties": {
                "sn": "00232430452003F200003171",
                "production_model": "A121000185"
            }
        }
        
        return self.send_command(command, request_id), request_id
    
    def test_missing_properties(self):
        """测试缺少必需参数"""
        print("\n" + "="*50)
        print("测试3: 缺少必需参数")
        print("="*50)
        
        request_id = f"test_missing_props_{int(time.time())}"
        command = {
            "service_id": "CommandService",
            "command_name": "COMMAND_SN_DELIVER",
            "properties": {
                # 缺少 "sn" 参数
                "production_model": "A121000185"
            }
        }
        
        return self.send_command(command, request_id), request_id
    
    def test_unsupported_command(self):
        """测试不支持的命令类型"""
        print("\n" + "="*50)
        print("测试4: 不支持的命令类型")
        print("="*50)
        
        request_id = f"test_unsupported_{int(time.time())}"
        command = {
            "service_id": "CommandService",
            "command_name": "COMMAND_UNKNOWN",  # 不支持的命令
            "properties": {}
        }
        
        return self.send_command(command, request_id), request_id
    
    def test_malformed_json(self):
        """测试格式错误的JSON"""
        print("\n" + "="*50)
        print("测试5: 格式错误的JSON")
        print("="*50)
        
        request_id = f"test_malformed_{int(time.time())}"
        command_topic = f"$oc/devices/{DEVICE_ID}/sys/commands/request_id={request_id}"
        
        # 发送格式错误的JSON
        malformed_json = '{"service_id": "CommandService", "command_name": "COMMAND_SN_DELIVER"'  # 缺少结束括号
        
        print(f"📤 发送格式错误的JSON:")
        print(f"   主题: {command_topic}")
        print(f"   RequestId: {request_id}")
        print(f"   消息体: {malformed_json}")
        
        result = self.client.publish(command_topic, malformed_json, qos=2)
        
        if result.rc == mqtt.MQTT_ERR_SUCCESS:
            print(f"✅ 消息发送成功")
            return True, request_id
        else:
            print(f"❌ 消息发送失败: {result.rc}")
            return False, request_id
    
    def test_duplicate_command(self):
        """测试重复命令（幂等性）"""
        print("\n" + "="*50)
        print("测试6: 重复命令（幂等性测试）")
        print("="*50)
        
        request_id = f"test_duplicate_{int(time.time())}"
        command = {
            "service_id": "CommandService",
            "command_name": "COMMAND_PAUSE",
            "properties": {}
        }
        
        print("发送第1次命令:")
        success1, _ = self.send_command(command, request_id)
        
        time.sleep(2)
        
        print("\n发送第2次命令（相同RequestId）:")
        success2, _ = self.send_command(command, request_id)
        
        return success1 and success2, request_id
    
    def wait_for_response(self, request_id, timeout=10):
        """等待命令响应"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            with self.lock:
                if request_id in self.responses:
                    return self.responses[request_id]
            time.sleep(0.1)
        return None
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("=== 增强命令下发功能综合测试 ===")
        print(f"目标: {MQTT_BROKER}:{MQTT_PORT}")
        
        # 创建MQTT客户端
        self.client = mqtt.Client(CLIENT_ID)
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        
        try:
            # 连接到MQTT Broker
            self.client.connect(MQTT_BROKER, MQTT_PORT, 60)
            self.client.loop_start()
            
            time.sleep(2)  # 等待连接建立
            
            # 运行各种测试
            tests = [
                ("有效命令", self.test_valid_command),
                ("无效service_id", self.test_invalid_service_id),
                ("缺少必需参数", self.test_missing_properties),
                ("不支持的命令", self.test_unsupported_command),
                ("格式错误JSON", self.test_malformed_json),
                ("重复命令", self.test_duplicate_command),
            ]
            
            results = []
            
            for test_name, test_func in tests:
                try:
                    success, request_id = test_func()
                    if success:
                        # 等待响应
                        print(f"⏳ 等待响应...")
                        response = self.wait_for_response(request_id, 15)
                        if response:
                            print(f"✅ 收到响应: {response.get('result_message', 'N/A')}")
                            results.append((test_name, "成功", response.get('result_code', 'N/A')))
                        else:
                            print(f"⏰ 响应超时")
                            results.append((test_name, "超时", "N/A"))
                    else:
                        results.append((test_name, "发送失败", "N/A"))
                        
                    time.sleep(3)  # 测试间隔
                    
                except Exception as e:
                    print(f"❌ 测试异常: {e}")
                    results.append((test_name, "异常", str(e)))
            
            # 打印测试结果汇总
            print("\n" + "="*60)
            print("测试结果汇总")
            print("="*60)
            print(f"{'测试项':<20} {'结果':<10} {'结果码':<10}")
            print("-" * 60)
            for test_name, result, code in results:
                print(f"{test_name:<20} {result:<10} {code:<10}")
            
        except Exception as e:
            print(f"❌ 测试异常: {e}")
        finally:
            if self.client:
                self.client.loop_stop()
                self.client.disconnect()

def main():
    tester = CommandTester()
    tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
