x64-windows-static-md/
x64-windows-static-md/debug/
x64-windows-static-md/debug/lib/
x64-windows-static-md/debug/lib/libcrypto.lib
x64-windows-static-md/debug/lib/libssl.lib
x64-windows-static-md/debug/lib/pkgconfig/
x64-windows-static-md/debug/lib/pkgconfig/libcrypto.pc
x64-windows-static-md/debug/lib/pkgconfig/libssl.pc
x64-windows-static-md/debug/lib/pkgconfig/openssl.pc
x64-windows-static-md/include/
x64-windows-static-md/include/openssl/
x64-windows-static-md/include/openssl/__DECC_INCLUDE_EPILOGUE.H
x64-windows-static-md/include/openssl/__DECC_INCLUDE_PROLOGUE.H
x64-windows-static-md/include/openssl/aes.h
x64-windows-static-md/include/openssl/asn1.h
x64-windows-static-md/include/openssl/asn1err.h
x64-windows-static-md/include/openssl/asn1t.h
x64-windows-static-md/include/openssl/async.h
x64-windows-static-md/include/openssl/asyncerr.h
x64-windows-static-md/include/openssl/bio.h
x64-windows-static-md/include/openssl/bioerr.h
x64-windows-static-md/include/openssl/blowfish.h
x64-windows-static-md/include/openssl/bn.h
x64-windows-static-md/include/openssl/bnerr.h
x64-windows-static-md/include/openssl/buffer.h
x64-windows-static-md/include/openssl/buffererr.h
x64-windows-static-md/include/openssl/byteorder.h
x64-windows-static-md/include/openssl/camellia.h
x64-windows-static-md/include/openssl/cast.h
x64-windows-static-md/include/openssl/cmac.h
x64-windows-static-md/include/openssl/cmp.h
x64-windows-static-md/include/openssl/cmp_util.h
x64-windows-static-md/include/openssl/cmperr.h
x64-windows-static-md/include/openssl/cms.h
x64-windows-static-md/include/openssl/cmserr.h
x64-windows-static-md/include/openssl/comp.h
x64-windows-static-md/include/openssl/comperr.h
x64-windows-static-md/include/openssl/conf.h
x64-windows-static-md/include/openssl/conf_api.h
x64-windows-static-md/include/openssl/conferr.h
x64-windows-static-md/include/openssl/configuration.h
x64-windows-static-md/include/openssl/conftypes.h
x64-windows-static-md/include/openssl/core.h
x64-windows-static-md/include/openssl/core_dispatch.h
x64-windows-static-md/include/openssl/core_names.h
x64-windows-static-md/include/openssl/core_object.h
x64-windows-static-md/include/openssl/crmf.h
x64-windows-static-md/include/openssl/crmferr.h
x64-windows-static-md/include/openssl/crypto.h
x64-windows-static-md/include/openssl/cryptoerr.h
x64-windows-static-md/include/openssl/cryptoerr_legacy.h
x64-windows-static-md/include/openssl/ct.h
x64-windows-static-md/include/openssl/cterr.h
x64-windows-static-md/include/openssl/decoder.h
x64-windows-static-md/include/openssl/decodererr.h
x64-windows-static-md/include/openssl/des.h
x64-windows-static-md/include/openssl/dh.h
x64-windows-static-md/include/openssl/dherr.h
x64-windows-static-md/include/openssl/dsa.h
x64-windows-static-md/include/openssl/dsaerr.h
x64-windows-static-md/include/openssl/dtls1.h
x64-windows-static-md/include/openssl/e_os2.h
x64-windows-static-md/include/openssl/e_ostime.h
x64-windows-static-md/include/openssl/ebcdic.h
x64-windows-static-md/include/openssl/ec.h
x64-windows-static-md/include/openssl/ecdh.h
x64-windows-static-md/include/openssl/ecdsa.h
x64-windows-static-md/include/openssl/ecerr.h
x64-windows-static-md/include/openssl/encoder.h
x64-windows-static-md/include/openssl/encodererr.h
x64-windows-static-md/include/openssl/engine.h
x64-windows-static-md/include/openssl/engineerr.h
x64-windows-static-md/include/openssl/err.h
x64-windows-static-md/include/openssl/ess.h
x64-windows-static-md/include/openssl/esserr.h
x64-windows-static-md/include/openssl/evp.h
x64-windows-static-md/include/openssl/evperr.h
x64-windows-static-md/include/openssl/fips_names.h
x64-windows-static-md/include/openssl/fipskey.h
x64-windows-static-md/include/openssl/hmac.h
x64-windows-static-md/include/openssl/hpke.h
x64-windows-static-md/include/openssl/http.h
x64-windows-static-md/include/openssl/httperr.h
x64-windows-static-md/include/openssl/idea.h
x64-windows-static-md/include/openssl/indicator.h
x64-windows-static-md/include/openssl/kdf.h
x64-windows-static-md/include/openssl/kdferr.h
x64-windows-static-md/include/openssl/lhash.h
x64-windows-static-md/include/openssl/macros.h
x64-windows-static-md/include/openssl/md2.h
x64-windows-static-md/include/openssl/md4.h
x64-windows-static-md/include/openssl/md5.h
x64-windows-static-md/include/openssl/mdc2.h
x64-windows-static-md/include/openssl/ml_kem.h
x64-windows-static-md/include/openssl/modes.h
x64-windows-static-md/include/openssl/obj_mac.h
x64-windows-static-md/include/openssl/objects.h
x64-windows-static-md/include/openssl/objectserr.h
x64-windows-static-md/include/openssl/ocsp.h
x64-windows-static-md/include/openssl/ocsperr.h
x64-windows-static-md/include/openssl/opensslconf.h
x64-windows-static-md/include/openssl/opensslv.h
x64-windows-static-md/include/openssl/ossl_typ.h
x64-windows-static-md/include/openssl/param_build.h
x64-windows-static-md/include/openssl/params.h
x64-windows-static-md/include/openssl/pem.h
x64-windows-static-md/include/openssl/pem2.h
x64-windows-static-md/include/openssl/pemerr.h
x64-windows-static-md/include/openssl/pkcs12.h
x64-windows-static-md/include/openssl/pkcs12err.h
x64-windows-static-md/include/openssl/pkcs7.h
x64-windows-static-md/include/openssl/pkcs7err.h
x64-windows-static-md/include/openssl/prov_ssl.h
x64-windows-static-md/include/openssl/proverr.h
x64-windows-static-md/include/openssl/provider.h
x64-windows-static-md/include/openssl/quic.h
x64-windows-static-md/include/openssl/rand.h
x64-windows-static-md/include/openssl/randerr.h
x64-windows-static-md/include/openssl/rc2.h
x64-windows-static-md/include/openssl/rc4.h
x64-windows-static-md/include/openssl/rc5.h
x64-windows-static-md/include/openssl/ripemd.h
x64-windows-static-md/include/openssl/rsa.h
x64-windows-static-md/include/openssl/rsaerr.h
x64-windows-static-md/include/openssl/safestack.h
x64-windows-static-md/include/openssl/seed.h
x64-windows-static-md/include/openssl/self_test.h
x64-windows-static-md/include/openssl/sha.h
x64-windows-static-md/include/openssl/srp.h
x64-windows-static-md/include/openssl/srtp.h
x64-windows-static-md/include/openssl/ssl.h
x64-windows-static-md/include/openssl/ssl2.h
x64-windows-static-md/include/openssl/ssl3.h
x64-windows-static-md/include/openssl/sslerr.h
x64-windows-static-md/include/openssl/sslerr_legacy.h
x64-windows-static-md/include/openssl/stack.h
x64-windows-static-md/include/openssl/store.h
x64-windows-static-md/include/openssl/storeerr.h
x64-windows-static-md/include/openssl/symhacks.h
x64-windows-static-md/include/openssl/thread.h
x64-windows-static-md/include/openssl/tls1.h
x64-windows-static-md/include/openssl/trace.h
x64-windows-static-md/include/openssl/ts.h
x64-windows-static-md/include/openssl/tserr.h
x64-windows-static-md/include/openssl/txt_db.h
x64-windows-static-md/include/openssl/types.h
x64-windows-static-md/include/openssl/ui.h
x64-windows-static-md/include/openssl/uierr.h
x64-windows-static-md/include/openssl/whrlpool.h
x64-windows-static-md/include/openssl/x509.h
x64-windows-static-md/include/openssl/x509_acert.h
x64-windows-static-md/include/openssl/x509_vfy.h
x64-windows-static-md/include/openssl/x509err.h
x64-windows-static-md/include/openssl/x509v3.h
x64-windows-static-md/include/openssl/x509v3err.h
x64-windows-static-md/lib/
x64-windows-static-md/lib/libcrypto.lib
x64-windows-static-md/lib/libssl.lib
x64-windows-static-md/lib/pkgconfig/
x64-windows-static-md/lib/pkgconfig/libcrypto.pc
x64-windows-static-md/lib/pkgconfig/libssl.pc
x64-windows-static-md/lib/pkgconfig/openssl.pc
x64-windows-static-md/share/
x64-windows-static-md/share/openssl/
x64-windows-static-md/share/openssl/OpenSSLConfig.cmake
x64-windows-static-md/share/openssl/OpenSSLConfigVersion.cmake
x64-windows-static-md/share/openssl/copyright
x64-windows-static-md/share/openssl/usage
x64-windows-static-md/share/openssl/vcpkg-cmake-wrapper.cmake
x64-windows-static-md/share/openssl/vcpkg.spdx.json
x64-windows-static-md/share/openssl/vcpkg_abi_info.txt
