#!/usr/bin/env python3
"""
SCADA平台模拟器
用于测试设备事件上报和响应机制
"""

import paho.mqtt.client as mqtt
import json
import time
import threading
import re
from datetime import datetime

class SCADAPlatformSimulator:
    def __init__(self):
        self.mqtt_broker = "192.168.88.44"
        self.mqtt_port = 1883
        self.device_id = "A320021760"
        self.client_id = f"scada_platform_simulator_{int(time.time())}"
        
        self.client = None
        self.connected = False
        self.event_count = 0
        
        # 事件处理配置
        self.auto_response = True
        self.response_delay = 0.5  # 500ms延迟模拟平台处理时间
        
    def on_connect(self, client, userdata, flags, rc):
        if rc == 0:
            self.connected = True
            print(f"🟢 SCADA平台模拟器连接成功")
            
            # 订阅设备事件上报
            event_topic = f"$oc/devices/{self.device_id}/sys/events/up/+"
            client.subscribe(event_topic, qos=2)
            print(f"📥 订阅设备事件: {event_topic}")
            
        else:
            print(f"❌ 连接失败: {rc}")
    
    def on_disconnect(self, client, userdata, rc):
        self.connected = False
        print(f"🔴 SCADA平台模拟器连接断开: {rc}")
    
    def on_message(self, client, userdata, msg):
        """处理设备事件上报"""
        try:
            # 提取request_id
            request_id = self.extract_request_id(msg.topic)
            if not request_id:
                print(f"⚠️ 无法提取request_id: {msg.topic}")
                return
            
            # 解析事件数据
            event_data = json.loads(msg.payload.decode())
            
            self.event_count += 1
            print(f"\n📨 收到设备事件 #{self.event_count}")
            print(f"   主题: {msg.topic}")
            print(f"   RequestId: {request_id}")
            print(f"   QoS: {msg.qos}")
            print(f"   大小: {len(msg.payload)} 字节")
            
            # 显示事件详情
            self.display_event_details(event_data)
            
            # 自动响应
            if self.auto_response:
                threading.Thread(
                    target=self.send_delayed_response,
                    args=(request_id, event_data),
                    daemon=True
                ).start()
                
        except Exception as e:
            print(f"❌ 处理事件失败: {e}")
    
    def extract_request_id(self, topic):
        """从主题中提取request_id"""
        # 主题格式: $oc/devices/{deviceId}/sys/events/up/request_id={requestId}
        match = re.search(r'request_id=([^/]+)', topic)
        return match.group(1) if match else None
    
    def display_event_details(self, event_data):
        """显示事件详情"""
        if "services" in event_data:
            for service in event_data["services"]:
                service_id = service.get("service_id", "Unknown")
                print(f"   服务: {service_id}")
                
                if service_id == "EventService":
                    event_type = service.get("event_type", "Unknown")
                    event_time = service.get("event_time", "Unknown")
                    properties = service.get("properties", {})
                    
                    print(f"     事件类型: {event_type}")
                    print(f"     事件时间: {event_time}")
                    print(f"     属性:")
                    for key, value in properties.items():
                        print(f"       {key}: {value}")
                        
                elif service_id == "DefaultService":
                    properties = service.get("properties", {})
                    print(f"     设备状态: {len(properties)} 个数据点")
    
    def send_delayed_response(self, request_id, event_data):
        """延迟发送响应"""
        time.sleep(self.response_delay)
        self.send_event_response(request_id, True, "事件处理成功")
    
    def send_event_response(self, request_id, success=True, message=""):
        """发送事件响应"""
        if not self.connected:
            print("❌ 未连接到MQTT，无法发送响应")
            return False
        
        # 构建响应主题
        response_topic = f"$oc/devices/{self.device_id}/sys/events/up/response/request_id={request_id}"
        
        # 构建响应消息
        response = {
            "result_code": 0 if success else -1,
            "result_message": message or ("处理成功" if success else "处理失败"),
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        }
        
        try:
            # 发送响应
            result = self.client.publish(
                response_topic,
                json.dumps(response, ensure_ascii=False),
                qos=2
            )
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                print(f"📤 发送事件响应成功")
                print(f"   RequestId: {request_id}")
                print(f"   结果: {'成功' if success else '失败'}")
                print(f"   消息: {response['result_message']}")
                return True
            else:
                print(f"❌ 发送响应失败: {result.rc}")
                return False
                
        except Exception as e:
            print(f"❌ 发送响应异常: {e}")
            return False
    
    def start(self):
        """启动SCADA平台模拟器"""
        print("=" * 60)
        print("SCADA平台模拟器启动")
        print("=" * 60)
        print(f"MQTT Broker: {self.mqtt_broker}:{self.mqtt_port}")
        print(f"设备ID: {self.device_id}")
        print(f"自动响应: {'开启' if self.auto_response else '关闭'}")
        print(f"响应延迟: {self.response_delay}s")
        print()
        
        # 创建MQTT客户端
        self.client = mqtt.Client(self.client_id)
        self.client.on_connect = self.on_connect
        self.client.on_disconnect = self.on_disconnect
        self.client.on_message = self.on_message
        
        try:
            # 连接到MQTT Broker
            self.client.connect(self.mqtt_broker, self.mqtt_port, 60)
            self.client.loop_start()
            
            print("🚀 SCADA平台模拟器运行中...")
            print("按 Ctrl+C 退出")
            
            # 保持运行
            while True:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n🛑 收到退出信号")
        except Exception as e:
            print(f"❌ 运行异常: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """停止SCADA平台模拟器"""
        if self.client:
            self.client.loop_stop()
            self.client.disconnect()
        print("🔴 SCADA平台模拟器已停止")

def main():
    simulator = SCADAPlatformSimulator()
    simulator.start()

if __name__ == "__main__":
    main()
