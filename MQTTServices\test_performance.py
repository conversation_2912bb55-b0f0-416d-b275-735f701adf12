#!/usr/bin/env python3
"""
测试 MQTT 网关性能
专门测试属性查询的响应时间
"""

import paho.mqtt.client as mqtt
import json
import time
import sys

# MQTT 配置
MQTT_BROKER = "192.168.88.44"
MQTT_PORT = 1883
DEVICE_ID = "A320021760"

# 认证信息（需要根据实际情况调整）
CLIENT_ID = f"perf_test_client_{int(time.time())}"

def on_connect(client, userdata, flags, rc):
    """连接回调"""
    if rc == 0:
        print(f"✅ 连接成功到 MQTT Broker: {MQTT_BROKER}:{MQTT_PORT}")
        
        # 订阅响应主题
        response_topic = f"$oc/devices/{DEVICE_ID}/sys/properties/get/response/+"
        client.subscribe(response_topic, qos=2)
        print(f"📥 订阅响应主题: {response_topic}")
        
    else:
        print(f"❌ 连接失败，返回码: {rc}")

def on_message(client, userdata, msg):
    """消息接收回调"""
    current_time = time.time()
    
    # 从主题中提取request_id
    topic_parts = msg.topic.split('/')
    request_id = topic_parts[-1].split('=')[-1] if 'request_id=' in topic_parts[-1] else "unknown"
    
    # 计算响应时间
    if hasattr(userdata, 'request_times') and request_id in userdata.request_times:
        response_time = (current_time - userdata.request_times[request_id]) * 1000  # 转换为毫秒
        print(f"📨 收到响应 - RequestId: {request_id}")
        print(f"   响应时间: {response_time:.1f} ms")
        print(f"   消息大小: {len(msg.payload)} 字节")
        
        # 删除已处理的请求时间记录
        del userdata.request_times[request_id]
    else:
        print(f"📨 收到响应 - RequestId: {request_id} (无法计算响应时间)")

def on_subscribe(client, userdata, mid, granted_qos):
    """订阅成功回调"""
    print(f"📥 订阅成功，QoS: {granted_qos}")

def send_property_query(client, userdata, request_id):
    """发送属性查询请求"""
    query_topic = f"$oc/devices/{DEVICE_ID}/sys/properties/get/request_id={request_id}"
    
    # 空消息体（按照协议要求）
    payload = {}
    
    # 记录请求发送时间
    if not hasattr(userdata, 'request_times'):
        userdata.request_times = {}
    userdata.request_times[request_id] = time.time()
    
    print(f"📤 发送属性查询请求 - RequestId: {request_id}")
    
    result = client.publish(query_topic, json.dumps(payload), qos=2)
    
    if result.rc == mqtt.MQTT_ERR_SUCCESS:
        print(f"✅ 请求发送成功")
    else:
        print(f"❌ 请求发送失败，错误码: {result.rc}")

def performance_test(client, userdata):
    """性能测试"""
    print("\n=== 性能测试 ===")
    
    test_count = 5
    print(f"🚀 发送 {test_count} 个属性查询请求，测试响应时间")
    
    for i in range(test_count):
        request_id = f"perf_test_{int(time.time() * 1000)}_{i}"
        print(f"\n📤 第 {i+1} 次请求:")
        send_property_query(client, userdata, request_id)
        time.sleep(1)  # 间隔1秒发送下一个请求

class UserData:
    def __init__(self):
        self.request_times = {}

def main():
    """主函数"""
    print("=== MQTT 网关性能测试工具 ===")
    print(f"目标设备: {DEVICE_ID}")
    print(f"MQTT Broker: {MQTT_BROKER}:{MQTT_PORT}")
    print()
    
    # 创建用户数据对象
    userdata = UserData()
    
    # 创建 MQTT 客户端
    client = mqtt.Client(CLIENT_ID, userdata=userdata)
    
    # 设置回调函数
    client.on_connect = on_connect
    client.on_message = on_message
    client.on_subscribe = on_subscribe
    
    try:
        # 连接到 MQTT Broker
        print(f"🔗 正在连接到 {MQTT_BROKER}:{MQTT_PORT}...")
        client.connect(MQTT_BROKER, MQTT_PORT, 60)
        
        # 启动网络循环
        client.loop_start()
        
        # 等待连接建立
        time.sleep(2)
        
        # 执行性能测试
        performance_test(client, userdata)
        
        # 等待所有响应
        print("\n⏳ 等待所有响应...")
        time.sleep(10)
        
        # 检查未收到响应的请求
        if hasattr(userdata, 'request_times') and userdata.request_times:
            print(f"\n⚠️ 有 {len(userdata.request_times)} 个请求未收到响应:")
            for req_id in userdata.request_times:
                print(f"   - {req_id}")
        
        print("\n🔚 性能测试完成")
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断测试")
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    finally:
        client.loop_stop()
        client.disconnect()

if __name__ == "__main__":
    main()
