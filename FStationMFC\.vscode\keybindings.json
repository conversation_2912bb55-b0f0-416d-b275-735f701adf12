[
    // 声明/实现跳转 - 类似 Visual Studio 的 F12
    {
        "key": "f12",
        "command": "C_Cpp.GoToDeclaration",
        "when": "editorTextFocus && editorLangId == cpp"
    },
    {
        "key": "ctrl+f12",
        "command": "C_Cpp.GoToDefinition",
        "when": "editorTextFocus && editorLangId == cpp"
    },
    {
        "key": "alt+f12",
        "command": "editor.action.goToImplementation",
        "when": "editorTextFocus && editorLangId == cpp"
    },
    // 查找所有引用 - 类似 Visual Studio 的 Shift+F12
    {
        "key": "shift+f12",
        "command": "editor.action.findReferences",
        "when": "editorTextFocus && editorLangId == cpp"
    },
    // 符号搜索 - 类似 Visual Studio 的 Ctrl+,
    {
        "key": "ctrl+,",
        "command": "workbench.action.showAllSymbols"
    },
    // 在文件中搜索符号 - 类似 Visual Studio 的 Ctrl+Shift+O
    {
        "key": "ctrl+shift+o",
        "command": "workbench.action.gotoSymbol"
    },
    // 快速打开文件 - 类似 Visual Studio 的 Ctrl+Shift+T
    {
        "key": "ctrl+shift+t",
        "command": "workbench.action.quickOpen"
    },
    // 切换头文件/源文件
    {
        "key": "alt+o",
        "command": "C_Cpp.SwitchHeaderSource",
        "when": "editorTextFocus && editorLangId == cpp"
    },
    // 手动格式化选项
    {
        "key": "ctrl+k ctrl+f",
        "command": "editor.action.formatSelection",
        "when": "editorTextFocus && editorHasSelection && editorLangId == cpp"
    },
    {
        "key": "ctrl+k ctrl+d",
        "command": "editor.action.formatDocument",
        "when": "editorTextFocus && editorLangId == cpp"
    },
    {
        "key": "ctrl+shift+i",
        "command": "editor.action.formatDocument",
        "when": "editorTextFocus && editorLangId == cpp"
    }
]