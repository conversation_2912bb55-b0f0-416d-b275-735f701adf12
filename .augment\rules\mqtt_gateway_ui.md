# MQTT网关UI项目规范

## 🎯 项目概述

MQTTGatewayUI是基于Qt6的现代化图形用户界面，为MQTT网关提供可视化管理和监控功能。采用多标签页设计，支持实时监控、数据点管理、系统托盘等功能。

## 🏗️ 技术栈

- **语言**: C++17
- **UI框架**: Qt6 (Widgets, Network, Charts)
- **构建系统**: CMake 3.16+
- **设计模式**: MVC架构
- **图表库**: Qt Charts
- **样式**: QSS样式表
- **图标**: 矢量图标资源

## 📁 项目结构

```
MQTTGatewayUI/
├── main.cpp                    # 程序入口
├── MainWindow.h/.cpp          # 主窗口
├── SingletonApplication.*     # 单例应用程序
├── SystemTrayManager.*        # 系统托盘管理
├── widgets/                   # UI组件
│   ├── RealTimeMonitorWidget.*
│   ├── DataPointWidget.*
│   ├── MQTTCommunicationWidget.*
│   ├── SocketCommunicationWidget.*
│   ├── SystemMonitorWidget.*
│   ├── ConfigManagerWidget.*
│   ├── LogViewerWidget.*
│   └── MQTTTestWidget.*
├── models/                    # 数据模型
│   ├── DataPointModel.*
│   └── LogModel.*
├── dialogs/                   # 对话框
│   ├── ConfigEditDialog.*
│   └── AboutDialog.*
├── resources/                 # 资源文件
│   ├── icons.qrc
│   ├── styles.qrc
│   ├── icons/
│   └── styles/
└── CMakeLists.txt
```

## 💻 Qt6 编码规范

### 现代Qt信号槽语法
```cpp
// ✅ 推荐：使用新式信号槽语法
connect(m_startButton, &QPushButton::clicked, 
        this, &MainWindow::onStartGateway);

// ✅ 推荐：使用lambda表达式
connect(m_timer, &QTimer::timeout, [this]() {
    updateStatusDisplay();
});

// ❌ 避免：旧式SIGNAL/SLOT宏
// connect(button, SIGNAL(clicked()), this, SLOT(onClicked()));
```

### 内存管理最佳实践
```cpp
// ✅ 推荐：使用父子关系管理内存
m_tabWidget = new QTabWidget(this); // this作为父对象

// ✅ 推荐：使用智能指针管理非Qt对象
std::unique_ptr<SystemTrayManager> m_trayManager;

// ✅ 推荐：使用QPointer管理Qt对象指针
QPointer<QDialog> m_configDialog;
```

### 布局管理
```cpp
// ✅ 推荐：使用布局管理器
auto layout = new QVBoxLayout(this);
layout->addWidget(m_toolbar);
layout->addWidget(m_tabWidget);
layout->addWidget(m_statusBar);

// ✅ 推荐：设置合理的间距和边距
layout->setContentsMargins(10, 10, 10, 10);
layout->setSpacing(5);
```

## 🎨 UI设计规范

### 界面风格指南
```cpp
// ✅ 推荐：使用QSS样式表
void MainWindow::setupStyles() {
    setStyleSheet(R"(
        QMainWindow {
            background-color: #f5f5f5;
        }
        QTabWidget::pane {
            border: 1px solid #c0c0c0;
            background-color: white;
        }
        QPushButton {
            background-color: #0078d4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
        }
        QPushButton:hover {
            background-color: #106ebe;
        }
    )");
}
```

### 响应式设计
```cpp
// ✅ 推荐：支持不同屏幕尺寸
void MainWindow::adjustForScreenSize() {
    QScreen *screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    
    if (screenGeometry.width() < 1920) {
        // 调整小屏幕布局
        m_tabWidget->setTabPosition(QTabWidget::West);
    }
}
```

### 图标和资源管理
```cpp
// ✅ 推荐：使用资源文件管理图标
QIcon connectIcon(":/icons/connect.svg");
QIcon disconnectIcon(":/icons/disconnect.svg");

// ✅ 推荐：支持高DPI显示
QApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
QApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);
```

## 📊 数据可视化规范

### Qt Charts使用
```cpp
// ✅ 推荐：使用Qt Charts进行数据可视化
class SystemMonitorWidget : public QWidget {
private:
    QChart *m_cpuChart;
    QLineSeries *m_cpuSeries;
    
public:
    void setupCharts() {
        m_cpuChart = new QChart();
        m_cpuSeries = new QLineSeries();
        
        m_cpuChart->addSeries(m_cpuSeries);
        m_cpuChart->setTitle("CPU使用率");
        m_cpuChart->setAnimationOptions(QChart::SeriesAnimations);
    }
};
```

### 实时数据更新
```cpp
// ✅ 推荐：使用定时器更新实时数据
void RealTimeMonitorWidget::setupUpdateTimer() {
    m_updateTimer = new QTimer(this);
    connect(m_updateTimer, &QTimer::timeout, 
            this, &RealTimeMonitorWidget::updateData);
    m_updateTimer->start(1000); // 每秒更新
}

void RealTimeMonitorWidget::updateData() {
    // 更新CPU使用率
    double cpuUsage = m_gateway->GetCPUUsage();
    m_cpuLabel->setText(QString("CPU: %1%").arg(cpuUsage, 0, 'f', 1));
    
    // 更新图表数据
    m_cpuSeries->append(QDateTime::currentMSecsSinceEpoch(), cpuUsage);
}
```

## 🔧 架构设计模式

### MVC模式实现
```cpp
// Model: 数据模型
class DataPointModel : public QAbstractTableModel {
    Q_OBJECT
public:
    void updateDataPoint(const QString &id, const QString &value);
    
private:
    QVector<DataPoint> m_dataPoints;
};

// View: 视图组件
class DataPointWidget : public QWidget {
    Q_OBJECT
public:
    void setModel(DataPointModel *model);
    
private:
    QTableView *m_view;
    DataPointModel *m_model;
};

// Controller: 控制器逻辑在MainWindow中实现
```

### 单例应用程序模式
```cpp
class SingletonApplication : public QApplication {
    Q_OBJECT
public:
    SingletonApplication(int &argc, char **argv);
    bool isRunning() const;
    
signals:
    void instanceStarted();
    
private:
    QLocalServer *m_localServer;
    QString m_serverName;
};
```

## 🔔 系统托盘集成

### 托盘图标管理
```cpp
class SystemTrayManager : public QObject {
    Q_OBJECT
public:
    void updateConnectionStatus(bool mqttConnected, bool socketConnected);
    void showNotification(const QString &title, const QString &message);
    
private:
    QSystemTrayIcon *m_trayIcon;
    QMenu *m_trayMenu;
    
    QIcon createStatusIcon(bool mqttConnected, bool socketConnected);
};
```

### 托盘菜单设计
```cpp
void SystemTrayManager::createContextMenu() {
    m_trayMenu = new QMenu();
    
    // 状态显示
    m_statusAction = m_trayMenu->addAction("状态: 连接中...");
    m_statusAction->setEnabled(false);
    m_trayMenu->addSeparator();
    
    // 功能菜单
    m_showAction = m_trayMenu->addAction("显示主窗口");
    m_hideAction = m_trayMenu->addAction("隐藏主窗口");
    m_trayMenu->addSeparator();
    
    m_exitAction = m_trayMenu->addAction("退出");
    
    m_trayIcon->setContextMenu(m_trayMenu);
}
```

## 🧪 测试和调试

### UI自动化测试
```cpp
// 使用Qt Test进行UI测试
class TestMainWindow : public QObject {
    Q_OBJECT
private slots:
    void testTabSwitching();
    void testGatewayStartStop();
    void testSystemTrayInteraction();
    
private:
    MainWindow *m_mainWindow;
};

void TestMainWindow::testTabSwitching() {
    // 测试标签页切换功能
    QTabWidget *tabWidget = m_mainWindow->findChild<QTabWidget*>();
    QVERIFY(tabWidget != nullptr);
    
    tabWidget->setCurrentIndex(1);
    QCOMPARE(tabWidget->currentIndex(), 1);
}
```

### 性能监控
```cpp
// ✅ 推荐：监控UI性能
class PerformanceMonitor {
public:
    static void measureUpdateTime(const QString &operation, 
                                 std::function<void()> func) {
        QElapsedTimer timer;
        timer.start();
        func();
        qint64 elapsed = timer.elapsed();
        
        if (elapsed > 16) { // 超过16ms可能影响60fps
            qWarning() << operation << "耗时:" << elapsed << "ms";
        }
    }
};
```

## 📱 用户体验优化

### 响应性设计
```cpp
// ✅ 推荐：避免阻塞UI线程
void DataPointWidget::loadLargeDataSet() {
    // 使用QFutureWatcher进行异步加载
    auto *watcher = new QFutureWatcher<QVector<DataPoint>>(this);
    connect(watcher, &QFutureWatcher<QVector<DataPoint>>::finished,
            [this, watcher]() {
                m_model->setDataPoints(watcher->result());
                watcher->deleteLater();
            });
    
    QFuture<QVector<DataPoint>> future = QtConcurrent::run(
        []() { return loadDataFromFile(); });
    watcher->setFuture(future);
}
```

### 错误处理和用户反馈
```cpp
// ✅ 推荐：友好的错误提示
void MainWindow::handleConnectionError(const QString &error) {
    QMessageBox msgBox(this);
    msgBox.setIcon(QMessageBox::Warning);
    msgBox.setWindowTitle("连接错误");
    msgBox.setText("无法连接到MQTT服务器");
    msgBox.setDetailedText(error);
    msgBox.setStandardButtons(QMessageBox::Retry | QMessageBox::Cancel);
    
    if (msgBox.exec() == QMessageBox::Retry) {
        retryConnection();
    }
}
```

## 📋 代码审查清单

### UI组件检查
- [ ] 所有Widget都有合适的父对象
- [ ] 布局管理器正确设置
- [ ] 信号槽连接使用新式语法
- [ ] 样式表语法正确
- [ ] 支持高DPI显示

### 性能检查
- [ ] UI更新不阻塞主线程
- [ ] 大数据集使用异步加载
- [ ] 图表数据点数量合理
- [ ] 内存泄漏检查通过

### 用户体验检查
- [ ] 错误信息友好易懂
- [ ] 加载状态有进度指示
- [ ] 快捷键设置合理
- [ ] 窗口状态正确保存和恢复

## 🚀 部署和分发

### 应用程序打包
```cmake
# 使用Qt6的部署工具
find_package(Qt6 REQUIRED COMPONENTS Tools)

# 自动部署Qt依赖
if(WIN32)
    add_custom_command(TARGET MQTTGatewayUI POST_BUILD
        COMMAND ${Qt6_DIR}/../../../bin/windeployqt.exe 
                $<TARGET_FILE:MQTTGatewayUI>
    )
endif()
```

### 安装程序配置
```cmake
# CPack配置
set(CPACK_PACKAGE_NAME "MQTT网关控制台")
set(CPACK_PACKAGE_VENDOR "FStation项目组")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "MQTT网关图形化管理工具")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})

if(WIN32)
    set(CPACK_GENERATOR "WIX")
    set(CPACK_WIX_UPGRADE_GUID "12345678-1234-1234-1234-123456789012")
endif()
```
