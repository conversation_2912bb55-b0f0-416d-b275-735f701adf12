{
    "files.autoGuessEncoding": true,
    "files.associations": {
        "*.h": "cpp",
        "*.cpp": "cpp",
        "*.rc": "cpp",
        "*.def": "cpp"
    },
    // C++ IntelliSense 配置 - 优化声明/实现跳转
    "C_Cpp.intelliSenseEngine": "default",
    "C_Cpp.intelliSenseEngineFallback": "enabled",
    "C_Cpp.autocomplete": "default",
    "C_Cpp.errorSquiggles": "enabled",
    "C_Cpp.dimInactiveRegions": true,
    "C_Cpp.enhancedColorization": "enabled",
    // 代码导航优化
    "C_Cpp.workspaceParsingPriority": "highest",
    "C_Cpp.maxCachedProcesses": 8,
    "C_Cpp.maxMemory": 4096,
    // 默认配置
    "C_Cpp.default.intelliSenseMode": "msvc-x86",
    "C_Cpp.default.compilerPath": "C:/Program Files (x86)/Microsoft Visual Studio 10.0/VC/bin/cl.exe",
    "C_Cpp.default.cStandard": "c11",
    "C_Cpp.default.cppStandard": "c++14",
    "C_Cpp.default.includePath": [
        "${workspaceFolder}/**",
        "${workspaceFolder}/FStation/**",
        "C:/Program Files (x86)/Microsoft Visual Studio 10.0/VC/include",
        "C:/Program Files (x86)/Microsoft Visual Studio 10.0/VC/atlmfc/include",
        "C:/Program Files (x86)/Microsoft SDKs/Windows/v7.0A/Include",
        "C:/Program Files (x86)/Microsoft SDKs/Windows/v7.0A/Include/gl"
    ],
    "C_Cpp.default.defines": [
        "_DEBUG",
        "WIN32",
        "_WINDOWS",
        "_AFXDLL",
        "_UNICODE",
        "UNICODE",
        "_WIN32_WINNT=0x0601",
        "WINVER=0x0601",
        "_CRT_SECURE_NO_WARNINGS"
    ],
    "C_Cpp.default.windowsSdkVersion": "7.0.7600.0",
    "C_Cpp.default.browse.path": [
        "${workspaceFolder}/**",
        "C:/Program Files (x86)/Microsoft Visual Studio 10.0/VC/include",
        "C:/Program Files (x86)/Microsoft Visual Studio 10.0/VC/atlmfc/include",
        "C:/Program Files (x86)/Microsoft SDKs/Windows/v7.0A/Include"
    ],
    "C_Cpp.default.browse.limitSymbolsToIncludedHeaders": false,
    "C_Cpp.default.browse.databaseFilename": "${workspaceFolder}/.vscode/browse.vc.db",
    // 编辑器优化
    "editor.wordWrap": "on",
    "editor.tabSize": 4,
    "editor.insertSpaces": false,
    "editor.detectIndentation": true,
    "editor.minimap.enabled": true,
    "editor.codeLens": true,
    // 格式化控制 - 禁用自动格式化，保留手动格式化
    "editor.formatOnSave": false,
    "editor.formatOnSaveMode": "file",
    "editor.formatOnPaste": false,
    "editor.formatOnType": false,
    "editor.autoIndent": "keep",
    "editor.trimAutoWhitespace": false,
    // C++ 特定的格式化设置
    "[cpp]": {
        "editor.formatOnSave": false,
        "editor.formatOnPaste": false,
        "editor.formatOnType": false,
        "editor.autoIndent": "keep",
        "editor.insertSpaces": false,
        "editor.tabSize": 4
    },
    "[c]": {
        "editor.formatOnSave": false,
        "editor.formatOnPaste": false,
        "editor.formatOnType": false,
        "editor.autoIndent": "keep",
        "editor.insertSpaces": false,
        "editor.tabSize": 4
    },
    // C++ 扩展格式化设置
    "C_Cpp.formatting": "disabled",
    "C_Cpp.vcFormat.indent.braces": false,
    "C_Cpp.vcFormat.indent.multiLineRelativeTo": "innermost",
    "C_Cpp.vcFormat.indent.preserveIndentationWithinParentheses": true,
    // 搜索和导航优化
    "search.exclude": {
        "**/build/**": true,
        "**/Debug/**": true,
        "**/Release/**": true,
        "**/x64/**": true,
        "**/*.sdf": true,
        "**/*.suo": true,
        "**/ipch/**": true
    },
    "files.exclude": {
        "**/*.aps": true,
        "**/*.ncb": true,
        "**/*.user": true,
        "**/Debug": true,
        "**/Release": true,
        "**/ipch": true
    }
}