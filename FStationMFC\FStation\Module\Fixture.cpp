﻿#include "stdafx.h"
#include "Fixture.h"

#include "Sys.h"

CFixture::CFixture() : CModule(3)
{
	MakePair("皮带正转方向", new CData(false, false)); // 往前方为正

	MakePair("自检完成标志", new CData(false, false));

	MakePair("治具预警标志", new CData(false, false));

	MakePair("AGV允许上料标志", new CData(false, false));

	MakePair("AGV送料标志", new CData(false, false));

	MakePair("AGV送料完成标志", new CData(false, false));

	MakePair("治具分板标志", new CData(false, false));	

	MakePair("治具分板完成标志", new CData(false, false));		

	MakePair("允许搬运取上盖标志", new CData(false, false));	

	MakePair("搬运取上盖完成标志", new CData(false, false));

	MakePair("允许搬运取下盖标志", new CData(false, false));

	MakePair("搬运取下盖完成标志", new CData(false, false));

	MakePair("测高失败暂停模式", new CData(false, false));	

	MakePair("治具开盖Z轴上层位置", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("治具开盖Z轴下层位置", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("治具开盖Z轴放料位置", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("治具分板Z轴支撑位置", new CData(20.0, -9999.999, 9999.999, true, 4));

	MakePair("治具分板Z轴分板位置", new CData(10.0, -9999.999, 9999.999, true, 4));

	MakePair("治具分板Z轴放料位置", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("合盖高度差正常范围", new CData(1.0));

	MakePair("治具搬运Z轴测高位置", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("治具搬运Y轴A轨测高位置1", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("治具搬运Y轴A轨测高位置1高度标准值1", new CData(0.0));

	MakePair("治具搬运Y轴A轨测高位置1高度标准值2", new CData(0.0));

	MakePair("治具搬运Y轴A轨测高位置2", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("治具搬运Y轴A轨测高位置2高度标准值1", new CData(0.0));

	MakePair("治具搬运Y轴A轨测高位置2高度标准值2", new CData(0.0));

	MakePair("治具搬运Y轴B轨测高位置1", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("治具搬运Y轴B轨测高位置1高度标准值1", new CData(0.0));

	MakePair("治具搬运Y轴B轨测高位置1高度标准值2", new CData(0.0));

	MakePair("治具搬运Y轴B轨测高位置2", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("治具搬运Y轴B轨测高位置2高度标准值1", new CData(0.0));

	MakePair("治具搬运Y轴B轨测高位置2高度标准值2", new CData(0.0));

	MakePair("治具搬运Y轴取上盖位置", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("治具搬运Z轴取上盖位置", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("治具搬运Z轴放上盖位置", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("治具搬运Y轴A轨放上盖位置", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("治具搬运Y轴B轨放上盖位置", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("治具搬运Y轴取下盖位置", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("治具搬运Z轴取下盖位置", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("治具搬运Z轴放下盖位置", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("治具搬运Y轴A轨放下盖位置", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("治具搬运Y轴B轨放下盖位置", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("治具搬运Z轴安全位置", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("延迟测高时间", new CData(5000, 0));

	MakePair("自检皮带传输超时", new CData(5000, 0));

	MakePair("治具吸真空超时", new CData(1000, 0));

	MakePair("治具传输到位延时", new CData(200, 0));

	MakePair("治具入口传输到位延时", new CData(2000, 0));

	Load();
}

CFixture::~CFixture()
{
}

CString CFixture::SupportCylinderOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具支撑气缸", true));

	return "OK";
}

CString CFixture::SupportCylinderOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具支撑气缸", false));

	return "OK";
}

CString CFixture::SupportCylinderStatus()
{
	bool bStatus1 = false, bStatus2 = false, bStatus3 = false, bStatus4 = false;

	EXCUTE_RETURN(g_pControl->ReadInput("治具支撑气缸1缩回信号", bStatus1));

	EXCUTE_RETURN(g_pControl->ReadInput("治具支撑气缸1伸出信号", bStatus2));

	EXCUTE_RETURN(g_pControl->ReadInput("治具支撑气缸2缩回信号", bStatus3));

	EXCUTE_RETURN(g_pControl->ReadInput("治具支撑气缸2伸出信号", bStatus4));

	if (bStatus1 && !bStatus2 && bStatus3 && !bStatus4) {
		return "Off";
	}
	else if (!bStatus1 && bStatus2 && !bStatus3 && bStatus4) {
		return "On";
	}
	else {
		return "UnKnow";
	}
}

CString CFixture::SeparateCylinderOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具分板气缸", true));

	return "OK";
}

CString CFixture::SeparateCylinderOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具分板气缸", false));

	return "OK";
}

CString CFixture::SeparateCylinderStatus()
{
	bool bStatus1 = false, bStatus2 = false, bStatus3 = false, bStatus4 = false;

	EXCUTE_RETURN(g_pControl->ReadInput("治具分板气缸1缩回信号", bStatus1));

	EXCUTE_RETURN(g_pControl->ReadInput("治具分板气缸1伸出信号", bStatus2));

	EXCUTE_RETURN(g_pControl->ReadInput("治具分板气缸2缩回信号", bStatus3));

	EXCUTE_RETURN(g_pControl->ReadInput("治具分板气缸2伸出信号", bStatus4));

	if (bStatus1 && !bStatus2 && bStatus3 && !bStatus4) {
		return "Off";
	}
	else if (!bStatus1 && bStatus2 && !bStatus3 && bStatus4) {
		return "On";
	}
	else {
		return "UnKnow";
	}
}

CString CFixture::UpLidLiftCylinderOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具上盖位顶升气缸缩回", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("治具上盖位顶升气缸伸出", true));

	return "OK";
}

CString CFixture::UpLidLiftCylinderOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具上盖位顶升气缸伸出", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("治具上盖位顶升气缸缩回", true));

	return "OK";
}

CString CFixture::UpLidLiftCylinderStatus()
{
	bool bStatus1 = false, bStatus2 = false;

	EXCUTE_RETURN(g_pControl->ReadInput("治具上盖位顶升气缸缩回信号", bStatus1));

	EXCUTE_RETURN(g_pControl->ReadInput("治具上盖位顶升气缸伸出信号", bStatus2));

	if (bStatus1 && !bStatus2) {
		return "Off";
	}
	else if (!bStatus1 && bStatus2) {
		return "On";
	}
	else {
		return "UnKnow";
	}
}

CString CFixture::UpLidStopCylinderOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具上盖位阻挡气缸缩回", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("治具上盖位阻挡气缸伸出", true));

	return "OK";
}

CString CFixture::UpLidStopCylinderOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具上盖位阻挡气缸伸出", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("治具上盖位阻挡气缸缩回", true));

	return "OK";
}

CString CFixture::UpLidStopCylinderStatus()
{
	bool bStatus1 = false, bStatus2 = false;

	EXCUTE_RETURN(g_pControl->ReadInput("治具上盖位阻挡气缸缩回信号", bStatus1));

	EXCUTE_RETURN(g_pControl->ReadInput("治具上盖位阻挡气缸伸出信号", bStatus2));

	if (bStatus1 && !bStatus2) {
		return "Off";
	}
	else if (!bStatus1 && bStatus2) {
		return "On";
	}
	else {
		return "UnKnow";
	}
}

CString CFixture::UpLidSetPosCylinderOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具上盖位侧定位气缸", true));

	return "OK";
}

CString CFixture::UpLidSetPosCylinderOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具上盖位侧定位气缸", false));

	return "OK";
}

CString CFixture::UpLidSetPosCylinderStatus()
{
	bool bStatus1 = false, bStatus2 = false;

	EXCUTE_RETURN(g_pControl->ReadInput("治具上盖位侧定位气缸缩回信号", bStatus1));

	EXCUTE_RETURN(g_pControl->ReadInput("治具上盖位侧定位气缸伸出信号", bStatus2));

	if (bStatus1 && !bStatus2) {
		return "Off";
	}
	else if (!bStatus1 && bStatus2) {
		return "On";
	}
	else {
		return "UnKnow";
	}
}

CString CFixture::DnLidSetPosCylinderOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具下盖位侧定位气缸", true));

	return "OK";
}

CString CFixture::DnLidSetPosCylinderOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具下盖位侧定位气缸", false));

	return "OK";
}

CString CFixture::DnLidSetPosCylinderStatus()
{
	bool bStatus1 = false, bStatus2 = false;

	EXCUTE_RETURN(g_pControl->ReadInput("治具下盖位侧定位气缸缩回信号", bStatus1));

	EXCUTE_RETURN(g_pControl->ReadInput("治具下盖位侧定位气缸伸出信号", bStatus2));

	if (bStatus1 && !bStatus2) {
		return "Off";
	}
	else if (!bStatus1 && bStatus2) {
		return "On";
	}
	else {
		return "UnKnow";
	}
}

CString CFixture::RollCylinderOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具旋转气缸缩回", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("治具旋转气缸伸出", true));

	return "OK";
}

CString CFixture::RollCylinderOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具旋转气缸伸出", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("治具旋转气缸缩回", true));

	return "OK";
}

CString CFixture::RollCylinderStatus()
{
	bool bStatus1 = false, bStatus2 = false;

	EXCUTE_RETURN(g_pControl->ReadInput("治具旋转气缸缩回信号", bStatus1));
	
	EXCUTE_RETURN(g_pControl->ReadInput("治具旋转气缸伸出信号", bStatus2));

	if (bStatus1 && !bStatus2) {
		return "Off";
	}
	else if (!bStatus1 && bStatus2) {
		return "On";
	}
	else {
		return "UnKnow";
	}
}

CString CFixture::VacuumOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具取料破真空", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("治具取料真空", true));

	return "OK";
}

CString CFixture::BrokenVacuumOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具取料真空", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("治具取料破真空", true));

	return "OK";
}

CString CFixture::VacuumOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具取料破真空", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("治具取料真空", false));

	return "OK";
}

CString CFixture::VacuumStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("治具取料真空信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CFixture::InStopCylinderOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具入口阻挡气缸", true));

	return "OK";
}

CString CFixture::InStopCylinderOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具入口阻挡气缸", false));

	return "OK";
}

CString CFixture::InStopCylinderStatus()
{
	bool bStatus1 = false, bStatus2 = false;

	EXCUTE_RETURN(g_pControl->ReadInput("治具入口阻挡气缸缩回信号", bStatus1));

	EXCUTE_RETURN(g_pControl->ReadInput("治具入口阻挡气缸伸出信号", bStatus2));

	if (bStatus1 && !bStatus2) {
		return "Off";
	}
	else if (!bStatus1 && bStatus2) {
		return "On";
	}
	else {
		return "UnKnow";
	}
}

CString CFixture::UploadBeltOn(bool bDir)
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具上料皮带方向", bDir));

	EXCUTE_RETURN(g_pControl->WriteOutput("治具上料皮带启动", true));

	return "OK";
}

CString CFixture::UploadBeltOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("治具上料皮带启动", false));

	return "OK";
}

CString CFixture::InStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("治具入口来料信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CFixture::InPosStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("治具入口到位信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CFixture::ErrorCheckStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("治具防呆检测信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CFixture::OutOfRangePreWarnStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("治具数量预警信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CFixture::OutOfRangeWarnStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("治具数量上限报警信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CFixture::UpLidInPosStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("治具上盖位到位信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CFixture::DnLidInPosStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("治具下盖位到位信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CFixture::UpLidUpLayerExistStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("治具上盖位上层有料信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CFixture::UpLidDnLayerExistStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("治具上盖位下层有料信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CFixture::TransportYHome()
{
	EXCUTE_RETURN(g_pControl->Home("治具搬运Y轴"));

	return "OK";
}

CString CFixture::IsTransportYHomeOK()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->IsHomeOk("治具搬运Y轴", bStatus));

	return bStatus ? "Yes" : "No";
}

CString CFixture::TransportZHome()
{
	EXCUTE_RETURN(g_pControl->Home("治具搬运Z轴"));

	return "OK";
}

CString CFixture::IsTransportZHomeOK()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->IsHomeOk("治具搬运Z轴", bStatus));

	return bStatus ? "Yes" : "No";
}

CString CFixture::SeparateZHome()
{
	EXCUTE_RETURN(g_pControl->Home("治具分板Z轴"));

	return "OK";
}

CString CFixture::IsSeparateZHomeOK()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->IsHomeOk("治具分板Z轴", bStatus));

	return bStatus ? "Yes" : "No";
}

CString CFixture::LidZHome()
{
	EXCUTE_RETURN(g_pControl->Home("治具开盖Z轴"));

	return "OK";
}

CString CFixture::IsLidZHomeOK()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->IsHomeOk("治具开盖Z轴", bStatus));

	return bStatus ? "Yes" : "No";
}

CString CFixture::TransportYMove(double nPos, bool bSafeCheckFlag/* = true*/,int nSpeedRate/* = 100*/)
{
	if (!CSys::m_bInit) {
		return "未复位";
	}

	CString sRet;

	bool bFlag = false;

	if (bSafeCheckFlag) {
		bFlag = OnSafeCheck("治具搬运Y轴", &sRet);
	}
	else {
		bFlag = true;
	}

	if (!bFlag) {
		REPORT("当前状态不允许操作: " + sRet, emLogLevelWarn)
		return "当前状态不允许操作: " + sRet;
	}

	EXCUTE_RETURN(g_pControl->MoveTo("治具搬运Y轴", nPos, nSpeedRate));

	return "OK";
}

CString CFixture::IsTransportYInPos(double nPos, double nTolerance/* = 0.005*/)
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->IsInPos("治具搬运Y轴", nPos, nTolerance, bStatus));

	return bStatus ? "Yes" : "No";
}

CString CFixture::TransportYStop()
{
	EXCUTE_RETURN(g_pControl->Stop("治具搬运Y轴"));

	return "OK";
}

CString CFixture::TransportZMove(double nPos, int nSpeedRate/* = 100*/)
{
	if (!CSys::m_bInit) {
		return "未复位";
	}

	EXCUTE_RETURN(g_pControl->MoveTo("治具搬运Z轴", nPos, nSpeedRate));

	return "OK";
}

CString CFixture::IsTransportZInPos(double nPos, double nTolerance/* = 0.05*/)
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->IsInPos("治具搬运Z轴", nPos, nTolerance, bStatus));

	return bStatus ? "Yes" : "No";
}

CString CFixture::TransportZStop()
{
	EXCUTE_RETURN(g_pControl->Stop("治具搬运Z轴"));

	return "OK";
}

CString CFixture::SeparateZMove(double nPos, int nSpeedRate/* = 100*/)
{
	if (!CSys::m_bInit) {
		return "未复位";
	}

	if (IsSeparateZHomeOK() != "Yes") {
		return "OK";
	}

	g_pControl->MoveTo("治具分板Z轴", nPos, nSpeedRate);

	return "OK";
}

CString CFixture::IsSeparateZInPos(double nPos, double nTolerance/* = 0.05*/)
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->IsInPos("治具分板Z轴", nPos, nTolerance, bStatus));

	return bStatus ? "Yes" : "No";
}

CString CFixture::SeparateZStop()
{
	EXCUTE_RETURN(g_pControl->Stop("治具分板Z轴"));

	return "OK";
}

CString CFixture::LidZMove(double nPos, int nSpeedRate/* = 100*/)
{
	if (!CSys::m_bInit) {
		return "未复位";
	}

	if (IsLidZHomeOK() != "Yes") {
		return "OK";
	}

	g_pControl->MoveTo("治具开盖Z轴", nPos, nSpeedRate);

	return "OK";
}

CString CFixture::IsLidZInPos(double nPos, double nTolerance/* = 0.05*/)
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->IsInPos("治具开盖Z轴", nPos, nTolerance, bStatus));

	return bStatus ? "Yes" : "No";
}

CString CFixture::LidZStop()
{
	EXCUTE_RETURN(g_pControl->Stop("治具开盖Z轴"));

	return "OK";
}

CString CFixture::GetHeightL(double &nHeight)
{
	return g_pHeightSensorL->GetHeight(nHeight);
}

CString CFixture::GetHeightR(double &nHeight)
{
	return g_pHeightSensorR->GetHeight(nHeight);
}
