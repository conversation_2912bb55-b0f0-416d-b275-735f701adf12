#pragma once

#include "Module.h"

class CTray : public CModule
{
public:
	CTray();
	virtual ~CTray();

public:
	CString FullTrayEndStopCylinderOn();
	CString FullTrayEndStopCylinderOff();
	CString FullTrayEndStopCylinderStatus();

	CString FullTraySupportCylinderOn();
	CString FullTraySupportCylinderOff();
	CString FullTraySupportCylinderStatus();

	CString FullTraySeparateCylinderOn();
	CString FullTraySeparateCylinderOff();
	CString FullTraySeparateCylinderStatus();
	
	CString FullTrayBeltOn(bool bDir);
	CString FullTrayBeltOff();

	CString FullTrayBeltInMaterialStatus();
	CString FullTrayBeltWaitMaterialStatus();
	CString FullTrayBeltInPosStatus();
	CString FullTrayUpInPosStatus();
	CString FullTrayMaterialExistStatus();

	CString EmptyTrayPullMaterialCylinderOn();
	CString EmptyTrayPullMaterialCylinderOff();
	CString EmptyTrayPullMaterialCylinderStatus();

	CString EmptyTrayVacuumOn();
	CString EmptyTrayBrokenVacuumOn();
	CString EmptyTrayVacuumOff();
	CString EmptyTrayVacuumStatus();

	CString EmptyTrayEndStopCylinderOn();
	CString EmptyTrayEndStopCylinderOff();
	CString EmptyTrayEndStopCylinderStatus();
	
	CString EmptyTraySupportCylinderOn();
	CString EmptyTraySupportCylinderOff();
	CString EmptyTraySupportCylinderStatus();
	
	CString EmptyTrayBeltOn(bool bDir);
	CString EmptyTrayBeltOff();

	CString EmptyTrayBeltInMaterialStatus();
	CString EmptyTrayBeltInPosStatus();

	CString EmptyTrayMaterialExistStatus();
	CString EmptyTrayOutOfRangePreWarnStatus();
	CString EmptyTrayOutOfRangeWarnStatus();

	CString FullTrayUpDnZHome();
	CString IsFullTrayUpDnZHomeOK();

	CString EmptyTrayTransportYHome();
	CString IsEmptyTrayTransportYHomeOK();

	CString EmptyTraySeparateZHome();
	CString IsEmptyTraySeparateZHomeOK();

	CString FullTrayUpDnZMove(double nPos, int nSpeedRate = 100);
	CString FullTrayUpDnZCurrentPos(double &nCurPos);
	CString IsFullTrayUpDnZInPos(double nPos, double nTolerance = 0.005);
	CString FullTrayUpDnZStop();

	CString EmptyTrayTransportYMove(double nPos, int nSpeedRate = 100);
	CString IsEmptyTrayTransportYInPos(double nPos, double nTolerance = 0.5);
	CString EmptyTrayTransportYStop();

	CString EmptyTraySeparateZMove(double nPos, int nSpeedRate = 100);
	CString IsEmptyTraySeparateZInPos(double nPos, double nTolerance = 0.5);
	CString EmptyTraySeparateZStop();
};
