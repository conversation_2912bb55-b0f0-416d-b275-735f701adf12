﻿#include "stdafx.h"
#include "LogicFixture.h"

#include "Sys.h"
#include "LogicMgr.h"

#define PARAM(NAME)			(*m_pFixture->m_mapParam[NAME])

#define PARAM_BOOL(NAME)	(*m_pFixture->m_mapParam[NAME]).B()
#define PARAM_INT(NAME)		(*m_pFixture->m_mapParam[NAME]).I()
#define PARAM_DOUBLE(NAME)	(*m_pFixture->m_mapParam[NAME]).D()
#define PARAM_STRING(NAME)	(*m_pFixture->m_mapParam[NAME]).S()

CLogicFixture::CLogicFixture()
{
	m_pFixture = g_pFixture;
	
	m_pFuncUpload = CLogicMgr::m_ThreadFactory.CreateThreadFunc("治具上料");
	m_pFuncSeparate = CLogicMgr::m_ThreadFactory.CreateThreadFunc("治具分板");
	m_pFuncAgv = CLogicMgr::m_ThreadFactory.CreateThreadFunc("AGV上料");
}

CLogicFixture::~CLogicFixture()
{
	delete m_pFuncUpload;
	delete m_pFuncSeparate;
	delete m_pFuncAgv;
}

EnumStatus CLogicFixture::OnSafeCheck()
{
	if (!CSys::m_bInit) {
		return emStop;
	}

	return emRun;
}

EnumStatus CLogicFixture::OnStart()
{
	return emRun;
}

EnumStatus CLogicFixture::OnPause()
{
	m_pFixture->UploadBeltOff();

	m_pFuncUpload->SetStatus(emPause);
	m_pFuncSeparate->SetStatus(emPause);

	return emRun;
}

EnumStatus CLogicFixture::OnResume()
{
	map<CString, DWORD>::iterator it = m_mapTick.begin();
	for (; it!=m_mapTick.end(); it++)
	{
		it->second = GetTickCount();
	}

	return emRun;
}

EnumStatus CLogicFixture::OnStop()
{
	m_pFixture->UploadBeltOff();

	m_pFixture->TransportYStop();

	m_pFixture->TransportZStop();

	m_pFuncUpload->SetStatus(emStop);
	m_pFuncSeparate->SetStatus(emStop);

	return emRun;
}

CStatus CLogicFixture::OnRun()
{
	PARAM("自检完成标志") = false;

	PARAM("治具分板标志") = false;

	PARAM("治具分板完成标志") = false;
	
	PARAM("允许搬运取上盖标志") = false;
	
	PARAM("搬运取上盖完成标志") = true;
	
	PARAM("允许搬运取下盖标志") = false;
	
	PARAM("搬运取下盖完成标志") = true;

	m_mapFlag["AGV空闲"] = true;

	m_pFuncUpload->SetAction(static_cast<THREAD_FUNC>(&CLogicFixture::OnFixtureUpload00));

	m_pFuncSeparate->SetAction(static_cast<THREAD_FUNC>(&CLogicFixture::OnFixtureSeparate00));

	m_pFuncAgv->SetAction(static_cast<THREAD_FUNC>(&CLogicFixture::OnFixtureAgv00));

	RETURN_STATE(&CLogicFixture::OnSelfCheck00, true);
}

CStatus CLogicFixture::OnSelfCheck00()
{
	RETURN_STATE(&CLogicFixture::OnSelfCheck01, true);
}

CStatus CLogicFixture::OnSelfCheck01()
{
	RUN_STOP_IF_ERROR(m_pFixture->InStopCylinderOn());

	RUN_STOP_IF_ERROR(m_pFixture->UpLidStopCylinderOn());

	RUN_STOP_IF_ERROR(m_pFixture->InStopCylinderStatus());
	
	if (m_sRet != "On") {
		RETURN_SELF("等待治具入料阻挡气缸伸出", true);
	}

	RUN_STOP_IF_ERROR(m_pFixture->UpLidStopCylinderStatus());
	
	if (m_sRet != "On") {
		RETURN_SELF("等待治具上盖位阻挡气缸伸出", true);
	}

	m_mapTick["自检皮带转动计时"] = GetTickCount();

	RETURN_STATE(&CLogicFixture::OnSelfCheck03, true);
}

CStatus CLogicFixture::OnSelfCheck02()
{
	CString sRet1, sRet2, sRet3;

	RUN_STOP_IF_ERROR(m_pFixture->InPosStatus());

	sRet1 = m_sRet;

	RUN_STOP_IF_ERROR(m_pFixture->UpLidInPosStatus());

	sRet2 = m_sRet;

	RUN_STOP_IF_ERROR(m_pFixture->DnLidInPosStatus());

	sRet3 = m_sRet;

	if (sRet1 != "On" || sRet2 != "On" || sRet3 != "On") {
		if (GetTickCount() - m_mapTick["自检皮带转动计时"] < (DWORD)PARAM_INT("自检皮带传输超时")) {
//			RUN_STOP_IF_ERROR(m_pFixture->UploadBeltOn(PARAM_BOOL("皮带正转方向")))
			RETURN_SELF("", false);
		}
	}
	
	RUN_STOP_IF_ERROR(m_pFixture->UploadBeltOff());

	RETURN_STATE(&CLogicFixture::OnSelfCheck03, true);
}

CStatus CLogicFixture::OnSelfCheck03()
{
	RUN_STOP_IF_ERROR(m_pFixture->DnLidInPosStatus());

	if (m_sRet == "On") {
		RUN_STOP_IF_ERROR(m_pFixture->DnLidSetPosCylinderOn());
		Sleep(200);
		RUN_STOP_IF_ERROR(m_pFixture->DnLidSetPosCylinderOff());
		PARAM("允许搬运取下盖标志") = true;
	}

	PARAM("自检完成标志") = true;

	RETURN_STATE(&CLogicFixture::OnFixture00, true);
}

CStatus CLogicFixture::OnFixture00()
{
	EnumStatus  status = m_pFuncUpload->Run(this);

	switch (status)
	{
	case emRun:
		break;
	case emStop:
		RETURN_STOP();
	case emPause:
		RETURN_PAUSE(NULL);
	default:
		break;
	}

	RETURN_STATE(&CLogicFixture::OnFixture01, false);
}

CStatus CLogicFixture::OnFixture01()
{
	EnumStatus  status = m_pFuncSeparate->Run(this);

	switch (status)
	{
	case emRun:
		break;
	case emStop:
		RETURN_STOP();
	case emPause:
		RETURN_PAUSE(NULL);
	default:
		break;
	}

	RETURN_STATE(&CLogicFixture::OnFixture02, false);
}

CStatus CLogicFixture::OnFixture02()
{
	EnumStatus  status = m_pFuncAgv->Run(this);

	switch (status)
	{
	case emRun:
		break;
	case emStop:
		RETURN_STOP();
	case emPause:
		RETURN_PAUSE(NULL);
	default:
		break;
	}

	RETURN_STATE(&CLogicFixture::OnFixture00, false);
}

CStatus CLogicFixture::OnFixtureUpload00()
{
	RUN_STOP_IF_ERROR(m_pFixture->UpLidInPosStatus());

	if (m_sRet !=  "On") {
		RETURN_STATE(&CLogicFixture::OnFixtureUpload13, true);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureUpload01, true);
}

CStatus CLogicFixture::OnFixtureUpload01()
{
	RUN_STOP_IF_ERROR(m_pFixture->UpLidDnLayerExistStatus());

	if (m_sRet == "On") {
		REPORT("治具上盖位皮带和治具上盖位下层都有料，请取走治具上盖位下层上盖板！", emLogLevelWarn);
		MESSAGEBOX("治具上盖位皮带和治具上盖位下层都有料，请取走治具上盖位下层上盖板！", "", false);
		RETURN_SELF("", false);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureUpload02, true);
}

CStatus CLogicFixture::OnFixtureUpload02()
{
	RUN_STOP_IF_ERROR(m_pFixture->UpLidLiftCylinderOn());

	RUN_STOP_IF_ERROR(m_pFixture->UpLidLiftCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待治具上盖位顶升气缸伸出", true);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureUpload03, true);
}

CStatus CLogicFixture::OnFixtureUpload03()
{
	RUN_STOP_IF_ERROR(m_pFixture->UpLidUpLayerExistStatus());

	if (m_sRet == "On") {
		m_mapPos["治具开盖Z轴当前目标位置"] = PARAM_DOUBLE("治具开盖Z轴下层位置");
	}
	else {
		m_mapPos["治具开盖Z轴当前目标位置"] = PARAM_DOUBLE("治具开盖Z轴上层位置");
	}

	RETURN_STATE(&CLogicFixture::OnFixtureUpload04, true);
}

CStatus CLogicFixture::OnFixtureUpload04()
{
	RUN_STOP_IF_ERROR(m_pFixture->LidZMove(m_mapPos["治具开盖Z轴当前目标位置"], 100));

	RUN_STOP_IF_ERROR(m_pFixture->IsLidZInPos(m_mapPos["治具开盖Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具开盖Z轴到位", true);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureUpload05, true);
}

CStatus CLogicFixture::OnFixtureUpload05()
{
	m_mapPos["治具开盖Z轴当前目标位置"] = PARAM_DOUBLE("治具开盖Z轴放料位置");

	RUN_STOP_IF_ERROR(m_pFixture->LidZMove(m_mapPos["治具开盖Z轴当前目标位置"], 100));

	RUN_STOP_IF_ERROR(m_pFixture->UpLidLiftCylinderOff());

	RUN_STOP_IF_ERROR(m_pFixture->UpLidLiftCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具上盖位顶升气缸缩回", true);
	}

	RUN_STOP_IF_ERROR(m_pFixture->IsLidZInPos(m_mapPos["治具开盖Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具开盖Z轴到位", true);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureUpload06, true);
}

CStatus CLogicFixture::OnFixtureUpload06()
{
	RUN_STOP_IF_ERROR(m_pFixture->UpLidUpLayerExistStatus());

	if (m_sRet == "On" ) {
		PARAM("允许搬运取上盖标志") = true;
	}

	RETURN_STATE(&CLogicFixture::OnFixtureUpload07, true);
}

CStatus CLogicFixture::OnFixtureUpload07()
{
	RETURN_STATE(&CLogicFixture::OnFixtureUpload08, true);
}

CStatus CLogicFixture::OnFixtureUpload08()
{
	RUN_STOP_IF_ERROR(m_pFixture->DnLidInPosStatus());

	if (m_sRet == "On" || !PARAM_BOOL("搬运取下盖完成标志")) {
		RETURN_SELF("等待治具下盖位空闲", false);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureUpload09, true);
}

CStatus CLogicFixture::OnFixtureUpload09()
{
	RUN_STOP_IF_ERROR(m_pFixture->UpLidStopCylinderOff());

	RUN_STOP_IF_ERROR(m_pFixture->UpLidStopCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具上盖位阻挡气缸缩回", true);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureUpload10, true);
}

CStatus CLogicFixture::OnFixtureUpload10()
{
	RUN_STOP_IF_ERROR(m_pFixture->UploadBeltOn(PARAM_BOOL("皮带正转方向")));

	RUN_STOP_IF_ERROR(m_pFixture->DnLidInPosStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待治具下盖传输到位", true);
	}

	m_mapTick["皮带转动计时"] = GetTickCount();

	RETURN_STATE(&CLogicFixture::OnFixtureUpload11, true);
}

CStatus CLogicFixture::OnFixtureUpload11()
{
	RUN_STOP_IF_ERROR(m_pFixture->UploadBeltOn(PARAM_BOOL("皮带正转方向")));

	if (GetTickCount() - m_mapTick["皮带转动计时"] < (DWORD)PARAM_INT("治具传输到位延时")) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(m_pFixture->UploadBeltOff());

	RUN_STOP_IF_ERROR(m_pFixture->LidZHome());

	RUN_STOP_IF_ERROR(m_pFixture->DnLidSetPosCylinderOn());

	Sleep(200);

	RUN_STOP_IF_ERROR(m_pFixture->DnLidSetPosCylinderOff());

	PARAM("允许搬运取下盖标志") = true;

	RETURN_STATE(&CLogicFixture::OnFixtureUpload12, true);
}

CStatus CLogicFixture::OnFixtureUpload12()
{
	RUN_STOP_IF_ERROR(m_pFixture->UpLidStopCylinderOn());

	RUN_STOP_IF_ERROR(m_pFixture->UpLidStopCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待治具上盖位阻挡气缸伸出", true);
	}

	RUN_STOP_IF_ERROR(m_pFixture->IsLidZHomeOK());

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具开盖Z轴回零完成", false);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureUpload00, true);
}

CStatus CLogicFixture::OnFixtureUpload13()
{
	CString sRet1, sRet2;

	RUN_STOP_IF_ERROR(m_pFixture->UpLidUpLayerExistStatus());

	sRet1 = m_sRet;

	RUN_STOP_IF_ERROR(m_pFixture->UpLidDnLayerExistStatus());

	sRet2 = m_sRet;

	if (sRet1 == "On") {
		PARAM("允许搬运取上盖标志") = true;
	}
	else {
		PARAM("允许搬运取上盖标志") = false;
	}

	if (sRet1 == "On" && sRet2 == "On") {
		RETURN_SELF("等待上盖被取走", false);
	}

	if (sRet2 == "Off") {
		PARAM("治具分板标志") = true;
		RETURN_STATE(&CLogicFixture::OnFixtureUpload16, true);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureUpload14, true);
}

CStatus CLogicFixture::OnFixtureUpload14()
{
	m_mapPos["治具开盖Z轴当前目标位置"] = PARAM_DOUBLE("治具开盖Z轴上层位置");

	RUN_STOP_IF_ERROR(m_pFixture->LidZMove(m_mapPos["治具开盖Z轴当前目标位置"], 100));

	RUN_STOP_IF_ERROR(m_pFixture->IsLidZInPos(m_mapPos["治具开盖Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具开盖Z轴到位", true);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureUpload15, true);
}

CStatus CLogicFixture::OnFixtureUpload15()
{
	m_mapPos["治具开盖Z轴当前目标位置"] = PARAM_DOUBLE("治具开盖Z轴放料位置");

	RUN_STOP_IF_ERROR(m_pFixture->LidZMove(m_mapPos["治具开盖Z轴当前目标位置"], 100));

	RUN_STOP_IF_ERROR(m_pFixture->IsLidZInPos(m_mapPos["治具开盖Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具开盖Z轴到位", true);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureUpload13, true);
}

CStatus CLogicFixture::OnFixtureUpload16()
{
	RUN_STOP_IF_ERROR(m_pFixture->InPosStatus());

	if (m_sRet != "On" || !PARAM_BOOL("治具分板完成标志")) {
		RETURN_SELF("等待治具分板完成", false);
	}

	PARAM("治具分板完成标志") = false;

	RETURN_STATE(&CLogicFixture::OnFixtureUpload17, true);
}

CStatus CLogicFixture::OnFixtureUpload17()
{
	RUN_STOP_IF_ERROR(m_pFixture->InStopCylinderOff());

	RUN_STOP_IF_ERROR(m_pFixture->InStopCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具阻挡气缸缩回", true);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureUpload18, true);
}

CStatus CLogicFixture::OnFixtureUpload18()
{
	RUN_STOP_IF_ERROR(m_pFixture->UploadBeltOn(PARAM_BOOL("皮带正转方向")));

	RUN_STOP_IF_ERROR(m_pFixture->UpLidInPosStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待治具传输到位", true);
	}

	RUN_STOP_IF_ERROR(m_pFixture->SeparateZHome());

	m_mapTick["皮带转动计时"] = GetTickCount();

	RETURN_STATE(&CLogicFixture::OnFixtureUpload19, true);
}

CStatus CLogicFixture::OnFixtureUpload19()
{
	RUN_STOP_IF_ERROR(m_pFixture->UploadBeltOn(PARAM_BOOL("皮带正转方向")));

	if (GetTickCount() - m_mapTick["皮带转动计时"] < (DWORD)PARAM_INT("治具传输到位延时")) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(m_pFixture->UploadBeltOff());

	RUN_STOP_IF_ERROR(m_pFixture->UpLidSetPosCylinderOn());

	Sleep(200);

	RUN_STOP_IF_ERROR(m_pFixture->UpLidSetPosCylinderOff());

	RETURN_STATE(&CLogicFixture::OnFixtureUpload20, true);
}

CStatus CLogicFixture::OnFixtureUpload20()
{
	RUN_STOP_IF_ERROR(m_pFixture->InStopCylinderOn());

	RUN_STOP_IF_ERROR(m_pFixture->InStopCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待治具阻挡气缸伸出", true);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureUpload00, true);
}

CStatus CLogicFixture::OnFixtureSeparate00()
{	
	if (!PARAM_BOOL("治具分板标志")) {
		RETURN_SELF("等待治具分板信号", false);
	}

	if (!m_mapFlag["AGV空闲"]) {
		RETURN_SELF("等待AGV空闲信号", false);
	}

	PARAM("治具分板标志") = false;

	RETURN_STATE(&CLogicFixture::OnFixtureSeparate00_0, true);
}

CStatus CLogicFixture::OnFixtureSeparate00_0()
{
	RUN_STOP_IF_ERROR(m_pFixture->OutOfRangeWarnStatus());

	if (m_sRet != "On") {
		RETURN_STATE(&CLogicFixture::OnFixtureSeparate07, true);
	}

	RUN_STOP_IF_ERROR(m_pFixture->OutOfRangePreWarnStatus());

	if (m_sRet != "On") {
		VAR_FIXTURE("治具预警标志") = true;
		REPORT("治具即将用完，请及时请添加空治具!", emLogLevelWarn);
	}
	else {
		VAR_FIXTURE("治具预警标志") = false;
	}
	
	RETURN_STATE(&CLogicFixture::OnFixtureSeparate00_1, true);
}

CStatus CLogicFixture::OnFixtureSeparate00_1()
{
	RUN_STOP_IF_ERROR(m_pFixture->ErrorCheckStatus());

	if (m_sRet == "On") {
		REPORT("空治具里面有治具方向不正确，请调整正确后重新放回!", emLogLevelWarn);
		MESSAGEBOX("空治具里面有治具方向不正确，请调整正确后重新放回!", "", false);
		RETURN_STATE(&CLogicFixture::OnFixtureSeparate00_0, true);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureSeparate01, true);
}

CStatus CLogicFixture::OnFixtureSeparate01()
{
	m_mapPos["治具分板Z轴当前目标位置"] = PARAM_DOUBLE("治具分板Z轴支撑位置");

	RUN_STOP_IF_ERROR(m_pFixture->SeparateZMove(m_mapPos["治具分板Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(m_pFixture->IsSeparateZInPos(m_mapPos["治具分板Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具分板Z轴到位", true);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureSeparate02, true);
}

CStatus CLogicFixture::OnFixtureSeparate02()
{
	RUN_STOP_IF_ERROR(m_pFixture->InStopCylinderOff());

	RUN_STOP_IF_ERROR(m_pFixture->SeparateCylinderOff());

	RUN_STOP_IF_ERROR(m_pFixture->SupportCylinderOff());

	RUN_STOP_IF_ERROR(m_pFixture->SeparateCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具分板气缸缩回", true);
	}

	RUN_STOP_IF_ERROR(m_pFixture->SupportCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具支撑气缸缩回", true);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureSeparate11, true);
}

CStatus CLogicFixture::OnFixtureSeparate03()
{
	m_mapPos["治具分板Z轴当前目标位置"] = PARAM_DOUBLE("治具分板Z轴分板位置");

	RUN_STOP_IF_ERROR(m_pFixture->SeparateZMove(m_mapPos["治具分板Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(m_pFixture->IsSeparateZInPos(m_mapPos["治具分板Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具分板Z轴到位", true);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureSeparate04, true);
}

CStatus CLogicFixture::OnFixtureSeparate04()
{
	RUN_STOP_IF_ERROR(m_pFixture->SupportCylinderOn());

	RUN_STOP_IF_ERROR(m_pFixture->SupportCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待治具支撑气缸伸出", true);
	}

	RUN_STOP_IF_ERROR(m_pFixture->SeparateCylinderOn());

	RETURN_STATE(&CLogicFixture::OnFixtureSeparate05, true);
}

CStatus CLogicFixture::OnFixtureSeparate05()
{
	m_mapPos["治具分板Z轴当前目标位置"] = PARAM_DOUBLE("治具分板Z轴放料位置");

	RUN_STOP_IF_ERROR(m_pFixture->SeparateZMove(m_mapPos["治具分板Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(m_pFixture->IsSeparateZInPos(m_mapPos["治具分板Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具分板Z轴到位", true);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureSeparate06, true);
}

CStatus CLogicFixture::OnFixtureSeparate06()
{
	RUN_STOP_IF_ERROR(m_pFixture->SeparateCylinderOff());

	RUN_STOP_IF_ERROR(m_pFixture->SeparateCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具分板气缸缩回", true);
	}

	PARAM("治具分板完成标志") = true;

	RETURN_STATE(&CLogicFixture::OnFixtureSeparate00, true);
}

CStatus CLogicFixture::OnFixtureSeparate07()
{
	RUN_STOP_IF_ERROR(m_pFixture->SupportCylinderOff());

	RUN_STOP_IF_ERROR(m_pFixture->SeparateCylinderOff());

	RUN_STOP_IF_ERROR(m_pFixture->SupportCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具支撑气缸缩回", false);
	}

	RUN_STOP_IF_ERROR(m_pFixture->SeparateCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具分板气缸缩回", false);
	}

	RUN_STOP_IF_ERROR(m_pFixture->InStatus());

	if (m_sRet != "On") {
		REPORT("空治具已用完，请添加空治具!", emLogLevelWarn);
		MESSAGEBOX("空治具已用完，请添加空治具!", "检测空治具", true);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureSeparate08, true);
}

CStatus CLogicFixture::OnFixtureSeparate08()
{
	CString sRet1, sRet2;

	RUN_STOP_IF_ERROR(m_pFixture->InStatus());
	sRet1 = m_sRet;

	RUN_STOP_IF_ERROR(m_pFixture->InPosStatus());
	sRet2 = m_sRet;

	if (sRet1 != "On" && sRet2 != "On") {
		RETURN_SELF("等待空治具上料", true);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureSeparate09, true);
}

CStatus CLogicFixture::OnFixtureSeparate09()
{
	RUN_STOP_IF_ERROR(m_pFixture->UploadBeltOn(PARAM_BOOL("皮带正转方向")));

	RUN_STOP_IF_ERROR(m_pFixture->InPosStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待治具传输到位", true);
	}

	m_mapTick["皮带转动计时"] = GetTickCount();

	RETURN_STATE(&CLogicFixture::OnFixtureSeparate10, true);
}

CStatus CLogicFixture::OnFixtureSeparate10()
{
	RUN_STOP_IF_ERROR(m_pFixture->UploadBeltOn(PARAM_BOOL("皮带正转方向")));

	if (GetTickCount() - m_mapTick["皮带转动计时"] < (DWORD)PARAM_INT("治具入口传输到位延时")) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(m_pFixture->UploadBeltOff());

	RETURN_STATE(&CLogicFixture::OnFixtureSeparate11, true);
}

CStatus CLogicFixture::OnFixtureSeparate11()
{
	RUN_STOP_IF_ERROR(m_pFixture->ErrorCheckStatus());

	if (m_sRet == "On") {
		REPORT("空治具里面有治具方向不正确，请调整正确后重新放回!!", emLogLevelWarn);
		MESSAGEBOX("空治具里面有治具方向不正确，请调整正确后重新放回!", "", false);
		RETURN_STATE(&CLogicFixture::OnFixtureSeparate07, true);
	}

	RETURN_STATE(&CLogicFixture::OnFixtureSeparate03, true);
}

CStatus CLogicFixture::OnFixtureAgv00()
{
	RUN_STOP_IF_ERROR(m_pFixture->OutOfRangePreWarnStatus());

	if (m_sRet == "On") {
		RETURN_SELF("等待治具用完", false);
	}

	RUN_STOP_IF_ERROR(m_pFixture->OutOfRangeWarnStatus());

	if (m_sRet == "On") {
		RETURN_SELF("等待治具用完", false);
	}

	RUN_STOP_IF_ERROR(m_pFixture->InStopCylinderStatus());
	
	if (m_sRet != "On") {
		RETURN_SELF("等待治具阻挡气缸伸出", false);
	}

	RUN_STOP_IF_ERROR(m_pFixture->IsSeparateZHomeOK())

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具分板Z轴回零完成", false);
	}

	if (!VAR_FIXTURE_B("AGV送料标志")) {
		RETURN_SELF("等待AGV小车到位", false);
	}

	m_mapFlag["AGV空闲"] = false;

	RETURN_STATE(&CLogicFixture::OnFixtureAgv01, true);
}

CStatus CLogicFixture::OnFixtureAgv01()
{
	RUN_STOP_IF_ERROR(m_pFixture->SupportCylinderOff());

	RUN_STOP_IF_ERROR(m_pFixture->SeparateCylinderOff());

	RUN_STOP_IF_ERROR(m_pFixture->SupportCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具支撑气缸缩回", true);
	}

	RUN_STOP_IF_ERROR(m_pFixture->SeparateCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具分板气缸缩回", true);
	}

	VAR_FIXTURE("AGV允许上料标志") = true;

	RETURN_STATE(&CLogicFixture::OnFixtureAgv02, true);
}

CStatus CLogicFixture::OnFixtureAgv02()
{
	RUN_STOP_IF_ERROR(m_pFixture->UploadBeltOn(PARAM_BOOL("皮带正转方向")));

	RUN_STOP_IF_ERROR(m_pFixture->InPosStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待治具传输到位", true);
	}

	m_mapTick["皮带转动计时"] = GetTickCount();

	RETURN_STATE(&CLogicFixture::OnFixtureAgv03, true);
}

CStatus CLogicFixture::OnFixtureAgv03()
{
	RUN_STOP_IF_ERROR(m_pFixture->UploadBeltOn(PARAM_BOOL("皮带正转方向")));

	if (GetTickCount() - m_mapTick["皮带转动计时"] < (DWORD)PARAM_INT("治具入口传输到位延时")) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(m_pFixture->UploadBeltOff());

	VAR_FIXTURE("AGV送料完成标志") = true;

	m_mapTick["AGV送料完成计时"] = GetTickCount();

	RETURN_STATE(&CLogicFixture::OnFixtureAgv04, true);
}

CStatus CLogicFixture::OnFixtureAgv04()
{
	if (GetTickCount() - m_mapTick["AGV送料完成计时"] < 3000) {
		RETURN_SELF("", false);
	}

	VAR_FIXTURE("AGV送料标志") = false;
	VAR_FIXTURE("AGV允许上料标志") = false;
	VAR_FIXTURE("AGV送料完成标志") = false;

	m_mapFlag["AGV空闲"] = true;

	RETURN_STATE(&CLogicFixture::OnFixtureAgv00, true);
}