#!/usr/bin/env python3
"""
测试事件时间戳格式
"""

import socket
import json
import time

def send_test_event():
    """发送测试事件到MQTT网关"""
    
    # 构建测试事件消息
    event_message = {
        "services": [
            {
                "service_id": "EventService",
                "event_type": "EVENT_SN_IN",
                "properties": {
                    "sn": "TEST_SN_TIMESTAMP_001",
                    "production_model": "TEST_MODEL",
                    "profiles": []
                },
                "event_time": "2025-07-31 12:35:00.123"  # FStationMFC格式的时间戳
            }
        ]
    }
    
    try:
        # 连接到MQTT网关的Socket服务器
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect(('127.0.0.1', 8888))
        
        # 发送事件消息
        message_json = json.dumps(event_message, ensure_ascii=False)
        sock.send(message_json.encode('utf-8'))
        
        print(f"✅ 发送测试事件成功")
        print(f"   事件类型: EVENT_SN_IN")
        print(f"   SN: TEST_SN_TIMESTAMP_001")
        print(f"   原始时间戳: 2025-07-31 12:35:00.123")
        
        sock.close()
        
    except Exception as e:
        print(f"❌ 发送事件失败: {e}")

if __name__ == "__main__":
    print("=" * 50)
    print("测试事件时间戳格式")
    print("=" * 50)
    send_test_event()
