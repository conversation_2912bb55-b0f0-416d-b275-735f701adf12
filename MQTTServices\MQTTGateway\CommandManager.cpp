#include "CommandManager.h"
#include <QJsonDocument>
#include <QJsonArray>
#include <QFile>
#include <QDebug>
#include <QLoggingCategory>

Q_LOGGING_CATEGORY(commandManager, "CommandManager")

CommandManager::CommandManager(QObject* parent)
    : QObject(parent)
    , m_timeoutTimer(new QTimer(this))
{
    // 设置超时检查定时器，每5秒检查一次
    m_timeoutTimer->setInterval(5000);
    connect(m_timeoutTimer, &QTimer::timeout, this, &CommandManager::OnTimeoutCheck);
    m_timeoutTimer->start();
    
    qCInfo(commandManager) << "命令管理器初始化完成";
}

CommandManager::~CommandManager()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    qCInfo(commandManager) << "命令管理器销毁 - 活跃命令数:" << m_activeCommands.size();
}

bool CommandManager::LoadCommandConfigs(const std::string& configPath)
{
    QFile file(QString::fromStdString(configPath));
    if (!file.open(QIODevice::ReadOnly)) {
        qCCritical(commandManager) << "无法打开命令配置文件:" << QString::fromStdString(configPath);
        return false;
    }
    
    QByteArray data = file.readAll();
    file.close();
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    if (error.error != QJsonParseError::NoError) {
        qCCritical(commandManager) << "命令配置文件JSON解析失败:" << error.errorString();
        return false;
    }
    
    QJsonObject root = doc.object();
    QJsonArray commands = root["commands"].toArray();
    
    std::lock_guard<std::mutex> lock(m_mutex);
    m_commandConfigs.clear();
    
    for (const auto& command : commands) {
        QJsonObject commandObj = command.toObject();
        std::string commandType = commandObj["type"].toString().toStdString();
        m_commandConfigs[commandType] = commandObj;
        qCDebug(commandManager) << "加载命令类型:" << QString::fromStdString(commandType);
    }

    qCInfo(commandManager) << "加载命令配置完成 - 命令类型数:" << m_commandConfigs.size();
    return true;
}

bool CommandManager::ValidateCommand(const QJsonObject& commandData, std::string& errorMessage)
{
    // 检查必需字段
    if (!commandData.contains("service_id")) {
        errorMessage = "缺少必需字段: service_id";
        return false;
    }
    
    if (!commandData.contains("command_name")) {
        errorMessage = "缺少必需字段: command_name";
        return false;
    }
    
    if (!commandData.contains("properties")) {
        errorMessage = "缺少必需字段: properties";
        return false;
    }
    
    // 检查service_id是否正确
    std::string serviceId = commandData["service_id"].toString().toStdString();
    if (serviceId != "CommandService") {
        errorMessage = "service_id必须为'CommandService'，当前值: " + serviceId;
        return false;
    }
    
    // 检查命令类型是否支持
    std::string commandName = commandData["command_name"].toString().toStdString();

    std::lock_guard<std::mutex> lock(m_mutex);

    // 调试信息：打印所有已加载的命令类型
    qCDebug(commandManager) << "验证命令:" << QString::fromStdString(commandName);
    qCDebug(commandManager) << "已加载的命令类型数量:" << m_commandConfigs.size();
    for (const auto& pair : m_commandConfigs) {
        qCDebug(commandManager) << "  - " << QString::fromStdString(pair.first);
    }

    auto it = m_commandConfigs.find(commandName);
    if (it == m_commandConfigs.end()) {
        errorMessage = "不支持的命令类型: " + commandName;
        return false;
    }
    
    // 验证命令参数
    QJsonObject properties = commandData["properties"].toObject();
    return ValidateCommandProperties(commandName, properties, errorMessage);
}

bool CommandManager::ValidateCommandProperties(const std::string& commandType, 
                                              const QJsonObject& properties, 
                                              std::string& errorMessage)
{
    auto it = m_commandConfigs.find(commandType);
    if (it == m_commandConfigs.end()) {
        errorMessage = "未知命令类型: " + commandType;
        return false;
    }
    
    QJsonObject config = it->second;
    QJsonArray requiredProps = config["properties"].toArray();
    
    // 检查必需参数
    for (const auto& prop : requiredProps) {
        std::string propName = prop.toString().toStdString();
        if (!properties.contains(QString::fromStdString(propName))) {
            errorMessage = "命令 " + commandType + " 缺少必需参数: " + propName;
            return false;
        }
    }
    
    return true;
}

bool CommandManager::RegisterCommand(const std::string& requestId, const QJsonObject& commandData)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // 检查是否已存在
    if (m_activeCommands.find(requestId) != m_activeCommands.end()) {
        qCWarning(commandManager) << "命令已存在:" << QString::fromStdString(requestId);
        return false;
    }
    
    // 创建命令信息
    CommandInfo info;
    info.requestId = requestId;
    info.commandName = commandData["command_name"].toString().toStdString();
    info.commandType = info.commandName;  // 在这个实现中，commandName就是commandType
    info.commandData = commandData;
    info.status = CommandStatus::PENDING;
    info.startTime = std::chrono::system_clock::now();
    
    // 获取超时时间
    auto configIt = m_commandConfigs.find(info.commandType);
    if (configIt != m_commandConfigs.end()) {
        info.timeoutMs = configIt->second["timeout_ms"].toInt();
    } else {
        info.timeoutMs = 30000;  // 默认30秒
    }
    
    m_activeCommands[requestId] = info;
    
    qCInfo(commandManager) << "注册命令:" << QString::fromStdString(info.commandName)
                          << "RequestId:" << QString::fromStdString(requestId)
                          << "超时时间:" << info.timeoutMs << "ms";
    
    return true;
}

void CommandManager::MarkCommandExecuting(const std::string& requestId)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_activeCommands.find(requestId);
    if (it != m_activeCommands.end()) {
        it->second.status = CommandStatus::EXECUTING;
        qCDebug(commandManager) << "标记命令执行中:" << QString::fromStdString(requestId);
    }
}

void CommandManager::MarkCommandCompleted(const std::string& requestId, bool success, 
                                         int resultCode, const std::string& resultMessage)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_activeCommands.find(requestId);
    if (it != m_activeCommands.end()) {
        it->second.status = success ? CommandStatus::SUCCESS : CommandStatus::FAILED;
        it->second.endTime = std::chrono::system_clock::now();
        it->second.resultCode = resultCode;
        it->second.resultMessage = resultMessage;
        
        qCInfo(commandManager) << "标记命令完成:" << QString::fromStdString(requestId)
                              << "成功:" << success
                              << "结果码:" << resultCode
                              << "消息:" << QString::fromStdString(resultMessage);
        
        // 发送完成信号
        emit CommandCompleted(QString::fromStdString(requestId), success, resultCode, 
                            QString::fromStdString(resultMessage));
    }
}

bool CommandManager::GetCommandInfo(const std::string& requestId, CommandInfo& commandInfo) const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_activeCommands.find(requestId);
    if (it != m_activeCommands.end()) {
        commandInfo = it->second;
        return true;
    }
    
    return false;
}

bool CommandManager::HasCommand(const std::string& requestId) const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_activeCommands.find(requestId) != m_activeCommands.end();
}

void CommandManager::RemoveCommand(const std::string& requestId)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_activeCommands.find(requestId);
    if (it != m_activeCommands.end()) {
        qCDebug(commandManager) << "移除命令:" << QString::fromStdString(requestId);
        m_activeCommands.erase(it);
    }
}

CommandManager::Statistics CommandManager::GetStatistics() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    Statistics stats;
    stats.activeCommands = m_activeCommands.size();
    stats.completedCommands = 0;
    stats.timeoutCommands = 0;
    stats.failedCommands = 0;
    
    for (const auto& pair : m_activeCommands) {
        const CommandInfo& info = pair.second;
        switch (info.status) {
            case CommandStatus::SUCCESS:
                stats.completedCommands++;
                break;
            case CommandStatus::TIMEOUT:
                stats.timeoutCommands++;
                break;
            case CommandStatus::FAILED:
                stats.failedCommands++;
                break;
            default:
                break;
        }
    }
    
    return stats;
}

void CommandManager::OnTimeoutCheck()
{
    CheckCommandTimeouts();
}

void CommandManager::CheckCommandTimeouts()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto now = std::chrono::system_clock::now();
    std::vector<std::string> timeoutCommands;
    
    for (auto& pair : m_activeCommands) {
        CommandInfo& info = pair.second;
        
        // 只检查正在执行或等待执行的命令
        if (info.status == CommandStatus::PENDING || info.status == CommandStatus::EXECUTING) {
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - info.startTime);
            
            if (elapsed.count() > info.timeoutMs) {
                info.status = CommandStatus::TIMEOUT;
                info.endTime = now;
                info.resultCode = -2;
                info.resultMessage = "命令执行超时";
                
                timeoutCommands.push_back(info.requestId);
                
                qCWarning(commandManager) << "命令执行超时:" << QString::fromStdString(info.commandName)
                                         << "RequestId:" << QString::fromStdString(info.requestId)
                                         << "超时时间:" << info.timeoutMs << "ms"
                                         << "实际耗时:" << elapsed.count() << "ms";
            }
        }
    }
    
    // 发送超时信号
    for (const std::string& requestId : timeoutCommands) {
        auto it = m_activeCommands.find(requestId);
        if (it != m_activeCommands.end()) {
            emit CommandTimeout(QString::fromStdString(requestId), 
                              QString::fromStdString(it->second.commandName));
        }
    }
}
