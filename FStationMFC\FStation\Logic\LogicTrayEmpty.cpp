﻿#include "stdafx.h"
#include "LogicTrayEmpty.h"

#include "Sys.h"
#include "LogicMgr.h"

#define PARAM(NAME)			(*m_pTray->m_mapParam[NAME])

#define PARAM_BOOL(NAME)	(*m_pTray->m_mapParam[NAME]).B()
#define PARAM_INT(NAME)		(*m_pTray->m_mapParam[NAME]).I()
#define PARAM_DOUBLE(NAME)	(*m_pTray->m_mapParam[NAME]).D()
#define PARAM_STRING(NAME)	(*m_pTray->m_mapParam[NAME]).S()

CLogicTrayEmpty::CLogicTrayEmpty()
{
	m_pTray = g_pTray;

}

CLogicTrayEmpty::~CLogicTrayEmpty()
{
}

EnumStatus CLogicTrayEmpty::OnSafeCheck()
{
	if (!CSys::m_bInit) {
		return emStop;
	}

	return emRun;
}

EnumStatus CLogicTrayEmpty::OnStart()
{
	return emRun;
}

EnumStatus CLogicTrayEmpty::OnPause()
{
	m_pTray->EmptyTrayBeltOff();
			
	return emRun;
}

EnumStatus CLogicTrayEmpty::OnResume()
{
	map<CString, DWORD>::iterator it = m_mapTick.begin();
	for (; it!=m_mapTick.end(); it++)
	{
		it->second = GetTickCount();
	}

	return emRun;
}

EnumStatus CLogicTrayEmpty::OnStop()
{
	m_pTray->EmptyTrayBeltOff();

	m_pTray->EmptyTrayTransportYStop();

	m_pTray->EmptyTraySeparateZStop();

	return emRun;
}

CStatus CLogicTrayEmpty::OnRun()
{
	PARAM("空TRAY盘进料标志") = false;

	PARAM("空TRAY盘退料标志") = false;

	VAR_TRAY("空TRAY盘提前进料标志") = false;

	RETURN_STATE(&CLogicTrayEmpty::OnSelfCheck00, true);
}

CStatus CLogicTrayEmpty::OnSelfCheck00()
{
// 	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySupportCylinderOff());
// 
// 	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySupportCylinderStatus());
// 
// 	if (m_sRet != "Off") {
// 		RETURN_SELF("等待空TRAY盘支撑气缸缩回", true);
// 	}

	RETURN_STATE(&CLogicTrayEmpty::OnSelfCheck01, true);
}

CStatus CLogicTrayEmpty::OnSelfCheck01()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySeparateZMove(PARAM_DOUBLE("空TRAY盘分盘Z轴支撑位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTraySeparateZInPos(PARAM_DOUBLE("空TRAY盘分盘Z轴支撑位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘分盘Z轴运动到支撑位", true);
	}

	RETURN_STATE(&CLogicTrayEmpty::OnSelfCheck02, true);
}

CStatus CLogicTrayEmpty::OnSelfCheck02()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySupportCylinderOn());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySupportCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待空TRAY盘支撑气缸伸出", true);
	}

	RETURN_STATE(&CLogicTrayEmpty::OnSelfCheck03, true);
}

CStatus CLogicTrayEmpty::OnSelfCheck03()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySeparateZMove(PARAM_DOUBLE("空TRAY盘分盘Z轴放料位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTraySeparateZInPos(PARAM_DOUBLE("空TRAY盘分盘Z轴放料位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘分盘Z轴运动到放料位", true);
	}

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTray00, true);
}

CStatus CLogicTrayEmpty::OnEmptyTray00()
{
	if (g_pRobot->m_mapParam["TRAY盘进料标志"]->B()) {
		*g_pRobot->m_mapParam["TRAY盘进料标志"] = false;
		PARAM("空TRAY盘退料标志") = true;
	}

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTray01, true);
}

CStatus CLogicTrayEmpty::OnEmptyTray01()
{
	if (VAR_TRAY_B("空TRAY盘提前进料标志")) {
		VAR_TRAY("空TRAY盘提前进料标志") = false;
		RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayFeed00, true);
	}

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTray02, true);
}

CStatus CLogicTrayEmpty::OnEmptyTray02()
{
	if (PARAM_BOOL("空TRAY盘退料标志")) {
		PARAM("空TRAY盘退料标志") = false;
		RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayBack00, true);
	}

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTray00, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayFeed00()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySeparateZMove(PARAM_DOUBLE("空TRAY盘分盘Z轴支撑位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTraySeparateZInPos(PARAM_DOUBLE("空TRAY盘分盘Z轴支撑位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘分盘Z轴运动到支撑位", true);
	}

	m_mapTick["真空检测计时"] = GetTickCount();

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayFeed01, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayFeed01()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySupportCylinderOff());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayVacuumOn());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayVacuumStatus());

	if (m_sRet != "On") {
		if (GetTickCount() - m_mapTick["真空检测计时"] > PARAM_INT("空TRAY盘真空检测超时")) {
			RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBrokenVacuumOn());
			Sleep(100);
			RUN_STOP_IF_ERROR(m_pTray->EmptyTrayVacuumOff());
			REPORT("真空检测异常，请上空TRAY盘!", emLogLevelWarn);
			MESSAGEBOX("真空检测异常，请上空TRAY盘!", "", true);
			m_mapTick["真空检测计时"] = GetTickCount();
		}

		RETURN_SELF("等待空TRAY盘真空信号", true);
	}

	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySupportCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待空TRAY盘支撑气缸缩回", true);
	}

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayFeed02, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayFeed02()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySeparateZMove(PARAM_DOUBLE("空TRAY盘分盘Z轴分盘位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTraySeparateZInPos(PARAM_DOUBLE("空TRAY盘分盘Z轴分盘位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘分盘Z轴运动到分盘位", true);
	}

	m_mapTick["真空检测计时"] = GetTickCount();

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayFeed03, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayFeed03()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySupportCylinderOn());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySupportCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待空TRAY盘支撑气缸伸出", true);
	}

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayFeed04, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayFeed04()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySeparateZMove(PARAM_DOUBLE("空TRAY盘分盘Z轴破真空位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTraySeparateZInPos(PARAM_DOUBLE("空TRAY盘分盘Z轴破真空位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘分盘Z轴运动到破真空位", true);
	}

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBrokenVacuumOn());

	Sleep(100);

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayVacuumOff());

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayFeed05, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayFeed05()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySeparateZMove(PARAM_DOUBLE("空TRAY盘分盘Z轴放料位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTraySeparateZInPos(PARAM_DOUBLE("空TRAY盘分盘Z轴放料位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘分盘Z轴运动到放料位", true);
	}

	if (!PARAM_BOOL("空TRAY盘进料标志")) {
		RETURN_SELF("等待空TRAY盘进料标志置位", false)
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTraySupportCylinderOn());

	PARAM("空TRAY盘进料标志") = false;

	m_mapFlag["皮带进料计时开始标志"] = false;
	m_mapFlag["皮带进料检测开始标志"] = false;

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayFeed06, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayFeed06()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBeltOn(PARAM_BOOL("空TRAY盘皮带正转方向")));

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayTransportYMove(PARAM_DOUBLE("空TRAY盘搬运Y轴进料起始位")));

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBeltInMaterialStatus());

	if (m_sRet == "On") {
		m_mapFlag["皮带进料检测开始标志"] = true;
		m_mapFlag["皮带进料计时开始标志"] = false;
	}

	if (m_mapFlag["皮带进料检测开始标志"]) {
		if (m_sRet != "On") {
			if (!m_mapFlag["皮带进料计时开始标志"]) {
				m_mapFlag["皮带进料计时开始标志"] = true;
				m_mapTick["空TRAY盘进料计时"] = GetTickCount();
			}
		}
	}
	else {
		RETURN_SELF("等待空TRAY盘进料到位", true);
	}

	if (m_mapFlag["皮带进料计时开始标志"]) {
		if (GetTickCount() - m_mapTick["空TRAY盘进料计时"] < (DWORD)PARAM_INT("空TRAY盘进料到位延时")) {
			RETURN_SELF("等待空TRAY盘进料到位", true);
		}
		else {
			RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBeltOff());
		}
	}
	else {
		RETURN_SELF("等待空TRAY盘进料到位", true);
	}
	
	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTrayTransportYInPos(PARAM_DOUBLE("空TRAY盘搬运Y轴进料起始位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘搬运Y轴运动到进料起始位", true);
	}

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayFeed07, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayFeed07()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayPullMaterialCylinderOn());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayPullMaterialCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待空TRAY盘拨料气缸伸出", true);
	}

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayFeed08, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayFeed08()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayTransportYMove(PARAM_DOUBLE("空TRAY盘搬运Y轴进料结束位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTrayTransportYInPos(PARAM_DOUBLE("空TRAY盘搬运Y轴进料结束位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘搬运Y轴运动到进料结束位", true);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayMaterialExistStatus());

	if (m_sRet != "On") {
		REPORT("空TRAY盘送料异常，请手动摆好TRAY盘？", emLogLevelWarn);
		MESSAGEBOX("空TRAY盘送料异常，请手动摆好TRAY盘？", "", false);
		RETURN_SELF("", false);
	}

	VAR_TRAY("空TRAY盘进料完成标志") = true;
	VAR_TRAY("满TRAY盘进料完成标志") = true;

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayFeed09, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayFeed09()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayTransportYMove(PARAM_DOUBLE("空TRAY盘搬运Y轴进料起始位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTrayTransportYInPos(PARAM_DOUBLE("空TRAY盘搬运Y轴进料起始位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘搬运Y轴运动到进料起始位", true);
	}

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayFeed10, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayFeed10()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayPullMaterialCylinderOff());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayPullMaterialCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待空TRAY盘拨料气缸缩回", true);
	}

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayTransportYHome());

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTray00, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayBack00()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayTransportYMove(PARAM_DOUBLE("空TRAY盘搬运Y轴退料起始位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTrayTransportYInPos(PARAM_DOUBLE("空TRAY盘搬运Y轴退料起始位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘搬运Y轴运动到退料起始位", true);
	}

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayBack01, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayBack01()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayPullMaterialCylinderOn());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayPullMaterialCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待空TRAY盘拨料气缸伸出", true);
	}
	
	m_mapTick["退料计时"] = GetTickCount();

	m_mapFlag["拨料成功标志"] = false;

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayBack02, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayBack02()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayTransportYMove(PARAM_DOUBLE("空TRAY盘搬运Y轴退料结束位")));

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBeltInMaterialStatus());

	if (m_sRet == "On") {
		m_mapFlag["拨料成功标志"] = true;
	}

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTrayTransportYInPos(PARAM_DOUBLE("空TRAY盘搬运Y轴退料结束位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘搬运Y轴运动到退料结束位", true);
	}
	
	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayBack03, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayBack03()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayPullMaterialCylinderOff());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayPullMaterialCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待空TRAY盘拨料气缸缩回", true);
	}

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayTransportYHome());

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayBack04, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayBack04()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayEndStopCylinderOn());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayEndStopCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待空TRAY盘尾端阻挡气缸伸出", true);
	}

	m_mapFlag["满TRAY盘进料设置完成"] = false;

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayBack05, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayBack05()
{
	CString sRet1, sRet2, sRet3, sRet4;

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBeltOn(!PARAM_BOOL("空TRAY盘皮带正转方向")));

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBeltInMaterialStatus());
	sRet1 = m_sRet;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayMaterialExistStatus());
	sRet2 = m_sRet;

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBeltInPosStatus());
	sRet3 = m_sRet;

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayMaterialExistStatus());
	sRet4 = m_sRet;
	
	if (sRet1 == "Off" && sRet2 == "Off" && sRet4 == "On" && m_mapFlag["拨料成功标志"]) {
		if (!m_mapFlag["满TRAY盘进料设置完成"]) {
			PARAM("满TRAY盘进料完成标志") = false;
			VAR_TRAY("空TRAY盘进料完成标志") = false;
			PARAM("满TRAY盘进料标志") = true;
			m_mapFlag["拨料成功标志"] = false;
			m_mapFlag["满TRAY盘进料设置完成"] = true;
		}
	}

	if (GetTickCount() - m_mapTick["退料计时"] > VAR_TRAY_I("空TRAY盘退料超时")) {
		RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBeltOff());
		REPORT("空TRAY盘退料超时，请确认空TRAY盘是否卡住并处理异常！", emLogLevelError);
		MESSAGEBOX("空TRAY盘退料超时，请确认空TRAY盘是否卡住并处理异常！", "", false);
 		if (!m_mapFlag["满TRAY盘进料设置完成"]) {
			PARAM("满TRAY盘进料完成标志") = false;
			VAR_TRAY("空TRAY盘进料完成标志") = false;
 			PARAM("满TRAY盘进料标志") = true;
 			m_mapFlag["拨料成功标志"] = false;
 			m_mapFlag["满TRAY盘进料设置完成"] = true;
 		}

		m_mapTick["退料计时"] = GetTickCount();

		m_mapTick["空TRAY盘退料到位计时"] = GetTickCount();

		RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayBack06, true);
	}

	if (sRet3 != "On") {
		RETURN_SELF("等待空TRAY盘到位信号", true);
	}

	m_mapTick["空TRAY盘退料到位计时"] = GetTickCount();

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayBack06, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayBack06()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBeltOn(!PARAM_BOOL("空TRAY盘皮带正转方向")));

	if (GetTickCount() - m_mapTick["空TRAY盘退料到位计时"] < (DWORD)PARAM_INT("皮带传输到位延时")) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayBeltOff());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayEndStopCylinderOff());

	if (!m_mapFlag["满TRAY盘进料设置完成"]) {
		PARAM("满TRAY盘进料完成标志") = false;
		VAR_TRAY("空TRAY盘进料完成标志") = false;
		PARAM("满TRAY盘进料标志") = true;
		m_mapFlag["拨料成功标志"] = false;
		m_mapFlag["满TRAY盘进料设置完成"] = true;
	}

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayBack07, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayBack07()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySeparateZMove(PARAM_DOUBLE("空TRAY盘分盘Z轴分盘位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTraySeparateZInPos(PARAM_DOUBLE("空TRAY盘分盘Z轴分盘位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘分盘Z轴运动到分盘位", true);
	}

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayBack08, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayBack08()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySupportCylinderOff());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySupportCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待空TRAY盘支撑气缸缩回", true);
	}

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayBack09, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayBack09()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySeparateZMove(PARAM_DOUBLE("空TRAY盘分盘Z轴支撑位")));

	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTraySeparateZInPos(PARAM_DOUBLE("空TRAY盘分盘Z轴支撑位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘分盘Z轴运动到支撑位", true);
	}

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayBack10, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayBack10()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySupportCylinderOn());

	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySupportCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待空TRAY盘支撑气缸伸出", true);
	}

	RUN_STOP_IF_ERROR(m_pTray->EmptyTraySeparateZHome());

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayBack11, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayBack11()
{
	RUN_STOP_IF_ERROR(m_pTray->IsEmptyTraySeparateZHomeOK());
	
	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘分盘Z轴回零完成", true);
	}

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayBack12, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayBack12()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayOutOfRangeWarnStatus());

	if (m_sRet == "On") {
		REPORT("空TRAY盘数量报警，请立即清空TRAY盘", emLogLevelWarn);
		MESSAGEBOX("空TRAY盘数量报警，请立即清空TRAY盘", "空TRAY盘检测", true);
		RETURN_SELF("", false);
	}

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTrayBack13, true);
}

CStatus CLogicTrayEmpty::OnEmptyTrayBack13()
{
	RUN_STOP_IF_ERROR(m_pTray->EmptyTrayOutOfRangePreWarnStatus());

	if (m_sRet == "On") {
		REPORT("空TRAY盘数量预警，请及时清空TRAY盘", emLogLevelWarn);
	}

	RETURN_STATE(&CLogicTrayEmpty::OnEmptyTray00, true);
}
