﻿#include "stdafx.h"
#include "LogicTrayFull.h"

#include "Sys.h"
#include "LogicMgr.h"

#define PARAM(NAME)			(*m_pTray->m_mapParam[NAME])

#define PARAM_BOOL(NAME)	(*m_pTray->m_mapParam[NAME]).B()
#define PARAM_INT(NAME)		(*m_pTray->m_mapParam[NAME]).I()
#define PARAM_DOUBLE(NAME)	(*m_pTray->m_mapParam[NAME]).D()
#define PARAM_STRING(NAME)	(*m_pTray->m_mapParam[NAME]).S()

CLogicTrayFull::CLogicTrayFull()
{
	m_pTray = g_pTray;
}

CLogicTrayFull::~CLogicTrayFull()
{
}

EnumStatus CLogicTrayFull::OnSafeCheck()
{
	if (!CSys::m_bInit) {
		return emStop;
	}

	return emRun;
}

EnumStatus CLogicTrayFull::OnStart()
{
	return emRun;
}

EnumStatus CLogicTrayFull::OnPause()
{
	m_pTray->FullTrayBeltOff();

	m_pTray->FullTrayUpDnZStop();
	
	return emRun;
}

EnumStatus CLogicTrayFull::OnResume()
{
	map<CString, DWORD>::iterator it = m_mapTick.begin();
	for (; it!=m_mapTick.end(); it++)
	{
		it->second = GetTickCount();
	}

	return emRun;
}

EnumStatus CLogicTrayFull::OnStop()
{
	m_pTray->FullTrayBeltOff();

	m_pTray->FullTrayUpDnZStop();
	
	return emRun;
}

CStatus CLogicTrayFull::OnRun()
{
	PARAM("满TRAY盘进料标志") = false;

	PARAM("满TRAY盘退料标志") = false;

	RETURN_STATE(&CLogicTrayFull::OnSelfCheck00, true);
}

CStatus CLogicTrayFull::OnSelfCheck00()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTraySupportCylinderOn());

	RUN_STOP_IF_ERROR(m_pTray->FullTraySupportCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待满TRAY盘支撑气缸伸出", true);
	}

	RETURN_STATE(&CLogicTrayFull::OnSelfCheck01, true);
}

CStatus CLogicTrayFull::OnSelfCheck01()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayMaterialExistStatus());

	if (m_sRet != "On") {
// 		REPORT("请手动放一个TRAY盘于满TRAY盘支撑气缸上，并确认满TRAY盘有料检测信号感应到", emLogLevelWarn);
// 		MESSAGEBOX("请手动放一个TRAY盘于满TRAY盘支撑气缸上，并确认满TRAY盘有料检测信号感应到", "", true);
//		REPORT("满TRAY盘有料检测信号未感应到，请取走TRAY盘", emLogLevelWarn);

		if (AfxMessageBox("请确认是否有TRAY盘，如果有，请确认【满TRAY盘有料检测信号】感应到！", MB_YESNO) == IDNO) {
			VAR_TRAY("满TRAY盘进料完成标志") = false;
			VAR_TRAY("空TRAY盘进料完成标志") = false;

			VAR_TRAY("空TRAY盘提前进料标志") = true;

			PARAM("空TRAY盘进料标志") = true; // 上空TRAY盘

			RETURN_STATE(&CLogicTrayFull::OnFullTray00, true);
		}
		else {
			RETURN_SELF("自检检测TRAY盘", false);
		}
	}
	
	VAR_TRAY("满TRAY盘进料完成标志") = true;
	VAR_TRAY("空TRAY盘进料完成标志") = true;

	RETURN_STATE(&CLogicTrayFull::OnFullTray00, true);
}

CStatus CLogicTrayFull::OnFullTray00()
{
	if (g_pRobot->m_mapParam["TRAY盘退料标志"]->B()) {
		*g_pRobot->m_mapParam["TRAY盘退料标志"] = false;
		PARAM("满TRAY盘退料标志") = true;
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTray01, true);
}

CStatus CLogicTrayFull::OnFullTray01()
{
	if (PARAM_BOOL("满TRAY盘进料标志")) {
		m_mapFlag["提前上TRAY盘标志"] = false;
		RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed00, true);
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTray02, true);
}

CStatus CLogicTrayFull::OnFullTray02()
{
	if (PARAM_BOOL("满TRAY盘退料标志")) {
		VAR_TRAY("空TRAY盘提前进料标志") = true;
		PARAM("满TRAY盘退料标志") = false;
		RETURN_STATE(&CLogicTrayFull::OnFullTrayBack00, true);
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTray00, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed00()
{
	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(VAR_TRAY_D("满TRAY盘升降Z轴放料位")));

	if (m_sRet != "Yes") {
		RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());
		if (m_sRet != "On") {
			RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed01_0, true);
		}

		if (m_mapFlag["提前上TRAY盘标志"]) {
			m_mapFlag["提前上TRAY盘标志"] = false;
			RETURN_STATE(&CLogicTrayFull::OnFullTray00, true);
		}

		RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed02, true);
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed01, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed01()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltInPosStatus());

	if (m_sRet != "On") { // 无TRAY盘
		RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed09, true);
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed01_0, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed01_0()
{
	CString sRet1, sRet2;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(PARAM_DOUBLE("满TRAY盘升降Z轴最大待料位"), 30));

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(PARAM_DOUBLE("满TRAY盘升降Z轴最大待料位")));

	sRet1 = m_sRet;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());

	sRet2 = m_sRet;

	if (sRet1 != "Yes" && sRet2 != "On") {
		RETURN_SELF("等待满TRAY盘升降Z轴送料到位", true);
	}

	if (sRet1 == "Yes" && sRet2 != "On") { //无料
		RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed08, true);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZStop());

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴当前位置"]));
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴原点位置"]));

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed01_1, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed01_1()
{
	double nPos = 0;

	nPos = m_mapPos["满TRAY盘升降Z轴当前位置"] - 20;

	if (nPos < PARAM_DOUBLE("满TRAY盘升降Z轴放料位")) {
		nPos = PARAM_DOUBLE("满TRAY盘升降Z轴放料位");
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(nPos, 50));

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(nPos));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴下降到位", true);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());

	if (m_sRet != "Off") {
		RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴当前位置"]));
		RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴原点位置"]));
		REPORT("等待满TRAY盘升降Z轴找原点失败", emLogLevelError);
		MESSAGEBOX("等待满TRAY盘升降Z轴找原点失败", "", false);
		RETURN_STOP();
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed01_2, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed01_2()
{
	CString sRet1, sRet2;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(PARAM_DOUBLE("满TRAY盘升降Z轴最大待料位"), 30));

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(PARAM_DOUBLE("满TRAY盘升降Z轴最大待料位")));

	sRet1 = m_sRet;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());

	sRet2 = m_sRet;

	if (sRet1 != "Yes" && sRet2 != "On") {
		RETURN_SELF("等待满TRAY盘升降Z轴送料到位", true);
	}

	if (sRet1 == "Yes" && sRet2 != "On") {
		REPORT("两次找原点位置值相差过大!", emLogLevelError);
		MESSAGEBOX("两次找原点位置值相差过大!", "", false);
		RETURN_STOP();
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZStop());

	double nPosCurrent = 0;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(nPosCurrent));

	if (fabs(nPosCurrent - m_mapPos["满TRAY盘升降Z轴原点位置"]) > 5) {
		REPORT("两次找原点位置值相差过大!", emLogLevelError);
		MESSAGEBOX("两次找原点位置值相差过大!", "", false);
		RETURN_STOP();
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed00, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed02()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayMaterialExistStatus());

	if (m_sRet == "On") {
		REPORT("满Tray盘有无检测传感器感应到有料，请取走！", emLogLevelWarn);
		MESSAGEBOX("满Tray盘有无检测传感器感应到有料，请取走！", "", false);
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTraySupportCylinderOff());

	RUN_STOP_IF_ERROR(m_pTray->FullTraySupportCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待满TRAY盘支撑气缸缩回", true);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴当前位置"]));

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴原点位置"]));

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed03_0, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed03()
{
// 	CString sRet1, sRet2;
// 
// 	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(PARAM_DOUBLE("满TRAY盘升降Z轴最大待料位"), 30));
// 
// 	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(PARAM_DOUBLE("满TRAY盘升降Z轴最大待料位")));
// 	
// 	sRet1 = m_sRet;
// 
// 	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());
// 
// 	sRet2 = m_sRet;
// 
// 	if (sRet1 != "Yes" && sRet2 != "On") {
// 		RETURN_SELF("等待满TRAY盘升降Z轴送料到位", true);
// 	}
// 
// 	if (sRet1 == "Yes" && sRet2 != "On") { //无料
// 		RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed08, true);
// 	}
// 
// 	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZStop());
// 
// 	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴当前位置"]));
// 
// 	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴原点位置"]));

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed03_0, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed03_0()
{
	double nCurrentPos = 0;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(m_mapPos["满TRAY盘升降Z轴当前位置"] + PARAM_DOUBLE("满TRAY盘升降Z轴送料偏移量"), 50));

	RUN_STOP_IF_ERROR(m_pTray->FullTrayMaterialExistStatus());

	if (m_sRet == "On") {
		RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZStop());
		RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(nCurrentPos));
		if (m_mapPos["满TRAY盘升降Z轴当前位置"] + PARAM_DOUBLE("满TRAY盘升降Z轴送料偏移量") - nCurrentPos > VAR_TRAY_D("单TRAY盘高度")) {
			REPORT("满TRAY盘原点存在异常，请复位TRAY盘模块重试！", emLogLevelError);
			RETURN_STOP();
		}
		else {
			RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed03_1, true);
		}
	}

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(m_mapPos["满TRAY盘升降Z轴当前位置"] + PARAM_DOUBLE("满TRAY盘升降Z轴送料偏移量")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到位", true);
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed03_1, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed03_1()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(m_mapPos["满TRAY盘升降Z轴当前位置"] + PARAM_DOUBLE("满TRAY盘升降Z轴送料偏移量"), 50));

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(m_mapPos["满TRAY盘升降Z轴当前位置"] + PARAM_DOUBLE("满TRAY盘升降Z轴送料偏移量")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到位", true);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayMaterialExistStatus());

	if (m_sRet != "On") {
		RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴当前位置"]));
		RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed03_2, true);
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed04, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed03_2()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(m_mapPos["满TRAY盘升降Z轴当前位置"] - 2));

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(m_mapPos["满TRAY盘升降Z轴当前位置"] - 2));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到位", true);
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed03_3, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed03_3()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayMaterialExistStatus());

	if (m_sRet != "On") {
		REPORT("满TRAY盘送料异常，请手动摆好TRAY盘？", emLogLevelWarn);
		MESSAGEBOX("满TRAY盘送料异常，请手动摆好TRAY盘？", "", false);
		RETURN_SELF("", false);
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed04, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed04()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTraySupportCylinderOn());

	RUN_STOP_IF_ERROR(m_pTray->FullTraySupportCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待满TRAY盘支撑气缸伸出", true);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴当前位置"]));

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed04_0, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed04_0()
{
	double nPos = 0;

	nPos = m_mapPos["满TRAY盘升降Z轴当前位置"] + VAR_TRAY_D("满TRAY盘升降Z轴分盘偏移量");

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(nPos));

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(nPos));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴到达分盘位", true);
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed04_1, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed04_1()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTraySeparateCylinderOn());

	RUN_STOP_IF_ERROR(m_pTray->FullTraySeparateCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待满TRAY盘分盘气缸伸出", true);
	}

	VAR_TRAY("满TRAY盘进料完成标志") = true;
	VAR_TRAY("空TRAY盘进料完成标志") = true;

	VAR_TRAY("满TRAY盘进料标志") = false;

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed05, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed05()
{
	double nPos = 0;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(m_mapPos["满TRAY盘升降Z轴原点位置"] - 40));
	
	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(m_mapPos["满TRAY盘升降Z轴原点位置"] - 40));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到原点位以下40毫米", true);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());

	if (m_sRet == "On") {
		REPORT("满TRAY盘升降Z轴下降到原点以下40毫米仍然检测到上升到位传感器感应，请确认TRAY盘是否卡住!", emLogLevelError);
		MESSAGEBOX("满TRAY盘升降Z轴下降原点以下40毫米仍然检测到上升到位传感器感应，请确认TRAY盘是否卡住!", "", false);
		RETURN_SELF("", false);
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed06, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed06()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTraySeparateCylinderOff());

	RUN_STOP_IF_ERROR(m_pTray->FullTraySeparateCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待满TRAY盘分盘气缸缩回", true);
	}

	m_mapFlag["提前上TRAY盘标志"] = true;

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed07, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed07()
{
	CString sRet1, sRet2;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(PARAM_DOUBLE("满TRAY盘升降Z轴最大待料位"), 30));

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(PARAM_DOUBLE("满TRAY盘升降Z轴最大待料位")));

	sRet1 = m_sRet;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());

	sRet2 = m_sRet;

	if (sRet1 != "Yes" && sRet2 != "On") {
		RETURN_SELF("等待满TRAY盘升降Z轴送料到位", true);
	}

	if (sRet1 == "Yes" && sRet2 != "On") { //无料
		RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed08, true);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZStop());

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴当前位置"]));

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴原点位置"]));

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed07_0, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed07_0()
{
	double nPos = 0;

	nPos = m_mapPos["满TRAY盘升降Z轴当前位置"] - 20;

	if (nPos < PARAM_DOUBLE("满TRAY盘升降Z轴放料位")) {
		nPos = PARAM_DOUBLE("满TRAY盘升降Z轴放料位");
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(nPos, 50));

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(nPos));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴下降到位", true);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());

	if (m_sRet != "Off") {
		RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴当前位置"]));
		RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴原点位置"]));
		REPORT("等待满TRAY盘升降Z轴找原点失败", emLogLevelError);
		MESSAGEBOX("等待满TRAY盘升降Z轴找原点失败", "", false);
		RETURN_STOP();
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed07_1, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed07_1()
{
	CString sRet1, sRet2;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(PARAM_DOUBLE("满TRAY盘升降Z轴最大待料位"), 30));

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(PARAM_DOUBLE("满TRAY盘升降Z轴最大待料位")));

	sRet1 = m_sRet;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());

	sRet2 = m_sRet;

	if (sRet1 != "Yes" && sRet2 != "On") {
		RETURN_SELF("等待满TRAY盘升降Z轴送料到位", true);
	}

	if (sRet1 == "Yes" && sRet2 != "On") {
		REPORT("两次找原点位置值相差过大!", emLogLevelError);
		MESSAGEBOX("两次找原点位置值相差过大!", "", false);
		RETURN_STOP();
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZStop());

	double nPosCurrent = 0;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(nPosCurrent));

	if (fabs(nPosCurrent - m_mapPos["满TRAY盘升降Z轴原点位置"]) > 5) {
		REPORT("两次找原点位置值相差过大!", emLogLevelError);
		MESSAGEBOX("两次找原点位置值相差过大!", "", false);
		RETURN_STOP();
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTray00, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed08()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(PARAM_DOUBLE("满TRAY盘升降Z轴放料位")));

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(PARAM_DOUBLE("满TRAY盘升降Z轴放料位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到位", true);
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed00, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed09()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltWaitMaterialStatus());

	if (m_sRet != "On") {
		RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed12, true);
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed10, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed10()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltOn(PARAM_BOOL("满TRAY盘皮带正转方向")));

	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltInPosStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待TRAY盘传送到位", true);
	}

	m_mapTick["TRAY盘传输到位计时"] = GetTickCount();

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed11, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed11()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltOn(PARAM_BOOL("满TRAY盘皮带正转方向")));

	if (GetTickCount() - m_mapTick["TRAY盘传输到位计时"] < (DWORD)PARAM_INT("皮带传输到位延时")) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltOff());

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed00, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed12()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltInMaterialStatus());

	if (m_sRet != "On") {
		if (m_mapTick["满TRAY盘无料"] == 0) {
			REPORT("满TRAY盘无料，请及时上料!", emLogLevelWarn);
			m_mapTick["满TRAY盘无料"] = GetTickCount();
		}
		else {
			if (GetTickCount() - m_mapTick["满TRAY盘无料"] > 10000) {
				m_mapTick["满TRAY盘无料"] = 0;
			}
		}
		
// 		CString sRet;
// 
// 		int nCnt = 0;
// 
// 		sRet = g_pRobot->InBeltAInStatus();
// 		if (sRet == "On") {
// 			nCnt++;
// 		}
// 
// 		sRet = g_pRobot->InBeltBInStatus();
// 		if (sRet == "On") {
// 			nCnt++;
// 		}
// 
// 		sRet = g_pRobot->InBeltCInStatus();
// 		if (sRet == "On") {
// 			nCnt++;
// 		}

		if (VAR_ROBOT_B("主板回放标志")) {
			VAR_ROBOT("主板回放标志") = false;
			PARAM("满TRAY盘进料标志") = false;
			RUN_STOP_IF_ERROR(m_pTray->FullTrayMaterialExistStatus());
			if (m_sRet != "On") {
				PARAM("空TRAY盘提前进料标志") = true;
				PARAM("空TRAY盘进料标志") = true;
			}
			RETURN_STATE(&CLogicTrayFull::OnFullTray00, true);
		}

		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayEndStopCylinderOff());

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed13, true);
}

CStatus CLogicTrayFull::OnFullTrayFeed13()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltOn(PARAM_BOOL("满TRAY盘皮带正转方向")));

	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltWaitMaterialStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待TRAY盘传送到待料位", true);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltOff());

	RETURN_STATE(&CLogicTrayFull::OnFullTrayFeed09, true);
}

CStatus CLogicTrayFull::OnFullTrayBack00()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());

	if (m_sRet != "On") {
		RETURN_STATE(&CLogicTrayFull::OnFullTrayBack10, true);
	}
	
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴当前位置"]));
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴原点位置"]));

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());

	if (fabs(m_mapPos["满TRAY盘升降Z轴当前位置"] - PARAM_DOUBLE("满TRAY盘升降Z轴最大待料位")) < 1 && m_sRet != "On") {
		m_mapPos["满TRAY盘升降Z轴接料位"] = m_mapPos["满TRAY盘升降Z轴当前位置"] + PARAM_DOUBLE("满TRAY盘升降Z轴送料偏移量");
		TRACE("接料距离： 满TRAY盘升降Z轴送料偏移量\n");
	}
	else {
		m_mapPos["满TRAY盘升降Z轴接料位"] = m_mapPos["满TRAY盘升降Z轴当前位置"] + PARAM_DOUBLE("满TRAY盘升降Z轴接料偏移量");
		TRACE("接料距离： 满TRAY盘升降Z轴接料偏移量\n");
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayBack01, true);
}

CStatus CLogicTrayFull::OnFullTrayBack01()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(m_mapPos["满TRAY盘升降Z轴接料位"]));

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(m_mapPos["满TRAY盘升降Z轴接料位"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到位", true);
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayBack02, true);
}

CStatus CLogicTrayFull::OnFullTrayBack02()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTraySupportCylinderOff());

	RUN_STOP_IF_ERROR(m_pTray->FullTraySupportCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待满TRAY盘支撑气缸缩回", true);
	}
	
	RETURN_STATE(&CLogicTrayFull::OnFullTrayBack03, true);
}

CStatus CLogicTrayFull::OnFullTrayBack03()
{
	double nPos = 0;

	nPos = m_mapPos["满TRAY盘升降Z轴原点位置"] - 50;

	if (nPos < PARAM_DOUBLE("满TRAY盘升降Z轴放料位")) {
		nPos = PARAM_DOUBLE("满TRAY盘升降Z轴放料位");
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(nPos));
	
	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(nPos));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到位", true);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());

	if (m_sRet == "On") {
		REPORT("满TRAY盘升降Z轴下降到原点以下50毫米仍然检测到上升到位传感器感应，请确认TRAY盘是否卡住!", emLogLevelError);
		MESSAGEBOX("满TRAY盘升降Z轴下降原点以下50毫米仍然检测到上升到位传感器感应，请确认TRAY盘是否卡住!", "", false);
		RETURN_SELF("", false);
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayBack04, true);
}

CStatus CLogicTrayFull::OnFullTrayBack04()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTraySupportCylinderOn());

	RUN_STOP_IF_ERROR(m_pTray->FullTraySupportCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待满TRAY盘支撑气缸伸出", true);
	}

	PARAM("空TRAY盘进料标志") = true;

	RETURN_STATE(&CLogicTrayFull::OnFullTrayBack04_0, true);
}

CStatus CLogicTrayFull::OnFullTrayBack04_0()
{
	double nPos = 0;
	nPos = m_mapPos["满TRAY盘升降Z轴原点位置"] - VAR_TRAY_D("满TRAY盘回放安全距离");
	
	if (nPos < PARAM_DOUBLE("满TRAY盘升降Z轴放料位")) {
		nPos = PARAM_DOUBLE("满TRAY盘升降Z轴放料位");
	}
	else {
		RETURN_STATE(&CLogicTrayFull::OnFullTrayBack04_1, true);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(nPos));

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(nPos));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到位", true);
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayBack04_1, true);
}

CStatus CLogicTrayFull::OnFullTrayBack04_1()
{
	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(PARAM_DOUBLE("满TRAY盘升降Z轴放料位")));

	if (m_sRet == "No") {
		RETURN_STATE(&CLogicTrayFull::OnFullTray00, true);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltInPosStatus());

	if (m_sRet != "On") {
		RETURN_STATE(&CLogicTrayFull::OnFullTrayBack08, true);
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayBack05, true);
}

CStatus CLogicTrayFull::OnFullTrayBack05()
{
	CString sRet1, sRet2;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltInMaterialStatus());

	sRet1 = m_sRet;

	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltWaitMaterialStatus());

	sRet2 = m_sRet;

	if (sRet1 == "On" || sRet2 == "On") {
		REPORT("满TRAY盘皮带上有料，无法退料！", emLogLevelWarn);
		MESSAGEBOX("满TRAY盘皮带上有料，无法退料！", "", false);
		RETURN_SELF("", false);
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayBack06, true);
}

CStatus CLogicTrayFull::OnFullTrayBack06()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayEndStopCylinderOn());

	RUN_STOP_IF_ERROR(m_pTray->FullTrayEndStopCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待满TRAY盘尾端阻挡气缸伸出", true);
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayBack07, true);
}

CStatus CLogicTrayFull::OnFullTrayBack07()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltOn(!PARAM_BOOL("满TRAY盘皮带正转方向")));

	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltInMaterialStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待满TRAY盘传输到位", true);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayBeltOff());

	RETURN_STATE(&CLogicTrayFull::OnFullTrayBack08, true);
}

CStatus CLogicTrayFull::OnFullTrayBack08()
{
// 	RUN_STOP_IF_ERROR(m_pTray->FullTrayEndStopCylinderOff());
// 
// 	RUN_STOP_IF_ERROR(m_pTray->FullTrayEndStopCylinderStatus());
// 
// 	if (m_sRet != "Off") {
// 		RETURN_SELF("等待满TRAY盘尾端阻挡气缸缩回", true);
// 	}

	RETURN_STATE(&CLogicTrayFull::OnFullTrayBack09, true);
}

CStatus CLogicTrayFull::OnFullTrayBack09()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(PARAM_DOUBLE("满TRAY盘升降Z轴最大待料位")));

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());

	if (m_sRet == "On") {
		RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZStop());
		RETURN_STATE(&CLogicTrayFull::OnFullTray00, true);
	}

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(PARAM_DOUBLE("满TRAY盘升降Z轴最大待料位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到最大待料位", true);
	}

	RETURN_STATE(&CLogicTrayFull::OnFullTray00, true);
}

CStatus CLogicTrayFull::OnFullTrayBack10()
{
	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZMove(PARAM_DOUBLE("满TRAY盘升降Z轴最大待料位")));

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());

	if (m_sRet == "On") {
		RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZStop());
		RETURN_STATE(&CLogicTrayFull::OnFullTrayBack00, true);
	}

	RUN_STOP_IF_ERROR(m_pTray->IsFullTrayUpDnZInPos(PARAM_DOUBLE("满TRAY盘升降Z轴最大待料位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴运动到最大待料位", true);
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴当前位置"]));

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpInPosStatus());

	if (fabs(m_mapPos["满TRAY盘升降Z轴当前位置"] - PARAM_DOUBLE("满TRAY盘升降Z轴最大待料位")) < 1 && m_sRet != "On") {
		m_mapPos["满TRAY盘升降Z轴接料位"] = m_mapPos["满TRAY盘升降Z轴当前位置"] + PARAM_DOUBLE("满TRAY盘升降Z轴送料偏移量");
		TRACE("接料距离： 满TRAY盘升降Z轴送料偏移量\n");
	}
	else {
		m_mapPos["满TRAY盘升降Z轴接料位"] = m_mapPos["满TRAY盘升降Z轴当前位置"] + PARAM_DOUBLE("满TRAY盘升降Z轴接料偏移量");
		TRACE("接料距离： 满TRAY盘升降Z轴接料偏移量\n");
	}

	RUN_STOP_IF_ERROR(m_pTray->FullTrayUpDnZCurrentPos(m_mapPos["满TRAY盘升降Z轴原点位置"]));

	RETURN_STATE(&CLogicTrayFull::OnFullTrayBack01, true);
}
