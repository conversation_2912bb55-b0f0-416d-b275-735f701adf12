# FStation-MQTTGateway Socket通信协议 v1.0

## 1. 文档概述

### 1.1. 目的

本文档定义了FStation主程序与MQTT网关之间的Socket通信协议，用于本地数据传输和状态同步。本协议基于SCADA平台MQTT协议设计，保持数据格式和参数定义的一致性，便于后续升级到MQTT通信。

### 1.2. 适用范围

- FStation主程序（VS2010 MFC环境）
- MQTT网关（Qt6环境）
- 本地Socket通信（TCP/IP）

### 1.3. 版本历史

| 版本号 | 修订日期   | 修订内容                           |
| :----- | :--------- | :--------------------------------- |
| v1.0   | 2024-12-24 | 初始版本，基于SCADA MQTT协议设计   |

---

## 2. 核心概念与设计原则

### 2.1. 通信协议

- **传输协议**: TCP Socket
- **数据格式**: JSON
- **编码**: UTF-8（传输时统一使用UTF-8）
- **端口**: 8888（默认）

### 2.2. 消息格式

所有Socket消息均遵循以下基础格式：

```json
{
  "type": "消息类型",
  "data": "JSON字符串或对象",
  "timestamp": "时间戳"
}
```

### 2.3. 数据格式规范

本协议完全兼容SCADA平台MQTT协议的数据格式，使用相同的`services`数组结构和数据代码。

#### 2.3.1. 基础数据结构

```json
{
  "services": [
    {
      "service_id": "服务ID",
      "properties": {
        "数据代码": "数据值"
      },
      "event_time": "事件时间"
    }
  ]
}
```

#### 2.3.2. 核心服务类型

- **`DefaultService`**: 设备通用参数和生产数据
- **`SnService`**: 产品SN码相关数据
- **`StatusService`**: 设备状态信息

---

## 3. 消息类型定义

### 3.1. 设备状态消息 (DEVICE_STATUS)

#### 功能说明
用于FStation向MQTT网关上报设备当前状态信息。

#### 消息格式
```json
{
  "type": "DEVICE_STATUS",
  "data": {
    "services": [
      {
        "service_id": "DefaultService",
        "properties": {
          "A00002": true,           // 正常生产
          "A00003": false,          // 运行暂停
          "A00004": false,          // 设备故障
          "A00006": false,          // 待机状态
          "A00010": "2AC112-0",     // 机型程序名
          "A00014": 25.3,           // 运行周期
          "A00015": 3500,           // 累计产能
          "C00001": "2001000168005843127309215",  // 主板SN号
          "C00002": "A049991234",   // 设备资产编码
          "C00003": 1,              // 轨道号
          "C00004": "T",            // 面别
          "C00005": "2AC112-0"      // 程序名
        },
        "event_time": "2024-01-11 16:02:21.165"
      },
      {
        "service_id": "StatusService",
        "properties": {
          "device_status_enum": "production",
          "device_status_desc": "正常运行"
        },
        "event_time": "2024-01-11 16:02:21.165"
      }
    ]
  },
  "timestamp": "2024-01-11 16:02:21.165"
}
```

### 3.2. 生产数据消息 (PRODUCTION_DATA)

#### 功能说明
用于FStation向MQTT网关上报生产过程中的具体数据。

#### 消息格式
```json
{
  "type": "PRODUCTION_DATA",
  "data": {
    "services": [
      {
        "service_id": "DefaultService",
        "properties": {
          "C00001": "2001000168005843127309215",  // 主板SN号
          "C00002": "A049991234",   // 设备资产编码
          "C00003": 1,              // 轨道号
          "C00004": "T",            // 面别
          "C00005": "2AC112-0",     // 程序名
          "C00007": 25.153,         // 实际加工周期
          "C00008": 8.103,          // 等前时间
          "C00009": 8.103           // 等后时间
        },
        "event_time": "2024-01-11 16:02:21.165"
      },
      {
        "service_id": "SnService",
        "properties": {
          "sn": "2001000168005843127309215",
          "production_model": "C380_B75",
          "profiles": [
            { "name": "5380495513.xml" },
            { "name": "5382000005.xml" }
          ]
        },
        "event_time": "2024-01-11 16:02:21.165"
      }
    ]
  },
  "timestamp": "2024-01-11 16:02:21.165"
}
```

### 3.3. 告警事件消息 (ALARM_EVENT)

#### 功能说明
用于FStation向MQTT网关上报设备故障和告警信息。

#### 消息格式
```json
{
  "type": "ALARM_EVENT",
  "data": {
    "services": [
      {
        "service_id": "EventService",
        "event_type": "EVENT_FAULT",
        "properties": {
          "sn": "2001000168005843127309215",
          "production_model": "C380_B75",
          "fault_code": "E03",
          "fault_type": "error",
          "fault_msg": "RAM错误"
        },
        "event_time": "2024-01-11 16:12:00.456"
      },
      {
        "service_id": "DefaultService",
        "properties": {
          "C00001": "2001000168005843127309215",
          "C00002": "A049991234",
          "C00010": "E03",          // 故障代码
          "C00011": "RAM错误",       // 故障信息
          "C00012": "2024-01-11 16:12:00.456",  // 故障开始时间
          "C00013": "2024-01-11 16:12:30.789",  // 故障结束时间
          "C00014": 30.333          // 故障时间
        },
        "event_time": "2024-01-11 16:12:00.456"
      }
    ]
  },
  "timestamp": "2024-01-11 16:12:00.456"
}
```

### 3.4. 响应消息 (RESPONSE)

#### 功能说明
用于MQTT网关向FStation响应数据查询或命令执行结果。

#### 消息格式
```json
{
  "type": "RESPONSE",
  "data": {
    "requestId": "查询请求ID",
    "result_code": 0,
    "result_desc": "success",
    "data": {
      "services": [
        {
          "service_id": "DefaultService",
          "properties": {
            "C00001": "2001000168005843127309215",
            "C00002": "A049991234"
          },
          "event_time": "2024-01-11 16:05:30.482"
        }
      ]
    }
  },
  "timestamp": "2024-01-11 16:05:30.482"
}
```

### 3.5. 心跳消息 (HEARTBEAT)

#### 功能说明
用于FStation向MQTT网关发送心跳信号，保持连接活跃。

#### 消息格式
```json
{
  "type": "HEARTBEAT",
  "data": {
    "source": "FStation",
    "timestamp": "2024-01-11 16:02:21.165"
  },
  "timestamp": "2024-01-11 16:02:21.165"
}
```

---

## 4. 数据代码定义

### 4.1. 设备运行状态与基础信息 (A系列)

| 数据代码 | 参数名称   | 数据类型 | 单位 | 说明                     |
| :------- | :--------- | :------- | :--- | :----------------------- |
| A00002   | 正常生产   | bool     |      | 正常运行状态             |
| A00003   | 运行暂停   | bool     |      | 暂停状态                 |
| A00004   | 设备故障   | bool     |      | 故障状态                 |
| A00006   | 待机状态   | bool     |      | 待出、待料               |
| A00010   | 机型程序名 | string   |      | 当前生产程序名           |
| A00014   | 运行周期   | decimal  | s    | 单个生产周期（秒）       |
| A00015   | 累计产能   | int      | PCS  | 每班累计产能             |

### 4.2. 设备硬件资源监控 (A系列)

| 数据代码 | 参数名称     | 数据类型 | 单位 | 说明         |
| :------- | :----------- | :------- | :--- | :----------- |
| A00020   | 内存容量     | int      | MB   | 内存容量     |
| A00021   | 磁盘容量     | int      | GB   | 磁盘容量     |
| A00022   | C盘容量      | int      | GB   | C盘容量      |
| A00023   | D盘容量      | int      | GB   | D盘容量      |
| A00024   | E盘容量      | int      | GB   | E盘容量      |
| A00025   | 磁盘剩余容量 | int      | GB   | 磁盘剩余容量 |
| A00026   | C盘剩余容量  | int      | GB   | C盘剩余容量  |
| A00027   | D盘剩余容量  | int      | GB   | D盘剩余容量  |
| A00028   | E盘剩余容量  | int      | GB   | E盘剩余容量  |
| A00029   | CPU利用率    | float    | %    | CPU利用率    |
| A00030   | 内存利用率   | float    | %    | 内存利用率   |

### 4.3. 生产过程与主板追溯参数 (C系列)

| 数据代码 | 参数名称     | 数据类型 | 单位 | 说明                    |
| :------- | :----------- | :------- | :--- | :---------------------- |
| C00001   | 主板SN号     | string   |      | 当前扫描主板SN          |
| C00002   | 设备资产编码   | string   |      | 当前设备SN              |
| C00003   | 轨道号       | int      |      | 当前主板生产轨道号      |
| C00004   | 面别         | enum     |      | 当前主板生产面别        |
| C00005   | 程序名       | string   |      | 当前主板生产程序名      |
| C00006   | 程序路径     | string   |      | 服务器下发程序路径      |
| C00007   | 实际加工周期 | decimal  | s    | 当前主板实际生产时间    |
| C00008   | 等前时间     | decimal  | s    | 等待PCB进入设备的时间   |
| C00009   | 等后时间     | decimal  | s    | PCB到达出口后到离开时间 |

### 4.4. 故障与报警信息 (C系列)

| 数据代码 | 参数名称     | 数据类型 | 单位 | 说明                     |
| :------- | :----------- | :------- | :--- | :----------------------- |
| C00010   | 故障代码     | string   |      | 故障时的设备故障代码     |
| C00011   | 故障信息     | string   |      | 故障时的设备故障信息     |
| C00012   | 故障开始时间 | datetime |      | 故障发生时间             |
| C00013   | 故障结束时间 | datetime |      | 故障结束时间             |
| C00014   | 故障时间     | decimal  | s    | 故障持续时间（秒）       |

### 4.5. 设备参数与测高数据 (B系列)

| 数据代码 | 参数名称       | 数据类型 | 单位 | 说明         |
| :------- | :------------- | :------- | :--- | :----------- |
| B40008   | 1轨测高实际值1 | decimal  | mm   | 1轨测高实际值1 |
| B40009   | 1轨测高实际值2 | decimal  | mm   | 1轨测高实际值2 |
| B40010   | 1轨测高实际值3 | decimal  | mm   | 1轨测高实际值3 |
| B40011   | 1轨测高实际值4 | decimal  | mm   | 1轨测高实际值4 |
| B40012   | 2轨测高实际值1 | decimal  | mm   | 2轨测高实际值1 |
| B40013   | 2轨测高实际值2 | decimal  | mm   | 2轨测高实际值2 |
| B40014   | 2轨测高实际值3 | decimal  | mm   | 2轨测高实际值3 |
| B40015   | 2轨测高实际值4 | decimal  | mm   | 2轨测高实际值4 |
| B40016   | 测高设定值     | decimal  | mm   | 测高设定值     |

---

## 5. 编码处理规范

### 5.1. 编码要求

- **传输编码**: 统一使用UTF-8
- **FStation端**: 内部使用GBK2312，发送时转换为UTF-8
- **MQTTGateway端**: 直接使用UTF-8

### 5.2. FStation端编码转换

```cpp
// GBK2312转UTF-8
std::string GBKToUTF8(const std::string& gbkStr);

// UTF-8转GBK2312  
std::string UTF8ToGBK(const std::string& utf8Str);
```

### 5.3. 时间格式

- **格式**: `YYYY-MM-DD hh:mm:ss.SSS`
- **示例**: `2024-01-11 16:02:21.165`
- **时区**: 本地时间（UTC+8）

---

## 6. 错误处理

### 6.1. 连接错误

- **连接失败**: 记录错误日志，尝试重连
- **连接断开**: 自动重连机制
- **超时处理**: 设置合理的超时时间

### 6.2. 数据错误

- **JSON解析失败**: 记录错误日志，跳过该消息
- **数据格式错误**: 验证数据格式，记录错误
- **编码错误**: 尝试编码转换，记录转换结果

### 6.3. 业务错误

- **数据代码不存在**: 记录警告日志
- **数据类型不匹配**: 记录错误日志
- **必填字段缺失**: 记录错误日志

---

## 7. 实现示例

### 7.1. FStation端发送设备状态

```cpp
// 构建设备状态JSON
CString BuildDeviceStatusJson(const CString& status, int prodCount, int errCount)
{
    Json::Value root;
    Json::Value services;
    
    // 构建DefaultService
    Json::Value defaultService;
    defaultService["service_id"] = "DefaultService";
    defaultService["event_time"] = GetCurrentTimestamp();
    
    Json::Value properties;
    properties["A00002"] = (status == "运行中");  // 正常生产
    properties["A00003"] = (status == "暂停");    // 运行暂停
    properties["A00004"] = (status == "故障");    // 设备故障
    properties["A00006"] = (status == "待机");    // 待机状态
    properties["A00015"] = prodCount;             // 累计产能
    
    defaultService["properties"] = properties;
    services.append(defaultService);
    
    root["services"] = services;
    return JsonToString(root);
}

// 发送设备状态
bool SendDeviceStatus(const CString& status, int prodCount, int errCount)
{
    CString jsonData = BuildDeviceStatusJson(status, prodCount, errCount);
    return SendMessage("DEVICE_STATUS", jsonData);
}
```

### 7.2. MQTTGateway端接收处理

```cpp
// 处理设备状态消息
void ProcessDeviceStatusMessage(const QJsonObject& data)
{
    if (data.contains("services") && data["services"].isArray()) {
        QJsonArray services = data["services"].toArray();
        
        for (const QJsonValue& serviceValue : services) {
            QJsonObject service = serviceValue.toObject();
            QString serviceId = service["service_id"].toString();
            
            if (serviceId == "DefaultService") {
                QJsonObject properties = service["properties"].toObject();
                
                // 处理设备状态数据
                bool isRunning = properties["A00002"].toBool();
                bool isPaused = properties["A00003"].toBool();
                bool isFault = properties["A00004"].toBool();
                int productionCount = properties["A00015"].toInt();
                
                // 更新数据点
                m_dataPointManager->updateDataPoint("A00002", isRunning);
                m_dataPointManager->updateDataPoint("A00003", isPaused);
                m_dataPointManager->updateDataPoint("A00004", isFault);
                m_dataPointManager->updateDataPoint("A00015", productionCount);
            }
        }
    }
}
```

---

## 8. 附录

### 8.1. 消息类型列表

| 消息类型        | 方向                    | 说明                     |
| :-------------- | :---------------------- | :----------------------- |
| DEVICE_STATUS   | FStation → MQTTGateway  | 设备状态上报             |
| PRODUCTION_DATA | FStation → MQTTGateway  | 生产数据上报             |
| ALARM_EVENT     | FStation → MQTTGateway  | 告警事件上报             |
| RESPONSE        | MQTTGateway → FStation  | 响应消息                 |
| HEARTBEAT       | FStation → MQTTGateway  | 心跳消息                 |

### 8.2. 设备状态枚举

| 状态值      | 说明     |
| :---------- | :------- |
| shutdown    | 未开机   |
| ready       | 待机状态 |
| production  | 正常生产 |
| pause       | 运行暂停 |
| fault_warn  | 故障警告 |
| fault_error | 故障错误 |

### 8.3. 面别枚举

| 面别值 | 说明 |
| :----- | :--- |
| T      | Top  |
| B      | Bottom |

---

> **注意**: 本协议完全兼容SCADA平台MQTT协议，便于后续升级到MQTT通信。所有数据代码和格式定义均与科瑞F站数采清单保持一致。 