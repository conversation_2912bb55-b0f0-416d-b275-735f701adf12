﻿#include "stdafx.h"
#include "LogicReset.h"

#include "Sys.h"

CLogicReset::CLogicReset()
{
}

CLogicReset::~CLogicReset()
{
}

EnumStatus CLogicReset::OnSafeCheck()
{
	return emRun;
}

EnumStatus CLogicReset::OnStart()
{
	return emRun;
}

EnumStatus CLogicReset::OnPause()
{
	return emRun;
}

EnumStatus CLogicReset::OnResume()
{
	map<CString, DWORD>::iterator it = m_mapTick.begin();
	for (; it!=m_mapTick.end(); it++)
	{
		it->second = GetTickCount();
	}

	return emRun;
}

EnumStatus CLogicReset::OnStop()
{
	return emRun;
}

CStatus CLogicReset::OnRun()
{
	CSys::m_bInit = false;

	RETURN_STATE(&CLogicReset::OnReset00, true);
}

CStatus CLogicReset::OnReset00()
{
	CString sRet;

	for (int i=0; i<4; i++)
	{
		RUN_STOP_IF_ERROR(g_pRobot->PickCylinderOff(i));
	}

	for (int i=0; i<4; i++)
	{
		RUN_STOP_IF_ERROR(g_pRobot->PickCylinderStatus(i));

		if (m_sRet != "Off") {
			sRet.Format("等待机械手气缸%d缩回", i + 1);
			RETURN_SELF(sRet, true);
		}
	}

	RETURN_STATE(&CLogicReset::OnReset01, true);
}

CStatus CLogicReset::OnReset01()
{
	RETURN_STATE(&CLogicReset::OnReset01_0, true);
}

CStatus CLogicReset::OnReset01_0()
{
	ROBOTPOINT stRobPnt;
	stRobPnt = *g_pRobot->m_mapRobotPoint["空闲等待位"];

	stRobPnt.speed = 50;

	RUN_STOP_IF_ERROR(g_pRobot->Move(stRobPnt, false));

	RUN_STOP_IF_ERROR(g_pRobot->IsInPos("空闲等待位"));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到空闲等待位", true);
	}

	bool bFlag = false;

	for (int i=0; i<4; i++)
	{
		RUN_STOP_IF_ERROR(g_pRobot->PickCylinderVacuumStatus(i));
		if (m_sRet == "On") {
//			bFlag = true;
			break;
		}
	}

	if (!bFlag) {
		RETURN_STATE(&CLogicReset::OnReset02, true);
	}

	RETURN_STATE(&CLogicReset::OnReset01_1, true);
}

CStatus CLogicReset::OnReset01_1()
{
	RUN_STOP_IF_ERROR(g_pRobot->Move("NG物料动作起始位"));

	RUN_STOP_IF_ERROR(g_pRobot->IsInPos("NG物料动作起始位"));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到NG预放料位", true);
	}

	m_mapTick["当前吸嘴索引"] = 0;

	RETURN_STATE(&CLogicReset::OnReset01_2, true);
}

CStatus CLogicReset::OnReset01_2()
{
	if (m_mapTick["当前吸嘴索引"] >= 4) {
		RETURN_STATE(&CLogicReset::OnReset01_0, true);
	}
// 
// 	RUN_STOP_IF_ERROR(g_pRobot->PickCylinderVacuumStatus(m_mapTick["当前吸嘴索引"]));
// 	if (m_sRet == "On") {
// 		m_mapTick["当前吸嘴索引"]++;
// 		RETURN_SELF("", false);
// 	}

	RETURN_STATE(&CLogicReset::OnReset01_3, true);
}

CStatus CLogicReset::OnReset01_3()
{
	CString sPostion = "吸嘴1NG物料放料位";

	ROBOTPOINT stRobPnt = *g_pRobot->m_mapRobotPoint[sPostion];

	double nOffX = 0, nOffY = 0;

	switch (m_mapTick["当前吸嘴索引"])
	{
	case 0:
		break;
	case 1:
		CCvImage::Rotate(0, 0, VAR_ROBOT_D("吸嘴中心间距X"), 0, g_pRobot->m_mapRobotPoint[sPostion]->r - g_pRobot->m_mapRobotPoint["空闲等待位"]->r, nOffX, nOffY);
		stRobPnt.x -= nOffX;
		stRobPnt.y += nOffY;
		break;
	case 2:
		CCvImage::Rotate(0, 0, VAR_ROBOT_D("吸嘴中心间距X"), VAR_ROBOT_D("吸嘴中心间距Y"), g_pRobot->m_mapRobotPoint[sPostion]->r - g_pRobot->m_mapRobotPoint["空闲等待位"]->r, nOffX, nOffY);
		stRobPnt.x -= nOffX;
		stRobPnt.y += nOffY;
		break;
	case 3:
		CCvImage::Rotate(0, 0, 0, VAR_ROBOT_D("吸嘴中心间距Y"), g_pRobot->m_mapRobotPoint[sPostion]->r - g_pRobot->m_mapRobotPoint["空闲等待位"]->r, nOffX, nOffY);
		stRobPnt.x -= nOffX;
		stRobPnt.y += nOffY;
		break;
	default:
		RETURN_STOP();
	}

	RUN_STOP_IF_ERROR(g_pRobot->Move(stRobPnt));

	RUN_STOP_IF_ERROR(g_pRobot->IsInPos(stRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到NG物料放料位", true);
	}

	RETURN_STATE(&CLogicReset::OnReset01_4, true);
}

CStatus CLogicReset::OnReset01_4()
{
	if (!VAR_MACHINE_B("NG皮带允许放料标志")) {
		RETURN_SELF("等待NG皮带允许放料", false);
	}

	RETURN_STATE(&CLogicReset::OnReset01_5, true);
}

CStatus CLogicReset::OnReset01_5()
{
	RUN_STOP_IF_ERROR(g_pRobot->PickCylinderOn(m_mapTick["当前吸嘴索引"]));

	RUN_STOP_IF_ERROR(g_pRobot->PickCylinderStatus(m_mapTick["当前吸嘴索引"]));

	if (m_sRet != "On") {
		m_sRet.Format("等待吸嘴气缸%d伸出", m_mapTick["当前吸嘴索引"] + 1);
		RETURN_SELF(m_sRet, true);
	}

	VAR_MACHINE("NG皮带放料完成标志") = false;

	RUN_STOP_IF_ERROR(g_pRobot->PickCylinderBrokenVacuumOn(m_mapTick["当前吸嘴索引"]));

	Sleep(100);

	RUN_STOP_IF_ERROR(g_pRobot->PickCylinderVacuumOff(m_mapTick["当前吸嘴索引"]));

	VAR_MACHINE("NG皮带放料完成标志") = true;

	RETURN_STATE(&CLogicReset::OnReset01_6, true);
}

CStatus CLogicReset::OnReset01_6()
{
	RUN_STOP_IF_ERROR(g_pRobot->PickCylinderOff(m_mapTick["当前吸嘴索引"]));

	RUN_STOP_IF_ERROR(g_pRobot->PickCylinderStatus(m_mapTick["当前吸嘴索引"]));

	if (m_sRet != "Off") {
		m_sRet.Format("等待吸嘴气缸%d缩回", m_mapTick["当前吸嘴索引"] + 1);
		RETURN_SELF(m_sRet, true);
	}

	m_mapTick["当前吸嘴索引"]++;

	RETURN_STATE(&CLogicReset::OnReset01_2, true);
}

CStatus CLogicReset::OnReset02()
{
	RUN_STOP_IF_ERROR(g_pTray->EmptyTrayBrokenVacuumOn());
	Sleep(100);
	RUN_STOP_IF_ERROR(g_pTray->EmptyTrayVacuumOff());
	
	RETURN_STATE(&CLogicReset::OnReset03, true);
}

CStatus CLogicReset::OnReset03()
{
	RUN_STOP_IF_ERROR(g_pTray->EmptyTrayPullMaterialCylinderOff());
	RUN_STOP_IF_ERROR(g_pTray->EmptyTrayEndStopCylinderOff());

	RUN_STOP_IF_ERROR(g_pTray->FullTraySeparateCylinderOff());
	RUN_STOP_IF_ERROR(g_pTray->FullTrayEndStopCylinderOff());

	RUN_STOP_IF_ERROR(g_pFixture->SeparateCylinderOff());
	RUN_STOP_IF_ERROR(g_pFixture->InStopCylinderOn());

	RUN_STOP_IF_ERROR(g_pFixture->UpLidLiftCylinderOff());
	RUN_STOP_IF_ERROR(g_pFixture->UpLidStopCylinderOn());

	RUN_STOP_IF_ERROR(g_pBeltA->MainBoardSetPosCylinderOff());
	RUN_STOP_IF_ERROR(g_pBeltA->FixtureAssembleSetPosCylinderOff());
	RUN_STOP_IF_ERROR(g_pBeltA->FixtureAssembleStopCylinderOff());

	RUN_STOP_IF_ERROR(g_pBeltB->MainBoardSetPosCylinderOff());
	RUN_STOP_IF_ERROR(g_pBeltB->FixtureAssembleSetPosCylinderOff());
	RUN_STOP_IF_ERROR(g_pBeltB->FixtureAssembleStopCylinderOff());

	RUN_STOP_IF_ERROR(g_pTray->EmptyTrayPullMaterialCylinderStatus());
	if (m_sRet != "Off") {
		RETURN_SELF("等待空TRAY盘拨料气缸缩回", true);
	}

	RUN_STOP_IF_ERROR(g_pTray->EmptyTrayEndStopCylinderStatus());
	if (m_sRet != "Off") {
		RETURN_SELF("等待空TRAY盘尾端阻挡气缸缩回", true);
	}

	RUN_STOP_IF_ERROR(g_pTray->FullTraySeparateCylinderStatus());
	if (m_sRet != "Off") {
		RETURN_SELF("等待满TRAY盘分盘气缸缩回", true);
	}

	RUN_STOP_IF_ERROR(g_pTray->FullTrayEndStopCylinderStatus());
	if (m_sRet != "Off") {
		RETURN_SELF("等待满TRAY盘尾端阻挡气缸缩回", true);
	}

	RUN_STOP_IF_ERROR(g_pFixture->SeparateCylinderStatus());
	if (m_sRet != "Off") {
		RETURN_SELF("等待治具分板气缸缩回", true);
	}

	RUN_STOP_IF_ERROR(g_pFixture->InStopCylinderStatus());
	if (m_sRet != "On") {
		RETURN_SELF("等待治具入口阻挡气缸伸出", true);
	}

	RUN_STOP_IF_ERROR(g_pFixture->UpLidLiftCylinderStatus());
	if (m_sRet != "Off") {
		RETURN_SELF("等待治具上盖位顶升气缸缩回", true);
	}

	RUN_STOP_IF_ERROR(g_pFixture->UpLidStopCylinderStatus());
	if (m_sRet != "On") {
		RETURN_SELF("等待治具上盖位阻挡气缸伸出", true);
	}

	RUN_STOP_IF_ERROR(g_pBeltA->MainBoardSetPosCylinderStatus());
	if (m_sRet != "Off") {
		RETURN_SELF("等待A轨主板装配位定位气缸缩回", true);
	}

	RUN_STOP_IF_ERROR(g_pBeltA->FixtureAssembleSetPosCylinderStatus());
	if (m_sRet != "Off") {
		RETURN_SELF("等待A轨治具装配位定位气缸缩回", true);
	}

	RUN_STOP_IF_ERROR(g_pBeltA->FixtureAssembleStopCylinderStatus());
	if (m_sRet != "Off") {
		RETURN_SELF("等待A轨治具装配位气缸缩回", true);
	}
	
	RUN_STOP_IF_ERROR(g_pBeltB->MainBoardSetPosCylinderStatus());
	if (m_sRet != "Off") {
		RETURN_SELF("等待B轨治具装配位定位气缸缩回", true);
	}

	RUN_STOP_IF_ERROR(g_pBeltB->FixtureAssembleSetPosCylinderStatus());
	if (m_sRet != "Off") {
		RETURN_SELF("等待B轨治具装配位定位气缸缩回", true);
	}

	RUN_STOP_IF_ERROR(g_pBeltB->FixtureAssembleStopCylinderStatus());
	if (m_sRet != "Off") {
		RETURN_SELF("等待B轨治具装配位气缸缩回", true);
	}

	REPORT("满TRAY盘升降Z轴即将下降，请确认升降Z轴下方是否有料，如果有料请手动取走，谨防压伤？", emLogLevelWarn);
	MESSAGEBOX("满TRAY盘升降Z轴即将下降，请确认升降Z轴下方是否有料，如果有料请手动取走，谨防压伤？", "", true);

	RETURN_STATE(&CLogicReset::OnReset04, true);
}

CStatus CLogicReset::OnReset04()
{
	RUN_STOP_IF_ERROR(g_pTray->FullTrayUpDnZHome());

	RUN_STOP_IF_ERROR(g_pTray->EmptyTraySeparateZHome());
	RUN_STOP_IF_ERROR(g_pTray->EmptyTrayTransportYHome());

	RUN_STOP_IF_ERROR(g_pFixture->TransportZHome());
	RUN_STOP_IF_ERROR(g_pFixture->LidZHome());
	RUN_STOP_IF_ERROR(g_pFixture->SeparateZHome());

	RETURN_STATE(&CLogicReset::OnReset05, true);
}

CStatus CLogicReset::OnReset05()
{
	RUN_STOP_IF_ERROR(g_pFixture->IsTransportZHomeOK());
	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴回零完成", false);
	}

	RUN_STOP_IF_ERROR(g_pFixture->TransportYHome());

	RETURN_STATE(&CLogicReset::OnReset06, true);
}

CStatus CLogicReset::OnReset06()
{
	RUN_STOP_IF_ERROR(g_pTray->IsFullTrayUpDnZHomeOK());
	if (m_sRet != "Yes") {
		RETURN_SELF("等待满TRAY盘升降Z轴回零完成", false);
	}

	RUN_STOP_IF_ERROR(g_pTray->IsEmptyTraySeparateZHomeOK());
	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘分盘Z轴回零完成", false);
	}

	RUN_STOP_IF_ERROR(g_pTray->IsEmptyTrayTransportYHomeOK());
	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘搬运Y轴回零完成", false);
	}

	RUN_STOP_IF_ERROR(g_pFixture->IsSeparateZHomeOK());
	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具分盘Z轴回零完成", false);
	}

	RUN_STOP_IF_ERROR(g_pFixture->IsLidZHomeOK());
	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具开盖Z轴回零完成", false);
	}

	RUN_STOP_IF_ERROR(g_pFixture->IsTransportYHomeOK());
	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Y轴回零完成", false);
	}

	RETURN_STATE(&CLogicReset::OnReset07, true);
}

CStatus CLogicReset::OnReset07()
{
	CSys::m_bInit = true;

	RETURN_STOP();
}
