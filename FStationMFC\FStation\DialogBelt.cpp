﻿// DialogBelt.cpp : 实现文件
//

#include "stdafx.h"
#include "FStation.h"
#include "DialogBelt.h"
#include "afxdialogex.h"

#include "LogicMgr.h"

// CDialogBelt 对话框

IMPLEMENT_DYNAMIC(CDialogBelt, CDialogEx)

CDialogBelt::CDialogBelt(CPoint pt, CString sBelt, CWnd* pParent /*=NULL*/)
	: CDialogEx(CDialogBelt::IDD, pParent)
{
	m_pt = pt;

	m_sBelt = sBelt;
}

CDialogBelt::~CDialogBelt()
{
}

void CDialogBelt::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_BUTTON1, m_btnReset);
	DDX_Control(pDX, IDC_BUTTON2, m_btnStart);
	DDX_Control(pDX, IDC_BUTTON3, m_btnPause);
	DDX_Control(pDX, IDC_BUTTON4, m_btnStop);
}


BEGIN_MESSAGE_MAP(CDialogBelt, CDialogEx)
	ON_WM_TIMER()
	ON_BN_CLICKED(IDC_BUTTON1, &CDialogBelt::OnBnClickedButton1)
	ON_BN_CLICKED(IDC_BUTTON2, &CDialogBelt::OnBnClickedButton2)
	ON_BN_CLICKED(IDC_BUTTON3, &CDialogBelt::OnBnClickedButton3)
	ON_BN_CLICKED(IDC_BUTTON4, &CDialogBelt::OnBnClickedButton4)
	ON_BN_CLICKED(IDC_BUTTON5, &CDialogBelt::OnBnClickedButton5)
	ON_BN_CLICKED(IDC_BUTTON6, &CDialogBelt::OnBnClickedButton6)
	ON_BN_CLICKED(IDC_BUTTON7, &CDialogBelt::OnBnClickedButton7)
	ON_BN_CLICKED(IDC_BUTTON8, &CDialogBelt::OnBnClickedButton8)
	ON_BN_CLICKED(IDC_BUTTON9, &CDialogBelt::OnBnClickedButton9)
END_MESSAGE_MAP()

BOOL CDialogBelt::PreTranslateMessage( MSG* pMsg )
{
	if ((WM_SYSKEYDOWN == pMsg->message && VK_F4 == pMsg->wParam) || (pMsg->message == WM_KEYDOWN && pMsg->wParam == VK_ESCAPE)) { //屏蔽Alt + F4
		return TRUE;
	}

	return CDialogEx::PreTranslateMessage(pMsg);
}

BOOL CDialogBelt::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	CRect rc;

	GetWindowRect(&rc);

	MoveWindow(m_pt.x, m_pt.y, rc.Width(), rc.Height());

	CString strPath = CString(GetModulePath().c_str());
	strPath += "\\Sys\\images\\";

	CString strName[] = { 
		"resetbk.bmp", 
		"start.bmp", 
		"pause.bmp", 
		"stop.bmp"
	};

	CColorButton *pBtn[] = { 
		&m_btnReset, 
		&m_btnStart, 
		&m_btnPause, 
		&m_btnStop
	};

	for (int i=0; i<4; i++)
	{
		pBtn[i]->SetColor(RGB(240, 240, 240), RGB(0, 0, 0));

		pBtn[i]->SetImagePos(5, 0);
		pBtn[i]->SetIcon(strPath + strName[i], strPath + strName[i], false);

		pBtn[i]->SetFontSize(20, true);
		pBtn[i]->SetTextPos(68, 0);

	}

	bool bEnable = false;

	if (m_sBelt.Find("A") >= 0) {
		bEnable = VAR_BELTA_B("轨道启用标志");
	}
	else {
		bEnable = VAR_BELTB_B("轨道启用标志");
	}

	if (bEnable) {
		SetDlgItemText(IDC_BUTTON9, "轨道已启用");
	}
	else {
		SetDlgItemText(IDC_BUTTON9, "轨道已禁用");
	}

	SetTimer(99, 100, NULL);

	return TRUE;
}

// CDialogBelt 消息处理程序

void CDialogBelt::OnTimer(UINT_PTR nIDEvent)
{
	if (nIDEvent == 99) {
		UpdateButtonState();
	}

	CDialogEx::OnTimer(nIDEvent);
}

void CDialogBelt::OnBnClickedButton1()
{
	CBelt* pBelt = NULL;

	if (m_sBelt.Find("A") >= 0) {
		pBelt = g_pBeltA;
	}
	else {
		pBelt = g_pBeltB;
	}

	pBelt->MainBoardSetPosCylinderOff();

	pBelt->FixtureAssembleSetPosCylinderOff();

	pBelt->FixtureAssembleStopCylinderOff();

	AfxMessageBox("复位完成");
}

void CDialogBelt::OnBnClickedButton2()
{
	CLogicMgr::m_mapThread[m_sBelt].pThread->Start();
}

void CDialogBelt::OnBnClickedButton3()
{
	CLogicMgr::m_mapThread[m_sBelt].pThread->Pause();
}

void CDialogBelt::OnBnClickedButton4()
{
	CLogicMgr::m_mapThread[m_sBelt].pThread->Stop();
}

void CDialogBelt::OnBnClickedButton5()
{
	// TODO: 在此添加控件通知处理程序代码
}

void CDialogBelt::OnBnClickedButton6()
{
	PREMISSION_CTRL();

	if (m_sBelt.Find("A") >= 0) {
		VAR_BELTA("允许机械手装配主板标志") = true;
	}
	else {
		VAR_BELTB("允许机械手装配主板标志") = true;
	}
}

void CDialogBelt::OnBnClickedButton7()
{
	PREMISSION_CTRL();

	if (m_sBelt.Find("A") >= 0) {
		VAR_BELTA("允许治具下盖上料标志") = true;
	}
	else {
		VAR_BELTB("允许治具下盖上料标志") = true;
	}
}

void CDialogBelt::OnBnClickedButton8()
{
	PREMISSION_CTRL();

	if (m_sBelt.Find("A") >= 0) {
		VAR_BELTA("治具提前取上盖标志") = true;
		VAR_BELTA("允许治具合盖标志") = true;
	}
	else {
		VAR_BELTB("治具提前取上盖标志") = true;
		VAR_BELTB("允许治具合盖标志") = true;
	}
}

void CDialogBelt::OnBnClickedButton9()
{
	PREMISSION_CTRL();

	CString str;

	GetDlgItemText(IDC_BUTTON9, str);

	if (str.Find("禁用") >= 0) {
		SetDlgItemText(IDC_BUTTON9, "轨道已启用");
		if (m_sBelt.Find("A") >= 0) {
			VAR_BELTA("轨道启用标志") = true;
			CLogicMgr::m_mapThread["BeltA"].bEnable = true;
		}
		else {
			VAR_BELTB("轨道启用标志") = true;
			CLogicMgr::m_mapThread["BeltB"].bEnable = true;
		}
	}
	else {
		SetDlgItemText(IDC_BUTTON9, "轨道已禁用");
		if (m_sBelt.Find("A") >= 0) {
			VAR_BELTA("轨道启用标志") = false;
			CLogicMgr::m_mapThread["BeltA"].bEnable = false;
		}
		else {
			VAR_BELTB("轨道启用标志") = false;
			CLogicMgr::m_mapThread["BeltB"].bEnable = false;
		}
	}

	g_pBeltA->Save();
	g_pBeltB->Save();
}

void CDialogBelt::UpdateButtonState()
{
	CString strPath = CString(GetModulePath().c_str());
	strPath += "\\Sys\\images\\";

	EnumStatus eStat1 = CLogicMgr::m_mapThread["BeltA"].pThread->GetStatus();
	EnumStatus eStat2 = CLogicMgr::m_mapThread["BeltB"].pThread->GetStatus();

	EnumStatus eStat = emStop;

	if (m_sBelt.Find("A") >= 0) {
		eStat = eStat1;
	}
	else {
		eStat = eStat2;
	}

	if (eStat == emStop) {
		m_btnReset.SetIcon(strPath + "resetbk.bmp", strPath + "resetbk.bmp");
		m_btnReset.EnableWindow(TRUE);
	}
	else {
		m_btnReset.SetIcon(strPath + "resetbk_d.bmp", strPath + "resetbk_d.bmp");
		m_btnReset.EnableWindow(FALSE);
	}

	if (eStat == emPause || eStat == emStop) {
		m_btnStart.SetIcon(strPath + "start.bmp", strPath + "start.bmp");
		m_btnStart.EnableWindow(TRUE);
	}
	else {
		m_btnStart.SetIcon(strPath + "start_d.bmp", strPath + "start_d.bmp");
		m_btnStart.EnableWindow(FALSE);
	}

	if (eStat == emRun) {
		m_btnPause.SetIcon(strPath + "pause.bmp", strPath + "pause.bmp");
		m_btnPause.EnableWindow(TRUE);
	}
	else {
		m_btnPause.SetIcon(strPath + "pause_d.bmp", strPath + "pause_d.bmp");
		m_btnPause.EnableWindow(FALSE);
	}

	if (eStat == emRun || eStat == emPause) {
		m_btnStop.SetIcon(strPath + "stop.bmp", strPath + "stop.bmp");
		m_btnStop.EnableWindow(TRUE);
	}
	else {
		m_btnStop.SetIcon(strPath + "stop_d.bmp", strPath + "stop_d.bmp");
		m_btnStop.EnableWindow(FALSE);
	}
}
