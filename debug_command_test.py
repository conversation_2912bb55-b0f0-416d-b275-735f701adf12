#!/usr/bin/env python3
"""
调试版SCADA命令测试脚本
"""

import paho.mqtt.client as mqtt
import json
import time

# MQTT 配置
MQTT_BROKER = "192.168.88.44"
MQTT_PORT = 1883
DEVICE_ID = "A320021760"
CLIENT_ID = f"debug_test_{int(time.time())}"

def on_connect(client, userdata, flags, rc):
    if rc == 0:
        print(f"✅ 连接成功到 EMQX")
        # 订阅命令响应
        response_topic = f"$oc/devices/{DEVICE_ID}/sys/commands/response/+"
        client.subscribe(response_topic, qos=1)
        print(f"📥 订阅命令响应主题")
    else:
        print(f"❌ 连接失败: {rc}")

def on_message(client, userdata, msg):
    print(f"\n🎉 收到响应!")
    print(f"   主题: {msg.topic}")
    print(f"   原始数据: {msg.payload.decode()}")
    
    try:
        response = json.loads(msg.payload.decode())
        print(f"   解析后的JSON: {json.dumps(response, indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"   JSON解析失败: {e}")

def test_command():
    print("=" * 60)
    print("调试版SCADA命令测试")
    print("=" * 60)
    
    # 创建MQTT客户端
    client = mqtt.Client(CLIENT_ID)
    client.on_connect = on_connect
    client.on_message = on_message
    
    try:
        # 连接到MQTT Broker
        print(f"🔗 连接到MQTT Broker: {MQTT_BROKER}:{MQTT_PORT}")
        client.connect(MQTT_BROKER, MQTT_PORT, 60)
        client.loop_start()
        
        time.sleep(3)  # 等待连接建立
        
        # 测试不同的命令格式
        test_commands = [
            {
                "name": "标准SN下发命令",
                "command": {
                    "service_id": "CommandService",
                    "command_name": "COMMAND_SN_DELIVER",
                    "properties": {
                        "sn": "DEBUG_TEST_SN_001",
                        "production_model": "A121000185"
                    }
                }
            },
            {
                "name": "暂停命令",
                "command": {
                    "service_id": "CommandService", 
                    "command_name": "COMMAND_PAUSE",
                    "properties": {
                        "reason": "调试测试暂停"
                    }
                }
            }
        ]
        
        for i, test in enumerate(test_commands):
            print(f"\n{'='*40}")
            print(f"测试 {i+1}: {test['name']}")
            print(f"{'='*40}")
            
            request_id = f"debug_test_{int(time.time())}_{i}"
            command_topic = f"$oc/devices/{DEVICE_ID}/sys/commands/request_id={request_id}"
            
            print(f"📤 发送命令:")
            print(f"   主题: {command_topic}")
            print(f"   RequestId: {request_id}")
            print(f"   命令内容: {json.dumps(test['command'], indent=2, ensure_ascii=False)}")
            
            result = client.publish(command_topic, json.dumps(test['command']), qos=2)
            
            if result.rc == mqtt.MQTT_ERR_SUCCESS:
                print(f"✅ 命令发送成功")
            else:
                print(f"❌ 命令发送失败: {result.rc}")
            
            print(f"⏳ 等待响应...")
            time.sleep(8)  # 等待响应
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
    finally:
        if client:
            client.loop_stop()
            client.disconnect()
            print("\n🔌 MQTT连接已断开")

if __name__ == "__main__":
    test_command()
