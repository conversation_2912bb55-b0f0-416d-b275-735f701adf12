#!/usr/bin/env python3
"""
MQTT性能测试和诊断工具
测试不同QoS级别的性能差异
"""

import paho.mqtt.client as mqtt
import json
import time
import threading
import statistics

# MQTT 配置
MQTT_BROKER = "192.168.88.44"
MQTT_PORT = 1883
CLIENT_ID = f"perf_test_{int(time.time())}"

class MQTTPerformanceTester:
    def __init__(self):
        self.results = []
        self.lock = threading.Lock()
        self.client = None
        
    def on_connect(self, client, userdata, flags, rc):
        if rc == 0:
            print(f"✅ 连接成功到 EMQX: {MQTT_BROKER}:{MQTT_PORT}")
        else:
            print(f"❌ 连接失败: {rc}")
    
    def on_publish(self, client, userdata, mid):
        """发布完成回调"""
        end_time = time.time()
        with self.lock:
            # 查找对应的开始时间
            for result in self.results:
                if result['mid'] == mid and 'end_time' not in result:
                    result['end_time'] = end_time
                    result['duration'] = (end_time - result['start_time']) * 1000  # 转换为毫秒
                    break
    
    def test_qos_performance(self, qos_level, message_count=10):
        """测试指定QoS级别的性能"""
        print(f"\n=== 测试 QoS {qos_level} 性能 ===")
        
        # 清空结果
        with self.lock:
            self.results = []
        
        # 发送测试消息
        for i in range(message_count):
            topic = f"test/performance/qos{qos_level}/{i}"
            payload = json.dumps({
                "test_id": i,
                "qos": qos_level,
                "timestamp": time.time(),
                "data": "x" * 100  # 100字节测试数据
            })
            
            start_time = time.time()
            result = self.client.publish(topic, payload, qos=qos_level)
            
            with self.lock:
                self.results.append({
                    'mid': result.mid,
                    'start_time': start_time,
                    'qos': qos_level,
                    'test_id': i
                })
            
            time.sleep(0.1)  # 100ms间隔
        
        # 等待所有消息完成
        print(f"⏳ 等待 {message_count} 个 QoS {qos_level} 消息完成...")
        time.sleep(5)
        
        # 分析结果
        self.analyze_results(qos_level)
    
    def analyze_results(self, qos_level):
        """分析测试结果"""
        with self.lock:
            completed = [r for r in self.results if 'duration' in r]
            total = len(self.results)
        
        if not completed:
            print(f"❌ QoS {qos_level}: 没有消息完成")
            return
        
        durations = [r['duration'] for r in completed]
        
        print(f"📊 QoS {qos_level} 性能统计:")
        print(f"   总消息数: {total}")
        print(f"   完成数: {len(completed)}")
        print(f"   成功率: {len(completed)/total*100:.1f}%")
        print(f"   平均延迟: {statistics.mean(durations):.1f} ms")
        print(f"   最小延迟: {min(durations):.1f} ms")
        print(f"   最大延迟: {max(durations):.1f} ms")
        if len(durations) > 1:
            print(f"   标准差: {statistics.stdev(durations):.1f} ms")
    
    def test_emqx_status(self):
        """测试EMQX状态"""
        print("\n=== EMQX状态检查 ===")
        
        import requests
        try:
            # 检查EMQX管理API
            response = requests.get(f"http://{MQTT_BROKER}:8081/api/v5/stats", 
                                  auth=('admin', 'public'), timeout=5)
            if response.status_code == 200:
                stats = response.json()
                print("✅ EMQX管理API可访问")
                print(f"   连接数: {stats.get('connections', {}).get('count', 'N/A')}")
                print(f"   会话数: {stats.get('sessions', {}).get('count', 'N/A')}")
                print(f"   主题数: {stats.get('topics', {}).get('count', 'N/A')}")
                print(f"   订阅数: {stats.get('subscriptions', {}).get('count', 'N/A')}")
            else:
                print(f"⚠️ EMQX管理API响应异常: {response.status_code}")
        except Exception as e:
            print(f"❌ 无法访问EMQX管理API: {e}")
    
    def run_comprehensive_test(self):
        """运行综合性能测试"""
        print("=== MQTT性能综合测试 ===")
        print(f"目标: {MQTT_BROKER}:{MQTT_PORT}")
        
        # 创建MQTT客户端
        self.client = mqtt.Client(CLIENT_ID)
        self.client.on_connect = self.on_connect
        self.client.on_publish = self.on_publish
        
        try:
            # 连接到MQTT Broker
            self.client.connect(MQTT_BROKER, MQTT_PORT, 60)
            self.client.loop_start()
            
            time.sleep(2)  # 等待连接建立
            
            # 检查EMQX状态
            self.test_emqx_status()
            
            # 测试不同QoS级别
            self.test_qos_performance(0, 10)  # QoS 0
            self.test_qos_performance(1, 10)  # QoS 1
            self.test_qos_performance(2, 5)   # QoS 2 (较少消息，因为更慢)
            
            print("\n=== 性能建议 ===")
            print("1. 如果QoS 1延迟过高（>100ms），考虑优化EMQX配置")
            print("2. 如果QoS 0很快但QoS 1很慢，问题在于确认机制")
            print("3. 检查Docker容器资源限制和网络配置")
            print("4. 考虑使用优化的docker-compose配置")
            
        except Exception as e:
            print(f"❌ 测试异常: {e}")
        finally:
            if self.client:
                self.client.loop_stop()
                self.client.disconnect()

def main():
    tester = MQTTPerformanceTester()
    tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
