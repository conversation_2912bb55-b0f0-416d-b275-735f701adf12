﻿#pragma once

#include "Resource.h"

#include "ColorButton.h"

// CDialogBelt 对话框

class CDialogBelt : public CDialogEx
{
	DECLARE_DYNAMIC(CDialogBelt)

public:
	CDialogBelt(CPoint pt, CString sBelt, CWnd* pParent = NULL);   // 标准构造函数
	virtual ~CDialogBelt();

// 对话框数据
	enum { IDD = IDD_DIALOG_BELT };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持
	virtual BOOL PreTranslateMessage(MSG* pMsg);
	virtual void OnOK() {};
	virtual BOOL OnInitDialog();

	DECLARE_MESSAGE_MAP()
	afx_msg void OnTimer(UINT_PTR nIDEvent);
	afx_msg void OnBnClickedButton1();
	afx_msg void OnBnClickedButton2();
	afx_msg void OnBnClickedButton3();
	afx_msg void OnBnClickedButton4();
	afx_msg void OnBnClickedButton5();
	afx_msg void OnBnClickedButton6();
	afx_msg void OnBnClickedButton7();
	afx_msg void OnBnClickedButton8();
	afx_msg void OnBnClickedButton9();

public:
	void UpdateButtonState();

protected:
	CColorButton	m_btnReset;
	CColorButton	m_btnStart;
	CColorButton	m_btnPause;
	CColorButton	m_btnStop;

private:
	CPoint			m_pt;

	CString			m_sBelt;
};
