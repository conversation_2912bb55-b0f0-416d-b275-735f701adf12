#pragma once

#include "Module.h"

typedef struct _ROBOT_POINT_
{
	double	x;
	double	y;
	double	z;
	double	r;
	int		cf1;
	int		cf4;
	int		cf6;
	int		cfx;
	int		speed;
	int		confj;
}ROBOTPOINT, *PROBOTPOINT;

typedef struct _IMAG_INFO_
{
	unsigned char*	pBuffer;
	int				nWidth;
	int				nHeight;
	bool			bTranspose;
	bool			bFlipX;
	bool			bFlipY;
	CString			sCamera;
}IMAGINFO, *PIMAGINFO;

typedef struct _MAIN_BOARD_BACK_POS
{
	bool	bExistFlag;
	double	nPixelX;
	double	nPixelY;
}MAINBOARDBACKPOS, *PMAINBOARDBACKPOS;

typedef struct _PROC_INFO_
{
	CImageFlow*		pImageFlow;
	CImageFlow*		pImageFlowScanCode;
	CImageWindow*	pImageWnd;
	CString			sDir;
	CString			sName;

	int				nHeadIndex;

	bool			bScanCode2D;
	bool			bScanAll;
	CString			sCode2D;
	bool			bMesResult;

	bool			bBadCheck;
	bool			bBadCheckResult;

	bool			bTakePicOk;
	bool			bProcOk;
}PROCINFO, *PPROCINFO;

typedef struct _PROC_RESULT_
{
	CCvImage*		pImage;

	CImageWindow*	pWnd;

	CString			sDir;
	CString			sName;

	bool			bFlagOK;
}PROCRESULT, *PPROCRESULT;

class CRobot : public CModule
{
public:
	CRobot();
	virtual ~CRobot();

public:
	CString PickCylinderVacuumOn(int nIndex);
	CString PickCylinderBrokenVacuumOn(int nIndex);
	CString PickCylinderVacuumOff(int nIndex);
	CString PickCylinderVacuumStatus(int nIndex);

	CString PickCylinderOn(int nIndex);
	CString PickCylinderOff(int nIndex);
	CString PickCylinderStatus(int nIndex);

	CString GetPos(ROBOTPOINT &rbPoint);
	CString Move(ROBOTPOINT stRobPnt, bool bCtrlSpeed = true, bool bSafeCheck = true, CString *pStr = NULL, int nSpeedRate = -1);
	CString Move(CString sName, bool bCtrlSpeed = true, bool bSafeCheck = true, CString *pStr = NULL, int nSpeedRate = -1);
	CString Move(CString sName, double nX, double nY, double nZ, double nR, bool bCtrlSpeed = true, bool bSafeCheck = true, CString *pStr = NULL, int nSpeedRate = -1);
	CString Stop();
	CString IsInPos(ROBOTPOINT stRobPnt);
	CString IsInPos(CString sName);

	CString InBeltAInStatus();
	CString InBeltBInStatus();
	CString InBeltCInStatus();

	CString UpCameraLightOn();
	CString UpCameraLightOff();

	CString DnCameraLightOn();
	CString DnCameraLightOff();

	CString TrayCameraLightOn();
	CString TrayCameraLightOff();

	CString RegisterCamera();

	CString ProcPrepare(int nIndex);
	CString PushImageFlow(CImageFlow* pFlow, CImageFlow* pFlowScanCode, CImageWindow* pWnd, CString sDir, CString sName, CString sCode2D, bool bScanCode2D, bool bScanAll, int nHeadIndex, bool bBadCheck, int nIndex);
	CString ProcStart(int nIndex);
	CString ProcStop(int nIndex);
	CString PushImage(unsigned char *pBuff, int nWidth, int nHeight, bool bTranspose, bool bFlipX, bool bFlipY, CString sCamera, int nIndex);
	CString PushImageResult(CCvImage* pImage, CImageWindow* pWnd, CString sDir, CString sName, bool bFlagOK);

public:
	void SimulateTakePicturesUp();
	void SimulateTakePicturesDn();
	void SimulateTakePicturesMark();
	void SimulateTakePicturesTray();
	void CreateRobotPoint(CString sName);
	void LoadRobotPoint();
	void SaveRobotPoint(CString sName);
	void SaveRobotPoint();
	void LoadMainBoardBackPos();
	void SaveMainBoardBackPos();
	void SaveMainBoardBackPos(int nIndex);
	void SaveMainBoardBackPos(int nIndex, double nPixelX, double nPixelY, bool bExistFlag);
	void GetCurPos(ROBOTPOINT &stRobPnt);

public:
	vector<CString>				m_vecRobotPoint;
	map<CString, PROBOTPOINT>	m_mapRobotPoint;

	vector<MAINBOARDBACKPOS>	m_vecMainBoardBackPos;

	vector<IMAGINFO>			m_vImage[4];
	vector<PROCINFO>			m_vImageFlow[4];

	vector<PPROCRESULT>			m_vImageResult;

	unsigned char*				m_pImageBufferTray;
	unsigned char*				m_pImageBufferUp[4];
	unsigned char*				m_pImageBufferDn[4];
	
	bool						m_bRobotMoveFlag;

	CRITICAL_SECTION			m_cs;

	ROBOTPOINT					m_stCurRobPnt;
};
