{"version": "1.0", "description": "SCADA协议命令配置 - 基于v1.13协议标准", "commands": [{"type": "COMMAND_SN_DELIVER", "name": "下发SN", "description": "向设备下发当前工序需要处理的产品SN", "properties": ["sn", "production_model"], "response_required": true, "timeout_ms": 10000}, {"type": "COMMAND_BOP_DELIVER", "name": "转产/下发生产参数", "description": "设备自主清空旧配置，并应用新的转产生产参数", "properties": ["production_model", "profiles"], "response_required": true, "timeout_ms": 30000, "note": "profiles包含工艺参数配置文件列表"}, {"type": "COMMAND_PAUSE", "name": "暂停生产", "description": "指示设备暂停当前生产活动", "properties": [], "response_required": true, "timeout_ms": 10000}, {"type": "COMMAND_PRODUCTION", "name": "恢复生产", "description": "将设备从暂停状态恢复到生产状态或待机状态", "properties": [], "response_required": true, "timeout_ms": 10000}, {"type": "COMMAND_MQTT_CONFIG_DELIVER", "name": "下发MQTT配置参数", "description": "设置设备属性上报频率，设备需将此配置持久化到本地", "properties": ["prop_report_cycle"], "response_required": true, "timeout_ms": 10000, "note": "prop_report_cycle单位为毫秒，为0则不主动上报"}]}