﻿// DialogWarnCount.cpp : 实现文件
//

#include "stdafx.h"
#include "FStation.h"
#include "DialogWarnCount.h"
#include "afxdialogex.h"

// CDialogWarnCount 对话框

IMPLEMENT_DYNAMIC(CDialogWarnCount, CDialogEx)

CDialogWarnCount::CDialogWarnCount(CWnd* pParent /*=NULL*/)
	: CDialogEx(CDialogWarnCount::IDD, pParent)
{
}

CDialogWarnCount::~CDialogWarnCount()
{
}

void CDialogWarnCount::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_LIST1, m_list);
}

BEGIN_MESSAGE_MAP(CDialogWarnCount, CDialogEx)
	ON_WM_TIMER()
END_MESSAGE_MAP()

// CDialogWarnCount 消息处理程序

BOOL CDialogWarnCount::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	CenterWindow();

	m_imgList.Create(20, 20, TRUE, 2, 2);

	//Node
	LONG lStyle = 0;
	lStyle = GetWindowLong(m_list.m_hWnd, GWL_STYLE);//获取当前窗口样式
	lStyle &= ~LVS_TYPEMASK;//清除显位方式位
	lStyle |= LVS_REPORT;
	SetWindowLong(m_list.m_hWnd, GWL_STYLE, lStyle);

	DWORD dwStyle = 0;
	dwStyle = m_list.GetExtendedStyle();//获取当前窗口扩展样式
	dwStyle |= LVS_EX_FULLROWSELECT;//选中某行使整行高亮
	dwStyle |= LVS_EX_GRIDLINES;//网格线
	m_list.SetExtendedStyle(dwStyle);

	m_list.SetImageList(&m_imgList, LVSIL_SMALL);

	m_list.InsertColumn(0, "", LVCFMT_CENTER, 1);
	m_list.InsertColumn(1, "日期", LVCFMT_CENTER, 65);

	CString str;

	for (int i=0; i<99; i++)
	{
		str.Format("Err%03d", i + 1);
		m_list.InsertColumn(m_list.GetColumnCount(), str, LVCFMT_CENTER, 60);
	}

	SetTimer(1, 100, NULL);

	return TRUE;
}

CString CDialogWarnCount::Load()
{	
	CTime t = CTime::GetCurrentTime();

	CString str;

	map<CString, int> mListHead;
	vector<CString> vListHead;

	int nListLastIndex = 2; 

	for (int i=0; i<30; i++)
	{
		CTime tNow = t - CTimeSpan(i, 0, 0, 0);

		m_list.InsertItem(i, "");

		for (int j=0; j<101; j++)
		{
			m_list.SetItemText(i, j, "0");
		}

		str.Format("%04d%02d%02d", tNow.GetYear(), tNow.GetMonth(), tNow.GetDay());
		m_list.SetItemText(i, 1, str);

		CString sFile;
		sFile.Format("%s\\Log\\%04d-%02d-%02d\\Error.txt", GetModulePath().c_str(), tNow.GetYear(), tNow.GetMonth(), tNow.GetDay());

		CStdioFile sf;
		if (!sf.Open(sFile, CFile::modeCreate | CFile::modeNoTruncate | CFile::modeRead | CFile::shareDenyNone)) {
			continue;
		}

		sf.SeekToBegin();

		map<CString, int> mErrCnt;

		for (;;)
		{
			sf.ReadString(str);
			if (str.IsEmpty()) {
				break;
			}

			int nIndex = 0;
			nIndex = str.Find(" - ");

			if (nIndex > 0) {
				str = str.Mid(nIndex + 3);

				mErrCnt[str]++;
			}
		}

		sf.Flush();
		sf.Close();
		
		map<CString, int>::iterator it = mErrCnt.begin();
		for (; it!=mErrCnt.end(); it++)
		{
			bool bFlag = false;

			map<CString, int>::iterator itHead = mListHead.begin();
			for (; itHead!=mListHead.end(); itHead++)
			{
				if (itHead->first == it->first) {
					bFlag = true;
					break;
				}
			}

			if (!bFlag) {
				mListHead[it->first] = nListLastIndex++;
				vListHead.push_back(it->first);
			}

			str.Format("%d", it->second);
			m_list.SetItemText(i, mListHead[it->first], str);
		}
	}

	CString sText = "报警代码详解:\r\n";

	vector<CString>::iterator it  = vListHead.begin();
	for (; it!=vListHead.end(); it++)
	{
		str.Format("Err%03d : %s\r\n", mListHead[*it] - 1, *it);
		sText += str;
	}

	SetDlgItemText(IDC_EDIT1, sText);

	return "OK";
}

void CDialogWarnCount::OnTimer(UINT_PTR nIDEvent)
{
	KillTimer(nIDEvent);

	Load();

	CDialogEx::OnTimer(nIDEvent);
}
