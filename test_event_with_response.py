#!/usr/bin/env python3
"""
测试事件上报和平台响应的完整流程
"""

import socket
import json
import time

def send_test_event():
    """发送测试事件到MQTT网关"""
    
    # 构建测试事件消息
    event_message = {
        "services": [
            {
                "service_id": "EventService",
                "event_type": "EVENT_SN_IN",
                "properties": {
                    "sn": "RESPONSE_TEST_SN_001",
                    "production_model": "RESPONSE_TEST_MODEL",
                    "profiles": []
                },
                "event_time": "2025-07-31 13:00:00.123"
            }
        ]
    }
    
    try:
        # 记录开始时间
        start_time = time.time()
        
        # 连接到MQTT网关的Socket服务器
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect(('127.0.0.1', 8888))
        
        # 发送事件消息
        message_json = json.dumps(event_message, ensure_ascii=False)
        sock.send(message_json.encode('utf-8'))
        
        # 记录结束时间
        end_time = time.time()
        duration_ms = (end_time - start_time) * 1000
        
        print(f"✅ 事件发送成功")
        print(f"   事件类型: EVENT_SN_IN")
        print(f"   SN: RESPONSE_TEST_SN_001")
        print(f"   发送耗时: {duration_ms:.2f} ms")
        print(f"   等待SCADA平台响应...")
        
        sock.close()
        return True
        
    except Exception as e:
        print(f"❌ 发送事件失败: {e}")
        return False

def main():
    print("=" * 60)
    print("测试事件上报和平台响应流程")
    print("=" * 60)
    print("1. 发送事件到MQTT网关")
    print("2. MQTT网关异步发布到SCADA平台")
    print("3. SCADA平台模拟器自动响应")
    print("4. MQTT网关接收响应并转发给FStation")
    print()
    
    # 发送测试事件
    if send_test_event():
        print("\n📋 请观察以下日志:")
        print("   - MQTT网关: 事件处理和异步发布")
        print("   - SCADA平台模拟器: 接收事件和发送响应")
        print("   - MQTT网关: 接收响应和转发")
        print("\n⏱️ 整个流程应该在1秒内完成")

if __name__ == "__main__":
    main()
