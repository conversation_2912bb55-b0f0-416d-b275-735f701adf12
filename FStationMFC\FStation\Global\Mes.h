﻿#pragma once

#include "Device.h"
using namespace yzBase;

#include "string.h"

class CBASE64
{
public:

	// 执行BASE64编码操作
	static std::string encode(const std::string& str);

	// 执行BASE64解码操作
	static std::string decode(const std::string& str);

private:

	// 分组编码
	static int __encode(unsigned char* pDest, const unsigned char* pSrc, size_t nSrcLen);

	// 分组解码
	static int __decode(unsigned char* pDest, const unsigned char* pSrc, size_t nSrcLen);

private:

	// 编解码转换表
	static unsigned char s_encTable[];
	static unsigned char s_decTable[];
};

class CMes
{
public:
	CMes();
	virtual ~CMes();

public:
	static bool HttpPost(CString sIp, int nPort, CString sCode, CString sUserName, CString sPassword, CString sFactory, CString *pStrRecv);
	static bool HttpPostPassStation(CString sIp, int nPort, CString sCode, CString sLineId, CString sOper, CString sUserName, CString sPassword, CString sFactory, CString *pStrRecv);
	static bool HttpPostFixture(CString sIp, int nPort, CString sFixtureCode, CString sPcbaCode, CString sLineId, CString sStation, CString sOper, CString sUserName, CString sPassword, CString sFactory, CString *pStrRecv);
	static bool HttpPostSwitchProduct(CString sIp, int nPort, CString sPcbaCode, CString sUserName, CString sPassword, CString sFactory, CString *pStrRecv);
	static bool HttpPostUploadParam(CString sIp, int nPort, CString sParamName, CString sParamValue, CString sUserName, CString sPassword, CString sFactory, CString *pStrRecv);
};
