﻿#include "stdafx.h"
#include "Global.h"

const COLORREF		g_clrLogLevel[emLogLevelSum] = { RGB(255, 255, 255),  RGB(255, 255, 0), RGB(255, 0, 0) };
const CString		g_strLogLevel[emLogLevelSum] = { "消息", "警告", "错误" };

const CString		g_strRunStatus[emRunStatusSum] = { "待复位", "复位中", "空闲", "运行中", "暂停", "报警" };

EnumRunStatus		g_emRunStatus = emRunStatusPreReset;

bool				g_bWaitFlag = false;

bool				g_bWarn = false;

bool				g_bWarnFlag = false;

int					g_nPermissionLevel = emPremissionLevelEngineer;

int					g_nWarnTimes = 0;

int					g_nPicIndex = 0;

HWND				g_hMainWnd = NULL;

HINSTANCE			g_hInstDebug = NULL;

CDbInfo*			g_pInfo = NULL;

CControlBase*		g_pControl = NULL;

CCameraBase*		g_pCamera = NULL;

CProductionBase*	g_pProduction = NULL;

CDat*				g_pDatLog = NULL;

CDat*				g_pDatImage = NULL;

CSockBase*			g_pSockRobot = NULL;

CSockBase*			g_pSockAgv = NULL;

CImageWindow*		g_pImageWndUp = NULL;

CImageWindow*		g_pImageWndDn = NULL;

CImageWindow*		g_pImageWndTray = NULL;

CBelt*				g_pBeltA = NULL;

CBelt*				g_pBeltB = NULL;

CFixture*			g_pFixture = NULL;

CMachine*			g_pMachine = NULL;

CRobot*				g_pRobot = NULL;

CTray*				g_pTray = NULL;

HANDLE				g_hEventProcStart[4] = { NULL };

HANDLE				g_hEventProcStop[4] = { NULL };

CImageCalibrate*	g_pImageCalibrateTray = NULL;

CImageFlow*			g_pImageFlowCalibrateTray = NULL;

CImageCalibrate*	g_pImageCalibrateUp = NULL;

CImageFlow*			g_pImageFlowCalibrateUp = NULL;

CImageFlow*			g_pImageFlowCalibrateBoardUp = NULL;

CImageCalibrate*	g_pImageCalibrateDn = NULL;

CImageFlow*			g_pImageFlowCalibrateDn = NULL;

CImageFlow*			g_pImageFlowCalibrateBoardDn = NULL;

CImageFlow*			g_pImageFlowUp[4] = { NULL };

CImageFlow*			g_pImageFlowTray = NULL;

CImageFlow*			g_pImageFlowDn[4] = { NULL };

CImageFlow*			g_pImageFlowMark[4] = { NULL };

CImageFlow*			g_pImageFlowRobot = NULL;

CImageFlow*			g_pImageFlowScanCodeMainBoard[4] = { NULL };

CImageFlow*			g_pImageFlowScanCodeFixture = NULL;

CHeightSensor*		g_pHeightSensorL = NULL;

CHeightSensor*		g_pHeightSensorR = NULL;

vector<USER>		g_vUser;

bool				g_bAuto = true;

bool				g_bExit = false;