#!/usr/bin/env python3
"""
测试异步事件上报性能
"""

import socket
import json
import time

def send_test_event():
    """发送测试事件到MQTT网关"""
    
    # 构建测试事件消息
    event_message = {
        "services": [
            {
                "service_id": "EventService",
                "event_type": "EVENT_SN_IN",
                "properties": {
                    "sn": "ASYNC_TEST_SN_001",
                    "production_model": "ASYNC_TEST_MODEL",
                    "profiles": []
                },
                "event_time": "2025-07-31 12:51:00.123"
            }
        ]
    }
    
    try:
        # 记录开始时间
        start_time = time.time()
        
        # 连接到MQTT网关的Socket服务器
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect(('127.0.0.1', 8888))
        
        # 发送事件消息
        message_json = json.dumps(event_message, ensure_ascii=False)
        sock.send(message_json.encode('utf-8'))
        
        # 记录结束时间
        end_time = time.time()
        duration_ms = (end_time - start_time) * 1000
        
        print(f"✅ 异步事件发送成功")
        print(f"   事件类型: EVENT_SN_IN")
        print(f"   SN: ASYNC_TEST_SN_001")
        print(f"   发送耗时: {duration_ms:.2f} ms")
        print(f"   模式: 异步（不等待QoS 2确认）")
        
        sock.close()
        
    except Exception as e:
        print(f"❌ 发送事件失败: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("测试异步事件上报性能")
    print("=" * 60)
    
    # 发送多个事件测试性能
    for i in range(3):
        print(f"\n第 {i+1} 次测试:")
        send_test_event()
        time.sleep(1)  # 间隔1秒
