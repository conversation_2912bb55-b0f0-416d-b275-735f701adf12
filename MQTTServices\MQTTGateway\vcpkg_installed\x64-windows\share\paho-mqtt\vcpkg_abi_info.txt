cmake 3.30.1
features core
fix-ODR-libuuid-linux.patch ebaa47ddc8e5c0cd946b724dc55885756616c42e8ed5ed44c09a52c7611de646
fix-unresolvedsymbol-arm.patch d99ebb089a988de02dd62e5accd1a1e2047790083da62a32b2f6e2b73ef8c943
openssl 253bb4bf9b62871c6de32cf1f4c52f2ae7a93f34adc6df60993705d9b45216d6
portfile.cmake 023711d7b6ac4fe89374121f6521d49f842f9cec1a2f2bd2fa82b94f2f8b78f8
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
powershell 7.2.24
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
vcpkg-cmake 8112fdacac7427b4feea02a5a660bd2fb97e5b898a970d195ff6370d43a794fb
vcpkg-cmake-config 8b1ae8f4be6cff022eadef50d38cffca5e75f23d30fc7e280b912b164b7e8ea1
vcpkg.json c8a5f3df2f074ff946aebb7138397ee4d5531374c2a4b7023fc4104ff7992547
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_copy_tools 3d45ff761bddbabe8923b52330168dc3abd295fa469d3f2e47cb14dce85332d5
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
vcpkg_replace_string b450deb79207478b37119743e00808ebc42de0628e7b98c14ab24728bd5c78b8
