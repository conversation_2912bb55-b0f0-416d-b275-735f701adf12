# FStation数据流和通信机制详解

## 概述

FStation作为工业4.0自动化系统，具有复杂的数据流和多种通信机制。系统集成了MQTT网关、Socket通信、MES接口、本地数据存储等多种数据交换方式，实现设备与上位系统、云平台的无缝对接。

## 整体数据流架构

### 1. 数据流总体架构

```
FStation数据流架构
├── 本地数据层
│   ├── 实时参数数据
│   ├── 生产数据统计
│   ├── 图像数据存储
│   └── 历史日志记录
├── 设备通信层
│   ├── PLC/IO模块通信
│   ├── 机器人控制器通信
│   ├── 相机系统通信
│   └── 传感器数据采集
├── 本地网络层
│   ├── Socket服务器
│   ├── TCP/IP通信
│   └── 本地数据交换
├── 工厂网络层
│   ├── MES系统接口
│   ├── SCADA平台对接
│   └── 数据库同步
└── 云平台层
    ├── MQTT消息推送
    ├── 物联网数据上报
    └── 远程监控支持
```

### 2. 数据类型分类

```cpp
// 数据类型枚举
enum DataType {
    // 实时状态数据
    REALTIME_STATUS,        // 设备运行状态
    REALTIME_SENSOR,        // 传感器数据
    REALTIME_POSITION,      // 位置信息
    
    // 生产过程数据
    PRODUCTION_COUNT,       // 生产计数
    PRODUCTION_QUALITY,     // 质量数据
    PRODUCTION_TIMING,      // 时间统计
    
    // 配置参数数据
    CONFIG_MACHINE,         // 机器参数
    CONFIG_PROCESS,         // 工艺参数
    CONFIG_VISION,          // 视觉参数
    
    // 报警和事件数据
    ALARM_SYSTEM,           // 系统报警
    EVENT_OPERATION,        // 操作事件
    EVENT_MAINTENANCE,      // 维护事件
    
    // 图像和文件数据
    IMAGE_VISION,           // 视觉图像
    FILE_LOG,               // 日志文件
    FILE_RECIPE            // 配方文件
};
```

## 本地数据管理系统

### 1. 数据采集接口

```cpp
// 全局数据采集映射表
typedef struct _DATA_COLLECTION_ {
    CString sMachineType;    // 设备类型
    CString sDataCode;       // 数据编码
    CString sSectionCode;    // 段码标识
    CString sDataSource;     // 数据源
    bool    bMachineExisted; // 设备存在标志
    CData*  pDat;           // 数据指针
} DATACOLLECTION, *PDATACOLLECTION;

// 静态数据集合
static map<CString, DATACOLLECTION> m_mapDataCollection;
static CRITICAL_SECTION m_csDataCollection;

// 数据采集创建接口
static void CreateDataCollection(
    CString sName,           // 数据名称
    CString sMachineType,    // 设备类型
    CString sDataCode,       // 数据编码
    CString sSectionCode,    // 段码
    CString sDataSource,     // 数据源
    bool bMachineExisted,    // 设备存在标志
    int/double/CString nInitValue  // 初始值
);
```

### 2. 数据更新机制

```cpp
// 数据更新接口
class CDat {
public:
    // 实时数值更新
    static void UpdateValue(CString sName, int nNewValue);
    static void UpdateValue(CString sName, double nNewValue);
    static void UpdateValue(CString sName, CString sValue);
    
    // 自动计数更新
    static void IncreaseValue(CString sName);
    static void UpdateValueEveryPiece();      // 每件产品更新
    static void UpdateValueEveryHour();       // 每小时更新
    
    // 状态控制更新
    static void UpdateValueManualOperation(int nStatus);
    static void UpdateValueErrorStart();      // 错误开始
    static void UpdateValueErrorEnd();        // 错误结束
    
    // 批量更新
    static void Update();                      // 全部更新
};

// 使用示例
CreateDataCollection("生产总数", "FStation", "PROD_COUNT", "MAIN", "LOCAL", true, 0);
CreateDataCollection("合格率", "FStation", "PASS_RATE", "QUALITY", "LOCAL", true, 0.0);
CreateDataCollection("设备状态", "FStation", "MACHINE_STATUS", "STATUS", "LOCAL", true, "空闲");

UpdateValue("生产总数", nProductionCount);
UpdateValue("合格率", dPassRate);
UpdateValue("设备状态", "运行中");
```

### 3. 数据存储机制

```cpp
// 数据文件管理
class CDat {
public:
    // 目录管理
    void GenDateDir();                    // 生成日期目录
    void ClearOutDateDir(int nOutDay);    // 清理过期目录
    CString GetDataDir();                 // 获取数据目录
    
    // 日志管理
    void UpdateLog(CString strLog);           // 普通日志
    void UpdateErrorLog(CString strErr);      // 错误日志
    void UpdateParameterLog(CString strLog);  // 参数日志
    
    // 图像存储
    void SaveImage(CCvImage* pImage, CString sDirName, 
                   CString sInfo, double nZoomRate, bool bInit = false);
    
    // 数据持久化
    static void ClearOutdateDataCollection(int nOutDay);
    static bool DeleteDir(CString dirName);
};

// 文件存储结构
数据根目录/
├── YYYY-MM-DD/              // 日期目录
│   ├── Log/                 // 日志文件
│   │   ├── Normal.log       // 普通日志
│   │   ├── Error.log        // 错误日志
│   │   └── Parameter.log    // 参数日志
│   ├── Image/               // 图像文件
│   │   ├── Up/              // 上相机图像
│   │   ├── Dn/              // 下相机图像
│   │   └── Tray/            // TRAY相机图像
│   └── Data/                // 数据文件
│       ├── Production.csv   // 生产数据
│       ├── Quality.csv      // 质量数据
│       └── Status.csv       // 状态数据
```

## MES系统接口

### 1. MES通信配置

```cpp
// MES系统参数配置
class CMachine : public CModule {
    // MES功能控制
    "MES功能启用":           false           // MES系统总开关
    "MES服务器地址":         "**************" // MES服务器IP
    "MES工厂":              "CASMT01"        // 工厂编码
    "MES用户名":            "PT11608"        // 登录用户名
    "MES密码":              "SMTPT11608"     // 登录密码
    "MES响应超时时间":       15000ms          // 通信超时时间
};
```

### 2. MES数据交换

```cpp
// MES数据交换流程
1. 生产指令接收
   - 接收生产订单信息
   - 获取工艺参数配置
   - 下载产品配方数据

2. 生产数据上报
   - 实时生产进度报告
   - 质量检测结果上传
   - 设备状态信息同步

3. 异常事件通知
   - 设备故障报警
   - 质量异常通知
   - 维护需求上报

// MES接口实现
class CMESInterface {
public:
    bool Connect();                           // 连接MES服务器
    bool SendProductionData(ProductionData& data);  // 发送生产数据
    bool ReceiveWorkOrder(WorkOrder& order);        // 接收工单
    bool ReportAlarm(AlarmInfo& alarm);            // 报告异常
    void Disconnect();                             // 断开连接
};
```

## MQTT网关通信

### 1. MQTT架构设计

```cpp
// MQTT网关架构
MQTTGateway模块
├── 配置管理 (ConfigManager)
│   ├── MQTT连接配置
│   ├── 设备认证信息
│   └── 数据点映射配置
├── 数据点管理 (DataPointManager)
│   ├── 数据点定义
│   ├── 数据验证
│   └── 数据转换
├── MQTT客户端
│   ├── 连接管理
│   ├── 消息发布
│   └── 消息订阅
└── Socket服务器
    ├── 本地通信接口
    ├── 数据请求处理
    └── 实时数据推送
```

### 2. MQTT配置管理

```cpp
// ConfigManager配置项
class ConfigManager {
public:
    // 设备基本信息
    QString deviceId;           // 设备ID
    QString deviceKey;          // 设备密钥
    QString deviceName;         // 设备名称
    
    // MQTT连接配置
    QString brokerHost;         // MQTT Broker地址
    int brokerPort;             // MQTT端口
    QString username;           // 用户名
    QString password;           // 密码
    int keepAliveInterval;      // 保活间隔
    
    // TLS安全配置
    bool enableTLS;             // 启用TLS
    QString caCertFile;         // CA证书文件
    QString clientCertFile;     // 客户端证书
    QString clientKeyFile;      // 客户端私钥
    
    // 数据上报配置
    int defaultReportPeriod;    // 默认报告周期
    int dataRequestTimeout;     // 数据请求超时
    
    // Socket服务配置
    int socketPort;             // Socket监听端口
    
    // 验证配置
    QStringList validationErrors;
    bool validateConfiguration();
};
```

### 3. 数据点管理

```cpp
// DataPointManager数据点定义
class DataPointManager {
public:
    struct DataPointDefinition {
        std::string id;              // 数据点ID
        std::string name;            // 数据点名称
        std::string dataType;        // 数据类型 (bool/int/double/string)
        std::string description;     // 描述信息
        bool readOnly;               // 只读标志
        double minValue;             // 最小值
        double maxValue;             // 最大值
        int maxLength;               // 最大长度(字符串)
        int reportPeriod;            // 上报周期
    };
    
    struct ValidationResult {
        bool isValid;                // 验证结果
        std::string errorMessage;    // 错误信息
    };
    
    // 数据点操作
    bool loadDataPointsConfig(const std::string& filename);
    ValidationResult validateDataPoint(const std::string& pointId, const std::string& value);
    bool updateDataPointValue(const std::string& pointId, const std::string& value);
    std::string getDataPointValue(const std::string& pointId);
    std::vector<std::string> getAllDataPointIds();
};

// 数据点配置示例 (data_points_config.json)
{
    "dataPoints": [
        {
            "id": "machine_status",
            "name": "设备状态",
            "dataType": "string",
            "description": "设备当前运行状态",
            "readOnly": true,
            "reportPeriod": 5000
        },
        {
            "id": "production_count",
            "name": "生产计数",
            "dataType": "int",
            "description": "当前生产数量",
            "readOnly": true,
            "minValue": 0,
            "maxValue": 999999,
            "reportPeriod": 10000
        },
        {
            "id": "quality_rate",
            "name": "良品率",
            "dataType": "double",
            "description": "产品合格率",
            "readOnly": true,
            "minValue": 0.0,
            "maxValue": 100.0,
            "reportPeriod": 30000
        }
    ]
}
```

### 4. MQTT消息格式

```cpp
// MQTT Topic结构
设备上报Topic: /device/{deviceId}/property/post
设备订阅Topic: /device/{deviceId}/property/set

// 消息格式 - 数据上报
{
    "id": "message_id",
    "timestamp": 1640995200000,
    "method": "thing.event.property.post",
    "params": {
        "machine_status": {
            "value": "运行中",
            "time": 1640995200000
        },
        "production_count": {
            "value": 1250,
            "time": 1640995200000
        },
        "quality_rate": {
            "value": 98.5,
            "time": 1640995200000
        }
    }
}

// 消息格式 - 属性设置
{
    "id": "message_id",
    "timestamp": 1640995200000,
    "method": "thing.service.property.set",
    "params": {
        "target_speed": 85,
        "auto_mode": true
    }
}

// 消息格式 - 设备响应
{
    "id": "message_id",
    "code": 200,
    "data": {
        "target_speed": 85,
        "auto_mode": true
    },
    "message": "success"
}
```

## Socket通信机制

### 1. Socket服务器架构

```cpp
// Socket服务器设计
class MQTTGateway {
private:
    SOCKET m_listenSocket;      // 监听Socket
    int m_port;                 // 监听端口
    std::thread m_socketThread; // Socket线程
    
public:
    bool startSocketServer();   // 启动Socket服务
    void stopSocketServer();    // 停止Socket服务
    void handleClient(SOCKET clientSocket);  // 处理客户端连接
    void processMessage(const std::string& message); // 处理消息
};
```

### 2. Socket消息协议

```cpp
// Socket消息格式
{
    "action": "get_data_point",     // 操作类型
    "point_id": "machine_status",   // 数据点ID
    "value": "",                    // 数据值(set操作)
    "timestamp": 1640995200000      // 时间戳
}

// 操作类型定义
"get_data_point"     - 获取数据点值
"set_data_point"     - 设置数据点值
"get_all_points"     - 获取所有数据点
"subscribe"          - 订阅数据变化
"unsubscribe"        - 取消订阅

// 响应格式
{
    "status": "success",            // 状态: success/error
    "data": {
        "point_id": "machine_status",
        "value": "运行中",
        "timestamp": 1640995200000
    },
    "message": "操作成功"           // 消息描述
}
```

### 3. Socket错误处理

```cpp
// Socket错误处理机制
class MQTTGateway {
private:
    void handleSocketError(int error) {
        QString errorDescription;
        switch (error) {
            case WSAECONNABORTED:
                errorDescription = "连接被软件中止";
                break;
            case WSAECONNREFUSED:
                errorDescription = "连接被拒绝";
                break;
            case WSAENETDOWN:
                errorDescription = "网络不可用";
                break;
            case WSAENETUNREACH:
                errorDescription = "网络不可达";
                break;
            case WSAETIMEDOUT:
                errorDescription = "连接超时";
                break;
            default:
                errorDescription = QString("未知错误 (%1)").arg(error);
        }
        qCWarning(mqttGateway) << "Socket错误:" << errorDescription;
    }
};
```

## 图像数据流

### 1. 图像采集流程

```cpp
// 图像采集和处理流程
相机触发 → 图像采集 → 数据传输 → 图像处理 → 结果存储 → 数据上报

// 图像数据结构
struct ImageData {
    unsigned char* pBuffer;     // 图像缓存
    int nWidth;                 // 图像宽度
    int nHeight;                // 图像高度
    CString sCameraName;        // 相机名称
    CString sImageType;         // 图像类型
    DWORD dwTimestamp;          // 时间戳
};

// 图像处理回调
typedef void (*CAMERACAPTUREFUNC)(unsigned char *pBuff, int nWidth, int nHeight, 
                                  bool bTranspose, bool bFlipX, bool bFlipY, 
                                  CString sCamera, int nIndex);

// 相机注册和图像处理
EXCUTE_RETURN(g_pCamera->Register("Up", (CAMERACAPTUREFUNC)(&OnCaptureUp), NULL));
EXCUTE_RETURN(g_pCamera->Register("Dn", (CAMERACAPTUREFUNC)(&OnCaptureDn), NULL));
EXCUTE_RETURN(g_pCamera->Register("Tray", (CAMERACAPTUREFUNC)(&OnCaptureTray), NULL));
```

### 2. 图像存储管理

```cpp
// 图像存储系统
class CDat {
public:
    void SaveImage(CCvImage* pImage, CString sDirName, CString sInfo, 
                   double nZoomRate, bool bInit = false);
};

// 图像存储配置
"图片存储大小":        0.35        // 图像压缩比例
"图片存储格式":        "jpg"       // 存储格式
"历史记录天数":        7           // 保存天数

// 图像存储结构
Image/
├── YYYY-MM-DD/
│   ├── Up/                      // 上相机图像
│   │   ├── 吸嘴1_HHMMSS.jpg
│   │   ├── 吸嘴2_HHMMSS.jpg
│   │   └── ...
│   ├── Dn/                      // 下相机图像
│   │   ├── 元件1_HHMMSS.jpg
│   │   └── ...
│   └── Tray/                    // TRAY相机图像
│       ├── 托盘1_HHMMSS.jpg
│       └── ...
```

## 实时数据同步

### 1. 数据同步机制

```cpp
// 全局数据对象管理
extern CMachine*      g_pMachine;      // 机器参数
extern CRobot*        g_pRobot;        // 机器人参数
extern CTray*         g_pTray;         // 托盘参数
extern CFixture*      g_pFixture;      // 治具参数
extern CBelt*         g_pBeltA;        // A轨参数
extern CBelt*         g_pBeltB;        // B轨参数

// 跨模块数据同步
if (VAR_ROBOT_B("TRAY盘进料标志")) {
    VAR_TRAY("满TRAY盘进料标志") = true;
    // 同步到MQTT数据点
    UpdateMQTTDataPoint("tray_feed_flag", "true");
}

// 参数变化监控
void OnParameterChanged(CString sName, CData* pData) {
    // 记录参数变化日志
    g_pDatLog->UpdateParameterLog(sName + " 变更为: " + pData->S());
    
    // 同步到MQTT数据点
    if (g_pMQTTGateway) {
        g_pMQTTGateway->updateDataPoint(sName.GetBuffer(), pData->S().GetBuffer());
    }
    
    // 通知MES系统
    if (VAR_MACHINE_B("MES功能启用")) {
        g_pMESInterface->ReportParameterChange(sName, pData->S());
    }
}
```

### 2. 数据验证和转换

```cpp
// 数据验证机制
class DataValidator {
public:
    struct ValidationRule {
        CString sName;              // 参数名称
        CString sType;              // 数据类型
        double dMin, dMax;          // 数值范围
        int nMaxLength;             // 字符串最大长度
        bool bRequired;             // 是否必填
    };
    
    bool ValidateData(CString sName, CString sValue, ValidationRule& rule) {
        if (rule.bRequired && sValue.IsEmpty()) {
            return false;
        }
        
        if (rule.sType == "int") {
            int nValue = _ttoi(sValue);
            return (nValue >= rule.dMin && nValue <= rule.dMax);
        }
        else if (rule.sType == "double") {
            double dValue = _ttof(sValue);
            return (dValue >= rule.dMin && dValue <= rule.dMax);
        }
        else if (rule.sType == "string") {
            return (sValue.GetLength() <= rule.nMaxLength);
        }
        
        return true;
    }
};
```

## 网络安全和认证

### 1. 设备认证

```cpp
// 设备认证信息
class DeviceAuth {
public:
    CString sDeviceId;          // 设备唯一标识
    CString sDeviceKey;         // 设备密钥
    CString sDeviceName;        // 设备名称
    CString sFirmwareVersion;   // 固件版本
    CString sHardwareVersion;   // 硬件版本
    
    // 认证方法
    bool AuthenticateDevice();
    CString GenerateToken();
    bool ValidateToken(CString sToken);
};
```

### 2. 通信加密

```cpp
// TLS/SSL配置
struct TLSConfig {
    bool bEnabled;              // 启用TLS
    CString sCaCertFile;        // CA证书
    CString sClientCertFile;    // 客户端证书
    CString sClientKeyFile;     // 客户端私钥
    CString sProtocolVersion;   // 协议版本
};

// 消息加密
class MessageEncryption {
public:
    CString EncryptMessage(CString sMessage, CString sKey);
    CString DecryptMessage(CString sEncryptedMessage, CString sKey);
    bool VerifySignature(CString sMessage, CString sSignature);
};
```

## 性能优化和监控

### 1. 数据缓存机制

```cpp
// 数据缓存管理
class DataCache {
private:
    map<CString, CacheItem> m_mapCache;
    CRITICAL_SECTION m_csCache;
    
    struct CacheItem {
        CString sValue;         // 缓存值
        DWORD dwTimestamp;      // 时间戳
        int nAccessCount;       // 访问计数
        bool bDirty;           // 脏数据标志
    };
    
public:
    void SetValue(CString sKey, CString sValue);
    CString GetValue(CString sKey);
    void FlushDirtyData();      // 刷新脏数据
    void ClearExpiredData();    // 清理过期数据
};
```

### 2. 性能监控

```cpp
// 性能监控指标
struct PerformanceMetrics {
    // 数据传输性能
    int nMQTTMessagesSent;      // MQTT发送消息数
    int nMQTTMessagesReceived;  // MQTT接收消息数
    double dDataTransferRate;   // 数据传输速率
    
    // 系统性能
    double dCPUUsage;           // CPU使用率
    double dMemoryUsage;        // 内存使用率
    double dDiskUsage;          // 磁盘使用率
    
    // 网络性能
    int nNetworkLatency;        // 网络延迟
    double dNetworkThroughput;  // 网络吞吐量
    int nConnectionErrors;      // 连接错误数
};

// 性能监控类
class PerformanceMonitor {
public:
    void StartMonitoring();
    void StopMonitoring();
    PerformanceMetrics GetMetrics();
    void LogPerformanceData();
};
```

## 总结

FStation的数据流和通信机制具有以下特点：

### 架构优势

1. **多层次数据管理**：从本地到云端的完整数据链路
2. **多协议支持**：MQTT、Socket、HTTP等多种通信协议
3. **实时性保证**：毫秒级数据更新和响应
4. **可扩展性**：模块化设计便于功能扩展
5. **安全性**：多重认证和加密保护

### 技术特色

1. **统一数据模型**：标准化的数据采集和存储格式
2. **智能缓存**：高效的数据缓存和同步机制
3. **故障恢复**：完善的通信故障检测和恢复
4. **性能优化**：多线程并发和缓存优化
5. **监控支持**：全方位的性能监控和诊断

该数据流和通信架构为工业4.0智能制造提供了完整的数据基础设施，支持设备的数字化转型和智能化升级。 