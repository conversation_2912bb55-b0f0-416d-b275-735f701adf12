#pragma once

void OnRecv(BYTE *pBuff, int nCount, void *pSender);

class CHeightSensor : public CSerialBase
{
public:
	CHeightSensor(CString sName);
	virtual ~CHeightSensor();

public:
	void Load();

public:
	CString Init();
	CString IsConnected();
	CString GetHeight(double &nHeight);

public:
	CString m_sName;

	int		m_nCom;
	int		m_nBaud;

	bool	m_bRecvFlag;
	bool	m_bRecvOkFlag;

	CString m_sRecv;
};