{"configurations": [{"name": "Win32-Debug", "includePath": ["${workspaceFolder}/**", "${workspaceFolder}/FStation/**", "${workspaceFolder}/FStation/Global/**", "${workspaceFolder}/FStation/Logic/**", "${workspaceFolder}/FStation/Module/**", "C:/Program Files (x86)/Microsoft Visual Studio 10.0/VC/include", "C:/Program Files (x86)/Microsoft Visual Studio 10.0/VC/atlmfc/include", "C:/Program Files (x86)/Microsoft SDKs/Windows/v7.0A/Include", "C:/Program Files (x86)/Microsoft SDKs/Windows/v7.0A/Include/gl"], "defines": ["_DEBUG", "WIN32", "_WINDOWS", "_AFXDLL", "_UNICODE", "UNICODE", "_WIN32_WINNT=0x0601", "WINVER=0x0601", "_CRT_SECURE_NO_WARNINGS", "_AFXEXT"], "windowsSdkVersion": "7.0.7600.0", "compilerPath": "C:/Program Files (x86)/Microsoft Visual Studio 10.0/VC/bin/cl.exe", "cStandard": "c11", "cppStandard": "c++14", "intelliSenseMode": "msvc-x86", "browse": {"path": ["${workspaceFolder}/**", "C:/Program Files (x86)/Microsoft Visual Studio 10.0/VC/include", "C:/Program Files (x86)/Microsoft Visual Studio 10.0/VC/atlmfc/include", "C:/Program Files (x86)/Microsoft SDKs/Windows/v7.0A/Include"], "limitSymbolsToIncludedHeaders": false, "databaseFilename": "${workspaceFolder}/.vscode/browse.vc.db"}}, {"name": "Win32-Release", "includePath": ["${workspaceFolder}/**", "${workspaceFolder}/FStation/**", "${workspaceFolder}/FStation/Global/**", "${workspaceFolder}/FStation/Logic/**", "${workspaceFolder}/FStation/Module/**", "C:/Program Files (x86)/Microsoft Visual Studio 10.0/VC/include", "C:/Program Files (x86)/Microsoft Visual Studio 10.0/VC/atlmfc/include", "C:/Program Files (x86)/Microsoft SDKs/Windows/v7.0A/Include", "C:/Program Files (x86)/Microsoft SDKs/Windows/v7.0A/Include/gl"], "defines": ["NDEBUG", "WIN32", "_WINDOWS", "_AFXDLL", "_UNICODE", "UNICODE", "_WIN32_WINNT=0x0601", "WINVER=0x0601", "_CRT_SECURE_NO_WARNINGS", "_AFXEXT"], "windowsSdkVersion": "7.0.7600.0", "compilerPath": "C:/Program Files (x86)/Microsoft Visual Studio 10.0/VC/bin/cl.exe", "cStandard": "c11", "cppStandard": "c++14", "intelliSenseMode": "msvc-x86", "browse": {"path": ["${workspaceFolder}/**", "C:/Program Files (x86)/Microsoft Visual Studio 10.0/VC/include", "C:/Program Files (x86)/Microsoft Visual Studio 10.0/VC/atlmfc/include", "C:/Program Files (x86)/Microsoft SDKs/Windows/v7.0A/Include"], "limitSymbolsToIncludedHeaders": false, "databaseFilename": "${workspaceFolder}/.vscode/browse.vc.db"}}], "version": 4}