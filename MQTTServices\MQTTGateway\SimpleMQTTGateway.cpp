#include "SimpleMQTTGateway.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDebug>
#include <QCoreApplication>
#include <chrono>
#include <thread>

Q_LOGGING_CATEGORY(simpleMqttGateway, "SimpleMQTTGateway")

SimpleMQTTGateway::SimpleMQTTGateway(QObject* parent)
    : QObject(parent), m_reportTimer(nullptr), m_commandManager(std::make_unique<CommandManager>(this)) {

    // 连接命令管理器信号
    connect(m_commandManager.get(), &CommandManager::CommandTimeout,
            this, &SimpleMQTTGateway::HandleCommandTimeout);
}

SimpleMQTTGateway::~SimpleMQTTGateway() {
    Stop();
}

bool SimpleMQTTGateway::Initialize(const std::string& configDir) {
    qCInfo(simpleMqttGateway) << "开始初始化极简MQTT网关...";
    
    // 1. 加载所有配置
    if (!m_configManager.LoadAllConfigs(configDir)) {
        qCCritical(simpleMqttGateway) << "配置加载失败";
        return false;
    }
    
    // 2. 初始化Topic管理器
    if (!m_topicManager.Initialize(m_configManager)) {
        qCCritical(simpleMqttGateway) << "Topic管理器初始化失败";
        return false;
    }

    // 2.5. 初始化命令管理器
    // 尝试多个可能的配置文件路径
    std::vector<std::string> possibleCommandPaths = {
        configDir + "/commands.json",
        "config/commands.json",
        "../config/commands.json",
        "./config/commands.json",
        "build/MQTTGateway/Debug/config/commands.json"
    };

    bool commandConfigLoaded = false;
    for (const auto& path : possibleCommandPaths) {
        if (m_commandManager->LoadCommandConfigs(path)) {
            qCInfo(simpleMqttGateway) << "命令配置加载成功，路径:" << QString::fromStdString(path);
            commandConfigLoaded = true;
            break;
        }
    }

    if (!commandConfigLoaded) {
        qCWarning(simpleMqttGateway) << "命令配置加载失败，已尝试以下路径:";
        for (const auto& path : possibleCommandPaths) {
            qCWarning(simpleMqttGateway) << "  -" << QString::fromStdString(path);
        }
        qCWarning(simpleMqttGateway) << "使用默认配置，命令验证功能会受限";
    }
    
    // 3. 初始化MQTT客户端
    if (!InitializeMQTT()) {
        qCCritical(simpleMqttGateway) << "MQTT客户端初始化失败";
        return false;
    }
    
    // 4. 初始化Socket服务器
    if (!InitializeSocket()) {
        qCCritical(simpleMqttGateway) << "Socket服务器初始化失败";
        return false;
    }
    
    // 5. 创建定时器
    m_reportTimer = new QTimer(this);
    connect(m_reportTimer, &QTimer::timeout, this, &SimpleMQTTGateway::OnPeriodicReport);
    
    qCInfo(simpleMqttGateway) << "极简MQTT网关初始化完成";
    return true;
}

void SimpleMQTTGateway::Run() {
    if (m_running) {
        qCWarning(simpleMqttGateway) << "网关已在运行中";
        return;
    }
    
    m_running = true;
    
    // 启动Socket服务器线程
    std::thread socketThread([this]() { 
        if (m_socketServer) {
            m_socketServer->Run(); 
        }
    });
    socketThread.detach();
    
    // 启动周期性上报
    StartPeriodicReporting();
    
    qCInfo(simpleMqttGateway) << "极简MQTT网关启动成功";
    qCInfo(simpleMqttGateway) << "- 数据点数量:" << m_configManager.GetDataPoints().size();
    qCInfo(simpleMqttGateway) << "- 事件类型数量:" << m_configManager.GetEvents().size();
    qCInfo(simpleMqttGateway) << "- 命令类型数量:" << m_configManager.GetCommands().size();
    qCInfo(simpleMqttGateway) << "- 订阅主题数量:" << m_configManager.GetSubscriptions().size();
    
    // 主循环处理Socket消息
    while (m_running) {
        ProcessSocketMessages();
        CleanupExpiredEvents();
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

void SimpleMQTTGateway::Stop() {
    if (!m_running) {
        return;
    }
    
    qCInfo(simpleMqttGateway) << "正在停止极简MQTT网关...";
    
    m_running = false;
    
    // 停止定时器
    if (m_reportTimer) {
        m_reportTimer->stop();
    }
    
    // 断开MQTT连接
    if (m_mqttClient && m_mqttConnected) {
        try {
            auto token = m_mqttClient->disconnect();
            token->wait();
            qCInfo(simpleMqttGateway) << "MQTT连接已断开";
        } catch (const mqtt::exception& exc) {
            qCWarning(simpleMqttGateway) << "MQTT断开连接异常:" << exc.what();
        }
    }
    
    // 停止Socket服务器
    if (m_socketServer) {
        m_socketServer->Stop();
    }
    
    qCInfo(simpleMqttGateway) << "极简MQTT网关已停止";
}

bool SimpleMQTTGateway::IsMQTTConnected() const {
    return m_mqttConnected;
}

bool SimpleMQTTGateway::IsSocketConnected() const {
    return m_socketServer ? m_socketServer->IsConnected() : false;
}

// MQTT回调实现
void SimpleMQTTGateway::connection_lost(const std::string& cause) {
    qCWarning(simpleMqttGateway) << "MQTT连接丢失:" << QString::fromStdString(cause);
    m_mqttConnected = false;
    emit connectionStatusChanged(false, IsSocketConnected());
}

void SimpleMQTTGateway::message_arrived(mqtt::const_message_ptr msg) {
    try {
        std::string topic = msg->get_topic();
        std::string payload = msg->to_string();

        qCDebug(simpleMqttGateway) << "📥 收到MQTT消息 - 主题:" << QString::fromStdString(topic)
                                  << "QoS:" << msg->get_qos()
                                  << "大小:" << payload.length() << "字节";

        // 根据Topic确定处理器
        std::string handler = m_topicManager.GetMessageHandler(topic);

        if (handler == "HandlePropertyGetRequest") {
            HandlePropertyGetRequest(msg);
        } else if (handler == "HandlePropertySetRequest") {
            HandlePropertySetRequest(msg);
        } else if (handler == "HandleCommandRequest") {
            HandleCommandRequest(msg);
        } else if (handler == "HandleEventResponse") {
            HandleEventResponse(msg);
        } else {
            qCWarning(simpleMqttGateway) << "未知消息处理器:" << QString::fromStdString(handler)
                                        << "主题:" << QString::fromStdString(topic);
        }

    } catch (const std::exception& e) {
        qCCritical(simpleMqttGateway) << "处理MQTT消息异常:" << e.what();
    }
}

void SimpleMQTTGateway::delivery_complete(mqtt::delivery_token_ptr token) {
    qCDebug(simpleMqttGateway) << "✅ MQTT消息发送完成 - Token:" << token->get_message_id();
}

// 私有方法实现
bool SimpleMQTTGateway::InitializeMQTT() {
    const auto& config = m_configManager.GetGatewayConfig();

    // 构建MQTT连接参数
    std::string brokerAddress = "tcp://" + config.mqtt.broker + ":" + std::to_string(config.mqtt.port);
    std::string clientId = config.device.deviceId + "_0_1_" + GetSCADATimestamp();

    qCInfo(simpleMqttGateway) << "准备连接MQTT Broker:" << QString::fromStdString(brokerAddress);
    qCInfo(simpleMqttGateway) << "Client ID:" << QString::fromStdString(clientId);

    try {
        // 创建MQTT客户端选项（性能优化）
        mqtt::create_options createOpts;
        createOpts.set_send_while_disconnected(false);  // 断线时不发送
        createOpts.set_max_buffered_messages(1000);     // 最大缓冲消息数
        createOpts.set_delete_oldest_messages(true);    // 缓冲满时删除最旧消息

        // 创建MQTT客户端
        m_mqttClient = std::make_unique<mqtt::async_client>(brokerAddress, clientId, createOpts);
        m_mqttClient->set_callback(*this);

        // 设置连接选项
        mqtt::connect_options connOpts;
        connOpts.set_keep_alive_interval(config.mqtt.keepAliveInterval);
        connOpts.set_clean_session(config.mqtt.cleanSession);
        connOpts.set_automatic_reconnect(config.mqtt.automaticReconnect);
        connOpts.set_user_name(config.device.userName);

        // 按照SCADA协议生成HMACSHA256密码
        std::string timestamp = ExtractTimestampFromClientId(clientId);
        std::string password = GenerateHMACPassword(config.device.secret, timestamp);
        connOpts.set_password(password);

        // 打印认证参数（用于调试）
        qCInfo(simpleMqttGateway) << "=== SCADA协议认证参数 ===";
        qCInfo(simpleMqttGateway) << "ClientId:" << QString::fromStdString(clientId);
        qCInfo(simpleMqttGateway) << "Username:" << QString::fromStdString(config.device.userName);
        qCInfo(simpleMqttGateway) << "Password:" << QString::fromStdString(password);
        qCInfo(simpleMqttGateway) << "Timestamp:" << QString::fromStdString(timestamp);
        qCInfo(simpleMqttGateway) << "Secret:" << QString::fromStdString(config.device.secret);
        qCInfo(simpleMqttGateway) << "========================";

        // 连接到MQTT服务器
        auto token = m_mqttClient->connect(connOpts);

        // 使用配置的超时时间进行连接等待
        int connectTimeoutMs = config.performance.responseTimeoutS * 1000;
        bool connectSuccess = token->wait_for(std::chrono::milliseconds(connectTimeoutMs));

        if (!connectSuccess) {
            qCCritical(simpleMqttGateway) << "⏰ MQTT连接超时 - 超时时间:" << connectTimeoutMs << "ms";
            return false;
        }

        // 获取连接返回码
        try {
            auto connectResponse = token->get_connect_response();
            auto reasonCode = token->get_reason_code();
            int returnCode = static_cast<int>(reasonCode);
            std::string returnCodeDesc = GetMQTTReturnCodeDescription(returnCode);

            qCInfo(simpleMqttGateway) << "=== MQTT连接返回码 ===";
            qCInfo(simpleMqttGateway) << "返回码:" << returnCode;
            qCInfo(simpleMqttGateway) << "描述:" << QString::fromStdString(returnCodeDesc);
            qCInfo(simpleMqttGateway) << "ReasonCode:" << static_cast<int>(reasonCode);
            qCInfo(simpleMqttGateway) << "=====================";

            if (returnCode == 0 || reasonCode == mqtt::ReasonCode::SUCCESS) {
                m_mqttConnected = true;
                qCInfo(simpleMqttGateway) << "MQTT连接成功";

                // 订阅主题
                return SubscribeToTopics();
            } else {
                qCCritical(simpleMqttGateway) << "MQTT连接被拒绝，返回码:" << returnCode << "(" << QString::fromStdString(returnCodeDesc) << ")";
                return false;
            }
        } catch (const std::exception& e) {
            qCWarning(simpleMqttGateway) << "获取连接响应异常:" << e.what();
            // 如果无法获取返回码，但连接成功，继续处理
            m_mqttConnected = true;
            qCInfo(simpleMqttGateway) << "MQTT连接成功（无法获取详细返回码）";
            return SubscribeToTopics();
        }

    } catch (const mqtt::exception& exc) {
        qCCritical(simpleMqttGateway) << "MQTT连接异常:" << exc.what();
        qCCritical(simpleMqttGateway) << "异常代码:" << exc.get_reason_code();
        return false;
    }
}

bool SimpleMQTTGateway::InitializeSocket() {
    const auto& config = m_configManager.GetGatewayConfig();

    m_socketServer = std::make_unique<SocketServer>();

    if (!m_socketServer->Initialize(config.socket.listenPort)) {
        qCCritical(simpleMqttGateway) << "Socket服务器初始化失败";
        return false;
    }

    qCInfo(simpleMqttGateway) << "Socket服务器初始化成功，端口:" << config.socket.listenPort;
    return true;
}

bool SimpleMQTTGateway::SubscribeToTopics() {
    auto subscriptionTopics = m_topicManager.GetSubscriptionTopics();

    for (const auto& topic : subscriptionTopics) {
        int qos = m_topicManager.GetSubscriptionQoS(topic);

        try {
            auto token = m_mqttClient->subscribe(topic, qos);

            // 使用配置的超时时间进行订阅等待
            int subscribeTimeoutMs = m_configManager.GetGatewayConfig().performance.responseTimeoutS * 1000;
            bool subscribeSuccess = token->wait_for(std::chrono::milliseconds(subscribeTimeoutMs));

            if (subscribeSuccess) {
                qCInfo(simpleMqttGateway) << "📥 订阅成功 - 主题:" << QString::fromStdString(topic)
                                         << "QoS:" << qos << "耗时: <" << subscribeTimeoutMs << "ms";
            } else {
                qCWarning(simpleMqttGateway) << "⏰ 订阅超时 - 主题:" << QString::fromStdString(topic)
                                            << "超时时间:" << subscribeTimeoutMs << "ms";
                return false;
            }
        } catch (const mqtt::exception& exc) {
            qCCritical(simpleMqttGateway) << "订阅异常 - 主题:" << QString::fromStdString(topic)
                                         << "错误:" << exc.what()
                                         << "错误码:" << exc.get_reason_code();
            return false;
        }
    }

    qCInfo(simpleMqttGateway) << "完成MQTT主题订阅，共订阅" << subscriptionTopics.size() << "个主题";
    return true;
}

void SimpleMQTTGateway::StartPeriodicReporting() {
    const auto& config = m_configManager.GetGatewayConfig();
    int intervalMs = config.features.defaultReportCycleS * 1000;

    if (intervalMs > 0) {
        m_reportTimer->start(intervalMs);
        qCInfo(simpleMqttGateway) << "启动周期性上报，间隔:" << config.features.defaultReportCycleS << "秒";

        // 立即发送一次初始报告
        OnPeriodicReport();
    } else {
        qCInfo(simpleMqttGateway) << "周期性上报已禁用";
    }
}

// Socket消息处理
void SimpleMQTTGateway::ProcessSocketMessages() {
    if (!m_socketServer) {
        return;
    }

    SocketMessage msg;
    while (m_running && m_socketServer->ReceiveMessage(msg)) {
        try {
            qCDebug(simpleMqttGateway) << "📨 收到Socket消息 - 类型:" << QString::fromStdString(msg.type)
                                      << "大小:" << msg.data.length() << "字节";

            QJsonParseError error;
            QJsonDocument doc = QJsonDocument::fromJson(QByteArray::fromStdString(msg.data), &error);
            if (error.error != QJsonParseError::NoError) {
                qCWarning(simpleMqttGateway) << "Socket消息JSON解析失败:" << error.errorString();
                continue;
            }

            QJsonObject data = doc.object();

            // 根据消息类型分发处理
            if (msg.type == "DEVICE_STATUS") {
                HandleDeviceStatusMessage(data);
            } else if (msg.type.find("EVENT_") == 0) {
                HandleEventMessage(msg.type, data);
            } else if (msg.type == "COMMAND_RESPONSE") {
                // 处理FStation返回的命令响应
                if (!msg.requestId.empty()) {
                    HandleCommandResponse(msg.requestId, data);
                } else {
                    qCWarning(simpleMqttGateway) << "命令响应缺少requestId";
                }
            } else {
                qCWarning(simpleMqttGateway) << "未知Socket消息类型:" << QString::fromStdString(msg.type);
            }

        } catch (const std::exception& e) {
            qCCritical(simpleMqttGateway) << "处理Socket消息异常:" << e.what();
        }
    }
}

void SimpleMQTTGateway::HandleDeviceStatusMessage(const QJsonObject& data) {
    qCDebug(simpleMqttGateway) << "处理设备状态消息";

    // 更新数据缓存（只缓存，不立即发布）
    UpdateDataCache(data);

    // 注意：不在这里发布属性上报
    // 属性上报由定时器控制，按照配置的周期进行
    // 这样避免了频繁的属性上报
}

void SimpleMQTTGateway::HandleEventMessage(const std::string& eventType, const QJsonObject& data) {
    qCDebug(simpleMqttGateway) << "处理事件消息 - 类型:" << QString::fromStdString(eventType);

    // 查找事件配置
    const EventConfig* eventConfig = m_configManager.FindEventConfig(eventType);
    if (!eventConfig) {
        qCWarning(simpleMqttGateway) << "未找到事件配置:" << QString::fromStdString(eventType);
        return;
    }

    // 生成request_id
    std::string requestId = GenerateRequestId();

    // FStationMFC已经发送了正确格式的事件消息，直接使用
    QJsonObject eventMsg = data;

    // 检查是否需要添加DefaultService
    if (eventMsg.contains("services") && eventMsg["services"].isArray()) {
        QJsonArray services = eventMsg["services"].toArray();

        // 检查是否已有DefaultService
        bool hasDefaultService = false;
        for (const auto& serviceValue : services) {
            QJsonObject service = serviceValue.toObject();
            if (service["service_id"].toString() == "DefaultService") {
                hasDefaultService = true;
                break;
            }
        }

        // 如果没有DefaultService，添加一个
        if (!hasDefaultService) {
            QJsonObject defaultService = BuildDefaultService();
            services.append(defaultService);
            eventMsg["services"] = services;
        }
    }

    // 注册待处理事件
    RegisterPendingEvent(requestId, eventType);

    // 发布到MQTT (QoS 2, 需要等待答复)
    std::map<std::string, std::string> params;
    params["requestId"] = requestId;

    if (PublishMessage("event_report", eventMsg, params)) {
        qCInfo(simpleMqttGateway) << "📤 事件已发布 - 类型:" << QString::fromStdString(eventType)
                                 << "RequestId:" << QString::fromStdString(requestId);
    } else {
        qCWarning(simpleMqttGateway) << "事件发布失败 - 类型:" << QString::fromStdString(eventType);
    }
}

// MQTT消息处理
void SimpleMQTTGateway::HandlePropertyGetRequest(mqtt::const_message_ptr msg) {
    qCDebug(simpleMqttGateway) << "处理属性查询请求";

    std::string requestId = m_topicManager.ExtractRequestId(msg->get_topic());
    if (requestId.empty()) {
        qCWarning(simpleMqttGateway) << "无法提取request_id";
        return;
    }

    // 幂等性检查：检查是否为重复请求
    if (m_requestIdManager.IsRequestProcessed(requestId)) {
        RequestIdManager::CachedResponse cachedResponse;
        if (m_requestIdManager.GetCachedResponse(requestId, cachedResponse)) {
            qCInfo(simpleMqttGateway) << "🔄 检测到重复请求，跳过处理 - RequestId:" << QString::fromStdString(requestId)
                                     << "原始处理时间:" << QString::fromStdString(cachedResponse.requestType);

            // 幂等性原则：重复请求不重复执行操作，直接返回
            // 注意：不重新发布消息，因为这违反了幂等性原则
            qCDebug(simpleMqttGateway) << "📋 幂等性保护：跳过重复的属性查询处理和消息发布";
            return;
        }
    }

    // 构建当前属性数据
    QJsonObject response = BuildServicesMessage(QJsonObject());

    qCDebug(simpleMqttGateway) << "构建属性查询响应 - RequestId:" << QString::fromStdString(requestId)
                              << "数据大小:" << QJsonDocument(response).toJson(QJsonDocument::Compact).size() << "字节";
    // 发布响应
    std::map<std::string, std::string> params;
    params["requestId"] = requestId;

    bool success = PublishMessage("property_get_response", response, params);

    // 标记请求为已处理并缓存响应
    m_requestIdManager.MarkRequestProcessed(requestId, "property_get", response, success);

    if (success) {
        qCInfo(simpleMqttGateway) << "📤 属性查询响应发送成功 - RequestId:" << QString::fromStdString(requestId);
    } else {
        qCCritical(simpleMqttGateway) << "❌ 属性查询响应发送失败 - RequestId:" << QString::fromStdString(requestId);
    }
}

void SimpleMQTTGateway::HandlePropertySetRequest(mqtt::const_message_ptr msg) {
    qCDebug(simpleMqttGateway) << "处理属性设置请求";

    std::string requestId = m_topicManager.ExtractRequestId(msg->get_topic());
    if (requestId.empty()) {
        qCWarning(simpleMqttGateway) << "无法提取request_id";
        return;
    }

    // 幂等性检查：检查是否为重复请求
    if (m_requestIdManager.IsRequestProcessed(requestId)) {
        RequestIdManager::CachedResponse cachedResponse;
        if (m_requestIdManager.GetCachedResponse(requestId, cachedResponse)) {
            qCInfo(simpleMqttGateway) << "🔄 检测到重复请求，跳过处理 - RequestId:" << QString::fromStdString(requestId)
                                     << "原始处理时间:" << QString::fromStdString(cachedResponse.requestType);

            // 幂等性原则：重复请求不重复执行操作，直接返回
            qCDebug(simpleMqttGateway) << "📋 幂等性保护：跳过重复的属性设置处理和消息发布";
            return;
        }
    }

    // 构建响应（简化版：直接返回成功）
    QJsonObject response;
    response["result_code"] = 0;
    response["result_message"] = "设置成功";

    // 发布响应
    std::map<std::string, std::string> params;
    params["requestId"] = requestId;

    bool success = PublishMessage("property_set_response", response, params);

    // 标记请求为已处理并缓存响应
    m_requestIdManager.MarkRequestProcessed(requestId, "property_set", response, success);

    if (success) {
        qCInfo(simpleMqttGateway) << "📤 属性设置响应发送成功 - RequestId:" << QString::fromStdString(requestId);
    } else {
        qCCritical(simpleMqttGateway) << "❌ 属性设置响应发送失败 - RequestId:" << QString::fromStdString(requestId);
    }
}

void SimpleMQTTGateway::HandleCommandRequest(mqtt::const_message_ptr msg) {
    qCDebug(simpleMqttGateway) << "处理命令请求";

    std::string requestId = m_topicManager.ExtractRequestId(msg->get_topic());
    if (requestId.empty()) {
        qCWarning(simpleMqttGateway) << "无法提取request_id";
        return;
    }

    // 幂等性检查：检查是否为重复请求
    if (m_requestIdManager.IsRequestProcessed(requestId)) {
        RequestIdManager::CachedResponse cachedResponse;
        if (m_requestIdManager.GetCachedResponse(requestId, cachedResponse)) {
            qCInfo(simpleMqttGateway) << "🔄 检测到重复请求，跳过处理 - RequestId:" << QString::fromStdString(requestId)
                                     << "原始处理时间:" << QString::fromStdString(cachedResponse.requestType);

            // 幂等性原则：重复请求不重复执行操作，直接返回
            qCDebug(simpleMqttGateway) << "📋 幂等性保护：跳过重复的命令处理和消息发布";
            return;
        }
    }

    // 命令管理器幂等性检查
    if (m_commandManager->HasCommand(requestId)) {
        qCInfo(simpleMqttGateway) << "🔄 命令管理器检测到重复请求，跳过处理 - RequestId:" << QString::fromStdString(requestId);
        return;
    }

    try {
        // 解析命令内容
        QJsonParseError error;
        QJsonDocument doc = QJsonDocument::fromJson(QByteArray::fromStdString(msg->to_string()), &error);
        if (error.error != QJsonParseError::NoError) {
            qCWarning(simpleMqttGateway) << "命令JSON解析失败:" << error.errorString();

            // 发送格式错误响应
            QJsonObject response;
            response["response_name"] = "COMMAND_RESPONSE";
            response["result_code"] = -3;

            QJsonObject properties;
            properties["result"] = "failed";
            properties["error_message"] = "命令格式错误: " + error.errorString();
            response["properties"] = properties;

            std::map<std::string, std::string> params;
            params["requestId"] = requestId;
            PublishMessage("command_response", response, params);
            return;
        }

        QJsonObject command = doc.object();

        // 验证命令格式和参数
        std::string validationError;
        if (!m_commandManager->ValidateCommand(command, validationError)) {
            qCWarning(simpleMqttGateway) << "命令验证失败:" << QString::fromStdString(validationError);

            // 发送验证失败响应
            QJsonObject response;
            response["response_name"] = "COMMAND_RESPONSE";
            response["result_code"] = -4;

            QJsonObject properties;
            properties["result"] = "failed";
            properties["error_message"] = "命令验证失败: " + QString::fromStdString(validationError);
            response["properties"] = properties;

            std::map<std::string, std::string> params;
            params["requestId"] = requestId;
            PublishMessage("command_response", response, params);
            return;
        }

        std::string commandName = command["command_name"].toString().toStdString();

        qCInfo(simpleMqttGateway) << "📨 收到命令:" << QString::fromStdString(commandName)
                                 << "RequestId:" << QString::fromStdString(requestId);

        // 注册命令到命令管理器
        if (!m_commandManager->RegisterCommand(requestId, command)) {
            qCWarning(simpleMqttGateway) << "命令注册失败:" << QString::fromStdString(requestId);

            // 发送注册失败响应
            QJsonObject response;
            response["response_name"] = "COMMAND_RESPONSE";
            response["result_code"] = -5;

            QJsonObject properties;
            properties["result"] = "failed";
            properties["error_message"] = "命令注册失败";
            response["properties"] = properties;

            std::map<std::string, std::string> params;
            params["requestId"] = requestId;
            PublishMessage("command_response", response, params);
            return;
        }

        // 标记命令开始执行
        m_commandManager->MarkCommandExecuting(requestId);

        // 转发给FStation（通过Socket）
        SocketMessage socketMsg;
        socketMsg.type = "COMMAND";
        socketMsg.data = msg->to_string();
        socketMsg.requestId = requestId;

        if (m_socketServer && m_socketServer->SendMessage(socketMsg)) {
            qCInfo(simpleMqttGateway) << "命令已转发给FStation";
        } else {
            qCWarning(simpleMqttGateway) << "命令转发失败";

            // 发送失败响应
            QJsonObject response;
            response["response_name"] = "COMMAND_RESPONSE";
            response["result_code"] = -1;

            QJsonObject properties;
            properties["result"] = "failed";
            properties["error_message"] = "设备连接失败";
            response["properties"] = properties;

            std::map<std::string, std::string> params;
            params["requestId"] = requestId;

            bool success = PublishMessage("command_response", response, params);

            // 标记命令执行失败
            m_commandManager->MarkCommandCompleted(requestId, false, -1, "设备连接失败");

            // 标记请求为已处理并缓存响应（失败情况）
            m_requestIdManager.MarkRequestProcessed(requestId, "command", response, success);

            if (success) {
                qCInfo(simpleMqttGateway) << "📤 命令失败响应发送成功 - RequestId:" << QString::fromStdString(requestId);
            } else {
                qCCritical(simpleMqttGateway) << "❌ 命令失败响应发送失败 - RequestId:" << QString::fromStdString(requestId);
            }
        }

    } catch (const std::exception& e) {
        qCCritical(simpleMqttGateway) << "处理命令异常:" << e.what();

        // 发送异常响应
        QJsonObject response;
        response["response_name"] = "COMMAND_RESPONSE";
        response["result_code"] = -6;

        QJsonObject properties;
        properties["result"] = "failed";
        properties["error_message"] = "命令处理异常: " + QString(e.what());
        response["properties"] = properties;

        std::map<std::string, std::string> params;
        params["requestId"] = requestId;
        PublishMessage("command_response", response, params);

        // 标记命令执行失败
        m_commandManager->MarkCommandCompleted(requestId, false, -6, "命令处理异常: " + std::string(e.what()));
    }
}

void SimpleMQTTGateway::HandleCommandResponse(const std::string& requestId, const QJsonObject& response)
{
    qCDebug(simpleMqttGateway) << "处理命令响应 - RequestId:" << QString::fromStdString(requestId);

    // 检查命令是否存在
    if (!m_commandManager->HasCommand(requestId)) {
        qCWarning(simpleMqttGateway) << "收到未知命令的响应:" << QString::fromStdString(requestId);
        return;
    }

    // 提取响应信息
    int resultCode = response["result_code"].toInt();
    std::string resultMessage = response["result_message"].toString().toStdString();
    bool success = (resultCode == 0);

    // 标记命令完成
    m_commandManager->MarkCommandCompleted(requestId, success, resultCode, resultMessage);

    // 发布MQTT响应
    std::map<std::string, std::string> params;
    params["requestId"] = requestId;

    bool publishSuccess = PublishMessage("command_response", response, params);

    // 标记请求为已处理并缓存响应
    m_requestIdManager.MarkRequestProcessed(requestId, "command", response, publishSuccess);

    if (publishSuccess) {
        qCInfo(simpleMqttGateway) << "📤 命令响应发送成功 - RequestId:" << QString::fromStdString(requestId)
                                 << "结果码:" << resultCode;
    } else {
        qCCritical(simpleMqttGateway) << "❌ 命令响应发送失败 - RequestId:" << QString::fromStdString(requestId);
    }

    // 清理命令（可选，也可以保留一段时间用于统计）
    // m_commandManager->RemoveCommand(requestId);
}

void SimpleMQTTGateway::HandleCommandTimeout(const QString& requestId, const QString& commandName)
{
    qCWarning(simpleMqttGateway) << "⏰ 命令执行超时:" << commandName << "RequestId:" << requestId;

    // 发送超时响应
    QJsonObject response;
    response["response_name"] = "COMMAND_RESPONSE";
    response["result_code"] = -2;

    QJsonObject properties;
    properties["result"] = "failed";
    properties["error_message"] = "命令执行超时";
    response["properties"] = properties;

    std::map<std::string, std::string> params;
    params["requestId"] = requestId.toStdString();

    bool success = PublishMessage("command_response", response, params);

    // 标记请求为已处理并缓存响应
    m_requestIdManager.MarkRequestProcessed(requestId.toStdString(), "command", response, success);

    if (success) {
        qCInfo(simpleMqttGateway) << "📤 命令超时响应发送成功 - RequestId:" << requestId;
    } else {
        qCCritical(simpleMqttGateway) << "❌ 命令超时响应发送失败 - RequestId:" << requestId;
    }
}

void SimpleMQTTGateway::HandleEventResponse(mqtt::const_message_ptr msg) {
    qCDebug(simpleMqttGateway) << "处理事件响应";

    std::string requestId = m_topicManager.ExtractRequestId(msg->get_topic());
    if (requestId.empty()) {
        qCWarning(simpleMqttGateway) << "无法提取request_id";
        return;
    }

    try {
        // 解析响应内容
        QJsonParseError error;
        QJsonDocument doc = QJsonDocument::fromJson(QByteArray::fromStdString(msg->to_string()), &error);
        if (error.error != QJsonParseError::NoError) {
            qCWarning(simpleMqttGateway) << "事件响应JSON解析失败:" << error.errorString();
            return;
        }

        QJsonObject response = doc.object();
        int resultCode = response["result_code"].toInt();
        std::string resultMessage = response["result_message"].toString().toStdString();

        bool success = (resultCode == 0);

        qCInfo(simpleMqttGateway) << "📥 收到事件响应 - RequestId:" << QString::fromStdString(requestId)
                                 << "结果:" << (success ? "成功" : "失败")
                                 << "消息:" << QString::fromStdString(resultMessage);

        // 处理事件响应
        if (ProcessEventResponse(requestId, success, resultMessage)) {
            // 转发响应给FStation
            SocketMessage socketMsg;
            socketMsg.type = "EVENT_RESPONSE";
            socketMsg.requestId = requestId;

            QJsonObject responseData;
            responseData["request_id"] = QString::fromStdString(requestId);
            responseData["result_code"] = resultCode;
            responseData["result_message"] = QString::fromStdString(resultMessage);

            QJsonDocument responseDoc(responseData);
            socketMsg.data = responseDoc.toJson(QJsonDocument::Compact).toStdString();

            if (m_socketServer && m_socketServer->SendMessage(socketMsg)) {
                qCInfo(simpleMqttGateway) << "事件响应已转发给FStation";
            } else {
                qCWarning(simpleMqttGateway) << "事件响应转发失败";
            }
        }

    } catch (const std::exception& e) {
        qCCritical(simpleMqttGateway) << "处理事件响应异常:" << e.what();
    }
}

// 定时器槽函数
void SimpleMQTTGateway::OnPeriodicReport() {
    qCDebug(simpleMqttGateway) << "执行周期性属性上报";

    // 构建默认服务数据
    QJsonObject servicesMsg = BuildServicesMessage(QJsonObject());

    // 发布到MQTT
    if (PublishMessage("property_report", servicesMsg)) {
        qCDebug(simpleMqttGateway) << "📤 周期性属性上报完成";
    } else {
        qCWarning(simpleMqttGateway) << "周期性属性上报失败";
    }
}

// 数据处理方法
QJsonObject SimpleMQTTGateway::BuildServicesMessage(const QJsonObject& sourceData) {
    QJsonObject message;
    QJsonArray services;

    // 按照SCADA协议要求，添加三个标准服务

    // 1. DefaultService - 包含所有数据点
    services.append(BuildDefaultService());

    // 2. SnService - 包含当前SN和生产模型信息
    //services.append(BuildSnService());

    // 3. StatusService - 包含设备状态信息
    services.append(BuildStatusService());

    message["services"] = services;
    return message;
}

QJsonObject SimpleMQTTGateway::BuildDefaultService() {
    QJsonObject service;
    service["service_id"] = "DefaultService";

    QJsonObject properties;

    // 根据配置构建属性
    const auto& dataPoints = m_configManager.GetDataPoints();

    std::lock_guard<std::mutex> lock(m_dataMutex);
    for (const auto& dataPoint : dataPoints) {
        QString code = QString::fromStdString(dataPoint.code);

        // 从缓存中获取数据，如果没有则使用默认值
        if (m_dataCache.find(dataPoint.code) != m_dataCache.end()) {
            properties[code] = QString::fromStdString(m_dataCache[dataPoint.code]);
        } else {
            // 根据数据类型设置默认值
            if (dataPoint.type == "bool") {
                properties[code] = false;
            } else if (dataPoint.type == "int") {
                properties[code] = 0;
            } else if (dataPoint.type == "float" || dataPoint.type == "decimal") {
                properties[code] = 0.0;
            } else {
                properties[code] = "";
            }
        }
    }

    service["properties"] = properties;
    service["event_time"] = QString::fromStdString(GetCurrentTimestamp());

    return service;
}

QJsonObject SimpleMQTTGateway::BuildEventService(const std::string& eventType, const QJsonObject& eventData) {
    QJsonObject service;
    service["service_id"] = "EventService";
    service["event_type"] = QString::fromStdString(eventType);

    // 根据事件配置构建属性
    const EventConfig* eventConfig = m_configManager.FindEventConfig(eventType);
    QJsonObject properties;

    if (eventConfig) {
        for (const auto& propName : eventConfig->properties) {
            QString propKey = QString::fromStdString(propName);
            if (eventData.contains(propKey)) {
                properties[propKey] = eventData[propKey];
            }
        }
    }

    service["properties"] = properties;
    service["event_time"] = QString::fromStdString(GetCurrentTimestamp());

    return service;
}

// MQTT发布方法
bool SimpleMQTTGateway::PublishMessage(const std::string& topicName, const QJsonObject& payload,
                                      const std::map<std::string, std::string>& params) {
    if (!m_mqttClient || !m_mqttConnected) {
        qCWarning(simpleMqttGateway) << "MQTT未连接，无法发布消息";
        return false;
    }

    // 构建Topic
    std::string topic = m_topicManager.BuildPublishTopic(topicName, params);
    if (topic.empty()) {
        qCWarning(simpleMqttGateway) << "无法构建发布主题:" << QString::fromStdString(topicName);
        return false;
    }

    // 获取QoS和Retain设置
    int qos = m_topicManager.GetPublishQoS(topicName);
    bool retain = m_topicManager.GetRetainFlag(topicName);

    // 构建消息内容
    QJsonDocument doc(payload);
    std::string payloadStr = doc.toJson(QJsonDocument::Compact).toStdString();

    try {
        auto message = mqtt::make_message(topic, payloadStr);
        message->set_qos(qos);
        message->set_retained(retain);

        auto token = m_mqttClient->publish(message);

        // 对于QoS 1的响应消息，使用异步模式（不等待PUBACK确认）
        if (qos == 1 && topic.find("/response/") != std::string::npos) {
            qCDebug(simpleMqttGateway) << "📤 MQTT响应消息异步发布 - 主题:" << QString::fromStdString(topic)
                                      << "QoS:" << qos << "大小:" << payloadStr.length() << "字节"
                                      << "模式: 异步（不等待PUBACK）";
            return true;  // 立即返回成功，不等待PUBACK确认
        }

        // 对于事件上报，使用异步模式（不等待QoS 2确认，通过平台响应确认）
        if (topicName == "event_report") {
            qCDebug(simpleMqttGateway) << "📤 MQTT事件异步发布 - 主题:" << QString::fromStdString(topic)
                                      << "QoS:" << qos << "大小:" << payloadStr.length() << "字节"
                                      << "模式: 异步（通过平台响应确认）";
            return true;  // 立即返回成功，通过平台响应来确认事件处理
        }

        // 对于其他消息，仍然等待确认
        // 记录开始时间
        auto startTime = std::chrono::steady_clock::now();

        // 使用配置的超时时间进行等待
        int timeoutMs = m_configManager.GetGatewayConfig().performance.publishTimeoutS * 1000;
        bool success = token->wait_for(std::chrono::milliseconds(timeoutMs));

        // 计算实际耗时
        auto endTime = std::chrono::steady_clock::now();
        auto actualDuration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

        if (success) {
            qCDebug(simpleMqttGateway) << "📤 MQTT消息发布成功 - 主题:" << QString::fromStdString(topic)
                                      << "QoS:" << qos << "大小:" << payloadStr.length() << "字节"
                                      << "实际耗时:" << actualDuration.count() << "ms";
            return true;
        } else {
            qCWarning(simpleMqttGateway) << "⏰ MQTT消息发布超时 - 主题:" << QString::fromStdString(topic)
                                        << "超时时间:" << timeoutMs << "ms"
                                        << "实际耗时:" << actualDuration.count() << "ms"
                                        << "QoS:" << qos;
            return false;
        }

    } catch (const mqtt::exception& exc) {
        qCCritical(simpleMqttGateway) << "MQTT消息发布异常 - 主题:" << QString::fromStdString(topic)
                                     << "错误:" << exc.what()
                                     << "错误码:" << exc.get_reason_code();
        return false;
    }
}

// 工具方法
std::string SimpleMQTTGateway::GenerateRequestId() {
    auto now = std::chrono::system_clock::now();
    auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();

    static int counter = 0;
    counter = (counter + 1) % 1000;

    return std::to_string(timestamp) + std::to_string(counter);
}

std::string SimpleMQTTGateway::GetCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;

    std::tm* tm = std::localtime(&time_t);
    char buffer[32];
    std::strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", tm);

    // 确保毫秒数始终是3位数字，不足的前面补0
    char msBuffer[8];
    snprintf(msBuffer, sizeof(msBuffer), ".%03d", static_cast<int>(ms.count()));

    return std::string(buffer) + std::string(msBuffer);
}

std::string SimpleMQTTGateway::GetSCADATimestamp() {
    // 协议版本：SCADA v1.13
    // 时间戳格式：设备连接平台的 UTC+8 时间，格式为 YYYYMMDDHH
    // 示例：A049998285_0_0_2024080506 中的时间戳是 2024080506（2024年08月05日06时）
    // 参考文档：SCADA平台MQTT协议设备接入指南.md

    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    // 转换为 UTC+8 时间（中国标准时间）
    // 注意：std::localtime 已经考虑了本地时区，如果系统时区是 UTC+8，则直接使用
    // 如果需要强制 UTC+8，可以手动添加8小时偏移
    std::tm* tm = std::localtime(&time_t);

    char buffer[16];
    snprintf(buffer, sizeof(buffer), "%04d%02d%02d%02d",
             tm->tm_year + 1900,  // 年份
             tm->tm_mon + 1,      // 月份 (1-12)
             tm->tm_mday,         // 日期 (1-31)
             tm->tm_hour);        // 小时 (0-23)

    std::string timestamp(buffer);

    qCDebug(simpleMqttGateway) << "生成SCADA时间戳:" << QString::fromStdString(timestamp)
                              << "格式: YYYYMMDDHH (UTC+8)";

    return timestamp;
}

void SimpleMQTTGateway::UpdateDataCache(const QJsonObject& data) {
    std::lock_guard<std::mutex> lock(m_dataMutex);

    for (auto it = data.begin(); it != data.end(); ++it) {
        std::string key = it.key().toStdString();
        std::string value;

        // 根据JSON值的类型进行正确的转换
        QJsonValue jsonValue = it.value();
        if (jsonValue.isString()) {
            value = jsonValue.toString().toStdString();
        } else if (jsonValue.isDouble()) {
            // 处理数字类型（包括整数和浮点数）
            double numValue = jsonValue.toDouble();
            if (numValue == (int)numValue) {
                // 整数
                value = std::to_string((int)numValue);
            } else {
                // 浮点数，保留1位小数
                char buffer[32];
                sprintf(buffer, "%.1f", numValue);
                value = buffer;
            }
        } else if (jsonValue.isBool()) {
            value = jsonValue.toBool() ? "true" : "false";
        } else {
            // 其他类型转为字符串
            value = jsonValue.toString().toStdString();
        }

        m_dataCache[key] = value;
    }
}

// 事件答复管理
void SimpleMQTTGateway::RegisterPendingEvent(const std::string& requestId, const std::string& eventType) {
    std::lock_guard<std::mutex> lock(m_eventMutex);

    PendingEvent event;
    event.eventType = eventType;
    event.timestamp = std::chrono::system_clock::now();
    event.responseReceived = false;

    m_pendingEvents[requestId] = event;

    qCDebug(simpleMqttGateway) << "注册待处理事件 - RequestId:" << QString::fromStdString(requestId)
                              << "类型:" << QString::fromStdString(eventType);
}

bool SimpleMQTTGateway::ProcessEventResponse(const std::string& requestId, bool success, const std::string& message) {
    std::lock_guard<std::mutex> lock(m_eventMutex);

    auto it = m_pendingEvents.find(requestId);
    if (it == m_pendingEvents.end()) {
        qCWarning(simpleMqttGateway) << "未找到待处理事件 - RequestId:" << QString::fromStdString(requestId);
        return false;
    }

    it->second.responseReceived = true;

    qCInfo(simpleMqttGateway) << "处理事件响应 - RequestId:" << QString::fromStdString(requestId)
                             << "类型:" << QString::fromStdString(it->second.eventType)
                             << "结果:" << (success ? "成功" : "失败");

    // 移除已处理的事件
    m_pendingEvents.erase(it);

    return true;
}

void SimpleMQTTGateway::CleanupExpiredEvents() {
    std::lock_guard<std::mutex> lock(m_eventMutex);

    auto now = std::chrono::system_clock::now();
    auto it = m_pendingEvents.begin();

    while (it != m_pendingEvents.end()) {
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - it->second.timestamp);

        if (elapsed.count() > 30) { // 30秒超时
            qCWarning(simpleMqttGateway) << "清理超时事件 - RequestId:" << QString::fromStdString(it->first)
                                        << "类型:" << QString::fromStdString(it->second.eventType);
            it = m_pendingEvents.erase(it);
        } else {
            ++it;
        }
    }
}

// 构建SnService - 符合SCADA协议要求
QJsonObject SimpleMQTTGateway::BuildSnService() {
    QJsonObject service;
    service["service_id"] = "SnService";

    QJsonObject properties;

    std::lock_guard<std::mutex> lock(m_dataMutex);

    // 获取当前SN（从C00001数据点）
    std::string currentSn = "";
    if (m_dataCache.find("C00001") != m_dataCache.end()) {
        currentSn = m_dataCache["C00001"];
    }

    // 获取当前生产模型（从A00010数据点）
    std::string currentModel = "";
    if (m_dataCache.find("A00010") != m_dataCache.end()) {
        currentModel = m_dataCache["A00010"];
    }

    properties["sn"] = QString::fromStdString(currentSn);
    properties["production_model"] = QString::fromStdString(currentModel);

    // 添加profiles数组（如果有的话）
    QJsonArray profiles;
    // 这里可以根据实际需要添加配置文件信息
    properties["profiles"] = profiles;

    service["properties"] = properties;
    service["event_time"] = QString::fromStdString(GetCurrentTimestamp());

    return service;
}

// 构建StatusService - 符合SCADA协议要求
QJsonObject SimpleMQTTGateway::BuildStatusService() {
    QJsonObject service;
    service["service_id"] = "StatusService";

    QJsonObject properties;

    std::lock_guard<std::mutex> lock(m_dataMutex);

    // 根据设备状态数据点确定状态
    std::string deviceStatusEnum = "standby"; // 默认运行状态
    std::string deviceStatusCode = "";
    std::string deviceStatusDesc = "";

    // 检查故障状态（A00004）
    if (m_dataCache.find("A00004") != m_dataCache.end() && m_dataCache["A00004"] == "true") {
        deviceStatusEnum = "fault_error";
        deviceStatusCode = "E001";
        deviceStatusDesc = "设备故障";
    }
    // 检查暂停状态（A00003）
    else if (m_dataCache.find("A00003") != m_dataCache.end() && m_dataCache["A00003"] == "true") {
        deviceStatusEnum = "standby";
        deviceStatusCode = "";
        deviceStatusDesc = "设备暂停";
    }
    // 检查正常生产状态（A00002）
    else if (m_dataCache.find("A00002") != m_dataCache.end() && m_dataCache["A00002"] == "true") {
        deviceStatusEnum = "running";
        deviceStatusCode = "";
        deviceStatusDesc = "正常生产";
    }

    properties["device_status_enum"] = QString::fromStdString(deviceStatusEnum);
    properties["device_status_code"] = QString::fromStdString(deviceStatusCode);
    properties["device_status_desc"] = QString::fromStdString(deviceStatusDesc);

    service["properties"] = properties;
    service["event_time"] = QString::fromStdString(GetCurrentTimestamp());

    return service;
}

// SCADA协议认证方法实现
std::string SimpleMQTTGateway::GenerateHMACPassword(const std::string& secret, const std::string& timestamp) {
    // 协议版本：SCADA v1.13
    // 密码生成：使用 HMACSHA256 算法，以 clientId 中的时间戳作为密钥，对设备的 secret 进行加密
    // 参考文档：SCADA平台MQTT协议设备接入指南.md

    unsigned char* digest = nullptr;
    unsigned int digest_len = 0;

    // 使用时间戳作为密钥，secret作为数据进行HMAC计算
    digest = HMAC(EVP_sha256(),
                  timestamp.c_str(), timestamp.length(),
                  reinterpret_cast<const unsigned char*>(secret.c_str()), secret.length(),
                  nullptr, &digest_len);

    if (!digest) {
        qCCritical(simpleMqttGateway) << "HMACSHA256计算失败";
        return "";
    }

    // 转换为十六进制字符串（小写）
    std::string hexString;
    hexString.reserve(digest_len * 2);

    for (unsigned int i = 0; i < digest_len; ++i) {
        char hex[3];
        snprintf(hex, sizeof(hex), "%02x", digest[i]);
        hexString += hex;
    }

    qCDebug(simpleMqttGateway) << "HMAC计算完成 - 密钥长度:" << timestamp.length()
                              << "数据长度:" << secret.length()
                              << "输出长度:" << hexString.length();

    return hexString;
}

std::string SimpleMQTTGateway::ExtractTimestampFromClientId(const std::string& clientId) {
    // ClientId格式: {deviceId}_0_1_{timestamp}
    // 提取最后一个下划线后的时间戳部分

    size_t lastUnderscorePos = clientId.find_last_of('_');
    if (lastUnderscorePos == std::string::npos) {
        qCWarning(simpleMqttGateway) << "ClientId格式错误，无法提取时间戳:" << QString::fromStdString(clientId);
        return "";
    }

    std::string timestamp = clientId.substr(lastUnderscorePos + 1);

    qCDebug(simpleMqttGateway) << "从ClientId提取时间戳:" << QString::fromStdString(timestamp);

    return timestamp;
}

std::string SimpleMQTTGateway::GetMQTTReturnCodeDescription(int returnCode) {
    // 协议版本：SCADA v1.13
    // MQTT连接返回码说明
    // 参考文档：SCADA平台MQTT协议设备接入指南.md

    switch (returnCode) {
        case 0x00:
            return "连接成功";
        case 0x01:
            return "请求拒绝，协议版本错误 - 服务器不支持客户端请求的 MQTT 协议版本";
        case 0x02:
            return "请求拒绝，无效的客户端标识符 - clientId 格式不正确或心跳间隔不满足平台要求";
        case 0x03:
            return "请求拒绝，服务器不可用 - 平台服务不可用";
        case 0x04:
            return "请求拒绝，用户名或密码错误 - 用户名或密码错误";
        case 0x05:
            return "请求拒绝，没有授权 - 客户端没有权限连接";
        default:
            return "未知返回码: " + std::to_string(returnCode);
    }
}
