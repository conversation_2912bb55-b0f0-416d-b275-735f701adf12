#!/usr/bin/env python3
"""
FStationMFC端命令处理测试脚本
测试完整的命令处理流程：MQTT网关 → FStationMFC → 响应
"""

import paho.mqtt.client as mqtt
import json
import time
import threading

# MQTT 配置
MQTT_BROKER = "192.168.88.44"
MQTT_PORT = 1883
DEVICE_ID = "A320021760"
CLIENT_ID = f"fstation_test_{int(time.time())}"

class FStationCommandTester:
    def __init__(self):
        self.client = None
        self.responses = {}
        self.lock = threading.Lock()
        
    def on_connect(self, client, userdata, flags, rc):
        if rc == 0:
            print(f"✅ 连接成功到 EMQX")
            # 订阅命令响应
            response_topic = f"$oc/devices/{DEVICE_ID}/sys/commands/response/+"
            client.subscribe(response_topic, qos=1)
            print(f"📥 订阅命令响应主题")
        else:
            print(f"❌ 连接失败: {rc}")
    
    def on_message(self, client, userdata, msg):
        print(f"\n📨 收到FStation命令响应:")
        print(f"   主题: {msg.topic}")
        print(f"   大小: {len(msg.payload)} 字节")
        
        # 提取request_id
        topic_parts = msg.topic.split('/')
        request_id = None
        for part in topic_parts:
            if part.startswith('request_id='):
                request_id = part.split('=', 1)[1]
                break
        
        try:
            response = json.loads(msg.payload.decode())
            print(f"   RequestId: {request_id}")
            print(f"   结果码: {response.get('result_code', 'N/A')}")
            print(f"   结果消息: {response.get('result_message', 'N/A')}")
            
            # 显示响应数据
            if 'data' in response:
                print(f"   响应数据: {json.dumps(response['data'], indent=2, ensure_ascii=False)}")
            
            with self.lock:
                self.responses[request_id] = response
                
        except Exception as e:
            print(f"   解析失败: {e}")
    
    def send_command(self, command_data, request_id):
        """发送命令到FStation"""
        command_topic = f"$oc/devices/{DEVICE_ID}/sys/commands/request_id={request_id}"
        
        print(f"\n📤 发送命令到FStation:")
        print(f"   主题: {command_topic}")
        print(f"   RequestId: {request_id}")
        print(f"   命令类型: {command_data.get('command_name', 'N/A')}")
        print(f"   消息体: {json.dumps(command_data, indent=2, ensure_ascii=False)}")
        
        result = self.client.publish(command_topic, json.dumps(command_data), qos=2)
        
        if result.rc == mqtt.MQTT_ERR_SUCCESS:
            print(f"✅ 命令发送成功")
            return True
        else:
            print(f"❌ 命令发送失败: {result.rc}")
            return False
    
    def test_sn_deliver_command(self):
        """测试SN下发命令"""
        print("\n" + "="*60)
        print("测试1: SN下发命令 (COMMAND_SN_DELIVER)")
        print("="*60)
        
        request_id = f"fstation_sn_deliver_{int(time.time())}"
        command = {
            "service_id": "CommandService",
            "command_name": "COMMAND_SN_DELIVER",
            "lane_no": 1,
            "side": "T",
            "properties": {
                "sn": "FSTATION_TEST_SN_2024073101",
                "production_model": "A121000185"
            }
        }
        
        return self.send_command(command, request_id), request_id
    
    def test_pause_command(self):
        """测试暂停命令"""
        print("\n" + "="*60)
        print("测试2: 暂停生产命令 (COMMAND_PAUSE)")
        print("="*60)
        
        request_id = f"fstation_pause_{int(time.time())}"
        command = {
            "service_id": "CommandService",
            "command_name": "COMMAND_PAUSE",
            "properties": {
                "reason": "FStation测试暂停"
            }
        }
        
        return self.send_command(command, request_id), request_id
    
    def test_production_command(self):
        """测试恢复生产命令"""
        print("\n" + "="*60)
        print("测试3: 恢复生产命令 (COMMAND_PRODUCTION)")
        print("="*60)
        
        request_id = f"fstation_production_{int(time.time())}"
        command = {
            "service_id": "CommandService",
            "command_name": "COMMAND_PRODUCTION",
            "properties": {}
        }
        
        return self.send_command(command, request_id), request_id
    
    def test_bop_deliver_command(self):
        """测试转产命令"""
        print("\n" + "="*60)
        print("测试4: 转产命令 (COMMAND_BOP_DELIVER)")
        print("="*60)
        
        request_id = f"fstation_bop_{int(time.time())}"
        command = {
            "service_id": "CommandService",
            "command_name": "COMMAND_BOP_DELIVER",
            "properties": {
                "production_model": "A121000186",
                "line_speed": 100,
                "quality_level": "A",
                "batch_size": 1000
            }
        }
        
        return self.send_command(command, request_id), request_id
    
    def test_mqtt_config_command(self):
        """测试MQTT配置命令"""
        print("\n" + "="*60)
        print("测试5: MQTT配置命令 (COMMAND_MQTT_CONFIG_DELIVER)")
        print("="*60)
        
        request_id = f"fstation_mqtt_config_{int(time.time())}"
        command = {
            "service_id": "CommandService",
            "command_name": "COMMAND_MQTT_CONFIG_DELIVER",
            "properties": {
                "report_interval": 30,
                "enable_mqtt": True,
                "qos_level": 1
            }
        }
        
        return self.send_command(command, request_id), request_id
    
    def wait_for_response(self, request_id, timeout=15):
        """等待FStation响应"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            with self.lock:
                if request_id in self.responses:
                    return self.responses[request_id]
            time.sleep(0.1)
        return None
    
    def run_comprehensive_test(self):
        """运行FStation命令处理综合测试"""
        print("=== FStationMFC端命令处理综合测试 ===")
        print(f"目标: {MQTT_BROKER}:{MQTT_PORT}")
        print("测试流程: MQTT网关 → FStationMFC → 命令处理 → 响应")
        
        # 创建MQTT客户端
        self.client = mqtt.Client(CLIENT_ID)
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        
        try:
            # 连接到MQTT Broker
            self.client.connect(MQTT_BROKER, MQTT_PORT, 60)
            self.client.loop_start()
            
            time.sleep(2)  # 等待连接建立
            
            # 运行各种命令测试
            tests = [
                ("SN下发命令", self.test_sn_deliver_command),
                ("暂停生产命令", self.test_pause_command),
                ("恢复生产命令", self.test_production_command),
                ("转产命令", self.test_bop_deliver_command),
                ("MQTT配置命令", self.test_mqtt_config_command),
            ]
            
            results = []
            
            for test_name, test_func in tests:
                try:
                    success, request_id = test_func()
                    if success:
                        # 等待FStation响应
                        print(f"⏳ 等待FStation处理和响应...")
                        response = self.wait_for_response(request_id, 20)
                        if response:
                            result_code = response.get('result_code', -999)
                            result_message = response.get('result_message', 'N/A')
                            
                            if result_code == 0:
                                print(f"✅ FStation处理成功: {result_message}")
                                results.append((test_name, "成功", result_code, result_message))
                            else:
                                print(f"❌ FStation处理失败: {result_message}")
                                results.append((test_name, "失败", result_code, result_message))
                        else:
                            print(f"⏰ FStation响应超时")
                            results.append((test_name, "超时", "N/A", "无响应"))
                    else:
                        results.append((test_name, "发送失败", "N/A", "MQTT发送失败"))
                        
                    time.sleep(5)  # 测试间隔
                    
                except Exception as e:
                    print(f"❌ 测试异常: {e}")
                    results.append((test_name, "异常", "N/A", str(e)))
            
            # 打印测试结果汇总
            print("\n" + "="*80)
            print("FStationMFC端命令处理测试结果汇总")
            print("="*80)
            print(f"{'测试项':<20} {'结果':<10} {'结果码':<10} {'消息':<30}")
            print("-" * 80)
            for test_name, result, code, message in results:
                print(f"{test_name:<20} {result:<10} {str(code):<10} {message[:30]:<30}")
            
            # 统计
            success_count = sum(1 for _, result, _, _ in results if result == "成功")
            total_count = len(results)
            print(f"\n📊 测试统计: {success_count}/{total_count} 成功")
            
            if success_count == total_count:
                print("🎉 所有测试通过！FStationMFC端命令处理功能完全正常！")
            else:
                print("⚠️ 部分测试失败，请检查FStationMFC端实现")
            
        except Exception as e:
            print(f"❌ 测试异常: {e}")
        finally:
            if self.client:
                self.client.loop_stop()
                self.client.disconnect()

def main():
    print("请确保以下条件满足：")
    print("1. MQTT网关正在运行")
    print("2. FStationMFC正在运行并已连接到MQTT网关")
    print("3. SCADA命令处理器已初始化")
    print()
    
    input("按回车键开始测试...")
    
    tester = FStationCommandTester()
    tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
