﻿#pragma once
#include "afxwin.h"

// CDialogLogin 对话框

class CDialogLogin : public CDialogEx
{
	DECLARE_DYNAMIC(CDialogLogin)

public:
	CDialogLogin(CWnd* pParent = NULL);   // 标准构造函数
	virtual ~CDialogLogin();

// 对话框数据
	enum { IDD = IDD_DIALOG_LOGIN };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持
	virtual BOOL OnInitDialog();

	DECLARE_MESSAGE_MAP()
	afx_msg void OnCbnSelchangeCombo1();
	afx_msg void OnBnClickedBnLogOn();
	afx_msg void OnBnClickedBnLogOut();
	afx_msg void OnBnClickedButton1();
	afx_msg void OnBnClickedButton2();

public:
	CComboBox m_comboUserName;
	CComboBox m_comboLevel;
};
