﻿#pragma once

class CSys
{
public:
	CSys() {}
	virtual ~CSys() {}

public:
	static void InitParam();
	static void InitMachine();
	static void DelayInit();
	static void DeInit();
	static string GetCurrentPro();
	static void SaveCurrentPro();
	static void SaveSys();

public:
	static string			m_strPro; // 当前导入产品
	static int				m_nRight; // 登陆权限等级
	static bool				m_bInit; // 复位标志，打开软件及急停后置为false,复位完成后置为true
	static bool				m_bTryRun; // 试运行
	static bool				m_bPickWithoutCamera;
	static EnumRunStatus	m_emRunStatus; // 设备当前状态

	static bool				m_bBeepEnable;
	static bool				m_bDoorEnable;
	static bool				m_bDoorNotUse;

	static int				m_nRecordOutDate;

	static int				m_nTotalSum;

	static int				m_nTodayOK;
	static int				m_nTodayNG;
	static CString			m_sToday;

	static bool				m_bEngineer;

	static int				m_nDevId;

	static CString			m_sFirstCode;
};