/*
 * WARNING: do not edit!
 * Generated by crypto/objects/objects.pl
 *
 * Copyright 2000-2025 The OpenSSL Project Authors. All Rights Reserved.
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#ifndef OPENSSL_OBJ_MAC_H
# define OPENSSL_OBJ_MAC_H
# pragma once

#define SN_undef                        "UNDEF"
#define LN_undef                        "undefined"
#define NID_undef                       0
#define OBJ_undef                       0L

#define SN_itu_t                "ITU-T"
#define LN_itu_t                "itu-t"
#define NID_itu_t               645
#define OBJ_itu_t               0L

#define NID_ccitt               404
#define OBJ_ccitt               OBJ_itu_t

#define SN_iso          "ISO"
#define LN_iso          "iso"
#define NID_iso         181
#define OBJ_iso         1L

#define SN_joint_iso_itu_t              "JOINT-ISO-ITU-T"
#define LN_joint_iso_itu_t              "joint-iso-itu-t"
#define NID_joint_iso_itu_t             646
#define OBJ_joint_iso_itu_t             2L

#define NID_joint_iso_ccitt             393
#define OBJ_joint_iso_ccitt             OBJ_joint_iso_itu_t

#define SN_member_body          "member-body"
#define LN_member_body          "ISO Member Body"
#define NID_member_body         182
#define OBJ_member_body         OBJ_iso,2L

#define SN_identified_organization              "identified-organization"
#define NID_identified_organization             676
#define OBJ_identified_organization             OBJ_iso,3L

#define SN_gmac         "GMAC"
#define LN_gmac         "gmac"
#define NID_gmac                1195
#define OBJ_gmac                OBJ_iso,0L,9797L,3L,4L

#define SN_hmac_md5             "HMAC-MD5"
#define LN_hmac_md5             "hmac-md5"
#define NID_hmac_md5            780
#define OBJ_hmac_md5            OBJ_identified_organization,6L,1L,5L,5L,8L,1L,1L

#define SN_hmac_sha1            "HMAC-SHA1"
#define LN_hmac_sha1            "hmac-sha1"
#define NID_hmac_sha1           781
#define OBJ_hmac_sha1           OBJ_identified_organization,6L,1L,5L,5L,8L,1L,2L

#define SN_x509ExtAdmission             "x509ExtAdmission"
#define LN_x509ExtAdmission             "Professional Information or basis for Admission"
#define NID_x509ExtAdmission            1093
#define OBJ_x509ExtAdmission            OBJ_identified_organization,36L,8L,3L,3L

#define SN_certicom_arc         "certicom-arc"
#define NID_certicom_arc                677
#define OBJ_certicom_arc                OBJ_identified_organization,132L

#define SN_ieee         "ieee"
#define NID_ieee                1170
#define OBJ_ieee                OBJ_identified_organization,111L

#define SN_ieee_siswg           "ieee-siswg"
#define LN_ieee_siswg           "IEEE Security in Storage Working Group"
#define NID_ieee_siswg          1171
#define OBJ_ieee_siswg          OBJ_ieee,2L,1619L

#define SN_international_organizations          "international-organizations"
#define LN_international_organizations          "International Organizations"
#define NID_international_organizations         647
#define OBJ_international_organizations         OBJ_joint_iso_itu_t,23L

#define SN_wap          "wap"
#define NID_wap         678
#define OBJ_wap         OBJ_international_organizations,43L

#define SN_wap_wsg              "wap-wsg"
#define NID_wap_wsg             679
#define OBJ_wap_wsg             OBJ_wap,1L

#define SN_selected_attribute_types             "selected-attribute-types"
#define LN_selected_attribute_types             "Selected Attribute Types"
#define NID_selected_attribute_types            394
#define OBJ_selected_attribute_types            OBJ_joint_iso_itu_t,5L,1L,5L

#define SN_clearance            "clearance"
#define NID_clearance           395
#define OBJ_clearance           OBJ_selected_attribute_types,55L

#define SN_ISO_US               "ISO-US"
#define LN_ISO_US               "ISO US Member Body"
#define NID_ISO_US              183
#define OBJ_ISO_US              OBJ_member_body,840L

#define SN_X9_57                "X9-57"
#define LN_X9_57                "X9.57"
#define NID_X9_57               184
#define OBJ_X9_57               OBJ_ISO_US,10040L

#define SN_X9cm         "X9cm"
#define LN_X9cm         "X9.57 CM ?"
#define NID_X9cm                185
#define OBJ_X9cm                OBJ_X9_57,4L

#define SN_ISO_CN               "ISO-CN"
#define LN_ISO_CN               "ISO CN Member Body"
#define NID_ISO_CN              1140
#define OBJ_ISO_CN              OBJ_member_body,156L

#define SN_oscca                "oscca"
#define NID_oscca               1141
#define OBJ_oscca               OBJ_ISO_CN,10197L

#define SN_sm_scheme            "sm-scheme"
#define NID_sm_scheme           1142
#define OBJ_sm_scheme           OBJ_oscca,1L

#define SN_dsa          "DSA"
#define LN_dsa          "dsaEncryption"
#define NID_dsa         116
#define OBJ_dsa         OBJ_X9cm,1L

#define SN_dsaWithSHA1          "DSA-SHA1"
#define LN_dsaWithSHA1          "dsaWithSHA1"
#define NID_dsaWithSHA1         113
#define OBJ_dsaWithSHA1         OBJ_X9cm,3L

#define SN_ansi_X9_62           "ansi-X9-62"
#define LN_ansi_X9_62           "ANSI X9.62"
#define NID_ansi_X9_62          405
#define OBJ_ansi_X9_62          OBJ_ISO_US,10045L

#define OBJ_X9_62_id_fieldType          OBJ_ansi_X9_62,1L

#define SN_X9_62_prime_field            "prime-field"
#define NID_X9_62_prime_field           406
#define OBJ_X9_62_prime_field           OBJ_X9_62_id_fieldType,1L

#define SN_X9_62_characteristic_two_field               "characteristic-two-field"
#define NID_X9_62_characteristic_two_field              407
#define OBJ_X9_62_characteristic_two_field              OBJ_X9_62_id_fieldType,2L

#define SN_X9_62_id_characteristic_two_basis            "id-characteristic-two-basis"
#define NID_X9_62_id_characteristic_two_basis           680
#define OBJ_X9_62_id_characteristic_two_basis           OBJ_X9_62_characteristic_two_field,3L

#define SN_X9_62_onBasis                "onBasis"
#define NID_X9_62_onBasis               681
#define OBJ_X9_62_onBasis               OBJ_X9_62_id_characteristic_two_basis,1L

#define SN_X9_62_tpBasis                "tpBasis"
#define NID_X9_62_tpBasis               682
#define OBJ_X9_62_tpBasis               OBJ_X9_62_id_characteristic_two_basis,2L

#define SN_X9_62_ppBasis                "ppBasis"
#define NID_X9_62_ppBasis               683
#define OBJ_X9_62_ppBasis               OBJ_X9_62_id_characteristic_two_basis,3L

#define OBJ_X9_62_id_publicKeyType              OBJ_ansi_X9_62,2L

#define SN_X9_62_id_ecPublicKey         "id-ecPublicKey"
#define NID_X9_62_id_ecPublicKey                408
#define OBJ_X9_62_id_ecPublicKey                OBJ_X9_62_id_publicKeyType,1L

#define OBJ_X9_62_ellipticCurve         OBJ_ansi_X9_62,3L

#define OBJ_X9_62_c_TwoCurve            OBJ_X9_62_ellipticCurve,0L

#define SN_X9_62_c2pnb163v1             "c2pnb163v1"
#define NID_X9_62_c2pnb163v1            684
#define OBJ_X9_62_c2pnb163v1            OBJ_X9_62_c_TwoCurve,1L

#define SN_X9_62_c2pnb163v2             "c2pnb163v2"
#define NID_X9_62_c2pnb163v2            685
#define OBJ_X9_62_c2pnb163v2            OBJ_X9_62_c_TwoCurve,2L

#define SN_X9_62_c2pnb163v3             "c2pnb163v3"
#define NID_X9_62_c2pnb163v3            686
#define OBJ_X9_62_c2pnb163v3            OBJ_X9_62_c_TwoCurve,3L

#define SN_X9_62_c2pnb176v1             "c2pnb176v1"
#define NID_X9_62_c2pnb176v1            687
#define OBJ_X9_62_c2pnb176v1            OBJ_X9_62_c_TwoCurve,4L

#define SN_X9_62_c2tnb191v1             "c2tnb191v1"
#define NID_X9_62_c2tnb191v1            688
#define OBJ_X9_62_c2tnb191v1            OBJ_X9_62_c_TwoCurve,5L

#define SN_X9_62_c2tnb191v2             "c2tnb191v2"
#define NID_X9_62_c2tnb191v2            689
#define OBJ_X9_62_c2tnb191v2            OBJ_X9_62_c_TwoCurve,6L

#define SN_X9_62_c2tnb191v3             "c2tnb191v3"
#define NID_X9_62_c2tnb191v3            690
#define OBJ_X9_62_c2tnb191v3            OBJ_X9_62_c_TwoCurve,7L

#define SN_X9_62_c2onb191v4             "c2onb191v4"
#define NID_X9_62_c2onb191v4            691
#define OBJ_X9_62_c2onb191v4            OBJ_X9_62_c_TwoCurve,8L

#define SN_X9_62_c2onb191v5             "c2onb191v5"
#define NID_X9_62_c2onb191v5            692
#define OBJ_X9_62_c2onb191v5            OBJ_X9_62_c_TwoCurve,9L

#define SN_X9_62_c2pnb208w1             "c2pnb208w1"
#define NID_X9_62_c2pnb208w1            693
#define OBJ_X9_62_c2pnb208w1            OBJ_X9_62_c_TwoCurve,10L

#define SN_X9_62_c2tnb239v1             "c2tnb239v1"
#define NID_X9_62_c2tnb239v1            694
#define OBJ_X9_62_c2tnb239v1            OBJ_X9_62_c_TwoCurve,11L

#define SN_X9_62_c2tnb239v2             "c2tnb239v2"
#define NID_X9_62_c2tnb239v2            695
#define OBJ_X9_62_c2tnb239v2            OBJ_X9_62_c_TwoCurve,12L

#define SN_X9_62_c2tnb239v3             "c2tnb239v3"
#define NID_X9_62_c2tnb239v3            696
#define OBJ_X9_62_c2tnb239v3            OBJ_X9_62_c_TwoCurve,13L

#define SN_X9_62_c2onb239v4             "c2onb239v4"
#define NID_X9_62_c2onb239v4            697
#define OBJ_X9_62_c2onb239v4            OBJ_X9_62_c_TwoCurve,14L

#define SN_X9_62_c2onb239v5             "c2onb239v5"
#define NID_X9_62_c2onb239v5            698
#define OBJ_X9_62_c2onb239v5            OBJ_X9_62_c_TwoCurve,15L

#define SN_X9_62_c2pnb272w1             "c2pnb272w1"
#define NID_X9_62_c2pnb272w1            699
#define OBJ_X9_62_c2pnb272w1            OBJ_X9_62_c_TwoCurve,16L

#define SN_X9_62_c2pnb304w1             "c2pnb304w1"
#define NID_X9_62_c2pnb304w1            700
#define OBJ_X9_62_c2pnb304w1            OBJ_X9_62_c_TwoCurve,17L

#define SN_X9_62_c2tnb359v1             "c2tnb359v1"
#define NID_X9_62_c2tnb359v1            701
#define OBJ_X9_62_c2tnb359v1            OBJ_X9_62_c_TwoCurve,18L

#define SN_X9_62_c2pnb368w1             "c2pnb368w1"
#define NID_X9_62_c2pnb368w1            702
#define OBJ_X9_62_c2pnb368w1            OBJ_X9_62_c_TwoCurve,19L

#define SN_X9_62_c2tnb431r1             "c2tnb431r1"
#define NID_X9_62_c2tnb431r1            703
#define OBJ_X9_62_c2tnb431r1            OBJ_X9_62_c_TwoCurve,20L

#define OBJ_X9_62_primeCurve            OBJ_X9_62_ellipticCurve,1L

#define SN_X9_62_prime192v1             "prime192v1"
#define NID_X9_62_prime192v1            409
#define OBJ_X9_62_prime192v1            OBJ_X9_62_primeCurve,1L

#define SN_X9_62_prime192v2             "prime192v2"
#define NID_X9_62_prime192v2            410
#define OBJ_X9_62_prime192v2            OBJ_X9_62_primeCurve,2L

#define SN_X9_62_prime192v3             "prime192v3"
#define NID_X9_62_prime192v3            411
#define OBJ_X9_62_prime192v3            OBJ_X9_62_primeCurve,3L

#define SN_X9_62_prime239v1             "prime239v1"
#define NID_X9_62_prime239v1            412
#define OBJ_X9_62_prime239v1            OBJ_X9_62_primeCurve,4L

#define SN_X9_62_prime239v2             "prime239v2"
#define NID_X9_62_prime239v2            413
#define OBJ_X9_62_prime239v2            OBJ_X9_62_primeCurve,5L

#define SN_X9_62_prime239v3             "prime239v3"
#define NID_X9_62_prime239v3            414
#define OBJ_X9_62_prime239v3            OBJ_X9_62_primeCurve,6L

#define SN_X9_62_prime256v1             "prime256v1"
#define NID_X9_62_prime256v1            415
#define OBJ_X9_62_prime256v1            OBJ_X9_62_primeCurve,7L

#define OBJ_X9_62_id_ecSigType          OBJ_ansi_X9_62,4L

#define SN_ecdsa_with_SHA1              "ecdsa-with-SHA1"
#define NID_ecdsa_with_SHA1             416
#define OBJ_ecdsa_with_SHA1             OBJ_X9_62_id_ecSigType,1L

#define SN_ecdsa_with_Recommended               "ecdsa-with-Recommended"
#define NID_ecdsa_with_Recommended              791
#define OBJ_ecdsa_with_Recommended              OBJ_X9_62_id_ecSigType,2L

#define SN_ecdsa_with_Specified         "ecdsa-with-Specified"
#define NID_ecdsa_with_Specified                792
#define OBJ_ecdsa_with_Specified                OBJ_X9_62_id_ecSigType,3L

#define SN_ecdsa_with_SHA224            "ecdsa-with-SHA224"
#define NID_ecdsa_with_SHA224           793
#define OBJ_ecdsa_with_SHA224           OBJ_ecdsa_with_Specified,1L

#define SN_ecdsa_with_SHA256            "ecdsa-with-SHA256"
#define NID_ecdsa_with_SHA256           794
#define OBJ_ecdsa_with_SHA256           OBJ_ecdsa_with_Specified,2L

#define SN_ecdsa_with_SHA384            "ecdsa-with-SHA384"
#define NID_ecdsa_with_SHA384           795
#define OBJ_ecdsa_with_SHA384           OBJ_ecdsa_with_Specified,3L

#define SN_ecdsa_with_SHA512            "ecdsa-with-SHA512"
#define NID_ecdsa_with_SHA512           796
#define OBJ_ecdsa_with_SHA512           OBJ_ecdsa_with_Specified,4L

#define OBJ_secg_ellipticCurve          OBJ_certicom_arc,0L

#define SN_secp112r1            "secp112r1"
#define NID_secp112r1           704
#define OBJ_secp112r1           OBJ_secg_ellipticCurve,6L

#define SN_secp112r2            "secp112r2"
#define NID_secp112r2           705
#define OBJ_secp112r2           OBJ_secg_ellipticCurve,7L

#define SN_secp128r1            "secp128r1"
#define NID_secp128r1           706
#define OBJ_secp128r1           OBJ_secg_ellipticCurve,28L

#define SN_secp128r2            "secp128r2"
#define NID_secp128r2           707
#define OBJ_secp128r2           OBJ_secg_ellipticCurve,29L

#define SN_secp160k1            "secp160k1"
#define NID_secp160k1           708
#define OBJ_secp160k1           OBJ_secg_ellipticCurve,9L

#define SN_secp160r1            "secp160r1"
#define NID_secp160r1           709
#define OBJ_secp160r1           OBJ_secg_ellipticCurve,8L

#define SN_secp160r2            "secp160r2"
#define NID_secp160r2           710
#define OBJ_secp160r2           OBJ_secg_ellipticCurve,30L

#define SN_secp192k1            "secp192k1"
#define NID_secp192k1           711
#define OBJ_secp192k1           OBJ_secg_ellipticCurve,31L

#define SN_secp224k1            "secp224k1"
#define NID_secp224k1           712
#define OBJ_secp224k1           OBJ_secg_ellipticCurve,32L

#define SN_secp224r1            "secp224r1"
#define NID_secp224r1           713
#define OBJ_secp224r1           OBJ_secg_ellipticCurve,33L

#define SN_secp256k1            "secp256k1"
#define NID_secp256k1           714
#define OBJ_secp256k1           OBJ_secg_ellipticCurve,10L

#define SN_secp384r1            "secp384r1"
#define NID_secp384r1           715
#define OBJ_secp384r1           OBJ_secg_ellipticCurve,34L

#define SN_secp521r1            "secp521r1"
#define NID_secp521r1           716
#define OBJ_secp521r1           OBJ_secg_ellipticCurve,35L

#define SN_sect113r1            "sect113r1"
#define NID_sect113r1           717
#define OBJ_sect113r1           OBJ_secg_ellipticCurve,4L

#define SN_sect113r2            "sect113r2"
#define NID_sect113r2           718
#define OBJ_sect113r2           OBJ_secg_ellipticCurve,5L

#define SN_sect131r1            "sect131r1"
#define NID_sect131r1           719
#define OBJ_sect131r1           OBJ_secg_ellipticCurve,22L

#define SN_sect131r2            "sect131r2"
#define NID_sect131r2           720
#define OBJ_sect131r2           OBJ_secg_ellipticCurve,23L

#define SN_sect163k1            "sect163k1"
#define NID_sect163k1           721
#define OBJ_sect163k1           OBJ_secg_ellipticCurve,1L

#define SN_sect163r1            "sect163r1"
#define NID_sect163r1           722
#define OBJ_sect163r1           OBJ_secg_ellipticCurve,2L

#define SN_sect163r2            "sect163r2"
#define NID_sect163r2           723
#define OBJ_sect163r2           OBJ_secg_ellipticCurve,15L

#define SN_sect193r1            "sect193r1"
#define NID_sect193r1           724
#define OBJ_sect193r1           OBJ_secg_ellipticCurve,24L

#define SN_sect193r2            "sect193r2"
#define NID_sect193r2           725
#define OBJ_sect193r2           OBJ_secg_ellipticCurve,25L

#define SN_sect233k1            "sect233k1"
#define NID_sect233k1           726
#define OBJ_sect233k1           OBJ_secg_ellipticCurve,26L

#define SN_sect233r1            "sect233r1"
#define NID_sect233r1           727
#define OBJ_sect233r1           OBJ_secg_ellipticCurve,27L

#define SN_sect239k1            "sect239k1"
#define NID_sect239k1           728
#define OBJ_sect239k1           OBJ_secg_ellipticCurve,3L

#define SN_sect283k1            "sect283k1"
#define NID_sect283k1           729
#define OBJ_sect283k1           OBJ_secg_ellipticCurve,16L

#define SN_sect283r1            "sect283r1"
#define NID_sect283r1           730
#define OBJ_sect283r1           OBJ_secg_ellipticCurve,17L

#define SN_sect409k1            "sect409k1"
#define NID_sect409k1           731
#define OBJ_sect409k1           OBJ_secg_ellipticCurve,36L

#define SN_sect409r1            "sect409r1"
#define NID_sect409r1           732
#define OBJ_sect409r1           OBJ_secg_ellipticCurve,37L

#define SN_sect571k1            "sect571k1"
#define NID_sect571k1           733
#define OBJ_sect571k1           OBJ_secg_ellipticCurve,38L

#define SN_sect571r1            "sect571r1"
#define NID_sect571r1           734
#define OBJ_sect571r1           OBJ_secg_ellipticCurve,39L

#define OBJ_wap_wsg_idm_ecid            OBJ_wap_wsg,4L

#define SN_wap_wsg_idm_ecid_wtls1               "wap-wsg-idm-ecid-wtls1"
#define NID_wap_wsg_idm_ecid_wtls1              735
#define OBJ_wap_wsg_idm_ecid_wtls1              OBJ_wap_wsg_idm_ecid,1L

#define SN_wap_wsg_idm_ecid_wtls3               "wap-wsg-idm-ecid-wtls3"
#define NID_wap_wsg_idm_ecid_wtls3              736
#define OBJ_wap_wsg_idm_ecid_wtls3              OBJ_wap_wsg_idm_ecid,3L

#define SN_wap_wsg_idm_ecid_wtls4               "wap-wsg-idm-ecid-wtls4"
#define NID_wap_wsg_idm_ecid_wtls4              737
#define OBJ_wap_wsg_idm_ecid_wtls4              OBJ_wap_wsg_idm_ecid,4L

#define SN_wap_wsg_idm_ecid_wtls5               "wap-wsg-idm-ecid-wtls5"
#define NID_wap_wsg_idm_ecid_wtls5              738
#define OBJ_wap_wsg_idm_ecid_wtls5              OBJ_wap_wsg_idm_ecid,5L

#define SN_wap_wsg_idm_ecid_wtls6               "wap-wsg-idm-ecid-wtls6"
#define NID_wap_wsg_idm_ecid_wtls6              739
#define OBJ_wap_wsg_idm_ecid_wtls6              OBJ_wap_wsg_idm_ecid,6L

#define SN_wap_wsg_idm_ecid_wtls7               "wap-wsg-idm-ecid-wtls7"
#define NID_wap_wsg_idm_ecid_wtls7              740
#define OBJ_wap_wsg_idm_ecid_wtls7              OBJ_wap_wsg_idm_ecid,7L

#define SN_wap_wsg_idm_ecid_wtls8               "wap-wsg-idm-ecid-wtls8"
#define NID_wap_wsg_idm_ecid_wtls8              741
#define OBJ_wap_wsg_idm_ecid_wtls8              OBJ_wap_wsg_idm_ecid,8L

#define SN_wap_wsg_idm_ecid_wtls9               "wap-wsg-idm-ecid-wtls9"
#define NID_wap_wsg_idm_ecid_wtls9              742
#define OBJ_wap_wsg_idm_ecid_wtls9              OBJ_wap_wsg_idm_ecid,9L

#define SN_wap_wsg_idm_ecid_wtls10              "wap-wsg-idm-ecid-wtls10"
#define NID_wap_wsg_idm_ecid_wtls10             743
#define OBJ_wap_wsg_idm_ecid_wtls10             OBJ_wap_wsg_idm_ecid,10L

#define SN_wap_wsg_idm_ecid_wtls11              "wap-wsg-idm-ecid-wtls11"
#define NID_wap_wsg_idm_ecid_wtls11             744
#define OBJ_wap_wsg_idm_ecid_wtls11             OBJ_wap_wsg_idm_ecid,11L

#define SN_wap_wsg_idm_ecid_wtls12              "wap-wsg-idm-ecid-wtls12"
#define NID_wap_wsg_idm_ecid_wtls12             745
#define OBJ_wap_wsg_idm_ecid_wtls12             OBJ_wap_wsg_idm_ecid,12L

#define SN_cast5_cbc            "CAST5-CBC"
#define LN_cast5_cbc            "cast5-cbc"
#define NID_cast5_cbc           108
#define OBJ_cast5_cbc           OBJ_ISO_US,113533L,7L,66L,10L

#define SN_cast5_ecb            "CAST5-ECB"
#define LN_cast5_ecb            "cast5-ecb"
#define NID_cast5_ecb           109

#define SN_cast5_cfb64          "CAST5-CFB"
#define LN_cast5_cfb64          "cast5-cfb"
#define NID_cast5_cfb64         110

#define SN_cast5_ofb64          "CAST5-OFB"
#define LN_cast5_ofb64          "cast5-ofb"
#define NID_cast5_ofb64         111

#define LN_pbeWithMD5AndCast5_CBC               "pbeWithMD5AndCast5CBC"
#define NID_pbeWithMD5AndCast5_CBC              112
#define OBJ_pbeWithMD5AndCast5_CBC              OBJ_ISO_US,113533L,7L,66L,12L

#define SN_id_PasswordBasedMAC          "id-PasswordBasedMAC"
#define LN_id_PasswordBasedMAC          "password based MAC"
#define NID_id_PasswordBasedMAC         782
#define OBJ_id_PasswordBasedMAC         OBJ_ISO_US,113533L,7L,66L,13L

#define SN_id_DHBasedMac                "id-DHBasedMac"
#define LN_id_DHBasedMac                "Diffie-Hellman based MAC"
#define NID_id_DHBasedMac               783
#define OBJ_id_DHBasedMac               OBJ_ISO_US,113533L,7L,66L,30L

#define SN_rsadsi               "rsadsi"
#define LN_rsadsi               "RSA Data Security, Inc."
#define NID_rsadsi              1
#define OBJ_rsadsi              OBJ_ISO_US,113549L

#define SN_pkcs         "pkcs"
#define LN_pkcs         "RSA Data Security, Inc. PKCS"
#define NID_pkcs                2
#define OBJ_pkcs                OBJ_rsadsi,1L

#define SN_pkcs1                "pkcs1"
#define NID_pkcs1               186
#define OBJ_pkcs1               OBJ_pkcs,1L

#define LN_rsaEncryption                "rsaEncryption"
#define NID_rsaEncryption               6
#define OBJ_rsaEncryption               OBJ_pkcs1,1L

#define SN_md2WithRSAEncryption         "RSA-MD2"
#define LN_md2WithRSAEncryption         "md2WithRSAEncryption"
#define NID_md2WithRSAEncryption                7
#define OBJ_md2WithRSAEncryption                OBJ_pkcs1,2L

#define SN_md4WithRSAEncryption         "RSA-MD4"
#define LN_md4WithRSAEncryption         "md4WithRSAEncryption"
#define NID_md4WithRSAEncryption                396
#define OBJ_md4WithRSAEncryption                OBJ_pkcs1,3L

#define SN_md5WithRSAEncryption         "RSA-MD5"
#define LN_md5WithRSAEncryption         "md5WithRSAEncryption"
#define NID_md5WithRSAEncryption                8
#define OBJ_md5WithRSAEncryption                OBJ_pkcs1,4L

#define SN_sha1WithRSAEncryption                "RSA-SHA1"
#define LN_sha1WithRSAEncryption                "sha1WithRSAEncryption"
#define NID_sha1WithRSAEncryption               65
#define OBJ_sha1WithRSAEncryption               OBJ_pkcs1,5L

#define SN_rsaesOaep            "RSAES-OAEP"
#define LN_rsaesOaep            "rsaesOaep"
#define NID_rsaesOaep           919
#define OBJ_rsaesOaep           OBJ_pkcs1,7L

#define SN_mgf1         "MGF1"
#define LN_mgf1         "mgf1"
#define NID_mgf1                911
#define OBJ_mgf1                OBJ_pkcs1,8L

#define SN_pSpecified           "PSPECIFIED"
#define LN_pSpecified           "pSpecified"
#define NID_pSpecified          935
#define OBJ_pSpecified          OBJ_pkcs1,9L

#define SN_rsassaPss            "RSASSA-PSS"
#define LN_rsassaPss            "rsassaPss"
#define NID_rsassaPss           912
#define OBJ_rsassaPss           OBJ_pkcs1,10L

#define SN_sha256WithRSAEncryption              "RSA-SHA256"
#define LN_sha256WithRSAEncryption              "sha256WithRSAEncryption"
#define NID_sha256WithRSAEncryption             668
#define OBJ_sha256WithRSAEncryption             OBJ_pkcs1,11L

#define SN_sha384WithRSAEncryption              "RSA-SHA384"
#define LN_sha384WithRSAEncryption              "sha384WithRSAEncryption"
#define NID_sha384WithRSAEncryption             669
#define OBJ_sha384WithRSAEncryption             OBJ_pkcs1,12L

#define SN_sha512WithRSAEncryption              "RSA-SHA512"
#define LN_sha512WithRSAEncryption              "sha512WithRSAEncryption"
#define NID_sha512WithRSAEncryption             670
#define OBJ_sha512WithRSAEncryption             OBJ_pkcs1,13L

#define SN_sha224WithRSAEncryption              "RSA-SHA224"
#define LN_sha224WithRSAEncryption              "sha224WithRSAEncryption"
#define NID_sha224WithRSAEncryption             671
#define OBJ_sha224WithRSAEncryption             OBJ_pkcs1,14L

#define SN_sha512_224WithRSAEncryption          "RSA-SHA512/224"
#define LN_sha512_224WithRSAEncryption          "sha512-224WithRSAEncryption"
#define NID_sha512_224WithRSAEncryption         1145
#define OBJ_sha512_224WithRSAEncryption         OBJ_pkcs1,15L

#define SN_sha512_256WithRSAEncryption          "RSA-SHA512/256"
#define LN_sha512_256WithRSAEncryption          "sha512-256WithRSAEncryption"
#define NID_sha512_256WithRSAEncryption         1146
#define OBJ_sha512_256WithRSAEncryption         OBJ_pkcs1,16L

#define SN_pkcs3                "pkcs3"
#define NID_pkcs3               27
#define OBJ_pkcs3               OBJ_pkcs,3L

#define LN_dhKeyAgreement               "dhKeyAgreement"
#define NID_dhKeyAgreement              28
#define OBJ_dhKeyAgreement              OBJ_pkcs3,1L

#define SN_pkcs5                "pkcs5"
#define NID_pkcs5               187
#define OBJ_pkcs5               OBJ_pkcs,5L

#define SN_pbeWithMD2AndDES_CBC         "PBE-MD2-DES"
#define LN_pbeWithMD2AndDES_CBC         "pbeWithMD2AndDES-CBC"
#define NID_pbeWithMD2AndDES_CBC                9
#define OBJ_pbeWithMD2AndDES_CBC                OBJ_pkcs5,1L

#define SN_pbeWithMD5AndDES_CBC         "PBE-MD5-DES"
#define LN_pbeWithMD5AndDES_CBC         "pbeWithMD5AndDES-CBC"
#define NID_pbeWithMD5AndDES_CBC                10
#define OBJ_pbeWithMD5AndDES_CBC                OBJ_pkcs5,3L

#define SN_pbeWithMD2AndRC2_CBC         "PBE-MD2-RC2-64"
#define LN_pbeWithMD2AndRC2_CBC         "pbeWithMD2AndRC2-CBC"
#define NID_pbeWithMD2AndRC2_CBC                168
#define OBJ_pbeWithMD2AndRC2_CBC                OBJ_pkcs5,4L

#define SN_pbeWithMD5AndRC2_CBC         "PBE-MD5-RC2-64"
#define LN_pbeWithMD5AndRC2_CBC         "pbeWithMD5AndRC2-CBC"
#define NID_pbeWithMD5AndRC2_CBC                169
#define OBJ_pbeWithMD5AndRC2_CBC                OBJ_pkcs5,6L

#define SN_pbeWithSHA1AndDES_CBC                "PBE-SHA1-DES"
#define LN_pbeWithSHA1AndDES_CBC                "pbeWithSHA1AndDES-CBC"
#define NID_pbeWithSHA1AndDES_CBC               170
#define OBJ_pbeWithSHA1AndDES_CBC               OBJ_pkcs5,10L

#define SN_pbeWithSHA1AndRC2_CBC                "PBE-SHA1-RC2-64"
#define LN_pbeWithSHA1AndRC2_CBC                "pbeWithSHA1AndRC2-CBC"
#define NID_pbeWithSHA1AndRC2_CBC               68
#define OBJ_pbeWithSHA1AndRC2_CBC               OBJ_pkcs5,11L

#define LN_id_pbkdf2            "PBKDF2"
#define NID_id_pbkdf2           69
#define OBJ_id_pbkdf2           OBJ_pkcs5,12L

#define LN_pbes2                "PBES2"
#define NID_pbes2               161
#define OBJ_pbes2               OBJ_pkcs5,13L

#define LN_pbmac1               "PBMAC1"
#define NID_pbmac1              162
#define OBJ_pbmac1              OBJ_pkcs5,14L

#define SN_pkcs7                "pkcs7"
#define NID_pkcs7               20
#define OBJ_pkcs7               OBJ_pkcs,7L

#define LN_pkcs7_data           "pkcs7-data"
#define NID_pkcs7_data          21
#define OBJ_pkcs7_data          OBJ_pkcs7,1L

#define LN_pkcs7_signed         "pkcs7-signedData"
#define NID_pkcs7_signed                22
#define OBJ_pkcs7_signed                OBJ_pkcs7,2L

#define LN_pkcs7_enveloped              "pkcs7-envelopedData"
#define NID_pkcs7_enveloped             23
#define OBJ_pkcs7_enveloped             OBJ_pkcs7,3L

#define LN_pkcs7_signedAndEnveloped             "pkcs7-signedAndEnvelopedData"
#define NID_pkcs7_signedAndEnveloped            24
#define OBJ_pkcs7_signedAndEnveloped            OBJ_pkcs7,4L

#define LN_pkcs7_digest         "pkcs7-digestData"
#define NID_pkcs7_digest                25
#define OBJ_pkcs7_digest                OBJ_pkcs7,5L

#define LN_pkcs7_encrypted              "pkcs7-encryptedData"
#define NID_pkcs7_encrypted             26
#define OBJ_pkcs7_encrypted             OBJ_pkcs7,6L

#define SN_pkcs9                "pkcs9"
#define NID_pkcs9               47
#define OBJ_pkcs9               OBJ_pkcs,9L

#define LN_pkcs9_emailAddress           "emailAddress"
#define NID_pkcs9_emailAddress          48
#define OBJ_pkcs9_emailAddress          OBJ_pkcs9,1L

#define LN_pkcs9_unstructuredName               "unstructuredName"
#define NID_pkcs9_unstructuredName              49
#define OBJ_pkcs9_unstructuredName              OBJ_pkcs9,2L

#define LN_pkcs9_contentType            "contentType"
#define NID_pkcs9_contentType           50
#define OBJ_pkcs9_contentType           OBJ_pkcs9,3L

#define LN_pkcs9_messageDigest          "messageDigest"
#define NID_pkcs9_messageDigest         51
#define OBJ_pkcs9_messageDigest         OBJ_pkcs9,4L

#define LN_pkcs9_signingTime            "signingTime"
#define NID_pkcs9_signingTime           52
#define OBJ_pkcs9_signingTime           OBJ_pkcs9,5L

#define LN_pkcs9_countersignature               "countersignature"
#define NID_pkcs9_countersignature              53
#define OBJ_pkcs9_countersignature              OBJ_pkcs9,6L

#define LN_pkcs9_challengePassword              "challengePassword"
#define NID_pkcs9_challengePassword             54
#define OBJ_pkcs9_challengePassword             OBJ_pkcs9,7L

#define LN_pkcs9_unstructuredAddress            "unstructuredAddress"
#define NID_pkcs9_unstructuredAddress           55
#define OBJ_pkcs9_unstructuredAddress           OBJ_pkcs9,8L

#define LN_pkcs9_extCertAttributes              "extendedCertificateAttributes"
#define NID_pkcs9_extCertAttributes             56
#define OBJ_pkcs9_extCertAttributes             OBJ_pkcs9,9L

#define SN_ext_req              "extReq"
#define LN_ext_req              "Extension Request"
#define NID_ext_req             172
#define OBJ_ext_req             OBJ_pkcs9,14L

#define SN_SMIMECapabilities            "SMIME-CAPS"
#define LN_SMIMECapabilities            "S/MIME Capabilities"
#define NID_SMIMECapabilities           167
#define OBJ_SMIMECapabilities           OBJ_pkcs9,15L

#define SN_SMIME                "SMIME"
#define LN_SMIME                "S/MIME"
#define NID_SMIME               188
#define OBJ_SMIME               OBJ_pkcs9,16L

#define SN_id_smime_mod         "id-smime-mod"
#define NID_id_smime_mod                189
#define OBJ_id_smime_mod                OBJ_SMIME,0L

#define SN_id_smime_ct          "id-smime-ct"
#define NID_id_smime_ct         190
#define OBJ_id_smime_ct         OBJ_SMIME,1L

#define SN_id_smime_aa          "id-smime-aa"
#define NID_id_smime_aa         191
#define OBJ_id_smime_aa         OBJ_SMIME,2L

#define SN_id_smime_alg         "id-smime-alg"
#define NID_id_smime_alg                192
#define OBJ_id_smime_alg                OBJ_SMIME,3L

#define SN_id_smime_cd          "id-smime-cd"
#define NID_id_smime_cd         193
#define OBJ_id_smime_cd         OBJ_SMIME,4L

#define SN_id_smime_spq         "id-smime-spq"
#define NID_id_smime_spq                194
#define OBJ_id_smime_spq                OBJ_SMIME,5L

#define SN_id_smime_cti         "id-smime-cti"
#define NID_id_smime_cti                195
#define OBJ_id_smime_cti                OBJ_SMIME,6L

#define SN_id_smime_mod_cms             "id-smime-mod-cms"
#define NID_id_smime_mod_cms            196
#define OBJ_id_smime_mod_cms            OBJ_id_smime_mod,1L

#define SN_id_smime_mod_ess             "id-smime-mod-ess"
#define NID_id_smime_mod_ess            197
#define OBJ_id_smime_mod_ess            OBJ_id_smime_mod,2L

#define SN_id_smime_mod_oid             "id-smime-mod-oid"
#define NID_id_smime_mod_oid            198
#define OBJ_id_smime_mod_oid            OBJ_id_smime_mod,3L

#define SN_id_smime_mod_msg_v3          "id-smime-mod-msg-v3"
#define NID_id_smime_mod_msg_v3         199
#define OBJ_id_smime_mod_msg_v3         OBJ_id_smime_mod,4L

#define SN_id_smime_mod_ets_eSignature_88               "id-smime-mod-ets-eSignature-88"
#define NID_id_smime_mod_ets_eSignature_88              200
#define OBJ_id_smime_mod_ets_eSignature_88              OBJ_id_smime_mod,5L

#define SN_id_smime_mod_ets_eSignature_97               "id-smime-mod-ets-eSignature-97"
#define NID_id_smime_mod_ets_eSignature_97              201
#define OBJ_id_smime_mod_ets_eSignature_97              OBJ_id_smime_mod,6L

#define SN_id_smime_mod_ets_eSigPolicy_88               "id-smime-mod-ets-eSigPolicy-88"
#define NID_id_smime_mod_ets_eSigPolicy_88              202
#define OBJ_id_smime_mod_ets_eSigPolicy_88              OBJ_id_smime_mod,7L

#define SN_id_smime_mod_ets_eSigPolicy_97               "id-smime-mod-ets-eSigPolicy-97"
#define NID_id_smime_mod_ets_eSigPolicy_97              203
#define OBJ_id_smime_mod_ets_eSigPolicy_97              OBJ_id_smime_mod,8L

#define SN_id_smime_ct_receipt          "id-smime-ct-receipt"
#define NID_id_smime_ct_receipt         204
#define OBJ_id_smime_ct_receipt         OBJ_id_smime_ct,1L

#define SN_id_smime_ct_authData         "id-smime-ct-authData"
#define NID_id_smime_ct_authData                205
#define OBJ_id_smime_ct_authData                OBJ_id_smime_ct,2L

#define SN_id_smime_ct_publishCert              "id-smime-ct-publishCert"
#define NID_id_smime_ct_publishCert             206
#define OBJ_id_smime_ct_publishCert             OBJ_id_smime_ct,3L

#define SN_id_smime_ct_TSTInfo          "id-smime-ct-TSTInfo"
#define NID_id_smime_ct_TSTInfo         207
#define OBJ_id_smime_ct_TSTInfo         OBJ_id_smime_ct,4L

#define SN_id_smime_ct_TDTInfo          "id-smime-ct-TDTInfo"
#define NID_id_smime_ct_TDTInfo         208
#define OBJ_id_smime_ct_TDTInfo         OBJ_id_smime_ct,5L

#define SN_id_smime_ct_contentInfo              "id-smime-ct-contentInfo"
#define NID_id_smime_ct_contentInfo             209
#define OBJ_id_smime_ct_contentInfo             OBJ_id_smime_ct,6L

#define SN_id_smime_ct_DVCSRequestData          "id-smime-ct-DVCSRequestData"
#define NID_id_smime_ct_DVCSRequestData         210
#define OBJ_id_smime_ct_DVCSRequestData         OBJ_id_smime_ct,7L

#define SN_id_smime_ct_DVCSResponseData         "id-smime-ct-DVCSResponseData"
#define NID_id_smime_ct_DVCSResponseData                211
#define OBJ_id_smime_ct_DVCSResponseData                OBJ_id_smime_ct,8L

#define SN_id_smime_ct_compressedData           "id-smime-ct-compressedData"
#define NID_id_smime_ct_compressedData          786
#define OBJ_id_smime_ct_compressedData          OBJ_id_smime_ct,9L

#define SN_id_smime_ct_contentCollection                "id-smime-ct-contentCollection"
#define NID_id_smime_ct_contentCollection               1058
#define OBJ_id_smime_ct_contentCollection               OBJ_id_smime_ct,19L

#define SN_id_smime_ct_authEnvelopedData                "id-smime-ct-authEnvelopedData"
#define NID_id_smime_ct_authEnvelopedData               1059
#define OBJ_id_smime_ct_authEnvelopedData               OBJ_id_smime_ct,23L

#define SN_id_ct_routeOriginAuthz               "id-ct-routeOriginAuthz"
#define NID_id_ct_routeOriginAuthz              1234
#define OBJ_id_ct_routeOriginAuthz              OBJ_id_smime_ct,24L

#define SN_id_ct_rpkiManifest           "id-ct-rpkiManifest"
#define NID_id_ct_rpkiManifest          1235
#define OBJ_id_ct_rpkiManifest          OBJ_id_smime_ct,26L

#define SN_id_ct_asciiTextWithCRLF              "id-ct-asciiTextWithCRLF"
#define NID_id_ct_asciiTextWithCRLF             787
#define OBJ_id_ct_asciiTextWithCRLF             OBJ_id_smime_ct,27L

#define SN_id_ct_xml            "id-ct-xml"
#define NID_id_ct_xml           1060
#define OBJ_id_ct_xml           OBJ_id_smime_ct,28L

#define SN_id_ct_rpkiGhostbusters               "id-ct-rpkiGhostbusters"
#define NID_id_ct_rpkiGhostbusters              1236
#define OBJ_id_ct_rpkiGhostbusters              OBJ_id_smime_ct,35L

#define SN_id_ct_resourceTaggedAttest           "id-ct-resourceTaggedAttest"
#define NID_id_ct_resourceTaggedAttest          1237
#define OBJ_id_ct_resourceTaggedAttest          OBJ_id_smime_ct,36L

#define SN_id_ct_geofeedCSVwithCRLF             "id-ct-geofeedCSVwithCRLF"
#define NID_id_ct_geofeedCSVwithCRLF            1246
#define OBJ_id_ct_geofeedCSVwithCRLF            OBJ_id_smime_ct,47L

#define SN_id_ct_signedChecklist                "id-ct-signedChecklist"
#define NID_id_ct_signedChecklist               1247
#define OBJ_id_ct_signedChecklist               OBJ_id_smime_ct,48L

#define SN_id_ct_ASPA           "id-ct-ASPA"
#define NID_id_ct_ASPA          1250
#define OBJ_id_ct_ASPA          OBJ_id_smime_ct,49L

#define SN_id_ct_signedTAL              "id-ct-signedTAL"
#define NID_id_ct_signedTAL             1284
#define OBJ_id_ct_signedTAL             OBJ_id_smime_ct,50L

#define SN_id_ct_rpkiSignedPrefixList           "id-ct-rpkiSignedPrefixList"
#define NID_id_ct_rpkiSignedPrefixList          1320
#define OBJ_id_ct_rpkiSignedPrefixList          OBJ_id_smime_ct,51L

#define SN_id_smime_aa_receiptRequest           "id-smime-aa-receiptRequest"
#define NID_id_smime_aa_receiptRequest          212
#define OBJ_id_smime_aa_receiptRequest          OBJ_id_smime_aa,1L

#define SN_id_smime_aa_securityLabel            "id-smime-aa-securityLabel"
#define NID_id_smime_aa_securityLabel           213
#define OBJ_id_smime_aa_securityLabel           OBJ_id_smime_aa,2L

#define SN_id_smime_aa_mlExpandHistory          "id-smime-aa-mlExpandHistory"
#define NID_id_smime_aa_mlExpandHistory         214
#define OBJ_id_smime_aa_mlExpandHistory         OBJ_id_smime_aa,3L

#define SN_id_smime_aa_contentHint              "id-smime-aa-contentHint"
#define NID_id_smime_aa_contentHint             215
#define OBJ_id_smime_aa_contentHint             OBJ_id_smime_aa,4L

#define SN_id_smime_aa_msgSigDigest             "id-smime-aa-msgSigDigest"
#define NID_id_smime_aa_msgSigDigest            216
#define OBJ_id_smime_aa_msgSigDigest            OBJ_id_smime_aa,5L

#define SN_id_smime_aa_encapContentType         "id-smime-aa-encapContentType"
#define NID_id_smime_aa_encapContentType                217
#define OBJ_id_smime_aa_encapContentType                OBJ_id_smime_aa,6L

#define SN_id_smime_aa_contentIdentifier                "id-smime-aa-contentIdentifier"
#define NID_id_smime_aa_contentIdentifier               218
#define OBJ_id_smime_aa_contentIdentifier               OBJ_id_smime_aa,7L

#define SN_id_smime_aa_macValue         "id-smime-aa-macValue"
#define NID_id_smime_aa_macValue                219
#define OBJ_id_smime_aa_macValue                OBJ_id_smime_aa,8L

#define SN_id_smime_aa_equivalentLabels         "id-smime-aa-equivalentLabels"
#define NID_id_smime_aa_equivalentLabels                220
#define OBJ_id_smime_aa_equivalentLabels                OBJ_id_smime_aa,9L

#define SN_id_smime_aa_contentReference         "id-smime-aa-contentReference"
#define NID_id_smime_aa_contentReference                221
#define OBJ_id_smime_aa_contentReference                OBJ_id_smime_aa,10L

#define SN_id_smime_aa_encrypKeyPref            "id-smime-aa-encrypKeyPref"
#define NID_id_smime_aa_encrypKeyPref           222
#define OBJ_id_smime_aa_encrypKeyPref           OBJ_id_smime_aa,11L

#define SN_id_smime_aa_signingCertificate               "id-smime-aa-signingCertificate"
#define NID_id_smime_aa_signingCertificate              223
#define OBJ_id_smime_aa_signingCertificate              OBJ_id_smime_aa,12L

#define SN_id_smime_aa_smimeEncryptCerts                "id-smime-aa-smimeEncryptCerts"
#define NID_id_smime_aa_smimeEncryptCerts               224
#define OBJ_id_smime_aa_smimeEncryptCerts               OBJ_id_smime_aa,13L

#define SN_id_smime_aa_timeStampToken           "id-smime-aa-timeStampToken"
#define NID_id_smime_aa_timeStampToken          225
#define OBJ_id_smime_aa_timeStampToken          OBJ_id_smime_aa,14L

#define SN_id_smime_aa_ets_sigPolicyId          "id-smime-aa-ets-sigPolicyId"
#define NID_id_smime_aa_ets_sigPolicyId         226
#define OBJ_id_smime_aa_ets_sigPolicyId         OBJ_id_smime_aa,15L

#define SN_id_smime_aa_ets_commitmentType               "id-smime-aa-ets-commitmentType"
#define NID_id_smime_aa_ets_commitmentType              227
#define OBJ_id_smime_aa_ets_commitmentType              OBJ_id_smime_aa,16L

#define SN_id_smime_aa_ets_signerLocation               "id-smime-aa-ets-signerLocation"
#define NID_id_smime_aa_ets_signerLocation              228
#define OBJ_id_smime_aa_ets_signerLocation              OBJ_id_smime_aa,17L

#define SN_id_smime_aa_ets_signerAttr           "id-smime-aa-ets-signerAttr"
#define NID_id_smime_aa_ets_signerAttr          229
#define OBJ_id_smime_aa_ets_signerAttr          OBJ_id_smime_aa,18L

#define SN_id_smime_aa_ets_otherSigCert         "id-smime-aa-ets-otherSigCert"
#define NID_id_smime_aa_ets_otherSigCert                230
#define OBJ_id_smime_aa_ets_otherSigCert                OBJ_id_smime_aa,19L

#define SN_id_smime_aa_ets_contentTimestamp             "id-smime-aa-ets-contentTimestamp"
#define NID_id_smime_aa_ets_contentTimestamp            231
#define OBJ_id_smime_aa_ets_contentTimestamp            OBJ_id_smime_aa,20L

#define SN_id_smime_aa_ets_CertificateRefs              "id-smime-aa-ets-CertificateRefs"
#define NID_id_smime_aa_ets_CertificateRefs             232
#define OBJ_id_smime_aa_ets_CertificateRefs             OBJ_id_smime_aa,21L

#define SN_id_smime_aa_ets_RevocationRefs               "id-smime-aa-ets-RevocationRefs"
#define NID_id_smime_aa_ets_RevocationRefs              233
#define OBJ_id_smime_aa_ets_RevocationRefs              OBJ_id_smime_aa,22L

#define SN_id_smime_aa_ets_certValues           "id-smime-aa-ets-certValues"
#define NID_id_smime_aa_ets_certValues          234
#define OBJ_id_smime_aa_ets_certValues          OBJ_id_smime_aa,23L

#define SN_id_smime_aa_ets_revocationValues             "id-smime-aa-ets-revocationValues"
#define NID_id_smime_aa_ets_revocationValues            235
#define OBJ_id_smime_aa_ets_revocationValues            OBJ_id_smime_aa,24L

#define SN_id_smime_aa_ets_escTimeStamp         "id-smime-aa-ets-escTimeStamp"
#define NID_id_smime_aa_ets_escTimeStamp                236
#define OBJ_id_smime_aa_ets_escTimeStamp                OBJ_id_smime_aa,25L

#define SN_id_smime_aa_ets_certCRLTimestamp             "id-smime-aa-ets-certCRLTimestamp"
#define NID_id_smime_aa_ets_certCRLTimestamp            237
#define OBJ_id_smime_aa_ets_certCRLTimestamp            OBJ_id_smime_aa,26L

#define SN_id_smime_aa_ets_archiveTimeStamp             "id-smime-aa-ets-archiveTimeStamp"
#define NID_id_smime_aa_ets_archiveTimeStamp            238
#define OBJ_id_smime_aa_ets_archiveTimeStamp            OBJ_id_smime_aa,27L

#define SN_id_smime_aa_signatureType            "id-smime-aa-signatureType"
#define NID_id_smime_aa_signatureType           239
#define OBJ_id_smime_aa_signatureType           OBJ_id_smime_aa,28L

#define SN_id_smime_aa_dvcs_dvc         "id-smime-aa-dvcs-dvc"
#define NID_id_smime_aa_dvcs_dvc                240
#define OBJ_id_smime_aa_dvcs_dvc                OBJ_id_smime_aa,29L

#define SN_id_aa_ets_attrCertificateRefs                "id-aa-ets-attrCertificateRefs"
#define NID_id_aa_ets_attrCertificateRefs               1261
#define OBJ_id_aa_ets_attrCertificateRefs               OBJ_id_smime_aa,44L

#define SN_id_aa_ets_attrRevocationRefs         "id-aa-ets-attrRevocationRefs"
#define NID_id_aa_ets_attrRevocationRefs                1262
#define OBJ_id_aa_ets_attrRevocationRefs                OBJ_id_smime_aa,45L

#define SN_id_smime_aa_signingCertificateV2             "id-smime-aa-signingCertificateV2"
#define NID_id_smime_aa_signingCertificateV2            1086
#define OBJ_id_smime_aa_signingCertificateV2            OBJ_id_smime_aa,47L

#define SN_id_aa_ets_archiveTimestampV2         "id-aa-ets-archiveTimestampV2"
#define NID_id_aa_ets_archiveTimestampV2                1280
#define OBJ_id_aa_ets_archiveTimestampV2                OBJ_id_smime_aa,48L

#define SN_id_smime_alg_ESDHwith3DES            "id-smime-alg-ESDHwith3DES"
#define NID_id_smime_alg_ESDHwith3DES           241
#define OBJ_id_smime_alg_ESDHwith3DES           OBJ_id_smime_alg,1L

#define SN_id_smime_alg_ESDHwithRC2             "id-smime-alg-ESDHwithRC2"
#define NID_id_smime_alg_ESDHwithRC2            242
#define OBJ_id_smime_alg_ESDHwithRC2            OBJ_id_smime_alg,2L

#define SN_id_smime_alg_3DESwrap                "id-smime-alg-3DESwrap"
#define NID_id_smime_alg_3DESwrap               243
#define OBJ_id_smime_alg_3DESwrap               OBJ_id_smime_alg,3L

#define SN_id_smime_alg_RC2wrap         "id-smime-alg-RC2wrap"
#define NID_id_smime_alg_RC2wrap                244
#define OBJ_id_smime_alg_RC2wrap                OBJ_id_smime_alg,4L

#define SN_id_smime_alg_ESDH            "id-smime-alg-ESDH"
#define NID_id_smime_alg_ESDH           245
#define OBJ_id_smime_alg_ESDH           OBJ_id_smime_alg,5L

#define SN_id_smime_alg_CMS3DESwrap             "id-smime-alg-CMS3DESwrap"
#define NID_id_smime_alg_CMS3DESwrap            246
#define OBJ_id_smime_alg_CMS3DESwrap            OBJ_id_smime_alg,6L

#define SN_id_smime_alg_CMSRC2wrap              "id-smime-alg-CMSRC2wrap"
#define NID_id_smime_alg_CMSRC2wrap             247
#define OBJ_id_smime_alg_CMSRC2wrap             OBJ_id_smime_alg,7L

#define SN_id_alg_PWRI_KEK              "id-alg-PWRI-KEK"
#define NID_id_alg_PWRI_KEK             893
#define OBJ_id_alg_PWRI_KEK             OBJ_id_smime_alg,9L

#define SN_id_smime_cd_ldap             "id-smime-cd-ldap"
#define NID_id_smime_cd_ldap            248
#define OBJ_id_smime_cd_ldap            OBJ_id_smime_cd,1L

#define SN_id_smime_spq_ets_sqt_uri             "id-smime-spq-ets-sqt-uri"
#define NID_id_smime_spq_ets_sqt_uri            249
#define OBJ_id_smime_spq_ets_sqt_uri            OBJ_id_smime_spq,1L

#define SN_id_smime_spq_ets_sqt_unotice         "id-smime-spq-ets-sqt-unotice"
#define NID_id_smime_spq_ets_sqt_unotice                250
#define OBJ_id_smime_spq_ets_sqt_unotice                OBJ_id_smime_spq,2L

#define SN_id_smime_cti_ets_proofOfOrigin               "id-smime-cti-ets-proofOfOrigin"
#define NID_id_smime_cti_ets_proofOfOrigin              251
#define OBJ_id_smime_cti_ets_proofOfOrigin              OBJ_id_smime_cti,1L

#define SN_id_smime_cti_ets_proofOfReceipt              "id-smime-cti-ets-proofOfReceipt"
#define NID_id_smime_cti_ets_proofOfReceipt             252
#define OBJ_id_smime_cti_ets_proofOfReceipt             OBJ_id_smime_cti,2L

#define SN_id_smime_cti_ets_proofOfDelivery             "id-smime-cti-ets-proofOfDelivery"
#define NID_id_smime_cti_ets_proofOfDelivery            253
#define OBJ_id_smime_cti_ets_proofOfDelivery            OBJ_id_smime_cti,3L

#define SN_id_smime_cti_ets_proofOfSender               "id-smime-cti-ets-proofOfSender"
#define NID_id_smime_cti_ets_proofOfSender              254
#define OBJ_id_smime_cti_ets_proofOfSender              OBJ_id_smime_cti,4L

#define SN_id_smime_cti_ets_proofOfApproval             "id-smime-cti-ets-proofOfApproval"
#define NID_id_smime_cti_ets_proofOfApproval            255
#define OBJ_id_smime_cti_ets_proofOfApproval            OBJ_id_smime_cti,5L

#define SN_id_smime_cti_ets_proofOfCreation             "id-smime-cti-ets-proofOfCreation"
#define NID_id_smime_cti_ets_proofOfCreation            256
#define OBJ_id_smime_cti_ets_proofOfCreation            OBJ_id_smime_cti,6L

#define LN_friendlyName         "friendlyName"
#define NID_friendlyName                156
#define OBJ_friendlyName                OBJ_pkcs9,20L

#define LN_localKeyID           "localKeyID"
#define NID_localKeyID          157
#define OBJ_localKeyID          OBJ_pkcs9,21L

#define OBJ_ms_corp             1L,3L,6L,1L,4L,1L,311L

#define SN_ms_csp_name          "CSPName"
#define LN_ms_csp_name          "Microsoft CSP Name"
#define NID_ms_csp_name         417
#define OBJ_ms_csp_name         OBJ_ms_corp,17L,1L

#define SN_LocalKeySet          "LocalKeySet"
#define LN_LocalKeySet          "Microsoft Local Key set"
#define NID_LocalKeySet         856
#define OBJ_LocalKeySet         OBJ_ms_corp,17L,2L

#define OBJ_certTypes           OBJ_pkcs9,22L

#define LN_x509Certificate              "x509Certificate"
#define NID_x509Certificate             158
#define OBJ_x509Certificate             OBJ_certTypes,1L

#define LN_sdsiCertificate              "sdsiCertificate"
#define NID_sdsiCertificate             159
#define OBJ_sdsiCertificate             OBJ_certTypes,2L

#define OBJ_crlTypes            OBJ_pkcs9,23L

#define LN_x509Crl              "x509Crl"
#define NID_x509Crl             160
#define OBJ_x509Crl             OBJ_crlTypes,1L

#define SN_id_aa_CMSAlgorithmProtection         "id-aa-CMSAlgorithmProtection"
#define NID_id_aa_CMSAlgorithmProtection                1263
#define OBJ_id_aa_CMSAlgorithmProtection                OBJ_pkcs9,52L

#define OBJ_pkcs12              OBJ_pkcs,12L

#define OBJ_pkcs12_pbeids               OBJ_pkcs12,1L

#define SN_pbe_WithSHA1And128BitRC4             "PBE-SHA1-RC4-128"
#define LN_pbe_WithSHA1And128BitRC4             "pbeWithSHA1And128BitRC4"
#define NID_pbe_WithSHA1And128BitRC4            144
#define OBJ_pbe_WithSHA1And128BitRC4            OBJ_pkcs12_pbeids,1L

#define SN_pbe_WithSHA1And40BitRC4              "PBE-SHA1-RC4-40"
#define LN_pbe_WithSHA1And40BitRC4              "pbeWithSHA1And40BitRC4"
#define NID_pbe_WithSHA1And40BitRC4             145
#define OBJ_pbe_WithSHA1And40BitRC4             OBJ_pkcs12_pbeids,2L

#define SN_pbe_WithSHA1And3_Key_TripleDES_CBC           "PBE-SHA1-3DES"
#define LN_pbe_WithSHA1And3_Key_TripleDES_CBC           "pbeWithSHA1And3-KeyTripleDES-CBC"
#define NID_pbe_WithSHA1And3_Key_TripleDES_CBC          146
#define OBJ_pbe_WithSHA1And3_Key_TripleDES_CBC          OBJ_pkcs12_pbeids,3L

#define SN_pbe_WithSHA1And2_Key_TripleDES_CBC           "PBE-SHA1-2DES"
#define LN_pbe_WithSHA1And2_Key_TripleDES_CBC           "pbeWithSHA1And2-KeyTripleDES-CBC"
#define NID_pbe_WithSHA1And2_Key_TripleDES_CBC          147
#define OBJ_pbe_WithSHA1And2_Key_TripleDES_CBC          OBJ_pkcs12_pbeids,4L

#define SN_pbe_WithSHA1And128BitRC2_CBC         "PBE-SHA1-RC2-128"
#define LN_pbe_WithSHA1And128BitRC2_CBC         "pbeWithSHA1And128BitRC2-CBC"
#define NID_pbe_WithSHA1And128BitRC2_CBC                148
#define OBJ_pbe_WithSHA1And128BitRC2_CBC                OBJ_pkcs12_pbeids,5L

#define SN_pbe_WithSHA1And40BitRC2_CBC          "PBE-SHA1-RC2-40"
#define LN_pbe_WithSHA1And40BitRC2_CBC          "pbeWithSHA1And40BitRC2-CBC"
#define NID_pbe_WithSHA1And40BitRC2_CBC         149
#define OBJ_pbe_WithSHA1And40BitRC2_CBC         OBJ_pkcs12_pbeids,6L

#define OBJ_pkcs12_Version1             OBJ_pkcs12,10L

#define OBJ_pkcs12_BagIds               OBJ_pkcs12_Version1,1L

#define LN_keyBag               "keyBag"
#define NID_keyBag              150
#define OBJ_keyBag              OBJ_pkcs12_BagIds,1L

#define LN_pkcs8ShroudedKeyBag          "pkcs8ShroudedKeyBag"
#define NID_pkcs8ShroudedKeyBag         151
#define OBJ_pkcs8ShroudedKeyBag         OBJ_pkcs12_BagIds,2L

#define LN_certBag              "certBag"
#define NID_certBag             152
#define OBJ_certBag             OBJ_pkcs12_BagIds,3L

#define LN_crlBag               "crlBag"
#define NID_crlBag              153
#define OBJ_crlBag              OBJ_pkcs12_BagIds,4L

#define LN_secretBag            "secretBag"
#define NID_secretBag           154
#define OBJ_secretBag           OBJ_pkcs12_BagIds,5L

#define LN_safeContentsBag              "safeContentsBag"
#define NID_safeContentsBag             155
#define OBJ_safeContentsBag             OBJ_pkcs12_BagIds,6L

#define SN_md2          "MD2"
#define LN_md2          "md2"
#define NID_md2         3
#define OBJ_md2         OBJ_rsadsi,2L,2L

#define SN_md4          "MD4"
#define LN_md4          "md4"
#define NID_md4         257
#define OBJ_md4         OBJ_rsadsi,2L,4L

#define SN_md5          "MD5"
#define LN_md5          "md5"
#define NID_md5         4
#define OBJ_md5         OBJ_rsadsi,2L,5L

#define SN_md5_sha1             "MD5-SHA1"
#define LN_md5_sha1             "md5-sha1"
#define NID_md5_sha1            114

#define LN_hmacWithMD5          "hmacWithMD5"
#define NID_hmacWithMD5         797
#define OBJ_hmacWithMD5         OBJ_rsadsi,2L,6L

#define LN_hmacWithSHA1         "hmacWithSHA1"
#define NID_hmacWithSHA1                163
#define OBJ_hmacWithSHA1                OBJ_rsadsi,2L,7L

#define SN_sm2          "SM2"
#define LN_sm2          "sm2"
#define NID_sm2         1172
#define OBJ_sm2         OBJ_sm_scheme,301L

#define SN_sm3          "SM3"
#define LN_sm3          "sm3"
#define NID_sm3         1143
#define OBJ_sm3         OBJ_sm_scheme,401L

#define SN_sm3WithRSAEncryption         "RSA-SM3"
#define LN_sm3WithRSAEncryption         "sm3WithRSAEncryption"
#define NID_sm3WithRSAEncryption                1144
#define OBJ_sm3WithRSAEncryption                OBJ_sm_scheme,504L

#define SN_SM2_with_SM3         "SM2-SM3"
#define LN_SM2_with_SM3         "SM2-with-SM3"
#define NID_SM2_with_SM3                1204
#define OBJ_SM2_with_SM3                OBJ_sm_scheme,501L

#define LN_hmacWithSM3          "hmacWithSM3"
#define NID_hmacWithSM3         1281
#define OBJ_hmacWithSM3         OBJ_sm3,3L,1L

#define LN_hmacWithSHA224               "hmacWithSHA224"
#define NID_hmacWithSHA224              798
#define OBJ_hmacWithSHA224              OBJ_rsadsi,2L,8L

#define LN_hmacWithSHA256               "hmacWithSHA256"
#define NID_hmacWithSHA256              799
#define OBJ_hmacWithSHA256              OBJ_rsadsi,2L,9L

#define LN_hmacWithSHA384               "hmacWithSHA384"
#define NID_hmacWithSHA384              800
#define OBJ_hmacWithSHA384              OBJ_rsadsi,2L,10L

#define LN_hmacWithSHA512               "hmacWithSHA512"
#define NID_hmacWithSHA512              801
#define OBJ_hmacWithSHA512              OBJ_rsadsi,2L,11L

#define LN_hmacWithSHA512_224           "hmacWithSHA512-224"
#define NID_hmacWithSHA512_224          1193
#define OBJ_hmacWithSHA512_224          OBJ_rsadsi,2L,12L

#define LN_hmacWithSHA512_256           "hmacWithSHA512-256"
#define NID_hmacWithSHA512_256          1194
#define OBJ_hmacWithSHA512_256          OBJ_rsadsi,2L,13L

#define SN_rc2_cbc              "RC2-CBC"
#define LN_rc2_cbc              "rc2-cbc"
#define NID_rc2_cbc             37
#define OBJ_rc2_cbc             OBJ_rsadsi,3L,2L

#define SN_rc2_ecb              "RC2-ECB"
#define LN_rc2_ecb              "rc2-ecb"
#define NID_rc2_ecb             38

#define SN_rc2_cfb64            "RC2-CFB"
#define LN_rc2_cfb64            "rc2-cfb"
#define NID_rc2_cfb64           39

#define SN_rc2_ofb64            "RC2-OFB"
#define LN_rc2_ofb64            "rc2-ofb"
#define NID_rc2_ofb64           40

#define SN_rc2_40_cbc           "RC2-40-CBC"
#define LN_rc2_40_cbc           "rc2-40-cbc"
#define NID_rc2_40_cbc          98

#define SN_rc2_64_cbc           "RC2-64-CBC"
#define LN_rc2_64_cbc           "rc2-64-cbc"
#define NID_rc2_64_cbc          166

#define SN_rc4          "RC4"
#define LN_rc4          "rc4"
#define NID_rc4         5
#define OBJ_rc4         OBJ_rsadsi,3L,4L

#define SN_rc4_40               "RC4-40"
#define LN_rc4_40               "rc4-40"
#define NID_rc4_40              97

#define SN_des_ede3_cbc         "DES-EDE3-CBC"
#define LN_des_ede3_cbc         "des-ede3-cbc"
#define NID_des_ede3_cbc                44
#define OBJ_des_ede3_cbc                OBJ_rsadsi,3L,7L

#define SN_rc5_cbc              "RC5-CBC"
#define LN_rc5_cbc              "rc5-cbc"
#define NID_rc5_cbc             120
#define OBJ_rc5_cbc             OBJ_rsadsi,3L,8L

#define SN_rc5_ecb              "RC5-ECB"
#define LN_rc5_ecb              "rc5-ecb"
#define NID_rc5_ecb             121

#define SN_rc5_cfb64            "RC5-CFB"
#define LN_rc5_cfb64            "rc5-cfb"
#define NID_rc5_cfb64           122

#define SN_rc5_ofb64            "RC5-OFB"
#define LN_rc5_ofb64            "rc5-ofb"
#define NID_rc5_ofb64           123

#define SN_ms_ext_req           "msExtReq"
#define LN_ms_ext_req           "Microsoft Extension Request"
#define NID_ms_ext_req          171
#define OBJ_ms_ext_req          OBJ_ms_corp,2L,1L,14L

#define SN_ms_code_ind          "msCodeInd"
#define LN_ms_code_ind          "Microsoft Individual Code Signing"
#define NID_ms_code_ind         134
#define OBJ_ms_code_ind         OBJ_ms_corp,2L,1L,21L

#define SN_ms_code_com          "msCodeCom"
#define LN_ms_code_com          "Microsoft Commercial Code Signing"
#define NID_ms_code_com         135
#define OBJ_ms_code_com         OBJ_ms_corp,2L,1L,22L

#define SN_ms_ctl_sign          "msCTLSign"
#define LN_ms_ctl_sign          "Microsoft Trust List Signing"
#define NID_ms_ctl_sign         136
#define OBJ_ms_ctl_sign         OBJ_ms_corp,10L,3L,1L

#define SN_ms_sgc               "msSGC"
#define LN_ms_sgc               "Microsoft Server Gated Crypto"
#define NID_ms_sgc              137
#define OBJ_ms_sgc              OBJ_ms_corp,10L,3L,3L

#define SN_ms_efs               "msEFS"
#define LN_ms_efs               "Microsoft Encrypted File System"
#define NID_ms_efs              138
#define OBJ_ms_efs              OBJ_ms_corp,10L,3L,4L

#define SN_ms_smartcard_login           "msSmartcardLogin"
#define LN_ms_smartcard_login           "Microsoft Smartcard Login"
#define NID_ms_smartcard_login          648
#define OBJ_ms_smartcard_login          OBJ_ms_corp,20L,2L,2L

#define SN_ms_upn               "msUPN"
#define LN_ms_upn               "Microsoft User Principal Name"
#define NID_ms_upn              649
#define OBJ_ms_upn              OBJ_ms_corp,20L,2L,3L

#define SN_ms_ntds_sec_ext              "ms-ntds-sec-ext"
#define LN_ms_ntds_sec_ext              "Microsoft NTDS CA Extension"
#define NID_ms_ntds_sec_ext             1292
#define OBJ_ms_ntds_sec_ext             OBJ_ms_corp,25L,2L

#define SN_ms_ntds_obj_sid              "ms-ntds-obj-sid"
#define LN_ms_ntds_obj_sid              "Microsoft NTDS AD objectSid"
#define NID_ms_ntds_obj_sid             1291
#define OBJ_ms_ntds_obj_sid             OBJ_ms_corp,25L,2L,1L

#define SN_ms_cert_templ                "ms-cert-templ"
#define LN_ms_cert_templ                "Microsoft certificate template"
#define NID_ms_cert_templ               1293
#define OBJ_ms_cert_templ               OBJ_ms_corp,21L,7L

#define SN_ms_app_policies              "ms-app-policies"
#define LN_ms_app_policies              "Microsoft Application Policies Extension"
#define NID_ms_app_policies             1294
#define OBJ_ms_app_policies             OBJ_ms_corp,21L,10L

#define SN_idea_cbc             "IDEA-CBC"
#define LN_idea_cbc             "idea-cbc"
#define NID_idea_cbc            34
#define OBJ_idea_cbc            1L,3L,6L,1L,4L,1L,188L,7L,1L,1L,2L

#define SN_idea_ecb             "IDEA-ECB"
#define LN_idea_ecb             "idea-ecb"
#define NID_idea_ecb            36

#define SN_idea_cfb64           "IDEA-CFB"
#define LN_idea_cfb64           "idea-cfb"
#define NID_idea_cfb64          35

#define SN_idea_ofb64           "IDEA-OFB"
#define LN_idea_ofb64           "idea-ofb"
#define NID_idea_ofb64          46

#define SN_bf_cbc               "BF-CBC"
#define LN_bf_cbc               "bf-cbc"
#define NID_bf_cbc              91
#define OBJ_bf_cbc              1L,3L,6L,1L,4L,1L,3029L,1L,2L

#define SN_bf_ecb               "BF-ECB"
#define LN_bf_ecb               "bf-ecb"
#define NID_bf_ecb              92

#define SN_bf_cfb64             "BF-CFB"
#define LN_bf_cfb64             "bf-cfb"
#define NID_bf_cfb64            93

#define SN_bf_ofb64             "BF-OFB"
#define LN_bf_ofb64             "bf-ofb"
#define NID_bf_ofb64            94

#define SN_id_pkix              "PKIX"
#define NID_id_pkix             127
#define OBJ_id_pkix             1L,3L,6L,1L,5L,5L,7L

#define SN_id_pkix_mod          "id-pkix-mod"
#define NID_id_pkix_mod         258
#define OBJ_id_pkix_mod         OBJ_id_pkix,0L

#define SN_id_pe                "id-pe"
#define NID_id_pe               175
#define OBJ_id_pe               OBJ_id_pkix,1L

#define SN_id_qt                "id-qt"
#define NID_id_qt               259
#define OBJ_id_qt               OBJ_id_pkix,2L

#define SN_id_kp                "id-kp"
#define NID_id_kp               128
#define OBJ_id_kp               OBJ_id_pkix,3L

#define SN_id_it                "id-it"
#define NID_id_it               260
#define OBJ_id_it               OBJ_id_pkix,4L

#define SN_id_pkip              "id-pkip"
#define NID_id_pkip             261
#define OBJ_id_pkip             OBJ_id_pkix,5L

#define SN_id_alg               "id-alg"
#define NID_id_alg              262
#define OBJ_id_alg              OBJ_id_pkix,6L

#define SN_id_cmc               "id-cmc"
#define NID_id_cmc              263
#define OBJ_id_cmc              OBJ_id_pkix,7L

#define SN_id_on                "id-on"
#define NID_id_on               264
#define OBJ_id_on               OBJ_id_pkix,8L

#define SN_id_pda               "id-pda"
#define NID_id_pda              265
#define OBJ_id_pda              OBJ_id_pkix,9L

#define SN_id_aca               "id-aca"
#define NID_id_aca              266
#define OBJ_id_aca              OBJ_id_pkix,10L

#define SN_id_qcs               "id-qcs"
#define NID_id_qcs              267
#define OBJ_id_qcs              OBJ_id_pkix,11L

#define SN_id_cp                "id-cp"
#define NID_id_cp               1238
#define OBJ_id_cp               OBJ_id_pkix,14L

#define SN_id_cct               "id-cct"
#define NID_id_cct              268
#define OBJ_id_cct              OBJ_id_pkix,12L

#define SN_id_ppl               "id-ppl"
#define NID_id_ppl              662
#define OBJ_id_ppl              OBJ_id_pkix,21L

#define SN_id_ad                "id-ad"
#define NID_id_ad               176
#define OBJ_id_ad               OBJ_id_pkix,48L

#define SN_id_pkix1_explicit_88         "id-pkix1-explicit-88"
#define NID_id_pkix1_explicit_88                269
#define OBJ_id_pkix1_explicit_88                OBJ_id_pkix_mod,1L

#define SN_id_pkix1_implicit_88         "id-pkix1-implicit-88"
#define NID_id_pkix1_implicit_88                270
#define OBJ_id_pkix1_implicit_88                OBJ_id_pkix_mod,2L

#define SN_id_pkix1_explicit_93         "id-pkix1-explicit-93"
#define NID_id_pkix1_explicit_93                271
#define OBJ_id_pkix1_explicit_93                OBJ_id_pkix_mod,3L

#define SN_id_pkix1_implicit_93         "id-pkix1-implicit-93"
#define NID_id_pkix1_implicit_93                272
#define OBJ_id_pkix1_implicit_93                OBJ_id_pkix_mod,4L

#define SN_id_mod_crmf          "id-mod-crmf"
#define NID_id_mod_crmf         273
#define OBJ_id_mod_crmf         OBJ_id_pkix_mod,5L

#define SN_id_mod_cmc           "id-mod-cmc"
#define NID_id_mod_cmc          274
#define OBJ_id_mod_cmc          OBJ_id_pkix_mod,6L

#define SN_id_mod_kea_profile_88                "id-mod-kea-profile-88"
#define NID_id_mod_kea_profile_88               275
#define OBJ_id_mod_kea_profile_88               OBJ_id_pkix_mod,7L

#define SN_id_mod_kea_profile_93                "id-mod-kea-profile-93"
#define NID_id_mod_kea_profile_93               276
#define OBJ_id_mod_kea_profile_93               OBJ_id_pkix_mod,8L

#define SN_id_mod_cmp           "id-mod-cmp"
#define NID_id_mod_cmp          277
#define OBJ_id_mod_cmp          OBJ_id_pkix_mod,9L

#define SN_id_mod_qualified_cert_88             "id-mod-qualified-cert-88"
#define NID_id_mod_qualified_cert_88            278
#define OBJ_id_mod_qualified_cert_88            OBJ_id_pkix_mod,10L

#define SN_id_mod_qualified_cert_93             "id-mod-qualified-cert-93"
#define NID_id_mod_qualified_cert_93            279
#define OBJ_id_mod_qualified_cert_93            OBJ_id_pkix_mod,11L

#define SN_id_mod_attribute_cert                "id-mod-attribute-cert"
#define NID_id_mod_attribute_cert               280
#define OBJ_id_mod_attribute_cert               OBJ_id_pkix_mod,12L

#define SN_id_mod_timestamp_protocol            "id-mod-timestamp-protocol"
#define NID_id_mod_timestamp_protocol           281
#define OBJ_id_mod_timestamp_protocol           OBJ_id_pkix_mod,13L

#define SN_id_mod_ocsp          "id-mod-ocsp"
#define NID_id_mod_ocsp         282
#define OBJ_id_mod_ocsp         OBJ_id_pkix_mod,14L

#define SN_id_mod_dvcs          "id-mod-dvcs"
#define NID_id_mod_dvcs         283
#define OBJ_id_mod_dvcs         OBJ_id_pkix_mod,15L

#define SN_id_mod_cmp2000               "id-mod-cmp2000"
#define NID_id_mod_cmp2000              284
#define OBJ_id_mod_cmp2000              OBJ_id_pkix_mod,16L

#define SN_id_mod_cmp2000_02            "id-mod-cmp2000-02"
#define NID_id_mod_cmp2000_02           1251
#define OBJ_id_mod_cmp2000_02           OBJ_id_pkix_mod,50L

#define SN_id_mod_cmp2021_88            "id-mod-cmp2021-88"
#define NID_id_mod_cmp2021_88           1252
#define OBJ_id_mod_cmp2021_88           OBJ_id_pkix_mod,99L

#define SN_id_mod_cmp2021_02            "id-mod-cmp2021-02"
#define NID_id_mod_cmp2021_02           1253
#define OBJ_id_mod_cmp2021_02           OBJ_id_pkix_mod,100L

#define SN_info_access          "authorityInfoAccess"
#define LN_info_access          "Authority Information Access"
#define NID_info_access         177
#define OBJ_info_access         OBJ_id_pe,1L

#define SN_biometricInfo                "biometricInfo"
#define LN_biometricInfo                "Biometric Info"
#define NID_biometricInfo               285
#define OBJ_biometricInfo               OBJ_id_pe,2L

#define SN_qcStatements         "qcStatements"
#define NID_qcStatements                286
#define OBJ_qcStatements                OBJ_id_pe,3L

#define SN_ac_auditIdentity             "ac-auditIdentity"
#define LN_ac_auditIdentity             "X509v3 Audit Identity"
#define NID_ac_auditIdentity            287
#define OBJ_ac_auditIdentity            OBJ_id_pe,4L

#define NID_ac_auditEntity              1323
#define OBJ_ac_auditEntity              OBJ_ac_auditIdentity

#define SN_ac_targeting         "ac-targeting"
#define NID_ac_targeting                288
#define OBJ_ac_targeting                OBJ_id_pe,5L

#define SN_aaControls           "aaControls"
#define NID_aaControls          289
#define OBJ_aaControls          OBJ_id_pe,6L

#define SN_sbgp_ipAddrBlock             "sbgp-ipAddrBlock"
#define NID_sbgp_ipAddrBlock            290
#define OBJ_sbgp_ipAddrBlock            OBJ_id_pe,7L

#define SN_sbgp_autonomousSysNum                "sbgp-autonomousSysNum"
#define NID_sbgp_autonomousSysNum               291
#define OBJ_sbgp_autonomousSysNum               OBJ_id_pe,8L

#define SN_sbgp_routerIdentifier                "sbgp-routerIdentifier"
#define NID_sbgp_routerIdentifier               292
#define OBJ_sbgp_routerIdentifier               OBJ_id_pe,9L

#define SN_ac_proxying          "ac-proxying"
#define NID_ac_proxying         397
#define OBJ_ac_proxying         OBJ_id_pe,10L

#define SN_sinfo_access         "subjectInfoAccess"
#define LN_sinfo_access         "Subject Information Access"
#define NID_sinfo_access                398
#define OBJ_sinfo_access                OBJ_id_pe,11L

#define SN_proxyCertInfo                "proxyCertInfo"
#define LN_proxyCertInfo                "Proxy Certificate Information"
#define NID_proxyCertInfo               663
#define OBJ_proxyCertInfo               OBJ_id_pe,14L

#define SN_tlsfeature           "tlsfeature"
#define LN_tlsfeature           "TLS Feature"
#define NID_tlsfeature          1020
#define OBJ_tlsfeature          OBJ_id_pe,24L

#define SN_sbgp_ipAddrBlockv2           "sbgp-ipAddrBlockv2"
#define NID_sbgp_ipAddrBlockv2          1239
#define OBJ_sbgp_ipAddrBlockv2          OBJ_id_pe,28L

#define SN_sbgp_autonomousSysNumv2              "sbgp-autonomousSysNumv2"
#define NID_sbgp_autonomousSysNumv2             1240
#define OBJ_sbgp_autonomousSysNumv2             OBJ_id_pe,29L

#define SN_id_qt_cps            "id-qt-cps"
#define LN_id_qt_cps            "Policy Qualifier CPS"
#define NID_id_qt_cps           164
#define OBJ_id_qt_cps           OBJ_id_qt,1L

#define SN_id_qt_unotice                "id-qt-unotice"
#define LN_id_qt_unotice                "Policy Qualifier User Notice"
#define NID_id_qt_unotice               165
#define OBJ_id_qt_unotice               OBJ_id_qt,2L

#define SN_textNotice           "textNotice"
#define NID_textNotice          293
#define OBJ_textNotice          OBJ_id_qt,3L

#define SN_server_auth          "serverAuth"
#define LN_server_auth          "TLS Web Server Authentication"
#define NID_server_auth         129
#define OBJ_server_auth         OBJ_id_kp,1L

#define SN_client_auth          "clientAuth"
#define LN_client_auth          "TLS Web Client Authentication"
#define NID_client_auth         130
#define OBJ_client_auth         OBJ_id_kp,2L

#define SN_code_sign            "codeSigning"
#define LN_code_sign            "Code Signing"
#define NID_code_sign           131
#define OBJ_code_sign           OBJ_id_kp,3L

#define SN_email_protect                "emailProtection"
#define LN_email_protect                "E-mail Protection"
#define NID_email_protect               132
#define OBJ_email_protect               OBJ_id_kp,4L

#define SN_ipsecEndSystem               "ipsecEndSystem"
#define LN_ipsecEndSystem               "IPSec End System"
#define NID_ipsecEndSystem              294
#define OBJ_ipsecEndSystem              OBJ_id_kp,5L

#define SN_ipsecTunnel          "ipsecTunnel"
#define LN_ipsecTunnel          "IPSec Tunnel"
#define NID_ipsecTunnel         295
#define OBJ_ipsecTunnel         OBJ_id_kp,6L

#define SN_ipsecUser            "ipsecUser"
#define LN_ipsecUser            "IPSec User"
#define NID_ipsecUser           296
#define OBJ_ipsecUser           OBJ_id_kp,7L

#define SN_time_stamp           "timeStamping"
#define LN_time_stamp           "Time Stamping"
#define NID_time_stamp          133
#define OBJ_time_stamp          OBJ_id_kp,8L

#define SN_OCSP_sign            "OCSPSigning"
#define LN_OCSP_sign            "OCSP Signing"
#define NID_OCSP_sign           180
#define OBJ_OCSP_sign           OBJ_id_kp,9L

#define SN_dvcs         "DVCS"
#define LN_dvcs         "dvcs"
#define NID_dvcs                297
#define OBJ_dvcs                OBJ_id_kp,10L

#define SN_ipsec_IKE            "ipsecIKE"
#define LN_ipsec_IKE            "ipsec Internet Key Exchange"
#define NID_ipsec_IKE           1022
#define OBJ_ipsec_IKE           OBJ_id_kp,17L

#define SN_capwapAC             "capwapAC"
#define LN_capwapAC             "Ctrl/provision WAP Access"
#define NID_capwapAC            1023
#define OBJ_capwapAC            OBJ_id_kp,18L

#define SN_capwapWTP            "capwapWTP"
#define LN_capwapWTP            "Ctrl/Provision WAP Termination"
#define NID_capwapWTP           1024
#define OBJ_capwapWTP           OBJ_id_kp,19L

#define SN_sshClient            "secureShellClient"
#define LN_sshClient            "SSH Client"
#define NID_sshClient           1025
#define OBJ_sshClient           OBJ_id_kp,21L

#define SN_sshServer            "secureShellServer"
#define LN_sshServer            "SSH Server"
#define NID_sshServer           1026
#define OBJ_sshServer           OBJ_id_kp,22L

#define SN_sendRouter           "sendRouter"
#define LN_sendRouter           "Send Router"
#define NID_sendRouter          1027
#define OBJ_sendRouter          OBJ_id_kp,23L

#define SN_sendProxiedRouter            "sendProxiedRouter"
#define LN_sendProxiedRouter            "Send Proxied Router"
#define NID_sendProxiedRouter           1028
#define OBJ_sendProxiedRouter           OBJ_id_kp,24L

#define SN_sendOwner            "sendOwner"
#define LN_sendOwner            "Send Owner"
#define NID_sendOwner           1029
#define OBJ_sendOwner           OBJ_id_kp,25L

#define SN_sendProxiedOwner             "sendProxiedOwner"
#define LN_sendProxiedOwner             "Send Proxied Owner"
#define NID_sendProxiedOwner            1030
#define OBJ_sendProxiedOwner            OBJ_id_kp,26L

#define SN_cmcCA                "cmcCA"
#define LN_cmcCA                "CMC Certificate Authority"
#define NID_cmcCA               1131
#define OBJ_cmcCA               OBJ_id_kp,27L

#define SN_cmcRA                "cmcRA"
#define LN_cmcRA                "CMC Registration Authority"
#define NID_cmcRA               1132
#define OBJ_cmcRA               OBJ_id_kp,28L

#define SN_cmcArchive           "cmcArchive"
#define LN_cmcArchive           "CMC Archive Server"
#define NID_cmcArchive          1219
#define OBJ_cmcArchive          OBJ_id_kp,29L

#define SN_id_kp_bgpsec_router          "id-kp-bgpsec-router"
#define LN_id_kp_bgpsec_router          "BGPsec Router"
#define NID_id_kp_bgpsec_router         1220
#define OBJ_id_kp_bgpsec_router         OBJ_id_kp,30L

#define SN_id_kp_BrandIndicatorforMessageIdentification         "id-kp-BrandIndicatorforMessageIdentification"
#define LN_id_kp_BrandIndicatorforMessageIdentification         "Brand Indicator for Message Identification"
#define NID_id_kp_BrandIndicatorforMessageIdentification                1221
#define OBJ_id_kp_BrandIndicatorforMessageIdentification                OBJ_id_kp,31L

#define SN_cmKGA                "cmKGA"
#define LN_cmKGA                "Certificate Management Key Generation Authority"
#define NID_cmKGA               1222
#define OBJ_cmKGA               OBJ_id_kp,32L

#define SN_id_it_caProtEncCert          "id-it-caProtEncCert"
#define NID_id_it_caProtEncCert         298
#define OBJ_id_it_caProtEncCert         OBJ_id_it,1L

#define SN_id_it_signKeyPairTypes               "id-it-signKeyPairTypes"
#define NID_id_it_signKeyPairTypes              299
#define OBJ_id_it_signKeyPairTypes              OBJ_id_it,2L

#define SN_id_it_encKeyPairTypes                "id-it-encKeyPairTypes"
#define NID_id_it_encKeyPairTypes               300
#define OBJ_id_it_encKeyPairTypes               OBJ_id_it,3L

#define SN_id_it_preferredSymmAlg               "id-it-preferredSymmAlg"
#define NID_id_it_preferredSymmAlg              301
#define OBJ_id_it_preferredSymmAlg              OBJ_id_it,4L

#define SN_id_it_caKeyUpdateInfo                "id-it-caKeyUpdateInfo"
#define NID_id_it_caKeyUpdateInfo               302
#define OBJ_id_it_caKeyUpdateInfo               OBJ_id_it,5L

#define SN_id_it_currentCRL             "id-it-currentCRL"
#define NID_id_it_currentCRL            303
#define OBJ_id_it_currentCRL            OBJ_id_it,6L

#define SN_id_it_unsupportedOIDs                "id-it-unsupportedOIDs"
#define NID_id_it_unsupportedOIDs               304
#define OBJ_id_it_unsupportedOIDs               OBJ_id_it,7L

#define SN_id_it_subscriptionRequest            "id-it-subscriptionRequest"
#define NID_id_it_subscriptionRequest           305
#define OBJ_id_it_subscriptionRequest           OBJ_id_it,8L

#define SN_id_it_subscriptionResponse           "id-it-subscriptionResponse"
#define NID_id_it_subscriptionResponse          306
#define OBJ_id_it_subscriptionResponse          OBJ_id_it,9L

#define SN_id_it_keyPairParamReq                "id-it-keyPairParamReq"
#define NID_id_it_keyPairParamReq               307
#define OBJ_id_it_keyPairParamReq               OBJ_id_it,10L

#define SN_id_it_keyPairParamRep                "id-it-keyPairParamRep"
#define NID_id_it_keyPairParamRep               308
#define OBJ_id_it_keyPairParamRep               OBJ_id_it,11L

#define SN_id_it_revPassphrase          "id-it-revPassphrase"
#define NID_id_it_revPassphrase         309
#define OBJ_id_it_revPassphrase         OBJ_id_it,12L

#define SN_id_it_implicitConfirm                "id-it-implicitConfirm"
#define NID_id_it_implicitConfirm               310
#define OBJ_id_it_implicitConfirm               OBJ_id_it,13L

#define SN_id_it_confirmWaitTime                "id-it-confirmWaitTime"
#define NID_id_it_confirmWaitTime               311
#define OBJ_id_it_confirmWaitTime               OBJ_id_it,14L

#define SN_id_it_origPKIMessage         "id-it-origPKIMessage"
#define NID_id_it_origPKIMessage                312
#define OBJ_id_it_origPKIMessage                OBJ_id_it,15L

#define SN_id_it_suppLangTags           "id-it-suppLangTags"
#define NID_id_it_suppLangTags          784
#define OBJ_id_it_suppLangTags          OBJ_id_it,16L

#define SN_id_it_caCerts                "id-it-caCerts"
#define NID_id_it_caCerts               1223
#define OBJ_id_it_caCerts               OBJ_id_it,17L

#define SN_id_it_rootCaKeyUpdate                "id-it-rootCaKeyUpdate"
#define NID_id_it_rootCaKeyUpdate               1224
#define OBJ_id_it_rootCaKeyUpdate               OBJ_id_it,18L

#define SN_id_it_certReqTemplate                "id-it-certReqTemplate"
#define NID_id_it_certReqTemplate               1225
#define OBJ_id_it_certReqTemplate               OBJ_id_it,19L

#define SN_id_it_rootCaCert             "id-it-rootCaCert"
#define NID_id_it_rootCaCert            1254
#define OBJ_id_it_rootCaCert            OBJ_id_it,20L

#define SN_id_it_certProfile            "id-it-certProfile"
#define NID_id_it_certProfile           1255
#define OBJ_id_it_certProfile           OBJ_id_it,21L

#define SN_id_it_crlStatusList          "id-it-crlStatusList"
#define NID_id_it_crlStatusList         1256
#define OBJ_id_it_crlStatusList         OBJ_id_it,22L

#define SN_id_it_crls           "id-it-crls"
#define NID_id_it_crls          1257
#define OBJ_id_it_crls          OBJ_id_it,23L

#define SN_id_regCtrl           "id-regCtrl"
#define NID_id_regCtrl          313
#define OBJ_id_regCtrl          OBJ_id_pkip,1L

#define SN_id_regInfo           "id-regInfo"
#define NID_id_regInfo          314
#define OBJ_id_regInfo          OBJ_id_pkip,2L

#define SN_id_regCtrl_regToken          "id-regCtrl-regToken"
#define NID_id_regCtrl_regToken         315
#define OBJ_id_regCtrl_regToken         OBJ_id_regCtrl,1L

#define SN_id_regCtrl_authenticator             "id-regCtrl-authenticator"
#define NID_id_regCtrl_authenticator            316
#define OBJ_id_regCtrl_authenticator            OBJ_id_regCtrl,2L

#define SN_id_regCtrl_pkiPublicationInfo                "id-regCtrl-pkiPublicationInfo"
#define NID_id_regCtrl_pkiPublicationInfo               317
#define OBJ_id_regCtrl_pkiPublicationInfo               OBJ_id_regCtrl,3L

#define SN_id_regCtrl_pkiArchiveOptions         "id-regCtrl-pkiArchiveOptions"
#define NID_id_regCtrl_pkiArchiveOptions                318
#define OBJ_id_regCtrl_pkiArchiveOptions                OBJ_id_regCtrl,4L

#define SN_id_regCtrl_oldCertID         "id-regCtrl-oldCertID"
#define NID_id_regCtrl_oldCertID                319
#define OBJ_id_regCtrl_oldCertID                OBJ_id_regCtrl,5L

#define SN_id_regCtrl_protocolEncrKey           "id-regCtrl-protocolEncrKey"
#define NID_id_regCtrl_protocolEncrKey          320
#define OBJ_id_regCtrl_protocolEncrKey          OBJ_id_regCtrl,6L

#define SN_id_regCtrl_altCertTemplate           "id-regCtrl-altCertTemplate"
#define NID_id_regCtrl_altCertTemplate          1258
#define OBJ_id_regCtrl_altCertTemplate          OBJ_id_regCtrl,7L

#define SN_id_regCtrl_algId             "id-regCtrl-algId"
#define NID_id_regCtrl_algId            1259
#define OBJ_id_regCtrl_algId            OBJ_id_regCtrl,11L

#define SN_id_regCtrl_rsaKeyLen         "id-regCtrl-rsaKeyLen"
#define NID_id_regCtrl_rsaKeyLen                1260
#define OBJ_id_regCtrl_rsaKeyLen                OBJ_id_regCtrl,12L

#define SN_id_regInfo_utf8Pairs         "id-regInfo-utf8Pairs"
#define NID_id_regInfo_utf8Pairs                321
#define OBJ_id_regInfo_utf8Pairs                OBJ_id_regInfo,1L

#define SN_id_regInfo_certReq           "id-regInfo-certReq"
#define NID_id_regInfo_certReq          322
#define OBJ_id_regInfo_certReq          OBJ_id_regInfo,2L

#define SN_id_alg_des40         "id-alg-des40"
#define NID_id_alg_des40                323
#define OBJ_id_alg_des40                OBJ_id_alg,1L

#define SN_id_alg_noSignature           "id-alg-noSignature"
#define NID_id_alg_noSignature          324
#define OBJ_id_alg_noSignature          OBJ_id_alg,2L

#define SN_id_alg_dh_sig_hmac_sha1              "id-alg-dh-sig-hmac-sha1"
#define NID_id_alg_dh_sig_hmac_sha1             325
#define OBJ_id_alg_dh_sig_hmac_sha1             OBJ_id_alg,3L

#define SN_id_alg_dh_pop                "id-alg-dh-pop"
#define NID_id_alg_dh_pop               326
#define OBJ_id_alg_dh_pop               OBJ_id_alg,4L

#define SN_id_cmc_statusInfo            "id-cmc-statusInfo"
#define NID_id_cmc_statusInfo           327
#define OBJ_id_cmc_statusInfo           OBJ_id_cmc,1L

#define SN_id_cmc_identification                "id-cmc-identification"
#define NID_id_cmc_identification               328
#define OBJ_id_cmc_identification               OBJ_id_cmc,2L

#define SN_id_cmc_identityProof         "id-cmc-identityProof"
#define NID_id_cmc_identityProof                329
#define OBJ_id_cmc_identityProof                OBJ_id_cmc,3L

#define SN_id_cmc_dataReturn            "id-cmc-dataReturn"
#define NID_id_cmc_dataReturn           330
#define OBJ_id_cmc_dataReturn           OBJ_id_cmc,4L

#define SN_id_cmc_transactionId         "id-cmc-transactionId"
#define NID_id_cmc_transactionId                331
#define OBJ_id_cmc_transactionId                OBJ_id_cmc,5L

#define SN_id_cmc_senderNonce           "id-cmc-senderNonce"
#define NID_id_cmc_senderNonce          332
#define OBJ_id_cmc_senderNonce          OBJ_id_cmc,6L

#define SN_id_cmc_recipientNonce                "id-cmc-recipientNonce"
#define NID_id_cmc_recipientNonce               333
#define OBJ_id_cmc_recipientNonce               OBJ_id_cmc,7L

#define SN_id_cmc_addExtensions         "id-cmc-addExtensions"
#define NID_id_cmc_addExtensions                334
#define OBJ_id_cmc_addExtensions                OBJ_id_cmc,8L

#define SN_id_cmc_encryptedPOP          "id-cmc-encryptedPOP"
#define NID_id_cmc_encryptedPOP         335
#define OBJ_id_cmc_encryptedPOP         OBJ_id_cmc,9L

#define SN_id_cmc_decryptedPOP          "id-cmc-decryptedPOP"
#define NID_id_cmc_decryptedPOP         336
#define OBJ_id_cmc_decryptedPOP         OBJ_id_cmc,10L

#define SN_id_cmc_lraPOPWitness         "id-cmc-lraPOPWitness"
#define NID_id_cmc_lraPOPWitness                337
#define OBJ_id_cmc_lraPOPWitness                OBJ_id_cmc,11L

#define SN_id_cmc_getCert               "id-cmc-getCert"
#define NID_id_cmc_getCert              338
#define OBJ_id_cmc_getCert              OBJ_id_cmc,15L

#define SN_id_cmc_getCRL                "id-cmc-getCRL"
#define NID_id_cmc_getCRL               339
#define OBJ_id_cmc_getCRL               OBJ_id_cmc,16L

#define SN_id_cmc_revokeRequest         "id-cmc-revokeRequest"
#define NID_id_cmc_revokeRequest                340
#define OBJ_id_cmc_revokeRequest                OBJ_id_cmc,17L

#define SN_id_cmc_regInfo               "id-cmc-regInfo"
#define NID_id_cmc_regInfo              341
#define OBJ_id_cmc_regInfo              OBJ_id_cmc,18L

#define SN_id_cmc_responseInfo          "id-cmc-responseInfo"
#define NID_id_cmc_responseInfo         342
#define OBJ_id_cmc_responseInfo         OBJ_id_cmc,19L

#define SN_id_cmc_queryPending          "id-cmc-queryPending"
#define NID_id_cmc_queryPending         343
#define OBJ_id_cmc_queryPending         OBJ_id_cmc,21L

#define SN_id_cmc_popLinkRandom         "id-cmc-popLinkRandom"
#define NID_id_cmc_popLinkRandom                344
#define OBJ_id_cmc_popLinkRandom                OBJ_id_cmc,22L

#define SN_id_cmc_popLinkWitness                "id-cmc-popLinkWitness"
#define NID_id_cmc_popLinkWitness               345
#define OBJ_id_cmc_popLinkWitness               OBJ_id_cmc,23L

#define SN_id_cmc_confirmCertAcceptance         "id-cmc-confirmCertAcceptance"
#define NID_id_cmc_confirmCertAcceptance                346
#define OBJ_id_cmc_confirmCertAcceptance                OBJ_id_cmc,24L

#define SN_id_on_personalData           "id-on-personalData"
#define NID_id_on_personalData          347
#define OBJ_id_on_personalData          OBJ_id_on,1L

#define SN_id_on_permanentIdentifier            "id-on-permanentIdentifier"
#define LN_id_on_permanentIdentifier            "Permanent Identifier"
#define NID_id_on_permanentIdentifier           858
#define OBJ_id_on_permanentIdentifier           OBJ_id_on,3L

#define SN_id_on_hardwareModuleName             "id-on-hardwareModuleName"
#define LN_id_on_hardwareModuleName             "Hardware Module Name"
#define NID_id_on_hardwareModuleName            1321
#define OBJ_id_on_hardwareModuleName            OBJ_id_on,4L

#define SN_XmppAddr             "id-on-xmppAddr"
#define LN_XmppAddr             "XmppAddr"
#define NID_XmppAddr            1209
#define OBJ_XmppAddr            OBJ_id_on,5L

#define SN_SRVName              "id-on-dnsSRV"
#define LN_SRVName              "SRVName"
#define NID_SRVName             1210
#define OBJ_SRVName             OBJ_id_on,7L

#define SN_NAIRealm             "id-on-NAIRealm"
#define LN_NAIRealm             "NAIRealm"
#define NID_NAIRealm            1211
#define OBJ_NAIRealm            OBJ_id_on,8L

#define SN_id_on_SmtpUTF8Mailbox                "id-on-SmtpUTF8Mailbox"
#define LN_id_on_SmtpUTF8Mailbox                "Smtp UTF8 Mailbox"
#define NID_id_on_SmtpUTF8Mailbox               1208
#define OBJ_id_on_SmtpUTF8Mailbox               OBJ_id_on,9L

#define SN_id_pda_dateOfBirth           "id-pda-dateOfBirth"
#define NID_id_pda_dateOfBirth          348
#define OBJ_id_pda_dateOfBirth          OBJ_id_pda,1L

#define SN_id_pda_placeOfBirth          "id-pda-placeOfBirth"
#define NID_id_pda_placeOfBirth         349
#define OBJ_id_pda_placeOfBirth         OBJ_id_pda,2L

#define SN_id_pda_gender                "id-pda-gender"
#define NID_id_pda_gender               351
#define OBJ_id_pda_gender               OBJ_id_pda,3L

#define SN_id_pda_countryOfCitizenship          "id-pda-countryOfCitizenship"
#define NID_id_pda_countryOfCitizenship         352
#define OBJ_id_pda_countryOfCitizenship         OBJ_id_pda,4L

#define SN_id_pda_countryOfResidence            "id-pda-countryOfResidence"
#define NID_id_pda_countryOfResidence           353
#define OBJ_id_pda_countryOfResidence           OBJ_id_pda,5L

#define SN_id_aca_authenticationInfo            "id-aca-authenticationInfo"
#define NID_id_aca_authenticationInfo           354
#define OBJ_id_aca_authenticationInfo           OBJ_id_aca,1L

#define SN_id_aca_accessIdentity                "id-aca-accessIdentity"
#define NID_id_aca_accessIdentity               355
#define OBJ_id_aca_accessIdentity               OBJ_id_aca,2L

#define SN_id_aca_chargingIdentity              "id-aca-chargingIdentity"
#define NID_id_aca_chargingIdentity             356
#define OBJ_id_aca_chargingIdentity             OBJ_id_aca,3L

#define SN_id_aca_group         "id-aca-group"
#define NID_id_aca_group                357
#define OBJ_id_aca_group                OBJ_id_aca,4L

#define SN_id_aca_role          "id-aca-role"
#define NID_id_aca_role         358
#define OBJ_id_aca_role         OBJ_id_aca,5L

#define SN_id_aca_encAttrs              "id-aca-encAttrs"
#define NID_id_aca_encAttrs             399
#define OBJ_id_aca_encAttrs             OBJ_id_aca,6L

#define SN_id_qcs_pkixQCSyntax_v1               "id-qcs-pkixQCSyntax-v1"
#define NID_id_qcs_pkixQCSyntax_v1              359
#define OBJ_id_qcs_pkixQCSyntax_v1              OBJ_id_qcs,1L

#define SN_ipAddr_asNumber              "ipAddr-asNumber"
#define NID_ipAddr_asNumber             1241
#define OBJ_ipAddr_asNumber             OBJ_id_cp,2L

#define SN_ipAddr_asNumberv2            "ipAddr-asNumberv2"
#define NID_ipAddr_asNumberv2           1242
#define OBJ_ipAddr_asNumberv2           OBJ_id_cp,3L

#define SN_id_cct_crs           "id-cct-crs"
#define NID_id_cct_crs          360
#define OBJ_id_cct_crs          OBJ_id_cct,1L

#define SN_id_cct_PKIData               "id-cct-PKIData"
#define NID_id_cct_PKIData              361
#define OBJ_id_cct_PKIData              OBJ_id_cct,2L

#define SN_id_cct_PKIResponse           "id-cct-PKIResponse"
#define NID_id_cct_PKIResponse          362
#define OBJ_id_cct_PKIResponse          OBJ_id_cct,3L

#define SN_id_ppl_anyLanguage           "id-ppl-anyLanguage"
#define LN_id_ppl_anyLanguage           "Any language"
#define NID_id_ppl_anyLanguage          664
#define OBJ_id_ppl_anyLanguage          OBJ_id_ppl,0L

#define SN_id_ppl_inheritAll            "id-ppl-inheritAll"
#define LN_id_ppl_inheritAll            "Inherit all"
#define NID_id_ppl_inheritAll           665
#define OBJ_id_ppl_inheritAll           OBJ_id_ppl,1L

#define SN_Independent          "id-ppl-independent"
#define LN_Independent          "Independent"
#define NID_Independent         667
#define OBJ_Independent         OBJ_id_ppl,2L

#define SN_ad_OCSP              "OCSP"
#define LN_ad_OCSP              "OCSP"
#define NID_ad_OCSP             178
#define OBJ_ad_OCSP             OBJ_id_ad,1L

#define SN_ad_ca_issuers                "caIssuers"
#define LN_ad_ca_issuers                "CA Issuers"
#define NID_ad_ca_issuers               179
#define OBJ_ad_ca_issuers               OBJ_id_ad,2L

#define SN_ad_timeStamping              "ad_timestamping"
#define LN_ad_timeStamping              "AD Time Stamping"
#define NID_ad_timeStamping             363
#define OBJ_ad_timeStamping             OBJ_id_ad,3L

#define SN_ad_dvcs              "AD_DVCS"
#define LN_ad_dvcs              "ad dvcs"
#define NID_ad_dvcs             364
#define OBJ_ad_dvcs             OBJ_id_ad,4L

#define SN_caRepository         "caRepository"
#define LN_caRepository         "CA Repository"
#define NID_caRepository                785
#define OBJ_caRepository                OBJ_id_ad,5L

#define SN_rpkiManifest         "rpkiManifest"
#define LN_rpkiManifest         "RPKI Manifest"
#define NID_rpkiManifest                1243
#define OBJ_rpkiManifest                OBJ_id_ad,10L

#define SN_signedObject         "signedObject"
#define LN_signedObject         "Signed Object"
#define NID_signedObject                1244
#define OBJ_signedObject                OBJ_id_ad,11L

#define SN_rpkiNotify           "rpkiNotify"
#define LN_rpkiNotify           "RPKI Notify"
#define NID_rpkiNotify          1245
#define OBJ_rpkiNotify          OBJ_id_ad,13L

#define OBJ_id_pkix_OCSP                OBJ_ad_OCSP

#define SN_id_pkix_OCSP_basic           "basicOCSPResponse"
#define LN_id_pkix_OCSP_basic           "Basic OCSP Response"
#define NID_id_pkix_OCSP_basic          365
#define OBJ_id_pkix_OCSP_basic          OBJ_id_pkix_OCSP,1L

#define SN_id_pkix_OCSP_Nonce           "Nonce"
#define LN_id_pkix_OCSP_Nonce           "OCSP Nonce"
#define NID_id_pkix_OCSP_Nonce          366
#define OBJ_id_pkix_OCSP_Nonce          OBJ_id_pkix_OCSP,2L

#define SN_id_pkix_OCSP_CrlID           "CrlID"
#define LN_id_pkix_OCSP_CrlID           "OCSP CRL ID"
#define NID_id_pkix_OCSP_CrlID          367
#define OBJ_id_pkix_OCSP_CrlID          OBJ_id_pkix_OCSP,3L

#define SN_id_pkix_OCSP_acceptableResponses             "acceptableResponses"
#define LN_id_pkix_OCSP_acceptableResponses             "Acceptable OCSP Responses"
#define NID_id_pkix_OCSP_acceptableResponses            368
#define OBJ_id_pkix_OCSP_acceptableResponses            OBJ_id_pkix_OCSP,4L

#define SN_id_pkix_OCSP_noCheck         "noCheck"
#define LN_id_pkix_OCSP_noCheck         "OCSP No Check"
#define NID_id_pkix_OCSP_noCheck                369
#define OBJ_id_pkix_OCSP_noCheck                OBJ_id_pkix_OCSP,5L

#define SN_id_pkix_OCSP_archiveCutoff           "archiveCutoff"
#define LN_id_pkix_OCSP_archiveCutoff           "OCSP Archive Cutoff"
#define NID_id_pkix_OCSP_archiveCutoff          370
#define OBJ_id_pkix_OCSP_archiveCutoff          OBJ_id_pkix_OCSP,6L

#define SN_id_pkix_OCSP_serviceLocator          "serviceLocator"
#define LN_id_pkix_OCSP_serviceLocator          "OCSP Service Locator"
#define NID_id_pkix_OCSP_serviceLocator         371
#define OBJ_id_pkix_OCSP_serviceLocator         OBJ_id_pkix_OCSP,7L

#define SN_id_pkix_OCSP_extendedStatus          "extendedStatus"
#define LN_id_pkix_OCSP_extendedStatus          "Extended OCSP Status"
#define NID_id_pkix_OCSP_extendedStatus         372
#define OBJ_id_pkix_OCSP_extendedStatus         OBJ_id_pkix_OCSP,8L

#define SN_id_pkix_OCSP_valid           "valid"
#define NID_id_pkix_OCSP_valid          373
#define OBJ_id_pkix_OCSP_valid          OBJ_id_pkix_OCSP,9L

#define SN_id_pkix_OCSP_path            "path"
#define NID_id_pkix_OCSP_path           374
#define OBJ_id_pkix_OCSP_path           OBJ_id_pkix_OCSP,10L

#define SN_id_pkix_OCSP_trustRoot               "trustRoot"
#define LN_id_pkix_OCSP_trustRoot               "Trust Root"
#define NID_id_pkix_OCSP_trustRoot              375
#define OBJ_id_pkix_OCSP_trustRoot              OBJ_id_pkix_OCSP,11L

#define SN_algorithm            "algorithm"
#define LN_algorithm            "algorithm"
#define NID_algorithm           376
#define OBJ_algorithm           1L,3L,14L,3L,2L

#define SN_md5WithRSA           "RSA-NP-MD5"
#define LN_md5WithRSA           "md5WithRSA"
#define NID_md5WithRSA          104
#define OBJ_md5WithRSA          OBJ_algorithm,3L

#define SN_des_ecb              "DES-ECB"
#define LN_des_ecb              "des-ecb"
#define NID_des_ecb             29
#define OBJ_des_ecb             OBJ_algorithm,6L

#define SN_des_cbc              "DES-CBC"
#define LN_des_cbc              "des-cbc"
#define NID_des_cbc             31
#define OBJ_des_cbc             OBJ_algorithm,7L

#define SN_des_ofb64            "DES-OFB"
#define LN_des_ofb64            "des-ofb"
#define NID_des_ofb64           45
#define OBJ_des_ofb64           OBJ_algorithm,8L

#define SN_des_cfb64            "DES-CFB"
#define LN_des_cfb64            "des-cfb"
#define NID_des_cfb64           30
#define OBJ_des_cfb64           OBJ_algorithm,9L

#define SN_rsaSignature         "rsaSignature"
#define NID_rsaSignature                377
#define OBJ_rsaSignature                OBJ_algorithm,11L

#define SN_dsa_2                "DSA-old"
#define LN_dsa_2                "dsaEncryption-old"
#define NID_dsa_2               67
#define OBJ_dsa_2               OBJ_algorithm,12L

#define SN_dsaWithSHA           "DSA-SHA"
#define LN_dsaWithSHA           "dsaWithSHA"
#define NID_dsaWithSHA          66
#define OBJ_dsaWithSHA          OBJ_algorithm,13L

#define SN_shaWithRSAEncryption         "RSA-SHA"
#define LN_shaWithRSAEncryption         "shaWithRSAEncryption"
#define NID_shaWithRSAEncryption                42
#define OBJ_shaWithRSAEncryption                OBJ_algorithm,15L

#define SN_des_ede_ecb          "DES-EDE"
#define LN_des_ede_ecb          "des-ede"
#define NID_des_ede_ecb         32
#define OBJ_des_ede_ecb         OBJ_algorithm,17L

#define SN_des_ede3_ecb         "DES-EDE3"
#define LN_des_ede3_ecb         "des-ede3"
#define NID_des_ede3_ecb                33

#define SN_des_ede_cbc          "DES-EDE-CBC"
#define LN_des_ede_cbc          "des-ede-cbc"
#define NID_des_ede_cbc         43

#define SN_des_ede_cfb64                "DES-EDE-CFB"
#define LN_des_ede_cfb64                "des-ede-cfb"
#define NID_des_ede_cfb64               60

#define SN_des_ede3_cfb64               "DES-EDE3-CFB"
#define LN_des_ede3_cfb64               "des-ede3-cfb"
#define NID_des_ede3_cfb64              61

#define SN_des_ede_ofb64                "DES-EDE-OFB"
#define LN_des_ede_ofb64                "des-ede-ofb"
#define NID_des_ede_ofb64               62

#define SN_des_ede3_ofb64               "DES-EDE3-OFB"
#define LN_des_ede3_ofb64               "des-ede3-ofb"
#define NID_des_ede3_ofb64              63

#define SN_desx_cbc             "DESX-CBC"
#define LN_desx_cbc             "desx-cbc"
#define NID_desx_cbc            80

#define SN_sha          "SHA"
#define LN_sha          "sha"
#define NID_sha         41
#define OBJ_sha         OBJ_algorithm,18L

#define SN_sha1         "SHA1"
#define LN_sha1         "sha1"
#define NID_sha1                64
#define OBJ_sha1                OBJ_algorithm,26L

#define SN_dsaWithSHA1_2                "DSA-SHA1-old"
#define LN_dsaWithSHA1_2                "dsaWithSHA1-old"
#define NID_dsaWithSHA1_2               70
#define OBJ_dsaWithSHA1_2               OBJ_algorithm,27L

#define SN_sha1WithRSA          "RSA-SHA1-2"
#define LN_sha1WithRSA          "sha1WithRSA"
#define NID_sha1WithRSA         115
#define OBJ_sha1WithRSA         OBJ_algorithm,29L

#define SN_ripemd160            "RIPEMD160"
#define LN_ripemd160            "ripemd160"
#define NID_ripemd160           117
#define OBJ_ripemd160           1L,3L,36L,3L,2L,1L

#define SN_ripemd160WithRSA             "RSA-RIPEMD160"
#define LN_ripemd160WithRSA             "ripemd160WithRSA"
#define NID_ripemd160WithRSA            119
#define OBJ_ripemd160WithRSA            1L,3L,36L,3L,3L,1L,2L

#define SN_blake2bmac           "BLAKE2BMAC"
#define LN_blake2bmac           "blake2bmac"
#define NID_blake2bmac          1201
#define OBJ_blake2bmac          1L,3L,6L,1L,4L,1L,1722L,12L,2L,1L

#define SN_blake2smac           "BLAKE2SMAC"
#define LN_blake2smac           "blake2smac"
#define NID_blake2smac          1202
#define OBJ_blake2smac          1L,3L,6L,1L,4L,1L,1722L,12L,2L,2L

#define SN_blake2b512           "BLAKE2b512"
#define LN_blake2b512           "blake2b512"
#define NID_blake2b512          1056
#define OBJ_blake2b512          OBJ_blake2bmac,16L

#define SN_blake2s256           "BLAKE2s256"
#define LN_blake2s256           "blake2s256"
#define NID_blake2s256          1057
#define OBJ_blake2s256          OBJ_blake2smac,8L

#define SN_sxnet                "SXNetID"
#define LN_sxnet                "Strong Extranet ID"
#define NID_sxnet               143
#define OBJ_sxnet               1L,3L,101L,1L,4L,1L

#define SN_X500         "X500"
#define LN_X500         "directory services (X.500)"
#define NID_X500                11
#define OBJ_X500                2L,5L

#define SN_X509         "X509"
#define NID_X509                12
#define OBJ_X509                OBJ_X500,4L

#define SN_commonName           "CN"
#define LN_commonName           "commonName"
#define NID_commonName          13
#define OBJ_commonName          OBJ_X509,3L

#define SN_surname              "SN"
#define LN_surname              "surname"
#define NID_surname             100
#define OBJ_surname             OBJ_X509,4L

#define LN_serialNumber         "serialNumber"
#define NID_serialNumber                105
#define OBJ_serialNumber                OBJ_X509,5L

#define SN_countryName          "C"
#define LN_countryName          "countryName"
#define NID_countryName         14
#define OBJ_countryName         OBJ_X509,6L

#define SN_localityName         "L"
#define LN_localityName         "localityName"
#define NID_localityName                15
#define OBJ_localityName                OBJ_X509,7L

#define SN_stateOrProvinceName          "ST"
#define LN_stateOrProvinceName          "stateOrProvinceName"
#define NID_stateOrProvinceName         16
#define OBJ_stateOrProvinceName         OBJ_X509,8L

#define SN_streetAddress                "street"
#define LN_streetAddress                "streetAddress"
#define NID_streetAddress               660
#define OBJ_streetAddress               OBJ_X509,9L

#define SN_organizationName             "O"
#define LN_organizationName             "organizationName"
#define NID_organizationName            17
#define OBJ_organizationName            OBJ_X509,10L

#define SN_organizationalUnitName               "OU"
#define LN_organizationalUnitName               "organizationalUnitName"
#define NID_organizationalUnitName              18
#define OBJ_organizationalUnitName              OBJ_X509,11L

#define SN_title                "title"
#define LN_title                "title"
#define NID_title               106
#define OBJ_title               OBJ_X509,12L

#define LN_description          "description"
#define NID_description         107
#define OBJ_description         OBJ_X509,13L

#define LN_searchGuide          "searchGuide"
#define NID_searchGuide         859
#define OBJ_searchGuide         OBJ_X509,14L

#define LN_businessCategory             "businessCategory"
#define NID_businessCategory            860
#define OBJ_businessCategory            OBJ_X509,15L

#define LN_postalAddress                "postalAddress"
#define NID_postalAddress               861
#define OBJ_postalAddress               OBJ_X509,16L

#define LN_postalCode           "postalCode"
#define NID_postalCode          661
#define OBJ_postalCode          OBJ_X509,17L

#define LN_postOfficeBox                "postOfficeBox"
#define NID_postOfficeBox               862
#define OBJ_postOfficeBox               OBJ_X509,18L

#define LN_physicalDeliveryOfficeName           "physicalDeliveryOfficeName"
#define NID_physicalDeliveryOfficeName          863
#define OBJ_physicalDeliveryOfficeName          OBJ_X509,19L

#define LN_telephoneNumber              "telephoneNumber"
#define NID_telephoneNumber             864
#define OBJ_telephoneNumber             OBJ_X509,20L

#define LN_telexNumber          "telexNumber"
#define NID_telexNumber         865
#define OBJ_telexNumber         OBJ_X509,21L

#define LN_teletexTerminalIdentifier            "teletexTerminalIdentifier"
#define NID_teletexTerminalIdentifier           866
#define OBJ_teletexTerminalIdentifier           OBJ_X509,22L

#define LN_facsimileTelephoneNumber             "facsimileTelephoneNumber"
#define NID_facsimileTelephoneNumber            867
#define OBJ_facsimileTelephoneNumber            OBJ_X509,23L

#define LN_x121Address          "x121Address"
#define NID_x121Address         868
#define OBJ_x121Address         OBJ_X509,24L

#define LN_internationaliSDNNumber              "internationaliSDNNumber"
#define NID_internationaliSDNNumber             869
#define OBJ_internationaliSDNNumber             OBJ_X509,25L

#define LN_registeredAddress            "registeredAddress"
#define NID_registeredAddress           870
#define OBJ_registeredAddress           OBJ_X509,26L

#define LN_destinationIndicator         "destinationIndicator"
#define NID_destinationIndicator                871
#define OBJ_destinationIndicator                OBJ_X509,27L

#define LN_preferredDeliveryMethod              "preferredDeliveryMethod"
#define NID_preferredDeliveryMethod             872
#define OBJ_preferredDeliveryMethod             OBJ_X509,28L

#define LN_presentationAddress          "presentationAddress"
#define NID_presentationAddress         873
#define OBJ_presentationAddress         OBJ_X509,29L

#define LN_supportedApplicationContext          "supportedApplicationContext"
#define NID_supportedApplicationContext         874
#define OBJ_supportedApplicationContext         OBJ_X509,30L

#define SN_member               "member"
#define NID_member              875
#define OBJ_member              OBJ_X509,31L

#define SN_owner                "owner"
#define NID_owner               876
#define OBJ_owner               OBJ_X509,32L

#define LN_roleOccupant         "roleOccupant"
#define NID_roleOccupant                877
#define OBJ_roleOccupant                OBJ_X509,33L

#define SN_seeAlso              "seeAlso"
#define NID_seeAlso             878
#define OBJ_seeAlso             OBJ_X509,34L

#define LN_userPassword         "userPassword"
#define NID_userPassword                879
#define OBJ_userPassword                OBJ_X509,35L

#define LN_userCertificate              "userCertificate"
#define NID_userCertificate             880
#define OBJ_userCertificate             OBJ_X509,36L

#define LN_cACertificate                "cACertificate"
#define NID_cACertificate               881
#define OBJ_cACertificate               OBJ_X509,37L

#define LN_authorityRevocationList              "authorityRevocationList"
#define NID_authorityRevocationList             882
#define OBJ_authorityRevocationList             OBJ_X509,38L

#define LN_certificateRevocationList            "certificateRevocationList"
#define NID_certificateRevocationList           883
#define OBJ_certificateRevocationList           OBJ_X509,39L

#define LN_crossCertificatePair         "crossCertificatePair"
#define NID_crossCertificatePair                884
#define OBJ_crossCertificatePair                OBJ_X509,40L

#define SN_name         "name"
#define LN_name         "name"
#define NID_name                173
#define OBJ_name                OBJ_X509,41L

#define SN_givenName            "GN"
#define LN_givenName            "givenName"
#define NID_givenName           99
#define OBJ_givenName           OBJ_X509,42L

#define SN_initials             "initials"
#define LN_initials             "initials"
#define NID_initials            101
#define OBJ_initials            OBJ_X509,43L

#define LN_generationQualifier          "generationQualifier"
#define NID_generationQualifier         509
#define OBJ_generationQualifier         OBJ_X509,44L

#define LN_x500UniqueIdentifier         "x500UniqueIdentifier"
#define NID_x500UniqueIdentifier                503
#define OBJ_x500UniqueIdentifier                OBJ_X509,45L

#define SN_dnQualifier          "dnQualifier"
#define LN_dnQualifier          "dnQualifier"
#define NID_dnQualifier         174
#define OBJ_dnQualifier         OBJ_X509,46L

#define LN_enhancedSearchGuide          "enhancedSearchGuide"
#define NID_enhancedSearchGuide         885
#define OBJ_enhancedSearchGuide         OBJ_X509,47L

#define LN_protocolInformation          "protocolInformation"
#define NID_protocolInformation         886
#define OBJ_protocolInformation         OBJ_X509,48L

#define LN_distinguishedName            "distinguishedName"
#define NID_distinguishedName           887
#define OBJ_distinguishedName           OBJ_X509,49L

#define LN_uniqueMember         "uniqueMember"
#define NID_uniqueMember                888
#define OBJ_uniqueMember                OBJ_X509,50L

#define LN_houseIdentifier              "houseIdentifier"
#define NID_houseIdentifier             889
#define OBJ_houseIdentifier             OBJ_X509,51L

#define LN_supportedAlgorithms          "supportedAlgorithms"
#define NID_supportedAlgorithms         890
#define OBJ_supportedAlgorithms         OBJ_X509,52L

#define LN_deltaRevocationList          "deltaRevocationList"
#define NID_deltaRevocationList         891
#define OBJ_deltaRevocationList         OBJ_X509,53L

#define SN_dmdName              "dmdName"
#define NID_dmdName             892
#define OBJ_dmdName             OBJ_X509,54L

#define LN_pseudonym            "pseudonym"
#define NID_pseudonym           510
#define OBJ_pseudonym           OBJ_X509,65L

#define SN_role         "role"
#define LN_role         "role"
#define NID_role                400
#define OBJ_role                OBJ_X509,72L

#define LN_organizationIdentifier               "organizationIdentifier"
#define NID_organizationIdentifier              1089
#define OBJ_organizationIdentifier              OBJ_X509,97L

#define SN_countryCode3c                "c3"
#define LN_countryCode3c                "countryCode3c"
#define NID_countryCode3c               1090
#define OBJ_countryCode3c               OBJ_X509,98L

#define SN_countryCode3n                "n3"
#define LN_countryCode3n                "countryCode3n"
#define NID_countryCode3n               1091
#define OBJ_countryCode3n               OBJ_X509,99L

#define LN_dnsName              "dnsName"
#define NID_dnsName             1092
#define OBJ_dnsName             OBJ_X509,100L

#define SN_X500algorithms               "X500algorithms"
#define LN_X500algorithms               "directory services - algorithms"
#define NID_X500algorithms              378
#define OBJ_X500algorithms              OBJ_X500,8L

#define SN_rsa          "RSA"
#define LN_rsa          "rsa"
#define NID_rsa         19
#define OBJ_rsa         OBJ_X500algorithms,1L,1L

#define SN_mdc2WithRSA          "RSA-MDC2"
#define LN_mdc2WithRSA          "mdc2WithRSA"
#define NID_mdc2WithRSA         96
#define OBJ_mdc2WithRSA         OBJ_X500algorithms,3L,100L

#define SN_mdc2         "MDC2"
#define LN_mdc2         "mdc2"
#define NID_mdc2                95
#define OBJ_mdc2                OBJ_X500algorithms,3L,101L

#define SN_id_ce                "id-ce"
#define NID_id_ce               81
#define OBJ_id_ce               OBJ_X500,29L

#define SN_subject_directory_attributes         "subjectDirectoryAttributes"
#define LN_subject_directory_attributes         "X509v3 Subject Directory Attributes"
#define NID_subject_directory_attributes                769
#define OBJ_subject_directory_attributes                OBJ_id_ce,9L

#define SN_subject_key_identifier               "subjectKeyIdentifier"
#define LN_subject_key_identifier               "X509v3 Subject Key Identifier"
#define NID_subject_key_identifier              82
#define OBJ_subject_key_identifier              OBJ_id_ce,14L

#define SN_key_usage            "keyUsage"
#define LN_key_usage            "X509v3 Key Usage"
#define NID_key_usage           83
#define OBJ_key_usage           OBJ_id_ce,15L

#define SN_private_key_usage_period             "privateKeyUsagePeriod"
#define LN_private_key_usage_period             "X509v3 Private Key Usage Period"
#define NID_private_key_usage_period            84
#define OBJ_private_key_usage_period            OBJ_id_ce,16L

#define SN_subject_alt_name             "subjectAltName"
#define LN_subject_alt_name             "X509v3 Subject Alternative Name"
#define NID_subject_alt_name            85
#define OBJ_subject_alt_name            OBJ_id_ce,17L

#define SN_issuer_alt_name              "issuerAltName"
#define LN_issuer_alt_name              "X509v3 Issuer Alternative Name"
#define NID_issuer_alt_name             86
#define OBJ_issuer_alt_name             OBJ_id_ce,18L

#define SN_basic_constraints            "basicConstraints"
#define LN_basic_constraints            "X509v3 Basic Constraints"
#define NID_basic_constraints           87
#define OBJ_basic_constraints           OBJ_id_ce,19L

#define SN_crl_number           "crlNumber"
#define LN_crl_number           "X509v3 CRL Number"
#define NID_crl_number          88
#define OBJ_crl_number          OBJ_id_ce,20L

#define SN_crl_reason           "CRLReason"
#define LN_crl_reason           "X509v3 CRL Reason Code"
#define NID_crl_reason          141
#define OBJ_crl_reason          OBJ_id_ce,21L

#define SN_invalidity_date              "invalidityDate"
#define LN_invalidity_date              "Invalidity Date"
#define NID_invalidity_date             142
#define OBJ_invalidity_date             OBJ_id_ce,24L

#define SN_delta_crl            "deltaCRL"
#define LN_delta_crl            "X509v3 Delta CRL Indicator"
#define NID_delta_crl           140
#define OBJ_delta_crl           OBJ_id_ce,27L

#define SN_issuing_distribution_point           "issuingDistributionPoint"
#define LN_issuing_distribution_point           "X509v3 Issuing Distribution Point"
#define NID_issuing_distribution_point          770
#define OBJ_issuing_distribution_point          OBJ_id_ce,28L

#define SN_certificate_issuer           "certificateIssuer"
#define LN_certificate_issuer           "X509v3 Certificate Issuer"
#define NID_certificate_issuer          771
#define OBJ_certificate_issuer          OBJ_id_ce,29L

#define SN_name_constraints             "nameConstraints"
#define LN_name_constraints             "X509v3 Name Constraints"
#define NID_name_constraints            666
#define OBJ_name_constraints            OBJ_id_ce,30L

#define SN_crl_distribution_points              "crlDistributionPoints"
#define LN_crl_distribution_points              "X509v3 CRL Distribution Points"
#define NID_crl_distribution_points             103
#define OBJ_crl_distribution_points             OBJ_id_ce,31L

#define SN_certificate_policies         "certificatePolicies"
#define LN_certificate_policies         "X509v3 Certificate Policies"
#define NID_certificate_policies                89
#define OBJ_certificate_policies                OBJ_id_ce,32L

#define SN_any_policy           "anyPolicy"
#define LN_any_policy           "X509v3 Any Policy"
#define NID_any_policy          746
#define OBJ_any_policy          OBJ_certificate_policies,0L

#define SN_policy_mappings              "policyMappings"
#define LN_policy_mappings              "X509v3 Policy Mappings"
#define NID_policy_mappings             747
#define OBJ_policy_mappings             OBJ_id_ce,33L

#define SN_authority_key_identifier             "authorityKeyIdentifier"
#define LN_authority_key_identifier             "X509v3 Authority Key Identifier"
#define NID_authority_key_identifier            90
#define OBJ_authority_key_identifier            OBJ_id_ce,35L

#define SN_policy_constraints           "policyConstraints"
#define LN_policy_constraints           "X509v3 Policy Constraints"
#define NID_policy_constraints          401
#define OBJ_policy_constraints          OBJ_id_ce,36L

#define SN_ext_key_usage                "extendedKeyUsage"
#define LN_ext_key_usage                "X509v3 Extended Key Usage"
#define NID_ext_key_usage               126
#define OBJ_ext_key_usage               OBJ_id_ce,37L

#define SN_authority_attribute_identifier               "authorityAttributeIdentifier"
#define LN_authority_attribute_identifier               "X509v3 Authority Attribute Identifier"
#define NID_authority_attribute_identifier              1295
#define OBJ_authority_attribute_identifier              OBJ_id_ce,38L

#define SN_role_spec_cert_identifier            "roleSpecCertIdentifier"
#define LN_role_spec_cert_identifier            "X509v3 Role Specification Certificate Identifier"
#define NID_role_spec_cert_identifier           1296
#define OBJ_role_spec_cert_identifier           OBJ_id_ce,39L

#define SN_basic_att_constraints                "basicAttConstraints"
#define LN_basic_att_constraints                "X509v3 Basic Attribute Certificate Constraints"
#define NID_basic_att_constraints               1297
#define OBJ_basic_att_constraints               OBJ_id_ce,41L

#define SN_delegated_name_constraints           "delegatedNameConstraints"
#define LN_delegated_name_constraints           "X509v3 Delegated Name Constraints"
#define NID_delegated_name_constraints          1298
#define OBJ_delegated_name_constraints          OBJ_id_ce,42L

#define SN_time_specification           "timeSpecification"
#define LN_time_specification           "X509v3 Time Specification"
#define NID_time_specification          1299
#define OBJ_time_specification          OBJ_id_ce,43L

#define SN_freshest_crl         "freshestCRL"
#define LN_freshest_crl         "X509v3 Freshest CRL"
#define NID_freshest_crl                857
#define OBJ_freshest_crl                OBJ_id_ce,46L

#define SN_attribute_descriptor         "attributeDescriptor"
#define LN_attribute_descriptor         "X509v3 Attribute Descriptor"
#define NID_attribute_descriptor                1300
#define OBJ_attribute_descriptor                OBJ_id_ce,48L

#define SN_user_notice          "userNotice"
#define LN_user_notice          "X509v3 User Notice"
#define NID_user_notice         1301
#define OBJ_user_notice         OBJ_id_ce,49L

#define SN_soa_identifier               "sOAIdentifier"
#define LN_soa_identifier               "X509v3 Source of Authority Identifier"
#define NID_soa_identifier              1302
#define OBJ_soa_identifier              OBJ_id_ce,50L

#define SN_acceptable_cert_policies             "acceptableCertPolicies"
#define LN_acceptable_cert_policies             "X509v3 Acceptable Certification Policies"
#define NID_acceptable_cert_policies            1303
#define OBJ_acceptable_cert_policies            OBJ_id_ce,52L

#define SN_inhibit_any_policy           "inhibitAnyPolicy"
#define LN_inhibit_any_policy           "X509v3 Inhibit Any Policy"
#define NID_inhibit_any_policy          748
#define OBJ_inhibit_any_policy          OBJ_id_ce,54L

#define SN_target_information           "targetInformation"
#define LN_target_information           "X509v3 AC Targeting"
#define NID_target_information          402
#define OBJ_target_information          OBJ_id_ce,55L

#define SN_no_rev_avail         "noRevAvail"
#define LN_no_rev_avail         "X509v3 No Revocation Available"
#define NID_no_rev_avail                403
#define OBJ_no_rev_avail                OBJ_id_ce,56L

#define SN_acceptable_privilege_policies                "acceptablePrivPolicies"
#define LN_acceptable_privilege_policies                "X509v3 Acceptable Privilege Policies"
#define NID_acceptable_privilege_policies               1304
#define OBJ_acceptable_privilege_policies               OBJ_id_ce,57L

#define SN_indirect_issuer              "indirectIssuer"
#define LN_indirect_issuer              "X509v3 Indirect Issuer"
#define NID_indirect_issuer             1305
#define OBJ_indirect_issuer             OBJ_id_ce,61L

#define SN_no_assertion         "noAssertion"
#define LN_no_assertion         "X509v3 No Assertion"
#define NID_no_assertion                1306
#define OBJ_no_assertion                OBJ_id_ce,62L

#define SN_id_aa_issuing_distribution_point             "aAissuingDistributionPoint"
#define LN_id_aa_issuing_distribution_point             "X509v3 Attribute Authority Issuing Distribution Point"
#define NID_id_aa_issuing_distribution_point            1307
#define OBJ_id_aa_issuing_distribution_point            OBJ_id_ce,63L

#define SN_issued_on_behalf_of          "issuedOnBehalfOf"
#define LN_issued_on_behalf_of          "X509v3 Issued On Behalf Of"
#define NID_issued_on_behalf_of         1308
#define OBJ_issued_on_behalf_of         OBJ_id_ce,64L

#define SN_single_use           "singleUse"
#define LN_single_use           "X509v3 Single Use"
#define NID_single_use          1309
#define OBJ_single_use          OBJ_id_ce,65L

#define SN_group_ac             "groupAC"
#define LN_group_ac             "X509v3 Group Attribute Certificate"
#define NID_group_ac            1310
#define OBJ_group_ac            OBJ_id_ce,66L

#define SN_allowed_attribute_assignments                "allowedAttributeAssignments"
#define LN_allowed_attribute_assignments                "X509v3 Allowed Attribute Assignments"
#define NID_allowed_attribute_assignments               1311
#define OBJ_allowed_attribute_assignments               OBJ_id_ce,67L

#define SN_attribute_mappings           "attributeMappings"
#define LN_attribute_mappings           "X509v3 Attribute Mappings"
#define NID_attribute_mappings          1312
#define OBJ_attribute_mappings          OBJ_id_ce,68L

#define SN_holder_name_constraints              "holderNameConstraints"
#define LN_holder_name_constraints              "X509v3 Holder Name Constraints"
#define NID_holder_name_constraints             1313
#define OBJ_holder_name_constraints             OBJ_id_ce,69L

#define SN_authorization_validation             "authorizationValidation"
#define LN_authorization_validation             "X509v3 Authorization Validation"
#define NID_authorization_validation            1314
#define OBJ_authorization_validation            OBJ_id_ce,70L

#define SN_prot_restrict                "protRestrict"
#define LN_prot_restrict                "X509v3 Protocol Restriction"
#define NID_prot_restrict               1315
#define OBJ_prot_restrict               OBJ_id_ce,71L

#define SN_subject_alt_public_key_info          "subjectAltPublicKeyInfo"
#define LN_subject_alt_public_key_info          "X509v3 Subject Alternative Public Key Info"
#define NID_subject_alt_public_key_info         1316
#define OBJ_subject_alt_public_key_info         OBJ_id_ce,72L

#define SN_alt_signature_algorithm              "altSignatureAlgorithm"
#define LN_alt_signature_algorithm              "X509v3 Alternative Signature Algorithm"
#define NID_alt_signature_algorithm             1317
#define OBJ_alt_signature_algorithm             OBJ_id_ce,73L

#define SN_alt_signature_value          "altSignatureValue"
#define LN_alt_signature_value          "X509v3 Alternative Signature Value"
#define NID_alt_signature_value         1318
#define OBJ_alt_signature_value         OBJ_id_ce,74L

#define SN_associated_information               "associatedInformation"
#define LN_associated_information               "X509v3 Associated Information"
#define NID_associated_information              1319
#define OBJ_associated_information              OBJ_id_ce,75L

#define SN_anyExtendedKeyUsage          "anyExtendedKeyUsage"
#define LN_anyExtendedKeyUsage          "Any Extended Key Usage"
#define NID_anyExtendedKeyUsage         910
#define OBJ_anyExtendedKeyUsage         OBJ_ext_key_usage,0L

#define SN_netscape             "Netscape"
#define LN_netscape             "Netscape Communications Corp."
#define NID_netscape            57
#define OBJ_netscape            2L,16L,840L,1L,113730L

#define SN_netscape_cert_extension              "nsCertExt"
#define LN_netscape_cert_extension              "Netscape Certificate Extension"
#define NID_netscape_cert_extension             58
#define OBJ_netscape_cert_extension             OBJ_netscape,1L

#define SN_netscape_data_type           "nsDataType"
#define LN_netscape_data_type           "Netscape Data Type"
#define NID_netscape_data_type          59
#define OBJ_netscape_data_type          OBJ_netscape,2L

#define SN_netscape_cert_type           "nsCertType"
#define LN_netscape_cert_type           "Netscape Cert Type"
#define NID_netscape_cert_type          71
#define OBJ_netscape_cert_type          OBJ_netscape_cert_extension,1L

#define SN_netscape_base_url            "nsBaseUrl"
#define LN_netscape_base_url            "Netscape Base Url"
#define NID_netscape_base_url           72
#define OBJ_netscape_base_url           OBJ_netscape_cert_extension,2L

#define SN_netscape_revocation_url              "nsRevocationUrl"
#define LN_netscape_revocation_url              "Netscape Revocation Url"
#define NID_netscape_revocation_url             73
#define OBJ_netscape_revocation_url             OBJ_netscape_cert_extension,3L

#define SN_netscape_ca_revocation_url           "nsCaRevocationUrl"
#define LN_netscape_ca_revocation_url           "Netscape CA Revocation Url"
#define NID_netscape_ca_revocation_url          74
#define OBJ_netscape_ca_revocation_url          OBJ_netscape_cert_extension,4L

#define SN_netscape_renewal_url         "nsRenewalUrl"
#define LN_netscape_renewal_url         "Netscape Renewal Url"
#define NID_netscape_renewal_url                75
#define OBJ_netscape_renewal_url                OBJ_netscape_cert_extension,7L

#define SN_netscape_ca_policy_url               "nsCaPolicyUrl"
#define LN_netscape_ca_policy_url               "Netscape CA Policy Url"
#define NID_netscape_ca_policy_url              76
#define OBJ_netscape_ca_policy_url              OBJ_netscape_cert_extension,8L

#define SN_netscape_ssl_server_name             "nsSslServerName"
#define LN_netscape_ssl_server_name             "Netscape SSL Server Name"
#define NID_netscape_ssl_server_name            77
#define OBJ_netscape_ssl_server_name            OBJ_netscape_cert_extension,12L

#define SN_netscape_comment             "nsComment"
#define LN_netscape_comment             "Netscape Comment"
#define NID_netscape_comment            78
#define OBJ_netscape_comment            OBJ_netscape_cert_extension,13L

#define SN_netscape_cert_sequence               "nsCertSequence"
#define LN_netscape_cert_sequence               "Netscape Certificate Sequence"
#define NID_netscape_cert_sequence              79
#define OBJ_netscape_cert_sequence              OBJ_netscape_data_type,5L

#define SN_ns_sgc               "nsSGC"
#define LN_ns_sgc               "Netscape Server Gated Crypto"
#define NID_ns_sgc              139
#define OBJ_ns_sgc              OBJ_netscape,4L,1L

#define SN_org          "ORG"
#define LN_org          "org"
#define NID_org         379
#define OBJ_org         OBJ_iso,3L

#define SN_dod          "DOD"
#define LN_dod          "dod"
#define NID_dod         380
#define OBJ_dod         OBJ_org,6L

#define SN_iana         "IANA"
#define LN_iana         "iana"
#define NID_iana                381
#define OBJ_iana                OBJ_dod,1L

#define OBJ_internet            OBJ_iana

#define SN_Directory            "directory"
#define LN_Directory            "Directory"
#define NID_Directory           382
#define OBJ_Directory           OBJ_internet,1L

#define SN_Management           "mgmt"
#define LN_Management           "Management"
#define NID_Management          383
#define OBJ_Management          OBJ_internet,2L

#define SN_Experimental         "experimental"
#define LN_Experimental         "Experimental"
#define NID_Experimental                384
#define OBJ_Experimental                OBJ_internet,3L

#define SN_Private              "private"
#define LN_Private              "Private"
#define NID_Private             385
#define OBJ_Private             OBJ_internet,4L

#define SN_Security             "security"
#define LN_Security             "Security"
#define NID_Security            386
#define OBJ_Security            OBJ_internet,5L

#define SN_SNMPv2               "snmpv2"
#define LN_SNMPv2               "SNMPv2"
#define NID_SNMPv2              387
#define OBJ_SNMPv2              OBJ_internet,6L

#define LN_Mail         "Mail"
#define NID_Mail                388
#define OBJ_Mail                OBJ_internet,7L

#define SN_Enterprises          "enterprises"
#define LN_Enterprises          "Enterprises"
#define NID_Enterprises         389
#define OBJ_Enterprises         OBJ_Private,1L

#define SN_dcObject             "dcobject"
#define LN_dcObject             "dcObject"
#define NID_dcObject            390
#define OBJ_dcObject            OBJ_Enterprises,1466L,344L

#define SN_id_kp_wisun_fan_device               "id-kp-wisun-fan-device"
#define LN_id_kp_wisun_fan_device               "Wi-SUN Alliance Field Area Network (FAN)"
#define NID_id_kp_wisun_fan_device              1322
#define OBJ_id_kp_wisun_fan_device              OBJ_Enterprises,45605L,1L

#define SN_mime_mhs             "mime-mhs"
#define LN_mime_mhs             "MIME MHS"
#define NID_mime_mhs            504
#define OBJ_mime_mhs            OBJ_Mail,1L

#define SN_mime_mhs_headings            "mime-mhs-headings"
#define LN_mime_mhs_headings            "mime-mhs-headings"
#define NID_mime_mhs_headings           505
#define OBJ_mime_mhs_headings           OBJ_mime_mhs,1L

#define SN_mime_mhs_bodies              "mime-mhs-bodies"
#define LN_mime_mhs_bodies              "mime-mhs-bodies"
#define NID_mime_mhs_bodies             506
#define OBJ_mime_mhs_bodies             OBJ_mime_mhs,2L

#define SN_id_hex_partial_message               "id-hex-partial-message"
#define LN_id_hex_partial_message               "id-hex-partial-message"
#define NID_id_hex_partial_message              507
#define OBJ_id_hex_partial_message              OBJ_mime_mhs_headings,1L

#define SN_id_hex_multipart_message             "id-hex-multipart-message"
#define LN_id_hex_multipart_message             "id-hex-multipart-message"
#define NID_id_hex_multipart_message            508
#define OBJ_id_hex_multipart_message            OBJ_mime_mhs_headings,2L

#define SN_zlib_compression             "ZLIB"
#define LN_zlib_compression             "zlib compression"
#define NID_zlib_compression            125
#define OBJ_zlib_compression            OBJ_id_smime_alg,8L

#define OBJ_csor                2L,16L,840L,1L,101L,3L

#define OBJ_nistAlgorithms              OBJ_csor,4L

#define OBJ_aes         OBJ_nistAlgorithms,1L

#define SN_aes_128_ecb          "AES-128-ECB"
#define LN_aes_128_ecb          "aes-128-ecb"
#define NID_aes_128_ecb         418
#define OBJ_aes_128_ecb         OBJ_aes,1L

#define SN_aes_128_cbc          "AES-128-CBC"
#define LN_aes_128_cbc          "aes-128-cbc"
#define NID_aes_128_cbc         419
#define OBJ_aes_128_cbc         OBJ_aes,2L

#define SN_aes_128_ofb128               "AES-128-OFB"
#define LN_aes_128_ofb128               "aes-128-ofb"
#define NID_aes_128_ofb128              420
#define OBJ_aes_128_ofb128              OBJ_aes,3L

#define SN_aes_128_cfb128               "AES-128-CFB"
#define LN_aes_128_cfb128               "aes-128-cfb"
#define NID_aes_128_cfb128              421
#define OBJ_aes_128_cfb128              OBJ_aes,4L

#define SN_id_aes128_wrap               "id-aes128-wrap"
#define NID_id_aes128_wrap              788
#define OBJ_id_aes128_wrap              OBJ_aes,5L

#define SN_aes_128_gcm          "id-aes128-GCM"
#define LN_aes_128_gcm          "aes-128-gcm"
#define NID_aes_128_gcm         895
#define OBJ_aes_128_gcm         OBJ_aes,6L

#define SN_aes_128_ccm          "id-aes128-CCM"
#define LN_aes_128_ccm          "aes-128-ccm"
#define NID_aes_128_ccm         896
#define OBJ_aes_128_ccm         OBJ_aes,7L

#define SN_id_aes128_wrap_pad           "id-aes128-wrap-pad"
#define NID_id_aes128_wrap_pad          897
#define OBJ_id_aes128_wrap_pad          OBJ_aes,8L

#define SN_aes_192_ecb          "AES-192-ECB"
#define LN_aes_192_ecb          "aes-192-ecb"
#define NID_aes_192_ecb         422
#define OBJ_aes_192_ecb         OBJ_aes,21L

#define SN_aes_192_cbc          "AES-192-CBC"
#define LN_aes_192_cbc          "aes-192-cbc"
#define NID_aes_192_cbc         423
#define OBJ_aes_192_cbc         OBJ_aes,22L

#define SN_aes_192_ofb128               "AES-192-OFB"
#define LN_aes_192_ofb128               "aes-192-ofb"
#define NID_aes_192_ofb128              424
#define OBJ_aes_192_ofb128              OBJ_aes,23L

#define SN_aes_192_cfb128               "AES-192-CFB"
#define LN_aes_192_cfb128               "aes-192-cfb"
#define NID_aes_192_cfb128              425
#define OBJ_aes_192_cfb128              OBJ_aes,24L

#define SN_id_aes192_wrap               "id-aes192-wrap"
#define NID_id_aes192_wrap              789
#define OBJ_id_aes192_wrap              OBJ_aes,25L

#define SN_aes_192_gcm          "id-aes192-GCM"
#define LN_aes_192_gcm          "aes-192-gcm"
#define NID_aes_192_gcm         898
#define OBJ_aes_192_gcm         OBJ_aes,26L

#define SN_aes_192_ccm          "id-aes192-CCM"
#define LN_aes_192_ccm          "aes-192-ccm"
#define NID_aes_192_ccm         899
#define OBJ_aes_192_ccm         OBJ_aes,27L

#define SN_id_aes192_wrap_pad           "id-aes192-wrap-pad"
#define NID_id_aes192_wrap_pad          900
#define OBJ_id_aes192_wrap_pad          OBJ_aes,28L

#define SN_aes_256_ecb          "AES-256-ECB"
#define LN_aes_256_ecb          "aes-256-ecb"
#define NID_aes_256_ecb         426
#define OBJ_aes_256_ecb         OBJ_aes,41L

#define SN_aes_256_cbc          "AES-256-CBC"
#define LN_aes_256_cbc          "aes-256-cbc"
#define NID_aes_256_cbc         427
#define OBJ_aes_256_cbc         OBJ_aes,42L

#define SN_aes_256_ofb128               "AES-256-OFB"
#define LN_aes_256_ofb128               "aes-256-ofb"
#define NID_aes_256_ofb128              428
#define OBJ_aes_256_ofb128              OBJ_aes,43L

#define SN_aes_256_cfb128               "AES-256-CFB"
#define LN_aes_256_cfb128               "aes-256-cfb"
#define NID_aes_256_cfb128              429
#define OBJ_aes_256_cfb128              OBJ_aes,44L

#define SN_id_aes256_wrap               "id-aes256-wrap"
#define NID_id_aes256_wrap              790
#define OBJ_id_aes256_wrap              OBJ_aes,45L

#define SN_aes_256_gcm          "id-aes256-GCM"
#define LN_aes_256_gcm          "aes-256-gcm"
#define NID_aes_256_gcm         901
#define OBJ_aes_256_gcm         OBJ_aes,46L

#define SN_aes_256_ccm          "id-aes256-CCM"
#define LN_aes_256_ccm          "aes-256-ccm"
#define NID_aes_256_ccm         902
#define OBJ_aes_256_ccm         OBJ_aes,47L

#define SN_id_aes256_wrap_pad           "id-aes256-wrap-pad"
#define NID_id_aes256_wrap_pad          903
#define OBJ_id_aes256_wrap_pad          OBJ_aes,48L

#define SN_aes_128_xts          "AES-128-XTS"
#define LN_aes_128_xts          "aes-128-xts"
#define NID_aes_128_xts         913
#define OBJ_aes_128_xts         OBJ_ieee_siswg,0L,1L,1L

#define SN_aes_256_xts          "AES-256-XTS"
#define LN_aes_256_xts          "aes-256-xts"
#define NID_aes_256_xts         914
#define OBJ_aes_256_xts         OBJ_ieee_siswg,0L,1L,2L

#define SN_aes_128_cfb1         "AES-128-CFB1"
#define LN_aes_128_cfb1         "aes-128-cfb1"
#define NID_aes_128_cfb1                650

#define SN_aes_192_cfb1         "AES-192-CFB1"
#define LN_aes_192_cfb1         "aes-192-cfb1"
#define NID_aes_192_cfb1                651

#define SN_aes_256_cfb1         "AES-256-CFB1"
#define LN_aes_256_cfb1         "aes-256-cfb1"
#define NID_aes_256_cfb1                652

#define SN_aes_128_cfb8         "AES-128-CFB8"
#define LN_aes_128_cfb8         "aes-128-cfb8"
#define NID_aes_128_cfb8                653

#define SN_aes_192_cfb8         "AES-192-CFB8"
#define LN_aes_192_cfb8         "aes-192-cfb8"
#define NID_aes_192_cfb8                654

#define SN_aes_256_cfb8         "AES-256-CFB8"
#define LN_aes_256_cfb8         "aes-256-cfb8"
#define NID_aes_256_cfb8                655

#define SN_aes_128_ctr          "AES-128-CTR"
#define LN_aes_128_ctr          "aes-128-ctr"
#define NID_aes_128_ctr         904

#define SN_aes_192_ctr          "AES-192-CTR"
#define LN_aes_192_ctr          "aes-192-ctr"
#define NID_aes_192_ctr         905

#define SN_aes_256_ctr          "AES-256-CTR"
#define LN_aes_256_ctr          "aes-256-ctr"
#define NID_aes_256_ctr         906

#define SN_aes_128_ocb          "AES-128-OCB"
#define LN_aes_128_ocb          "aes-128-ocb"
#define NID_aes_128_ocb         958

#define SN_aes_192_ocb          "AES-192-OCB"
#define LN_aes_192_ocb          "aes-192-ocb"
#define NID_aes_192_ocb         959

#define SN_aes_256_ocb          "AES-256-OCB"
#define LN_aes_256_ocb          "aes-256-ocb"
#define NID_aes_256_ocb         960

#define SN_des_cfb1             "DES-CFB1"
#define LN_des_cfb1             "des-cfb1"
#define NID_des_cfb1            656

#define SN_des_cfb8             "DES-CFB8"
#define LN_des_cfb8             "des-cfb8"
#define NID_des_cfb8            657

#define SN_des_ede3_cfb1                "DES-EDE3-CFB1"
#define LN_des_ede3_cfb1                "des-ede3-cfb1"
#define NID_des_ede3_cfb1               658

#define SN_des_ede3_cfb8                "DES-EDE3-CFB8"
#define LN_des_ede3_cfb8                "des-ede3-cfb8"
#define NID_des_ede3_cfb8               659

#define OBJ_nist_hashalgs               OBJ_nistAlgorithms,2L

#define SN_sha256               "SHA256"
#define LN_sha256               "sha256"
#define NID_sha256              672
#define OBJ_sha256              OBJ_nist_hashalgs,1L

#define SN_sha384               "SHA384"
#define LN_sha384               "sha384"
#define NID_sha384              673
#define OBJ_sha384              OBJ_nist_hashalgs,2L

#define SN_sha512               "SHA512"
#define LN_sha512               "sha512"
#define NID_sha512              674
#define OBJ_sha512              OBJ_nist_hashalgs,3L

#define SN_sha224               "SHA224"
#define LN_sha224               "sha224"
#define NID_sha224              675
#define OBJ_sha224              OBJ_nist_hashalgs,4L

#define SN_sha512_224           "SHA512-224"
#define LN_sha512_224           "sha512-224"
#define NID_sha512_224          1094
#define OBJ_sha512_224          OBJ_nist_hashalgs,5L

#define SN_sha512_256           "SHA512-256"
#define LN_sha512_256           "sha512-256"
#define NID_sha512_256          1095
#define OBJ_sha512_256          OBJ_nist_hashalgs,6L

#define SN_sha3_224             "SHA3-224"
#define LN_sha3_224             "sha3-224"
#define NID_sha3_224            1096
#define OBJ_sha3_224            OBJ_nist_hashalgs,7L

#define SN_sha3_256             "SHA3-256"
#define LN_sha3_256             "sha3-256"
#define NID_sha3_256            1097
#define OBJ_sha3_256            OBJ_nist_hashalgs,8L

#define SN_sha3_384             "SHA3-384"
#define LN_sha3_384             "sha3-384"
#define NID_sha3_384            1098
#define OBJ_sha3_384            OBJ_nist_hashalgs,9L

#define SN_sha3_512             "SHA3-512"
#define LN_sha3_512             "sha3-512"
#define NID_sha3_512            1099
#define OBJ_sha3_512            OBJ_nist_hashalgs,10L

#define SN_shake128             "SHAKE128"
#define LN_shake128             "shake128"
#define NID_shake128            1100
#define OBJ_shake128            OBJ_nist_hashalgs,11L

#define SN_shake256             "SHAKE256"
#define LN_shake256             "shake256"
#define NID_shake256            1101
#define OBJ_shake256            OBJ_nist_hashalgs,12L

#define SN_hmac_sha3_224                "id-hmacWithSHA3-224"
#define LN_hmac_sha3_224                "hmac-sha3-224"
#define NID_hmac_sha3_224               1102
#define OBJ_hmac_sha3_224               OBJ_nist_hashalgs,13L

#define SN_hmac_sha3_256                "id-hmacWithSHA3-256"
#define LN_hmac_sha3_256                "hmac-sha3-256"
#define NID_hmac_sha3_256               1103
#define OBJ_hmac_sha3_256               OBJ_nist_hashalgs,14L

#define SN_hmac_sha3_384                "id-hmacWithSHA3-384"
#define LN_hmac_sha3_384                "hmac-sha3-384"
#define NID_hmac_sha3_384               1104
#define OBJ_hmac_sha3_384               OBJ_nist_hashalgs,15L

#define SN_hmac_sha3_512                "id-hmacWithSHA3-512"
#define LN_hmac_sha3_512                "hmac-sha3-512"
#define NID_hmac_sha3_512               1105
#define OBJ_hmac_sha3_512               OBJ_nist_hashalgs,16L

#define SN_kmac128              "KMAC128"
#define LN_kmac128              "kmac128"
#define NID_kmac128             1196
#define OBJ_kmac128             OBJ_nist_hashalgs,19L

#define SN_kmac256              "KMAC256"
#define LN_kmac256              "kmac256"
#define NID_kmac256             1197
#define OBJ_kmac256             OBJ_nist_hashalgs,20L

#define OBJ_dsa_with_sha2               OBJ_nistAlgorithms,3L

#define SN_dsa_with_SHA224              "dsa_with_SHA224"
#define NID_dsa_with_SHA224             802
#define OBJ_dsa_with_SHA224             OBJ_dsa_with_sha2,1L

#define SN_dsa_with_SHA256              "dsa_with_SHA256"
#define NID_dsa_with_SHA256             803
#define OBJ_dsa_with_SHA256             OBJ_dsa_with_sha2,2L

#define OBJ_sigAlgs             OBJ_nistAlgorithms,3L

#define SN_dsa_with_SHA384              "id-dsa-with-sha384"
#define LN_dsa_with_SHA384              "dsa_with_SHA384"
#define NID_dsa_with_SHA384             1106
#define OBJ_dsa_with_SHA384             OBJ_sigAlgs,3L

#define SN_dsa_with_SHA512              "id-dsa-with-sha512"
#define LN_dsa_with_SHA512              "dsa_with_SHA512"
#define NID_dsa_with_SHA512             1107
#define OBJ_dsa_with_SHA512             OBJ_sigAlgs,4L

#define SN_dsa_with_SHA3_224            "id-dsa-with-sha3-224"
#define LN_dsa_with_SHA3_224            "dsa_with_SHA3-224"
#define NID_dsa_with_SHA3_224           1108
#define OBJ_dsa_with_SHA3_224           OBJ_sigAlgs,5L

#define SN_dsa_with_SHA3_256            "id-dsa-with-sha3-256"
#define LN_dsa_with_SHA3_256            "dsa_with_SHA3-256"
#define NID_dsa_with_SHA3_256           1109
#define OBJ_dsa_with_SHA3_256           OBJ_sigAlgs,6L

#define SN_dsa_with_SHA3_384            "id-dsa-with-sha3-384"
#define LN_dsa_with_SHA3_384            "dsa_with_SHA3-384"
#define NID_dsa_with_SHA3_384           1110
#define OBJ_dsa_with_SHA3_384           OBJ_sigAlgs,7L

#define SN_dsa_with_SHA3_512            "id-dsa-with-sha3-512"
#define LN_dsa_with_SHA3_512            "dsa_with_SHA3-512"
#define NID_dsa_with_SHA3_512           1111
#define OBJ_dsa_with_SHA3_512           OBJ_sigAlgs,8L

#define SN_ecdsa_with_SHA3_224          "id-ecdsa-with-sha3-224"
#define LN_ecdsa_with_SHA3_224          "ecdsa_with_SHA3-224"
#define NID_ecdsa_with_SHA3_224         1112
#define OBJ_ecdsa_with_SHA3_224         OBJ_sigAlgs,9L

#define SN_ecdsa_with_SHA3_256          "id-ecdsa-with-sha3-256"
#define LN_ecdsa_with_SHA3_256          "ecdsa_with_SHA3-256"
#define NID_ecdsa_with_SHA3_256         1113
#define OBJ_ecdsa_with_SHA3_256         OBJ_sigAlgs,10L

#define SN_ecdsa_with_SHA3_384          "id-ecdsa-with-sha3-384"
#define LN_ecdsa_with_SHA3_384          "ecdsa_with_SHA3-384"
#define NID_ecdsa_with_SHA3_384         1114
#define OBJ_ecdsa_with_SHA3_384         OBJ_sigAlgs,11L

#define SN_ecdsa_with_SHA3_512          "id-ecdsa-with-sha3-512"
#define LN_ecdsa_with_SHA3_512          "ecdsa_with_SHA3-512"
#define NID_ecdsa_with_SHA3_512         1115
#define OBJ_ecdsa_with_SHA3_512         OBJ_sigAlgs,12L

#define SN_RSA_SHA3_224         "id-rsassa-pkcs1-v1_5-with-sha3-224"
#define LN_RSA_SHA3_224         "RSA-SHA3-224"
#define NID_RSA_SHA3_224                1116
#define OBJ_RSA_SHA3_224                OBJ_sigAlgs,13L

#define SN_RSA_SHA3_256         "id-rsassa-pkcs1-v1_5-with-sha3-256"
#define LN_RSA_SHA3_256         "RSA-SHA3-256"
#define NID_RSA_SHA3_256                1117
#define OBJ_RSA_SHA3_256                OBJ_sigAlgs,14L

#define SN_RSA_SHA3_384         "id-rsassa-pkcs1-v1_5-with-sha3-384"
#define LN_RSA_SHA3_384         "RSA-SHA3-384"
#define NID_RSA_SHA3_384                1118
#define OBJ_RSA_SHA3_384                OBJ_sigAlgs,15L

#define SN_RSA_SHA3_512         "id-rsassa-pkcs1-v1_5-with-sha3-512"
#define LN_RSA_SHA3_512         "RSA-SHA3-512"
#define NID_RSA_SHA3_512                1119
#define OBJ_RSA_SHA3_512                OBJ_sigAlgs,16L

#define SN_ML_DSA_44            "id-ml-dsa-44"
#define LN_ML_DSA_44            "ML-DSA-44"
#define NID_ML_DSA_44           1457
#define OBJ_ML_DSA_44           OBJ_sigAlgs,17L

#define SN_ML_DSA_65            "id-ml-dsa-65"
#define LN_ML_DSA_65            "ML-DSA-65"
#define NID_ML_DSA_65           1458
#define OBJ_ML_DSA_65           OBJ_sigAlgs,18L

#define SN_ML_DSA_87            "id-ml-dsa-87"
#define LN_ML_DSA_87            "ML-DSA-87"
#define NID_ML_DSA_87           1459
#define OBJ_ML_DSA_87           OBJ_sigAlgs,19L

#define SN_SLH_DSA_SHA2_128s            "id-slh-dsa-sha2-128s"
#define LN_SLH_DSA_SHA2_128s            "SLH-DSA-SHA2-128s"
#define NID_SLH_DSA_SHA2_128s           1460
#define OBJ_SLH_DSA_SHA2_128s           OBJ_sigAlgs,20L

#define SN_SLH_DSA_SHA2_128f            "id-slh-dsa-sha2-128f"
#define LN_SLH_DSA_SHA2_128f            "SLH-DSA-SHA2-128f"
#define NID_SLH_DSA_SHA2_128f           1461
#define OBJ_SLH_DSA_SHA2_128f           OBJ_sigAlgs,21L

#define SN_SLH_DSA_SHA2_192s            "id-slh-dsa-sha2-192s"
#define LN_SLH_DSA_SHA2_192s            "SLH-DSA-SHA2-192s"
#define NID_SLH_DSA_SHA2_192s           1462
#define OBJ_SLH_DSA_SHA2_192s           OBJ_sigAlgs,22L

#define SN_SLH_DSA_SHA2_192f            "id-slh-dsa-sha2-192f"
#define LN_SLH_DSA_SHA2_192f            "SLH-DSA-SHA2-192f"
#define NID_SLH_DSA_SHA2_192f           1463
#define OBJ_SLH_DSA_SHA2_192f           OBJ_sigAlgs,23L

#define SN_SLH_DSA_SHA2_256s            "id-slh-dsa-sha2-256s"
#define LN_SLH_DSA_SHA2_256s            "SLH-DSA-SHA2-256s"
#define NID_SLH_DSA_SHA2_256s           1464
#define OBJ_SLH_DSA_SHA2_256s           OBJ_sigAlgs,24L

#define SN_SLH_DSA_SHA2_256f            "id-slh-dsa-sha2-256f"
#define LN_SLH_DSA_SHA2_256f            "SLH-DSA-SHA2-256f"
#define NID_SLH_DSA_SHA2_256f           1465
#define OBJ_SLH_DSA_SHA2_256f           OBJ_sigAlgs,25L

#define SN_SLH_DSA_SHAKE_128s           "id-slh-dsa-shake-128s"
#define LN_SLH_DSA_SHAKE_128s           "SLH-DSA-SHAKE-128s"
#define NID_SLH_DSA_SHAKE_128s          1466
#define OBJ_SLH_DSA_SHAKE_128s          OBJ_sigAlgs,26L

#define SN_SLH_DSA_SHAKE_128f           "id-slh-dsa-shake-128f"
#define LN_SLH_DSA_SHAKE_128f           "SLH-DSA-SHAKE-128f"
#define NID_SLH_DSA_SHAKE_128f          1467
#define OBJ_SLH_DSA_SHAKE_128f          OBJ_sigAlgs,27L

#define SN_SLH_DSA_SHAKE_192s           "id-slh-dsa-shake-192s"
#define LN_SLH_DSA_SHAKE_192s           "SLH-DSA-SHAKE-192s"
#define NID_SLH_DSA_SHAKE_192s          1468
#define OBJ_SLH_DSA_SHAKE_192s          OBJ_sigAlgs,28L

#define SN_SLH_DSA_SHAKE_192f           "id-slh-dsa-shake-192f"
#define LN_SLH_DSA_SHAKE_192f           "SLH-DSA-SHAKE-192f"
#define NID_SLH_DSA_SHAKE_192f          1469
#define OBJ_SLH_DSA_SHAKE_192f          OBJ_sigAlgs,29L

#define SN_SLH_DSA_SHAKE_256s           "id-slh-dsa-shake-256s"
#define LN_SLH_DSA_SHAKE_256s           "SLH-DSA-SHAKE-256s"
#define NID_SLH_DSA_SHAKE_256s          1470
#define OBJ_SLH_DSA_SHAKE_256s          OBJ_sigAlgs,30L

#define SN_SLH_DSA_SHAKE_256f           "id-slh-dsa-shake-256f"
#define LN_SLH_DSA_SHAKE_256f           "SLH-DSA-SHAKE-256f"
#define NID_SLH_DSA_SHAKE_256f          1471
#define OBJ_SLH_DSA_SHAKE_256f          OBJ_sigAlgs,31L

#define SN_HASH_ML_DSA_44_WITH_SHA512           "id-hash-ml-dsa-44-with-sha512"
#define LN_HASH_ML_DSA_44_WITH_SHA512           "HASH-ML-DSA-44-WITH-SHA512"
#define NID_HASH_ML_DSA_44_WITH_SHA512          1472
#define OBJ_HASH_ML_DSA_44_WITH_SHA512          OBJ_sigAlgs,32L

#define SN_HASH_ML_DSA_65_WITH_SHA512           "id-hash-ml-dsa-65-with-sha512"
#define LN_HASH_ML_DSA_65_WITH_SHA512           "HASH-ML-DSA-65-WITH-SHA512"
#define NID_HASH_ML_DSA_65_WITH_SHA512          1473
#define OBJ_HASH_ML_DSA_65_WITH_SHA512          OBJ_sigAlgs,33L

#define SN_HASH_ML_DSA_87_WITH_SHA512           "id-hash-ml-dsa-87-with-sha512"
#define LN_HASH_ML_DSA_87_WITH_SHA512           "HASH-ML-DSA-87-WITH-SHA512"
#define NID_HASH_ML_DSA_87_WITH_SHA512          1474
#define OBJ_HASH_ML_DSA_87_WITH_SHA512          OBJ_sigAlgs,34L

#define SN_SLH_DSA_SHA2_128s_WITH_SHA256                "id-hash-slh-dsa-sha2-128s-with-sha256"
#define LN_SLH_DSA_SHA2_128s_WITH_SHA256                "SLH-DSA-SHA2-128s-WITH-SHA256"
#define NID_SLH_DSA_SHA2_128s_WITH_SHA256               1475
#define OBJ_SLH_DSA_SHA2_128s_WITH_SHA256               OBJ_sigAlgs,35L

#define SN_SLH_DSA_SHA2_128f_WITH_SHA256                "id-hash-slh-dsa-sha2-128f-with-sha256"
#define LN_SLH_DSA_SHA2_128f_WITH_SHA256                "SLH-DSA-SHA2-128f-WITH-SHA256"
#define NID_SLH_DSA_SHA2_128f_WITH_SHA256               1476
#define OBJ_SLH_DSA_SHA2_128f_WITH_SHA256               OBJ_sigAlgs,36L

#define SN_SLH_DSA_SHA2_192s_WITH_SHA512                "id-hash-slh-dsa-sha2-192s-with-sha512"
#define LN_SLH_DSA_SHA2_192s_WITH_SHA512                "SLH-DSA-SHA2-192s-WITH-SHA512"
#define NID_SLH_DSA_SHA2_192s_WITH_SHA512               1477
#define OBJ_SLH_DSA_SHA2_192s_WITH_SHA512               OBJ_sigAlgs,37L

#define SN_SLH_DSA_SHA2_192f_WITH_SHA512                "id-hash-slh-dsa-sha2-192f-with-sha512"
#define LN_SLH_DSA_SHA2_192f_WITH_SHA512                "SLH-DSA-SHA2-192f-WITH-SHA512"
#define NID_SLH_DSA_SHA2_192f_WITH_SHA512               1478
#define OBJ_SLH_DSA_SHA2_192f_WITH_SHA512               OBJ_sigAlgs,38L

#define SN_SLH_DSA_SHA2_256s_WITH_SHA512                "id-hash-slh-dsa-sha2-256s-with-sha512"
#define LN_SLH_DSA_SHA2_256s_WITH_SHA512                "SLH-DSA-SHA2-256s-WITH-SHA512"
#define NID_SLH_DSA_SHA2_256s_WITH_SHA512               1479
#define OBJ_SLH_DSA_SHA2_256s_WITH_SHA512               OBJ_sigAlgs,39L

#define SN_SLH_DSA_SHA2_256f_WITH_SHA512                "id-hash-slh-dsa-sha2-256f-with-sha512"
#define LN_SLH_DSA_SHA2_256f_WITH_SHA512                "SLH-DSA-SHA2-256f-WITH-SHA512"
#define NID_SLH_DSA_SHA2_256f_WITH_SHA512               1480
#define OBJ_SLH_DSA_SHA2_256f_WITH_SHA512               OBJ_sigAlgs,40L

#define SN_SLH_DSA_SHAKE_128s_WITH_SHAKE128             "id-hash-slh-dsa-shake-128s-with-shake128"
#define LN_SLH_DSA_SHAKE_128s_WITH_SHAKE128             "SLH-DSA-SHAKE-128s-WITH-SHAKE128"
#define NID_SLH_DSA_SHAKE_128s_WITH_SHAKE128            1481
#define OBJ_SLH_DSA_SHAKE_128s_WITH_SHAKE128            OBJ_sigAlgs,41L

#define SN_SLH_DSA_SHAKE_128f_WITH_SHAKE128             "id-hash-slh-dsa-shake-128f-with-shake128"
#define LN_SLH_DSA_SHAKE_128f_WITH_SHAKE128             "SLH-DSA-SHAKE-128f-WITH-SHAKE128"
#define NID_SLH_DSA_SHAKE_128f_WITH_SHAKE128            1482
#define OBJ_SLH_DSA_SHAKE_128f_WITH_SHAKE128            OBJ_sigAlgs,42L

#define SN_SLH_DSA_SHAKE_192s_WITH_SHAKE256             "id-hash-slh-dsa-shake-192s-with-shake256"
#define LN_SLH_DSA_SHAKE_192s_WITH_SHAKE256             "SLH-DSA-SHAKE-192s-WITH-SHAKE256"
#define NID_SLH_DSA_SHAKE_192s_WITH_SHAKE256            1483
#define OBJ_SLH_DSA_SHAKE_192s_WITH_SHAKE256            OBJ_sigAlgs,43L

#define SN_SLH_DSA_SHAKE_192f_WITH_SHAKE256             "id-hash-slh-dsa-shake-192f-with-shake256"
#define LN_SLH_DSA_SHAKE_192f_WITH_SHAKE256             "SLH-DSA-SHAKE-192f-WITH-SHAKE256"
#define NID_SLH_DSA_SHAKE_192f_WITH_SHAKE256            1484
#define OBJ_SLH_DSA_SHAKE_192f_WITH_SHAKE256            OBJ_sigAlgs,44L

#define SN_SLH_DSA_SHAKE_256s_WITH_SHAKE256             "id-hash-slh-dsa-shake-256s-with-shake256"
#define LN_SLH_DSA_SHAKE_256s_WITH_SHAKE256             "SLH-DSA-SHAKE-256s-WITH-SHAKE256"
#define NID_SLH_DSA_SHAKE_256s_WITH_SHAKE256            1485
#define OBJ_SLH_DSA_SHAKE_256s_WITH_SHAKE256            OBJ_sigAlgs,45L

#define SN_SLH_DSA_SHAKE_256f_WITH_SHAKE256             "id-hash-slh-dsa-shake-256f-with-shake256"
#define LN_SLH_DSA_SHAKE_256f_WITH_SHAKE256             "SLH-DSA-SHAKE-256f-WITH-SHAKE256"
#define NID_SLH_DSA_SHAKE_256f_WITH_SHAKE256            1486
#define OBJ_SLH_DSA_SHAKE_256f_WITH_SHAKE256            OBJ_sigAlgs,46L

#define SN_hold_instruction_code                "holdInstructionCode"
#define LN_hold_instruction_code                "Hold Instruction Code"
#define NID_hold_instruction_code               430
#define OBJ_hold_instruction_code               OBJ_id_ce,23L

#define OBJ_holdInstruction             OBJ_X9_57,2L

#define SN_hold_instruction_none                "holdInstructionNone"
#define LN_hold_instruction_none                "Hold Instruction None"
#define NID_hold_instruction_none               431
#define OBJ_hold_instruction_none               OBJ_holdInstruction,1L

#define SN_hold_instruction_call_issuer         "holdInstructionCallIssuer"
#define LN_hold_instruction_call_issuer         "Hold Instruction Call Issuer"
#define NID_hold_instruction_call_issuer                432
#define OBJ_hold_instruction_call_issuer                OBJ_holdInstruction,2L

#define SN_hold_instruction_reject              "holdInstructionReject"
#define LN_hold_instruction_reject              "Hold Instruction Reject"
#define NID_hold_instruction_reject             433
#define OBJ_hold_instruction_reject             OBJ_holdInstruction,3L

#define SN_itu_t_identified_organization                "itu-t-identified-organization"
#define NID_itu_t_identified_organization               1264
#define OBJ_itu_t_identified_organization               OBJ_itu_t,4L

#define SN_etsi         "etsi"
#define NID_etsi                1265
#define OBJ_etsi                OBJ_itu_t_identified_organization,0L

#define SN_electronic_signature_standard                "electronic-signature-standard"
#define NID_electronic_signature_standard               1266
#define OBJ_electronic_signature_standard               OBJ_etsi,1733L

#define SN_ess_attributes               "ess-attributes"
#define NID_ess_attributes              1267
#define OBJ_ess_attributes              OBJ_electronic_signature_standard,2L

#define SN_id_aa_ets_mimeType           "id-aa-ets-mimeType"
#define NID_id_aa_ets_mimeType          1268
#define OBJ_id_aa_ets_mimeType          OBJ_ess_attributes,1L

#define SN_id_aa_ets_longTermValidation         "id-aa-ets-longTermValidation"
#define NID_id_aa_ets_longTermValidation                1269
#define OBJ_id_aa_ets_longTermValidation                OBJ_ess_attributes,2L

#define SN_id_aa_ets_SignaturePolicyDocument            "id-aa-ets-SignaturePolicyDocument"
#define NID_id_aa_ets_SignaturePolicyDocument           1270
#define OBJ_id_aa_ets_SignaturePolicyDocument           OBJ_ess_attributes,3L

#define SN_id_aa_ets_archiveTimestampV3         "id-aa-ets-archiveTimestampV3"
#define NID_id_aa_ets_archiveTimestampV3                1271
#define OBJ_id_aa_ets_archiveTimestampV3                OBJ_ess_attributes,4L

#define SN_id_aa_ATSHashIndex           "id-aa-ATSHashIndex"
#define NID_id_aa_ATSHashIndex          1272
#define OBJ_id_aa_ATSHashIndex          OBJ_ess_attributes,5L

#define SN_cades                "cades"
#define NID_cades               1273
#define OBJ_cades               OBJ_etsi,19122L

#define SN_cades_attributes             "cades-attributes"
#define NID_cades_attributes            1274
#define OBJ_cades_attributes            OBJ_cades,1L

#define SN_id_aa_ets_signerAttrV2               "id-aa-ets-signerAttrV2"
#define NID_id_aa_ets_signerAttrV2              1275
#define OBJ_id_aa_ets_signerAttrV2              OBJ_cades_attributes,1L

#define SN_id_aa_ets_sigPolicyStore             "id-aa-ets-sigPolicyStore"
#define NID_id_aa_ets_sigPolicyStore            1276
#define OBJ_id_aa_ets_sigPolicyStore            OBJ_cades_attributes,3L

#define SN_id_aa_ATSHashIndex_v2                "id-aa-ATSHashIndex-v2"
#define NID_id_aa_ATSHashIndex_v2               1277
#define OBJ_id_aa_ATSHashIndex_v2               OBJ_cades_attributes,4L

#define SN_id_aa_ATSHashIndex_v3                "id-aa-ATSHashIndex-v3"
#define NID_id_aa_ATSHashIndex_v3               1278
#define OBJ_id_aa_ATSHashIndex_v3               OBJ_cades_attributes,5L

#define SN_signedAssertion              "signedAssertion"
#define NID_signedAssertion             1279
#define OBJ_signedAssertion             OBJ_cades_attributes,6L

#define SN_data         "data"
#define NID_data                434
#define OBJ_data                OBJ_itu_t,9L

#define SN_pss          "pss"
#define NID_pss         435
#define OBJ_pss         OBJ_data,2342L

#define SN_ucl          "ucl"
#define NID_ucl         436
#define OBJ_ucl         OBJ_pss,19200300L

#define SN_pilot                "pilot"
#define NID_pilot               437
#define OBJ_pilot               OBJ_ucl,100L

#define LN_pilotAttributeType           "pilotAttributeType"
#define NID_pilotAttributeType          438
#define OBJ_pilotAttributeType          OBJ_pilot,1L

#define LN_pilotAttributeSyntax         "pilotAttributeSyntax"
#define NID_pilotAttributeSyntax                439
#define OBJ_pilotAttributeSyntax                OBJ_pilot,3L

#define LN_pilotObjectClass             "pilotObjectClass"
#define NID_pilotObjectClass            440
#define OBJ_pilotObjectClass            OBJ_pilot,4L

#define LN_pilotGroups          "pilotGroups"
#define NID_pilotGroups         441
#define OBJ_pilotGroups         OBJ_pilot,10L

#define LN_iA5StringSyntax              "iA5StringSyntax"
#define NID_iA5StringSyntax             442
#define OBJ_iA5StringSyntax             OBJ_pilotAttributeSyntax,4L

#define LN_caseIgnoreIA5StringSyntax            "caseIgnoreIA5StringSyntax"
#define NID_caseIgnoreIA5StringSyntax           443
#define OBJ_caseIgnoreIA5StringSyntax           OBJ_pilotAttributeSyntax,5L

#define LN_pilotObject          "pilotObject"
#define NID_pilotObject         444
#define OBJ_pilotObject         OBJ_pilotObjectClass,3L

#define LN_pilotPerson          "pilotPerson"
#define NID_pilotPerson         445
#define OBJ_pilotPerson         OBJ_pilotObjectClass,4L

#define SN_account              "account"
#define NID_account             446
#define OBJ_account             OBJ_pilotObjectClass,5L

#define SN_document             "document"
#define NID_document            447
#define OBJ_document            OBJ_pilotObjectClass,6L

#define SN_room         "room"
#define NID_room                448
#define OBJ_room                OBJ_pilotObjectClass,7L

#define LN_documentSeries               "documentSeries"
#define NID_documentSeries              449
#define OBJ_documentSeries              OBJ_pilotObjectClass,9L

#define SN_Domain               "domain"
#define LN_Domain               "Domain"
#define NID_Domain              392
#define OBJ_Domain              OBJ_pilotObjectClass,13L

#define LN_rFC822localPart              "rFC822localPart"
#define NID_rFC822localPart             450
#define OBJ_rFC822localPart             OBJ_pilotObjectClass,14L

#define LN_dNSDomain            "dNSDomain"
#define NID_dNSDomain           451
#define OBJ_dNSDomain           OBJ_pilotObjectClass,15L

#define LN_domainRelatedObject          "domainRelatedObject"
#define NID_domainRelatedObject         452
#define OBJ_domainRelatedObject         OBJ_pilotObjectClass,17L

#define LN_friendlyCountry              "friendlyCountry"
#define NID_friendlyCountry             453
#define OBJ_friendlyCountry             OBJ_pilotObjectClass,18L

#define LN_simpleSecurityObject         "simpleSecurityObject"
#define NID_simpleSecurityObject                454
#define OBJ_simpleSecurityObject                OBJ_pilotObjectClass,19L

#define LN_pilotOrganization            "pilotOrganization"
#define NID_pilotOrganization           455
#define OBJ_pilotOrganization           OBJ_pilotObjectClass,20L

#define LN_pilotDSA             "pilotDSA"
#define NID_pilotDSA            456
#define OBJ_pilotDSA            OBJ_pilotObjectClass,21L

#define LN_qualityLabelledData          "qualityLabelledData"
#define NID_qualityLabelledData         457
#define OBJ_qualityLabelledData         OBJ_pilotObjectClass,22L

#define SN_userId               "UID"
#define LN_userId               "userId"
#define NID_userId              458
#define OBJ_userId              OBJ_pilotAttributeType,1L

#define LN_textEncodedORAddress         "textEncodedORAddress"
#define NID_textEncodedORAddress                459
#define OBJ_textEncodedORAddress                OBJ_pilotAttributeType,2L

#define SN_rfc822Mailbox                "mail"
#define LN_rfc822Mailbox                "rfc822Mailbox"
#define NID_rfc822Mailbox               460
#define OBJ_rfc822Mailbox               OBJ_pilotAttributeType,3L

#define SN_info         "info"
#define NID_info                461
#define OBJ_info                OBJ_pilotAttributeType,4L

#define LN_favouriteDrink               "favouriteDrink"
#define NID_favouriteDrink              462
#define OBJ_favouriteDrink              OBJ_pilotAttributeType,5L

#define LN_roomNumber           "roomNumber"
#define NID_roomNumber          463
#define OBJ_roomNumber          OBJ_pilotAttributeType,6L

#define SN_photo                "photo"
#define NID_photo               464
#define OBJ_photo               OBJ_pilotAttributeType,7L

#define LN_userClass            "userClass"
#define NID_userClass           465
#define OBJ_userClass           OBJ_pilotAttributeType,8L

#define SN_host         "host"
#define NID_host                466
#define OBJ_host                OBJ_pilotAttributeType,9L

#define SN_manager              "manager"
#define NID_manager             467
#define OBJ_manager             OBJ_pilotAttributeType,10L

#define LN_documentIdentifier           "documentIdentifier"
#define NID_documentIdentifier          468
#define OBJ_documentIdentifier          OBJ_pilotAttributeType,11L

#define LN_documentTitle                "documentTitle"
#define NID_documentTitle               469
#define OBJ_documentTitle               OBJ_pilotAttributeType,12L

#define LN_documentVersion              "documentVersion"
#define NID_documentVersion             470
#define OBJ_documentVersion             OBJ_pilotAttributeType,13L

#define LN_documentAuthor               "documentAuthor"
#define NID_documentAuthor              471
#define OBJ_documentAuthor              OBJ_pilotAttributeType,14L

#define LN_documentLocation             "documentLocation"
#define NID_documentLocation            472
#define OBJ_documentLocation            OBJ_pilotAttributeType,15L

#define LN_homeTelephoneNumber          "homeTelephoneNumber"
#define NID_homeTelephoneNumber         473
#define OBJ_homeTelephoneNumber         OBJ_pilotAttributeType,20L

#define SN_secretary            "secretary"
#define NID_secretary           474
#define OBJ_secretary           OBJ_pilotAttributeType,21L

#define LN_otherMailbox         "otherMailbox"
#define NID_otherMailbox                475
#define OBJ_otherMailbox                OBJ_pilotAttributeType,22L

#define LN_lastModifiedTime             "lastModifiedTime"
#define NID_lastModifiedTime            476
#define OBJ_lastModifiedTime            OBJ_pilotAttributeType,23L

#define LN_lastModifiedBy               "lastModifiedBy"
#define NID_lastModifiedBy              477
#define OBJ_lastModifiedBy              OBJ_pilotAttributeType,24L

#define SN_domainComponent              "DC"
#define LN_domainComponent              "domainComponent"
#define NID_domainComponent             391
#define OBJ_domainComponent             OBJ_pilotAttributeType,25L

#define LN_aRecord              "aRecord"
#define NID_aRecord             478
#define OBJ_aRecord             OBJ_pilotAttributeType,26L

#define LN_pilotAttributeType27         "pilotAttributeType27"
#define NID_pilotAttributeType27                479
#define OBJ_pilotAttributeType27                OBJ_pilotAttributeType,27L

#define LN_mXRecord             "mXRecord"
#define NID_mXRecord            480
#define OBJ_mXRecord            OBJ_pilotAttributeType,28L

#define LN_nSRecord             "nSRecord"
#define NID_nSRecord            481
#define OBJ_nSRecord            OBJ_pilotAttributeType,29L

#define LN_sOARecord            "sOARecord"
#define NID_sOARecord           482
#define OBJ_sOARecord           OBJ_pilotAttributeType,30L

#define LN_cNAMERecord          "cNAMERecord"
#define NID_cNAMERecord         483
#define OBJ_cNAMERecord         OBJ_pilotAttributeType,31L

#define LN_associatedDomain             "associatedDomain"
#define NID_associatedDomain            484
#define OBJ_associatedDomain            OBJ_pilotAttributeType,37L

#define LN_associatedName               "associatedName"
#define NID_associatedName              485
#define OBJ_associatedName              OBJ_pilotAttributeType,38L

#define LN_homePostalAddress            "homePostalAddress"
#define NID_homePostalAddress           486
#define OBJ_homePostalAddress           OBJ_pilotAttributeType,39L

#define LN_personalTitle                "personalTitle"
#define NID_personalTitle               487
#define OBJ_personalTitle               OBJ_pilotAttributeType,40L

#define LN_mobileTelephoneNumber                "mobileTelephoneNumber"
#define NID_mobileTelephoneNumber               488
#define OBJ_mobileTelephoneNumber               OBJ_pilotAttributeType,41L

#define LN_pagerTelephoneNumber         "pagerTelephoneNumber"
#define NID_pagerTelephoneNumber                489
#define OBJ_pagerTelephoneNumber                OBJ_pilotAttributeType,42L

#define LN_friendlyCountryName          "friendlyCountryName"
#define NID_friendlyCountryName         490
#define OBJ_friendlyCountryName         OBJ_pilotAttributeType,43L

#define SN_uniqueIdentifier             "uid"
#define LN_uniqueIdentifier             "uniqueIdentifier"
#define NID_uniqueIdentifier            102
#define OBJ_uniqueIdentifier            OBJ_pilotAttributeType,44L

#define LN_organizationalStatus         "organizationalStatus"
#define NID_organizationalStatus                491
#define OBJ_organizationalStatus                OBJ_pilotAttributeType,45L

#define LN_janetMailbox         "janetMailbox"
#define NID_janetMailbox                492
#define OBJ_janetMailbox                OBJ_pilotAttributeType,46L

#define LN_mailPreferenceOption         "mailPreferenceOption"
#define NID_mailPreferenceOption                493
#define OBJ_mailPreferenceOption                OBJ_pilotAttributeType,47L

#define LN_buildingName         "buildingName"
#define NID_buildingName                494
#define OBJ_buildingName                OBJ_pilotAttributeType,48L

#define LN_dSAQuality           "dSAQuality"
#define NID_dSAQuality          495
#define OBJ_dSAQuality          OBJ_pilotAttributeType,49L

#define LN_singleLevelQuality           "singleLevelQuality"
#define NID_singleLevelQuality          496
#define OBJ_singleLevelQuality          OBJ_pilotAttributeType,50L

#define LN_subtreeMinimumQuality                "subtreeMinimumQuality"
#define NID_subtreeMinimumQuality               497
#define OBJ_subtreeMinimumQuality               OBJ_pilotAttributeType,51L

#define LN_subtreeMaximumQuality                "subtreeMaximumQuality"
#define NID_subtreeMaximumQuality               498
#define OBJ_subtreeMaximumQuality               OBJ_pilotAttributeType,52L

#define LN_personalSignature            "personalSignature"
#define NID_personalSignature           499
#define OBJ_personalSignature           OBJ_pilotAttributeType,53L

#define LN_dITRedirect          "dITRedirect"
#define NID_dITRedirect         500
#define OBJ_dITRedirect         OBJ_pilotAttributeType,54L

#define SN_audio                "audio"
#define NID_audio               501
#define OBJ_audio               OBJ_pilotAttributeType,55L

#define LN_documentPublisher            "documentPublisher"
#define NID_documentPublisher           502
#define OBJ_documentPublisher           OBJ_pilotAttributeType,56L

#define SN_id_set               "id-set"
#define LN_id_set               "Secure Electronic Transactions"
#define NID_id_set              512
#define OBJ_id_set              OBJ_international_organizations,42L

#define SN_set_ctype            "set-ctype"
#define LN_set_ctype            "content types"
#define NID_set_ctype           513
#define OBJ_set_ctype           OBJ_id_set,0L

#define SN_set_msgExt           "set-msgExt"
#define LN_set_msgExt           "message extensions"
#define NID_set_msgExt          514
#define OBJ_set_msgExt          OBJ_id_set,1L

#define SN_set_attr             "set-attr"
#define NID_set_attr            515
#define OBJ_set_attr            OBJ_id_set,3L

#define SN_set_policy           "set-policy"
#define NID_set_policy          516
#define OBJ_set_policy          OBJ_id_set,5L

#define SN_set_certExt          "set-certExt"
#define LN_set_certExt          "certificate extensions"
#define NID_set_certExt         517
#define OBJ_set_certExt         OBJ_id_set,7L

#define SN_set_brand            "set-brand"
#define NID_set_brand           518
#define OBJ_set_brand           OBJ_id_set,8L

#define SN_setct_PANData                "setct-PANData"
#define NID_setct_PANData               519
#define OBJ_setct_PANData               OBJ_set_ctype,0L

#define SN_setct_PANToken               "setct-PANToken"
#define NID_setct_PANToken              520
#define OBJ_setct_PANToken              OBJ_set_ctype,1L

#define SN_setct_PANOnly                "setct-PANOnly"
#define NID_setct_PANOnly               521
#define OBJ_setct_PANOnly               OBJ_set_ctype,2L

#define SN_setct_OIData         "setct-OIData"
#define NID_setct_OIData                522
#define OBJ_setct_OIData                OBJ_set_ctype,3L

#define SN_setct_PI             "setct-PI"
#define NID_setct_PI            523
#define OBJ_setct_PI            OBJ_set_ctype,4L

#define SN_setct_PIData         "setct-PIData"
#define NID_setct_PIData                524
#define OBJ_setct_PIData                OBJ_set_ctype,5L

#define SN_setct_PIDataUnsigned         "setct-PIDataUnsigned"
#define NID_setct_PIDataUnsigned                525
#define OBJ_setct_PIDataUnsigned                OBJ_set_ctype,6L

#define SN_setct_HODInput               "setct-HODInput"
#define NID_setct_HODInput              526
#define OBJ_setct_HODInput              OBJ_set_ctype,7L

#define SN_setct_AuthResBaggage         "setct-AuthResBaggage"
#define NID_setct_AuthResBaggage                527
#define OBJ_setct_AuthResBaggage                OBJ_set_ctype,8L

#define SN_setct_AuthRevReqBaggage              "setct-AuthRevReqBaggage"
#define NID_setct_AuthRevReqBaggage             528
#define OBJ_setct_AuthRevReqBaggage             OBJ_set_ctype,9L

#define SN_setct_AuthRevResBaggage              "setct-AuthRevResBaggage"
#define NID_setct_AuthRevResBaggage             529
#define OBJ_setct_AuthRevResBaggage             OBJ_set_ctype,10L

#define SN_setct_CapTokenSeq            "setct-CapTokenSeq"
#define NID_setct_CapTokenSeq           530
#define OBJ_setct_CapTokenSeq           OBJ_set_ctype,11L

#define SN_setct_PInitResData           "setct-PInitResData"
#define NID_setct_PInitResData          531
#define OBJ_setct_PInitResData          OBJ_set_ctype,12L

#define SN_setct_PI_TBS         "setct-PI-TBS"
#define NID_setct_PI_TBS                532
#define OBJ_setct_PI_TBS                OBJ_set_ctype,13L

#define SN_setct_PResData               "setct-PResData"
#define NID_setct_PResData              533
#define OBJ_setct_PResData              OBJ_set_ctype,14L

#define SN_setct_AuthReqTBS             "setct-AuthReqTBS"
#define NID_setct_AuthReqTBS            534
#define OBJ_setct_AuthReqTBS            OBJ_set_ctype,16L

#define SN_setct_AuthResTBS             "setct-AuthResTBS"
#define NID_setct_AuthResTBS            535
#define OBJ_setct_AuthResTBS            OBJ_set_ctype,17L

#define SN_setct_AuthResTBSX            "setct-AuthResTBSX"
#define NID_setct_AuthResTBSX           536
#define OBJ_setct_AuthResTBSX           OBJ_set_ctype,18L

#define SN_setct_AuthTokenTBS           "setct-AuthTokenTBS"
#define NID_setct_AuthTokenTBS          537
#define OBJ_setct_AuthTokenTBS          OBJ_set_ctype,19L

#define SN_setct_CapTokenData           "setct-CapTokenData"
#define NID_setct_CapTokenData          538
#define OBJ_setct_CapTokenData          OBJ_set_ctype,20L

#define SN_setct_CapTokenTBS            "setct-CapTokenTBS"
#define NID_setct_CapTokenTBS           539
#define OBJ_setct_CapTokenTBS           OBJ_set_ctype,21L

#define SN_setct_AcqCardCodeMsg         "setct-AcqCardCodeMsg"
#define NID_setct_AcqCardCodeMsg                540
#define OBJ_setct_AcqCardCodeMsg                OBJ_set_ctype,22L

#define SN_setct_AuthRevReqTBS          "setct-AuthRevReqTBS"
#define NID_setct_AuthRevReqTBS         541
#define OBJ_setct_AuthRevReqTBS         OBJ_set_ctype,23L

#define SN_setct_AuthRevResData         "setct-AuthRevResData"
#define NID_setct_AuthRevResData                542
#define OBJ_setct_AuthRevResData                OBJ_set_ctype,24L

#define SN_setct_AuthRevResTBS          "setct-AuthRevResTBS"
#define NID_setct_AuthRevResTBS         543
#define OBJ_setct_AuthRevResTBS         OBJ_set_ctype,25L

#define SN_setct_CapReqTBS              "setct-CapReqTBS"
#define NID_setct_CapReqTBS             544
#define OBJ_setct_CapReqTBS             OBJ_set_ctype,26L

#define SN_setct_CapReqTBSX             "setct-CapReqTBSX"
#define NID_setct_CapReqTBSX            545
#define OBJ_setct_CapReqTBSX            OBJ_set_ctype,27L

#define SN_setct_CapResData             "setct-CapResData"
#define NID_setct_CapResData            546
#define OBJ_setct_CapResData            OBJ_set_ctype,28L

#define SN_setct_CapRevReqTBS           "setct-CapRevReqTBS"
#define NID_setct_CapRevReqTBS          547
#define OBJ_setct_CapRevReqTBS          OBJ_set_ctype,29L

#define SN_setct_CapRevReqTBSX          "setct-CapRevReqTBSX"
#define NID_setct_CapRevReqTBSX         548
#define OBJ_setct_CapRevReqTBSX         OBJ_set_ctype,30L

#define SN_setct_CapRevResData          "setct-CapRevResData"
#define NID_setct_CapRevResData         549
#define OBJ_setct_CapRevResData         OBJ_set_ctype,31L

#define SN_setct_CredReqTBS             "setct-CredReqTBS"
#define NID_setct_CredReqTBS            550
#define OBJ_setct_CredReqTBS            OBJ_set_ctype,32L

#define SN_setct_CredReqTBSX            "setct-CredReqTBSX"
#define NID_setct_CredReqTBSX           551
#define OBJ_setct_CredReqTBSX           OBJ_set_ctype,33L

#define SN_setct_CredResData            "setct-CredResData"
#define NID_setct_CredResData           552
#define OBJ_setct_CredResData           OBJ_set_ctype,34L

#define SN_setct_CredRevReqTBS          "setct-CredRevReqTBS"
#define NID_setct_CredRevReqTBS         553
#define OBJ_setct_CredRevReqTBS         OBJ_set_ctype,35L

#define SN_setct_CredRevReqTBSX         "setct-CredRevReqTBSX"
#define NID_setct_CredRevReqTBSX                554
#define OBJ_setct_CredRevReqTBSX                OBJ_set_ctype,36L

#define SN_setct_CredRevResData         "setct-CredRevResData"
#define NID_setct_CredRevResData                555
#define OBJ_setct_CredRevResData                OBJ_set_ctype,37L

#define SN_setct_PCertReqData           "setct-PCertReqData"
#define NID_setct_PCertReqData          556
#define OBJ_setct_PCertReqData          OBJ_set_ctype,38L

#define SN_setct_PCertResTBS            "setct-PCertResTBS"
#define NID_setct_PCertResTBS           557
#define OBJ_setct_PCertResTBS           OBJ_set_ctype,39L

#define SN_setct_BatchAdminReqData              "setct-BatchAdminReqData"
#define NID_setct_BatchAdminReqData             558
#define OBJ_setct_BatchAdminReqData             OBJ_set_ctype,40L

#define SN_setct_BatchAdminResData              "setct-BatchAdminResData"
#define NID_setct_BatchAdminResData             559
#define OBJ_setct_BatchAdminResData             OBJ_set_ctype,41L

#define SN_setct_CardCInitResTBS                "setct-CardCInitResTBS"
#define NID_setct_CardCInitResTBS               560
#define OBJ_setct_CardCInitResTBS               OBJ_set_ctype,42L

#define SN_setct_MeAqCInitResTBS                "setct-MeAqCInitResTBS"
#define NID_setct_MeAqCInitResTBS               561
#define OBJ_setct_MeAqCInitResTBS               OBJ_set_ctype,43L

#define SN_setct_RegFormResTBS          "setct-RegFormResTBS"
#define NID_setct_RegFormResTBS         562
#define OBJ_setct_RegFormResTBS         OBJ_set_ctype,44L

#define SN_setct_CertReqData            "setct-CertReqData"
#define NID_setct_CertReqData           563
#define OBJ_setct_CertReqData           OBJ_set_ctype,45L

#define SN_setct_CertReqTBS             "setct-CertReqTBS"
#define NID_setct_CertReqTBS            564
#define OBJ_setct_CertReqTBS            OBJ_set_ctype,46L

#define SN_setct_CertResData            "setct-CertResData"
#define NID_setct_CertResData           565
#define OBJ_setct_CertResData           OBJ_set_ctype,47L

#define SN_setct_CertInqReqTBS          "setct-CertInqReqTBS"
#define NID_setct_CertInqReqTBS         566
#define OBJ_setct_CertInqReqTBS         OBJ_set_ctype,48L

#define SN_setct_ErrorTBS               "setct-ErrorTBS"
#define NID_setct_ErrorTBS              567
#define OBJ_setct_ErrorTBS              OBJ_set_ctype,49L

#define SN_setct_PIDualSignedTBE                "setct-PIDualSignedTBE"
#define NID_setct_PIDualSignedTBE               568
#define OBJ_setct_PIDualSignedTBE               OBJ_set_ctype,50L

#define SN_setct_PIUnsignedTBE          "setct-PIUnsignedTBE"
#define NID_setct_PIUnsignedTBE         569
#define OBJ_setct_PIUnsignedTBE         OBJ_set_ctype,51L

#define SN_setct_AuthReqTBE             "setct-AuthReqTBE"
#define NID_setct_AuthReqTBE            570
#define OBJ_setct_AuthReqTBE            OBJ_set_ctype,52L

#define SN_setct_AuthResTBE             "setct-AuthResTBE"
#define NID_setct_AuthResTBE            571
#define OBJ_setct_AuthResTBE            OBJ_set_ctype,53L

#define SN_setct_AuthResTBEX            "setct-AuthResTBEX"
#define NID_setct_AuthResTBEX           572
#define OBJ_setct_AuthResTBEX           OBJ_set_ctype,54L

#define SN_setct_AuthTokenTBE           "setct-AuthTokenTBE"
#define NID_setct_AuthTokenTBE          573
#define OBJ_setct_AuthTokenTBE          OBJ_set_ctype,55L

#define SN_setct_CapTokenTBE            "setct-CapTokenTBE"
#define NID_setct_CapTokenTBE           574
#define OBJ_setct_CapTokenTBE           OBJ_set_ctype,56L

#define SN_setct_CapTokenTBEX           "setct-CapTokenTBEX"
#define NID_setct_CapTokenTBEX          575
#define OBJ_setct_CapTokenTBEX          OBJ_set_ctype,57L

#define SN_setct_AcqCardCodeMsgTBE              "setct-AcqCardCodeMsgTBE"
#define NID_setct_AcqCardCodeMsgTBE             576
#define OBJ_setct_AcqCardCodeMsgTBE             OBJ_set_ctype,58L

#define SN_setct_AuthRevReqTBE          "setct-AuthRevReqTBE"
#define NID_setct_AuthRevReqTBE         577
#define OBJ_setct_AuthRevReqTBE         OBJ_set_ctype,59L

#define SN_setct_AuthRevResTBE          "setct-AuthRevResTBE"
#define NID_setct_AuthRevResTBE         578
#define OBJ_setct_AuthRevResTBE         OBJ_set_ctype,60L

#define SN_setct_AuthRevResTBEB         "setct-AuthRevResTBEB"
#define NID_setct_AuthRevResTBEB                579
#define OBJ_setct_AuthRevResTBEB                OBJ_set_ctype,61L

#define SN_setct_CapReqTBE              "setct-CapReqTBE"
#define NID_setct_CapReqTBE             580
#define OBJ_setct_CapReqTBE             OBJ_set_ctype,62L

#define SN_setct_CapReqTBEX             "setct-CapReqTBEX"
#define NID_setct_CapReqTBEX            581
#define OBJ_setct_CapReqTBEX            OBJ_set_ctype,63L

#define SN_setct_CapResTBE              "setct-CapResTBE"
#define NID_setct_CapResTBE             582
#define OBJ_setct_CapResTBE             OBJ_set_ctype,64L

#define SN_setct_CapRevReqTBE           "setct-CapRevReqTBE"
#define NID_setct_CapRevReqTBE          583
#define OBJ_setct_CapRevReqTBE          OBJ_set_ctype,65L

#define SN_setct_CapRevReqTBEX          "setct-CapRevReqTBEX"
#define NID_setct_CapRevReqTBEX         584
#define OBJ_setct_CapRevReqTBEX         OBJ_set_ctype,66L

#define SN_setct_CapRevResTBE           "setct-CapRevResTBE"
#define NID_setct_CapRevResTBE          585
#define OBJ_setct_CapRevResTBE          OBJ_set_ctype,67L

#define SN_setct_CredReqTBE             "setct-CredReqTBE"
#define NID_setct_CredReqTBE            586
#define OBJ_setct_CredReqTBE            OBJ_set_ctype,68L

#define SN_setct_CredReqTBEX            "setct-CredReqTBEX"
#define NID_setct_CredReqTBEX           587
#define OBJ_setct_CredReqTBEX           OBJ_set_ctype,69L

#define SN_setct_CredResTBE             "setct-CredResTBE"
#define NID_setct_CredResTBE            588
#define OBJ_setct_CredResTBE            OBJ_set_ctype,70L

#define SN_setct_CredRevReqTBE          "setct-CredRevReqTBE"
#define NID_setct_CredRevReqTBE         589
#define OBJ_setct_CredRevReqTBE         OBJ_set_ctype,71L

#define SN_setct_CredRevReqTBEX         "setct-CredRevReqTBEX"
#define NID_setct_CredRevReqTBEX                590
#define OBJ_setct_CredRevReqTBEX                OBJ_set_ctype,72L

#define SN_setct_CredRevResTBE          "setct-CredRevResTBE"
#define NID_setct_CredRevResTBE         591
#define OBJ_setct_CredRevResTBE         OBJ_set_ctype,73L

#define SN_setct_BatchAdminReqTBE               "setct-BatchAdminReqTBE"
#define NID_setct_BatchAdminReqTBE              592
#define OBJ_setct_BatchAdminReqTBE              OBJ_set_ctype,74L

#define SN_setct_BatchAdminResTBE               "setct-BatchAdminResTBE"
#define NID_setct_BatchAdminResTBE              593
#define OBJ_setct_BatchAdminResTBE              OBJ_set_ctype,75L

#define SN_setct_RegFormReqTBE          "setct-RegFormReqTBE"
#define NID_setct_RegFormReqTBE         594
#define OBJ_setct_RegFormReqTBE         OBJ_set_ctype,76L

#define SN_setct_CertReqTBE             "setct-CertReqTBE"
#define NID_setct_CertReqTBE            595
#define OBJ_setct_CertReqTBE            OBJ_set_ctype,77L

#define SN_setct_CertReqTBEX            "setct-CertReqTBEX"
#define NID_setct_CertReqTBEX           596
#define OBJ_setct_CertReqTBEX           OBJ_set_ctype,78L

#define SN_setct_CertResTBE             "setct-CertResTBE"
#define NID_setct_CertResTBE            597
#define OBJ_setct_CertResTBE            OBJ_set_ctype,79L

#define SN_setct_CRLNotificationTBS             "setct-CRLNotificationTBS"
#define NID_setct_CRLNotificationTBS            598
#define OBJ_setct_CRLNotificationTBS            OBJ_set_ctype,80L

#define SN_setct_CRLNotificationResTBS          "setct-CRLNotificationResTBS"
#define NID_setct_CRLNotificationResTBS         599
#define OBJ_setct_CRLNotificationResTBS         OBJ_set_ctype,81L

#define SN_setct_BCIDistributionTBS             "setct-BCIDistributionTBS"
#define NID_setct_BCIDistributionTBS            600
#define OBJ_setct_BCIDistributionTBS            OBJ_set_ctype,82L

#define SN_setext_genCrypt              "setext-genCrypt"
#define LN_setext_genCrypt              "generic cryptogram"
#define NID_setext_genCrypt             601
#define OBJ_setext_genCrypt             OBJ_set_msgExt,1L

#define SN_setext_miAuth                "setext-miAuth"
#define LN_setext_miAuth                "merchant initiated auth"
#define NID_setext_miAuth               602
#define OBJ_setext_miAuth               OBJ_set_msgExt,3L

#define SN_setext_pinSecure             "setext-pinSecure"
#define NID_setext_pinSecure            603
#define OBJ_setext_pinSecure            OBJ_set_msgExt,4L

#define SN_setext_pinAny                "setext-pinAny"
#define NID_setext_pinAny               604
#define OBJ_setext_pinAny               OBJ_set_msgExt,5L

#define SN_setext_track2                "setext-track2"
#define NID_setext_track2               605
#define OBJ_setext_track2               OBJ_set_msgExt,7L

#define SN_setext_cv            "setext-cv"
#define LN_setext_cv            "additional verification"
#define NID_setext_cv           606
#define OBJ_setext_cv           OBJ_set_msgExt,8L

#define SN_set_policy_root              "set-policy-root"
#define NID_set_policy_root             607
#define OBJ_set_policy_root             OBJ_set_policy,0L

#define SN_setCext_hashedRoot           "setCext-hashedRoot"
#define NID_setCext_hashedRoot          608
#define OBJ_setCext_hashedRoot          OBJ_set_certExt,0L

#define SN_setCext_certType             "setCext-certType"
#define NID_setCext_certType            609
#define OBJ_setCext_certType            OBJ_set_certExt,1L

#define SN_setCext_merchData            "setCext-merchData"
#define NID_setCext_merchData           610
#define OBJ_setCext_merchData           OBJ_set_certExt,2L

#define SN_setCext_cCertRequired                "setCext-cCertRequired"
#define NID_setCext_cCertRequired               611
#define OBJ_setCext_cCertRequired               OBJ_set_certExt,3L

#define SN_setCext_tunneling            "setCext-tunneling"
#define NID_setCext_tunneling           612
#define OBJ_setCext_tunneling           OBJ_set_certExt,4L

#define SN_setCext_setExt               "setCext-setExt"
#define NID_setCext_setExt              613
#define OBJ_setCext_setExt              OBJ_set_certExt,5L

#define SN_setCext_setQualf             "setCext-setQualf"
#define NID_setCext_setQualf            614
#define OBJ_setCext_setQualf            OBJ_set_certExt,6L

#define SN_setCext_PGWYcapabilities             "setCext-PGWYcapabilities"
#define NID_setCext_PGWYcapabilities            615
#define OBJ_setCext_PGWYcapabilities            OBJ_set_certExt,7L

#define SN_setCext_TokenIdentifier              "setCext-TokenIdentifier"
#define NID_setCext_TokenIdentifier             616
#define OBJ_setCext_TokenIdentifier             OBJ_set_certExt,8L

#define SN_setCext_Track2Data           "setCext-Track2Data"
#define NID_setCext_Track2Data          617
#define OBJ_setCext_Track2Data          OBJ_set_certExt,9L

#define SN_setCext_TokenType            "setCext-TokenType"
#define NID_setCext_TokenType           618
#define OBJ_setCext_TokenType           OBJ_set_certExt,10L

#define SN_setCext_IssuerCapabilities           "setCext-IssuerCapabilities"
#define NID_setCext_IssuerCapabilities          619
#define OBJ_setCext_IssuerCapabilities          OBJ_set_certExt,11L

#define SN_setAttr_Cert         "setAttr-Cert"
#define NID_setAttr_Cert                620
#define OBJ_setAttr_Cert                OBJ_set_attr,0L

#define SN_setAttr_PGWYcap              "setAttr-PGWYcap"
#define LN_setAttr_PGWYcap              "payment gateway capabilities"
#define NID_setAttr_PGWYcap             621
#define OBJ_setAttr_PGWYcap             OBJ_set_attr,1L

#define SN_setAttr_TokenType            "setAttr-TokenType"
#define NID_setAttr_TokenType           622
#define OBJ_setAttr_TokenType           OBJ_set_attr,2L

#define SN_setAttr_IssCap               "setAttr-IssCap"
#define LN_setAttr_IssCap               "issuer capabilities"
#define NID_setAttr_IssCap              623
#define OBJ_setAttr_IssCap              OBJ_set_attr,3L

#define SN_set_rootKeyThumb             "set-rootKeyThumb"
#define NID_set_rootKeyThumb            624
#define OBJ_set_rootKeyThumb            OBJ_setAttr_Cert,0L

#define SN_set_addPolicy                "set-addPolicy"
#define NID_set_addPolicy               625
#define OBJ_set_addPolicy               OBJ_setAttr_Cert,1L

#define SN_setAttr_Token_EMV            "setAttr-Token-EMV"
#define NID_setAttr_Token_EMV           626
#define OBJ_setAttr_Token_EMV           OBJ_setAttr_TokenType,1L

#define SN_setAttr_Token_B0Prime                "setAttr-Token-B0Prime"
#define NID_setAttr_Token_B0Prime               627
#define OBJ_setAttr_Token_B0Prime               OBJ_setAttr_TokenType,2L

#define SN_setAttr_IssCap_CVM           "setAttr-IssCap-CVM"
#define NID_setAttr_IssCap_CVM          628
#define OBJ_setAttr_IssCap_CVM          OBJ_setAttr_IssCap,3L

#define SN_setAttr_IssCap_T2            "setAttr-IssCap-T2"
#define NID_setAttr_IssCap_T2           629
#define OBJ_setAttr_IssCap_T2           OBJ_setAttr_IssCap,4L

#define SN_setAttr_IssCap_Sig           "setAttr-IssCap-Sig"
#define NID_setAttr_IssCap_Sig          630
#define OBJ_setAttr_IssCap_Sig          OBJ_setAttr_IssCap,5L

#define SN_setAttr_GenCryptgrm          "setAttr-GenCryptgrm"
#define LN_setAttr_GenCryptgrm          "generate cryptogram"
#define NID_setAttr_GenCryptgrm         631
#define OBJ_setAttr_GenCryptgrm         OBJ_setAttr_IssCap_CVM,1L

#define SN_setAttr_T2Enc                "setAttr-T2Enc"
#define LN_setAttr_T2Enc                "encrypted track 2"
#define NID_setAttr_T2Enc               632
#define OBJ_setAttr_T2Enc               OBJ_setAttr_IssCap_T2,1L

#define SN_setAttr_T2cleartxt           "setAttr-T2cleartxt"
#define LN_setAttr_T2cleartxt           "cleartext track 2"
#define NID_setAttr_T2cleartxt          633
#define OBJ_setAttr_T2cleartxt          OBJ_setAttr_IssCap_T2,2L

#define SN_setAttr_TokICCsig            "setAttr-TokICCsig"
#define LN_setAttr_TokICCsig            "ICC or token signature"
#define NID_setAttr_TokICCsig           634
#define OBJ_setAttr_TokICCsig           OBJ_setAttr_IssCap_Sig,1L

#define SN_setAttr_SecDevSig            "setAttr-SecDevSig"
#define LN_setAttr_SecDevSig            "secure device signature"
#define NID_setAttr_SecDevSig           635
#define OBJ_setAttr_SecDevSig           OBJ_setAttr_IssCap_Sig,2L

#define SN_set_brand_IATA_ATA           "set-brand-IATA-ATA"
#define NID_set_brand_IATA_ATA          636
#define OBJ_set_brand_IATA_ATA          OBJ_set_brand,1L

#define SN_set_brand_Diners             "set-brand-Diners"
#define NID_set_brand_Diners            637
#define OBJ_set_brand_Diners            OBJ_set_brand,30L

#define SN_set_brand_AmericanExpress            "set-brand-AmericanExpress"
#define NID_set_brand_AmericanExpress           638
#define OBJ_set_brand_AmericanExpress           OBJ_set_brand,34L

#define SN_set_brand_JCB                "set-brand-JCB"
#define NID_set_brand_JCB               639
#define OBJ_set_brand_JCB               OBJ_set_brand,35L

#define SN_set_brand_Visa               "set-brand-Visa"
#define NID_set_brand_Visa              640
#define OBJ_set_brand_Visa              OBJ_set_brand,4L

#define SN_set_brand_MasterCard         "set-brand-MasterCard"
#define NID_set_brand_MasterCard                641
#define OBJ_set_brand_MasterCard                OBJ_set_brand,5L

#define SN_set_brand_Novus              "set-brand-Novus"
#define NID_set_brand_Novus             642
#define OBJ_set_brand_Novus             OBJ_set_brand,6011L

#define SN_des_cdmf             "DES-CDMF"
#define LN_des_cdmf             "des-cdmf"
#define NID_des_cdmf            643
#define OBJ_des_cdmf            OBJ_rsadsi,3L,10L

#define SN_rsaOAEPEncryptionSET         "rsaOAEPEncryptionSET"
#define NID_rsaOAEPEncryptionSET                644
#define OBJ_rsaOAEPEncryptionSET                OBJ_rsadsi,1L,1L,6L

#define SN_ipsec3               "Oakley-EC2N-3"
#define LN_ipsec3               "ipsec3"
#define NID_ipsec3              749

#define SN_ipsec4               "Oakley-EC2N-4"
#define LN_ipsec4               "ipsec4"
#define NID_ipsec4              750

#define SN_whirlpool            "whirlpool"
#define NID_whirlpool           804
#define OBJ_whirlpool           OBJ_iso,0L,10118L,3L,0L,55L

#define SN_cryptopro            "cryptopro"
#define NID_cryptopro           805
#define OBJ_cryptopro           OBJ_member_body,643L,2L,2L

#define SN_cryptocom            "cryptocom"
#define NID_cryptocom           806
#define OBJ_cryptocom           OBJ_member_body,643L,2L,9L

#define SN_id_tc26              "id-tc26"
#define NID_id_tc26             974
#define OBJ_id_tc26             OBJ_member_body,643L,7L,1L

#define SN_id_GostR3411_94_with_GostR3410_2001          "id-GostR3411-94-with-GostR3410-2001"
#define LN_id_GostR3411_94_with_GostR3410_2001          "GOST R 34.11-94 with GOST R 34.10-2001"
#define NID_id_GostR3411_94_with_GostR3410_2001         807
#define OBJ_id_GostR3411_94_with_GostR3410_2001         OBJ_cryptopro,3L

#define SN_id_GostR3411_94_with_GostR3410_94            "id-GostR3411-94-with-GostR3410-94"
#define LN_id_GostR3411_94_with_GostR3410_94            "GOST R 34.11-94 with GOST R 34.10-94"
#define NID_id_GostR3411_94_with_GostR3410_94           808
#define OBJ_id_GostR3411_94_with_GostR3410_94           OBJ_cryptopro,4L

#define SN_id_GostR3411_94              "md_gost94"
#define LN_id_GostR3411_94              "GOST R 34.11-94"
#define NID_id_GostR3411_94             809
#define OBJ_id_GostR3411_94             OBJ_cryptopro,9L

#define SN_id_HMACGostR3411_94          "id-HMACGostR3411-94"
#define LN_id_HMACGostR3411_94          "HMAC GOST 34.11-94"
#define NID_id_HMACGostR3411_94         810
#define OBJ_id_HMACGostR3411_94         OBJ_cryptopro,10L

#define SN_id_GostR3410_2001            "gost2001"
#define LN_id_GostR3410_2001            "GOST R 34.10-2001"
#define NID_id_GostR3410_2001           811
#define OBJ_id_GostR3410_2001           OBJ_cryptopro,19L

#define SN_id_GostR3410_94              "gost94"
#define LN_id_GostR3410_94              "GOST R 34.10-94"
#define NID_id_GostR3410_94             812
#define OBJ_id_GostR3410_94             OBJ_cryptopro,20L

#define SN_id_Gost28147_89              "gost89"
#define LN_id_Gost28147_89              "GOST 28147-89"
#define NID_id_Gost28147_89             813
#define OBJ_id_Gost28147_89             OBJ_cryptopro,21L

#define SN_gost89_cnt           "gost89-cnt"
#define NID_gost89_cnt          814

#define SN_gost89_cnt_12                "gost89-cnt-12"
#define NID_gost89_cnt_12               975

#define SN_gost89_cbc           "gost89-cbc"
#define NID_gost89_cbc          1009

#define SN_gost89_ecb           "gost89-ecb"
#define NID_gost89_ecb          1010

#define SN_gost89_ctr           "gost89-ctr"
#define NID_gost89_ctr          1011

#define SN_id_Gost28147_89_MAC          "gost-mac"
#define LN_id_Gost28147_89_MAC          "GOST 28147-89 MAC"
#define NID_id_Gost28147_89_MAC         815
#define OBJ_id_Gost28147_89_MAC         OBJ_cryptopro,22L

#define SN_gost_mac_12          "gost-mac-12"
#define NID_gost_mac_12         976

#define SN_id_GostR3411_94_prf          "prf-gostr3411-94"
#define LN_id_GostR3411_94_prf          "GOST R 34.11-94 PRF"
#define NID_id_GostR3411_94_prf         816
#define OBJ_id_GostR3411_94_prf         OBJ_cryptopro,23L

#define SN_id_GostR3410_2001DH          "id-GostR3410-2001DH"
#define LN_id_GostR3410_2001DH          "GOST R 34.10-2001 DH"
#define NID_id_GostR3410_2001DH         817
#define OBJ_id_GostR3410_2001DH         OBJ_cryptopro,98L

#define SN_id_GostR3410_94DH            "id-GostR3410-94DH"
#define LN_id_GostR3410_94DH            "GOST R 34.10-94 DH"
#define NID_id_GostR3410_94DH           818
#define OBJ_id_GostR3410_94DH           OBJ_cryptopro,99L

#define SN_id_Gost28147_89_CryptoPro_KeyMeshing         "id-Gost28147-89-CryptoPro-KeyMeshing"
#define NID_id_Gost28147_89_CryptoPro_KeyMeshing                819
#define OBJ_id_Gost28147_89_CryptoPro_KeyMeshing                OBJ_cryptopro,14L,1L

#define SN_id_Gost28147_89_None_KeyMeshing              "id-Gost28147-89-None-KeyMeshing"
#define NID_id_Gost28147_89_None_KeyMeshing             820
#define OBJ_id_Gost28147_89_None_KeyMeshing             OBJ_cryptopro,14L,0L

#define SN_id_GostR3411_94_TestParamSet         "id-GostR3411-94-TestParamSet"
#define NID_id_GostR3411_94_TestParamSet                821
#define OBJ_id_GostR3411_94_TestParamSet                OBJ_cryptopro,30L,0L

#define SN_id_GostR3411_94_CryptoProParamSet            "id-GostR3411-94-CryptoProParamSet"
#define NID_id_GostR3411_94_CryptoProParamSet           822
#define OBJ_id_GostR3411_94_CryptoProParamSet           OBJ_cryptopro,30L,1L

#define SN_id_Gost28147_89_TestParamSet         "id-Gost28147-89-TestParamSet"
#define NID_id_Gost28147_89_TestParamSet                823
#define OBJ_id_Gost28147_89_TestParamSet                OBJ_cryptopro,31L,0L

#define SN_id_Gost28147_89_CryptoPro_A_ParamSet         "id-Gost28147-89-CryptoPro-A-ParamSet"
#define NID_id_Gost28147_89_CryptoPro_A_ParamSet                824
#define OBJ_id_Gost28147_89_CryptoPro_A_ParamSet                OBJ_cryptopro,31L,1L

#define SN_id_Gost28147_89_CryptoPro_B_ParamSet         "id-Gost28147-89-CryptoPro-B-ParamSet"
#define NID_id_Gost28147_89_CryptoPro_B_ParamSet                825
#define OBJ_id_Gost28147_89_CryptoPro_B_ParamSet                OBJ_cryptopro,31L,2L

#define SN_id_Gost28147_89_CryptoPro_C_ParamSet         "id-Gost28147-89-CryptoPro-C-ParamSet"
#define NID_id_Gost28147_89_CryptoPro_C_ParamSet                826
#define OBJ_id_Gost28147_89_CryptoPro_C_ParamSet                OBJ_cryptopro,31L,3L

#define SN_id_Gost28147_89_CryptoPro_D_ParamSet         "id-Gost28147-89-CryptoPro-D-ParamSet"
#define NID_id_Gost28147_89_CryptoPro_D_ParamSet                827
#define OBJ_id_Gost28147_89_CryptoPro_D_ParamSet                OBJ_cryptopro,31L,4L

#define SN_id_Gost28147_89_CryptoPro_Oscar_1_1_ParamSet         "id-Gost28147-89-CryptoPro-Oscar-1-1-ParamSet"
#define NID_id_Gost28147_89_CryptoPro_Oscar_1_1_ParamSet                828
#define OBJ_id_Gost28147_89_CryptoPro_Oscar_1_1_ParamSet                OBJ_cryptopro,31L,5L

#define SN_id_Gost28147_89_CryptoPro_Oscar_1_0_ParamSet         "id-Gost28147-89-CryptoPro-Oscar-1-0-ParamSet"
#define NID_id_Gost28147_89_CryptoPro_Oscar_1_0_ParamSet                829
#define OBJ_id_Gost28147_89_CryptoPro_Oscar_1_0_ParamSet                OBJ_cryptopro,31L,6L

#define SN_id_Gost28147_89_CryptoPro_RIC_1_ParamSet             "id-Gost28147-89-CryptoPro-RIC-1-ParamSet"
#define NID_id_Gost28147_89_CryptoPro_RIC_1_ParamSet            830
#define OBJ_id_Gost28147_89_CryptoPro_RIC_1_ParamSet            OBJ_cryptopro,31L,7L

#define SN_id_GostR3410_94_TestParamSet         "id-GostR3410-94-TestParamSet"
#define NID_id_GostR3410_94_TestParamSet                831
#define OBJ_id_GostR3410_94_TestParamSet                OBJ_cryptopro,32L,0L

#define SN_id_GostR3410_94_CryptoPro_A_ParamSet         "id-GostR3410-94-CryptoPro-A-ParamSet"
#define NID_id_GostR3410_94_CryptoPro_A_ParamSet                832
#define OBJ_id_GostR3410_94_CryptoPro_A_ParamSet                OBJ_cryptopro,32L,2L

#define SN_id_GostR3410_94_CryptoPro_B_ParamSet         "id-GostR3410-94-CryptoPro-B-ParamSet"
#define NID_id_GostR3410_94_CryptoPro_B_ParamSet                833
#define OBJ_id_GostR3410_94_CryptoPro_B_ParamSet                OBJ_cryptopro,32L,3L

#define SN_id_GostR3410_94_CryptoPro_C_ParamSet         "id-GostR3410-94-CryptoPro-C-ParamSet"
#define NID_id_GostR3410_94_CryptoPro_C_ParamSet                834
#define OBJ_id_GostR3410_94_CryptoPro_C_ParamSet                OBJ_cryptopro,32L,4L

#define SN_id_GostR3410_94_CryptoPro_D_ParamSet         "id-GostR3410-94-CryptoPro-D-ParamSet"
#define NID_id_GostR3410_94_CryptoPro_D_ParamSet                835
#define OBJ_id_GostR3410_94_CryptoPro_D_ParamSet                OBJ_cryptopro,32L,5L

#define SN_id_GostR3410_94_CryptoPro_XchA_ParamSet              "id-GostR3410-94-CryptoPro-XchA-ParamSet"
#define NID_id_GostR3410_94_CryptoPro_XchA_ParamSet             836
#define OBJ_id_GostR3410_94_CryptoPro_XchA_ParamSet             OBJ_cryptopro,33L,1L

#define SN_id_GostR3410_94_CryptoPro_XchB_ParamSet              "id-GostR3410-94-CryptoPro-XchB-ParamSet"
#define NID_id_GostR3410_94_CryptoPro_XchB_ParamSet             837
#define OBJ_id_GostR3410_94_CryptoPro_XchB_ParamSet             OBJ_cryptopro,33L,2L

#define SN_id_GostR3410_94_CryptoPro_XchC_ParamSet              "id-GostR3410-94-CryptoPro-XchC-ParamSet"
#define NID_id_GostR3410_94_CryptoPro_XchC_ParamSet             838
#define OBJ_id_GostR3410_94_CryptoPro_XchC_ParamSet             OBJ_cryptopro,33L,3L

#define SN_id_GostR3410_2001_TestParamSet               "id-GostR3410-2001-TestParamSet"
#define NID_id_GostR3410_2001_TestParamSet              839
#define OBJ_id_GostR3410_2001_TestParamSet              OBJ_cryptopro,35L,0L

#define SN_id_GostR3410_2001_CryptoPro_A_ParamSet               "id-GostR3410-2001-CryptoPro-A-ParamSet"
#define NID_id_GostR3410_2001_CryptoPro_A_ParamSet              840
#define OBJ_id_GostR3410_2001_CryptoPro_A_ParamSet              OBJ_cryptopro,35L,1L

#define SN_id_GostR3410_2001_CryptoPro_B_ParamSet               "id-GostR3410-2001-CryptoPro-B-ParamSet"
#define NID_id_GostR3410_2001_CryptoPro_B_ParamSet              841
#define OBJ_id_GostR3410_2001_CryptoPro_B_ParamSet              OBJ_cryptopro,35L,2L

#define SN_id_GostR3410_2001_CryptoPro_C_ParamSet               "id-GostR3410-2001-CryptoPro-C-ParamSet"
#define NID_id_GostR3410_2001_CryptoPro_C_ParamSet              842
#define OBJ_id_GostR3410_2001_CryptoPro_C_ParamSet              OBJ_cryptopro,35L,3L

#define SN_id_GostR3410_2001_CryptoPro_XchA_ParamSet            "id-GostR3410-2001-CryptoPro-XchA-ParamSet"
#define NID_id_GostR3410_2001_CryptoPro_XchA_ParamSet           843
#define OBJ_id_GostR3410_2001_CryptoPro_XchA_ParamSet           OBJ_cryptopro,36L,0L

#define SN_id_GostR3410_2001_CryptoPro_XchB_ParamSet            "id-GostR3410-2001-CryptoPro-XchB-ParamSet"
#define NID_id_GostR3410_2001_CryptoPro_XchB_ParamSet           844
#define OBJ_id_GostR3410_2001_CryptoPro_XchB_ParamSet           OBJ_cryptopro,36L,1L

#define SN_id_GostR3410_94_a            "id-GostR3410-94-a"
#define NID_id_GostR3410_94_a           845
#define OBJ_id_GostR3410_94_a           OBJ_id_GostR3410_94,1L

#define SN_id_GostR3410_94_aBis         "id-GostR3410-94-aBis"
#define NID_id_GostR3410_94_aBis                846
#define OBJ_id_GostR3410_94_aBis                OBJ_id_GostR3410_94,2L

#define SN_id_GostR3410_94_b            "id-GostR3410-94-b"
#define NID_id_GostR3410_94_b           847
#define OBJ_id_GostR3410_94_b           OBJ_id_GostR3410_94,3L

#define SN_id_GostR3410_94_bBis         "id-GostR3410-94-bBis"
#define NID_id_GostR3410_94_bBis                848
#define OBJ_id_GostR3410_94_bBis                OBJ_id_GostR3410_94,4L

#define SN_id_Gost28147_89_cc           "id-Gost28147-89-cc"
#define LN_id_Gost28147_89_cc           "GOST 28147-89 Cryptocom ParamSet"
#define NID_id_Gost28147_89_cc          849
#define OBJ_id_Gost28147_89_cc          OBJ_cryptocom,1L,6L,1L

#define SN_id_GostR3410_94_cc           "gost94cc"
#define LN_id_GostR3410_94_cc           "GOST 34.10-94 Cryptocom"
#define NID_id_GostR3410_94_cc          850
#define OBJ_id_GostR3410_94_cc          OBJ_cryptocom,1L,5L,3L

#define SN_id_GostR3410_2001_cc         "gost2001cc"
#define LN_id_GostR3410_2001_cc         "GOST 34.10-2001 Cryptocom"
#define NID_id_GostR3410_2001_cc                851
#define OBJ_id_GostR3410_2001_cc                OBJ_cryptocom,1L,5L,4L

#define SN_id_GostR3411_94_with_GostR3410_94_cc         "id-GostR3411-94-with-GostR3410-94-cc"
#define LN_id_GostR3411_94_with_GostR3410_94_cc         "GOST R 34.11-94 with GOST R 34.10-94 Cryptocom"
#define NID_id_GostR3411_94_with_GostR3410_94_cc                852
#define OBJ_id_GostR3411_94_with_GostR3410_94_cc                OBJ_cryptocom,1L,3L,3L

#define SN_id_GostR3411_94_with_GostR3410_2001_cc               "id-GostR3411-94-with-GostR3410-2001-cc"
#define LN_id_GostR3411_94_with_GostR3410_2001_cc               "GOST R 34.11-94 with GOST R 34.10-2001 Cryptocom"
#define NID_id_GostR3411_94_with_GostR3410_2001_cc              853
#define OBJ_id_GostR3411_94_with_GostR3410_2001_cc              OBJ_cryptocom,1L,3L,4L

#define SN_id_GostR3410_2001_ParamSet_cc                "id-GostR3410-2001-ParamSet-cc"
#define LN_id_GostR3410_2001_ParamSet_cc                "GOST R 3410-2001 Parameter Set Cryptocom"
#define NID_id_GostR3410_2001_ParamSet_cc               854
#define OBJ_id_GostR3410_2001_ParamSet_cc               OBJ_cryptocom,1L,8L,1L

#define SN_id_tc26_algorithms           "id-tc26-algorithms"
#define NID_id_tc26_algorithms          977
#define OBJ_id_tc26_algorithms          OBJ_id_tc26,1L

#define SN_id_tc26_sign         "id-tc26-sign"
#define NID_id_tc26_sign                978
#define OBJ_id_tc26_sign                OBJ_id_tc26_algorithms,1L

#define SN_id_GostR3410_2012_256                "gost2012_256"
#define LN_id_GostR3410_2012_256                "GOST R 34.10-2012 with 256 bit modulus"
#define NID_id_GostR3410_2012_256               979
#define OBJ_id_GostR3410_2012_256               OBJ_id_tc26_sign,1L

#define SN_id_GostR3410_2012_512                "gost2012_512"
#define LN_id_GostR3410_2012_512                "GOST R 34.10-2012 with 512 bit modulus"
#define NID_id_GostR3410_2012_512               980
#define OBJ_id_GostR3410_2012_512               OBJ_id_tc26_sign,2L

#define SN_id_tc26_digest               "id-tc26-digest"
#define NID_id_tc26_digest              981
#define OBJ_id_tc26_digest              OBJ_id_tc26_algorithms,2L

#define SN_id_GostR3411_2012_256                "md_gost12_256"
#define LN_id_GostR3411_2012_256                "GOST R 34.11-2012 with 256 bit hash"
#define NID_id_GostR3411_2012_256               982
#define OBJ_id_GostR3411_2012_256               OBJ_id_tc26_digest,2L

#define SN_id_GostR3411_2012_512                "md_gost12_512"
#define LN_id_GostR3411_2012_512                "GOST R 34.11-2012 with 512 bit hash"
#define NID_id_GostR3411_2012_512               983
#define OBJ_id_GostR3411_2012_512               OBJ_id_tc26_digest,3L

#define SN_id_tc26_signwithdigest               "id-tc26-signwithdigest"
#define NID_id_tc26_signwithdigest              984
#define OBJ_id_tc26_signwithdigest              OBJ_id_tc26_algorithms,3L

#define SN_id_tc26_signwithdigest_gost3410_2012_256             "id-tc26-signwithdigest-gost3410-2012-256"
#define LN_id_tc26_signwithdigest_gost3410_2012_256             "GOST R 34.10-2012 with GOST R 34.11-2012 (256 bit)"
#define NID_id_tc26_signwithdigest_gost3410_2012_256            985
#define OBJ_id_tc26_signwithdigest_gost3410_2012_256            OBJ_id_tc26_signwithdigest,2L

#define SN_id_tc26_signwithdigest_gost3410_2012_512             "id-tc26-signwithdigest-gost3410-2012-512"
#define LN_id_tc26_signwithdigest_gost3410_2012_512             "GOST R 34.10-2012 with GOST R 34.11-2012 (512 bit)"
#define NID_id_tc26_signwithdigest_gost3410_2012_512            986
#define OBJ_id_tc26_signwithdigest_gost3410_2012_512            OBJ_id_tc26_signwithdigest,3L

#define SN_id_tc26_mac          "id-tc26-mac"
#define NID_id_tc26_mac         987
#define OBJ_id_tc26_mac         OBJ_id_tc26_algorithms,4L

#define SN_id_tc26_hmac_gost_3411_2012_256              "id-tc26-hmac-gost-3411-2012-256"
#define LN_id_tc26_hmac_gost_3411_2012_256              "HMAC GOST 34.11-2012 256 bit"
#define NID_id_tc26_hmac_gost_3411_2012_256             988
#define OBJ_id_tc26_hmac_gost_3411_2012_256             OBJ_id_tc26_mac,1L

#define SN_id_tc26_hmac_gost_3411_2012_512              "id-tc26-hmac-gost-3411-2012-512"
#define LN_id_tc26_hmac_gost_3411_2012_512              "HMAC GOST 34.11-2012 512 bit"
#define NID_id_tc26_hmac_gost_3411_2012_512             989
#define OBJ_id_tc26_hmac_gost_3411_2012_512             OBJ_id_tc26_mac,2L

#define SN_id_tc26_cipher               "id-tc26-cipher"
#define NID_id_tc26_cipher              990
#define OBJ_id_tc26_cipher              OBJ_id_tc26_algorithms,5L

#define SN_id_tc26_cipher_gostr3412_2015_magma          "id-tc26-cipher-gostr3412-2015-magma"
#define NID_id_tc26_cipher_gostr3412_2015_magma         1173
#define OBJ_id_tc26_cipher_gostr3412_2015_magma         OBJ_id_tc26_cipher,1L

#define SN_magma_ctr_acpkm              "magma-ctr-acpkm"
#define NID_magma_ctr_acpkm             1174
#define OBJ_magma_ctr_acpkm             OBJ_id_tc26_cipher_gostr3412_2015_magma,1L

#define SN_magma_ctr_acpkm_omac         "magma-ctr-acpkm-omac"
#define NID_magma_ctr_acpkm_omac                1175
#define OBJ_magma_ctr_acpkm_omac                OBJ_id_tc26_cipher_gostr3412_2015_magma,2L

#define SN_id_tc26_cipher_gostr3412_2015_kuznyechik             "id-tc26-cipher-gostr3412-2015-kuznyechik"
#define NID_id_tc26_cipher_gostr3412_2015_kuznyechik            1176
#define OBJ_id_tc26_cipher_gostr3412_2015_kuznyechik            OBJ_id_tc26_cipher,2L

#define SN_kuznyechik_ctr_acpkm         "kuznyechik-ctr-acpkm"
#define NID_kuznyechik_ctr_acpkm                1177
#define OBJ_kuznyechik_ctr_acpkm                OBJ_id_tc26_cipher_gostr3412_2015_kuznyechik,1L

#define SN_kuznyechik_ctr_acpkm_omac            "kuznyechik-ctr-acpkm-omac"
#define NID_kuznyechik_ctr_acpkm_omac           1178
#define OBJ_kuznyechik_ctr_acpkm_omac           OBJ_id_tc26_cipher_gostr3412_2015_kuznyechik,2L

#define SN_id_tc26_agreement            "id-tc26-agreement"
#define NID_id_tc26_agreement           991
#define OBJ_id_tc26_agreement           OBJ_id_tc26_algorithms,6L

#define SN_id_tc26_agreement_gost_3410_2012_256         "id-tc26-agreement-gost-3410-2012-256"
#define NID_id_tc26_agreement_gost_3410_2012_256                992
#define OBJ_id_tc26_agreement_gost_3410_2012_256                OBJ_id_tc26_agreement,1L

#define SN_id_tc26_agreement_gost_3410_2012_512         "id-tc26-agreement-gost-3410-2012-512"
#define NID_id_tc26_agreement_gost_3410_2012_512                993
#define OBJ_id_tc26_agreement_gost_3410_2012_512                OBJ_id_tc26_agreement,2L

#define SN_id_tc26_wrap         "id-tc26-wrap"
#define NID_id_tc26_wrap                1179
#define OBJ_id_tc26_wrap                OBJ_id_tc26_algorithms,7L

#define SN_id_tc26_wrap_gostr3412_2015_magma            "id-tc26-wrap-gostr3412-2015-magma"
#define NID_id_tc26_wrap_gostr3412_2015_magma           1180
#define OBJ_id_tc26_wrap_gostr3412_2015_magma           OBJ_id_tc26_wrap,1L

#define SN_magma_kexp15         "magma-kexp15"
#define NID_magma_kexp15                1181
#define OBJ_magma_kexp15                OBJ_id_tc26_wrap_gostr3412_2015_magma,1L

#define SN_id_tc26_wrap_gostr3412_2015_kuznyechik               "id-tc26-wrap-gostr3412-2015-kuznyechik"
#define NID_id_tc26_wrap_gostr3412_2015_kuznyechik              1182
#define OBJ_id_tc26_wrap_gostr3412_2015_kuznyechik              OBJ_id_tc26_wrap,2L

#define SN_kuznyechik_kexp15            "kuznyechik-kexp15"
#define NID_kuznyechik_kexp15           1183
#define OBJ_kuznyechik_kexp15           OBJ_id_tc26_wrap_gostr3412_2015_kuznyechik,1L

#define SN_id_tc26_constants            "id-tc26-constants"
#define NID_id_tc26_constants           994
#define OBJ_id_tc26_constants           OBJ_id_tc26,2L

#define SN_id_tc26_sign_constants               "id-tc26-sign-constants"
#define NID_id_tc26_sign_constants              995
#define OBJ_id_tc26_sign_constants              OBJ_id_tc26_constants,1L

#define SN_id_tc26_gost_3410_2012_256_constants         "id-tc26-gost-3410-2012-256-constants"
#define NID_id_tc26_gost_3410_2012_256_constants                1147
#define OBJ_id_tc26_gost_3410_2012_256_constants                OBJ_id_tc26_sign_constants,1L

#define SN_id_tc26_gost_3410_2012_256_paramSetA         "id-tc26-gost-3410-2012-256-paramSetA"
#define LN_id_tc26_gost_3410_2012_256_paramSetA         "GOST R 34.10-2012 (256 bit) ParamSet A"
#define NID_id_tc26_gost_3410_2012_256_paramSetA                1148
#define OBJ_id_tc26_gost_3410_2012_256_paramSetA                OBJ_id_tc26_gost_3410_2012_256_constants,1L

#define SN_id_tc26_gost_3410_2012_256_paramSetB         "id-tc26-gost-3410-2012-256-paramSetB"
#define LN_id_tc26_gost_3410_2012_256_paramSetB         "GOST R 34.10-2012 (256 bit) ParamSet B"
#define NID_id_tc26_gost_3410_2012_256_paramSetB                1184
#define OBJ_id_tc26_gost_3410_2012_256_paramSetB                OBJ_id_tc26_gost_3410_2012_256_constants,2L

#define SN_id_tc26_gost_3410_2012_256_paramSetC         "id-tc26-gost-3410-2012-256-paramSetC"
#define LN_id_tc26_gost_3410_2012_256_paramSetC         "GOST R 34.10-2012 (256 bit) ParamSet C"
#define NID_id_tc26_gost_3410_2012_256_paramSetC                1185
#define OBJ_id_tc26_gost_3410_2012_256_paramSetC                OBJ_id_tc26_gost_3410_2012_256_constants,3L

#define SN_id_tc26_gost_3410_2012_256_paramSetD         "id-tc26-gost-3410-2012-256-paramSetD"
#define LN_id_tc26_gost_3410_2012_256_paramSetD         "GOST R 34.10-2012 (256 bit) ParamSet D"
#define NID_id_tc26_gost_3410_2012_256_paramSetD                1186
#define OBJ_id_tc26_gost_3410_2012_256_paramSetD                OBJ_id_tc26_gost_3410_2012_256_constants,4L

#define SN_id_tc26_gost_3410_2012_512_constants         "id-tc26-gost-3410-2012-512-constants"
#define NID_id_tc26_gost_3410_2012_512_constants                996
#define OBJ_id_tc26_gost_3410_2012_512_constants                OBJ_id_tc26_sign_constants,2L

#define SN_id_tc26_gost_3410_2012_512_paramSetTest              "id-tc26-gost-3410-2012-512-paramSetTest"
#define LN_id_tc26_gost_3410_2012_512_paramSetTest              "GOST R 34.10-2012 (512 bit) testing parameter set"
#define NID_id_tc26_gost_3410_2012_512_paramSetTest             997
#define OBJ_id_tc26_gost_3410_2012_512_paramSetTest             OBJ_id_tc26_gost_3410_2012_512_constants,0L

#define SN_id_tc26_gost_3410_2012_512_paramSetA         "id-tc26-gost-3410-2012-512-paramSetA"
#define LN_id_tc26_gost_3410_2012_512_paramSetA         "GOST R 34.10-2012 (512 bit) ParamSet A"
#define NID_id_tc26_gost_3410_2012_512_paramSetA                998
#define OBJ_id_tc26_gost_3410_2012_512_paramSetA                OBJ_id_tc26_gost_3410_2012_512_constants,1L

#define SN_id_tc26_gost_3410_2012_512_paramSetB         "id-tc26-gost-3410-2012-512-paramSetB"
#define LN_id_tc26_gost_3410_2012_512_paramSetB         "GOST R 34.10-2012 (512 bit) ParamSet B"
#define NID_id_tc26_gost_3410_2012_512_paramSetB                999
#define OBJ_id_tc26_gost_3410_2012_512_paramSetB                OBJ_id_tc26_gost_3410_2012_512_constants,2L

#define SN_id_tc26_gost_3410_2012_512_paramSetC         "id-tc26-gost-3410-2012-512-paramSetC"
#define LN_id_tc26_gost_3410_2012_512_paramSetC         "GOST R 34.10-2012 (512 bit) ParamSet C"
#define NID_id_tc26_gost_3410_2012_512_paramSetC                1149
#define OBJ_id_tc26_gost_3410_2012_512_paramSetC                OBJ_id_tc26_gost_3410_2012_512_constants,3L

#define SN_id_tc26_digest_constants             "id-tc26-digest-constants"
#define NID_id_tc26_digest_constants            1000
#define OBJ_id_tc26_digest_constants            OBJ_id_tc26_constants,2L

#define SN_id_tc26_cipher_constants             "id-tc26-cipher-constants"
#define NID_id_tc26_cipher_constants            1001
#define OBJ_id_tc26_cipher_constants            OBJ_id_tc26_constants,5L

#define SN_id_tc26_gost_28147_constants         "id-tc26-gost-28147-constants"
#define NID_id_tc26_gost_28147_constants                1002
#define OBJ_id_tc26_gost_28147_constants                OBJ_id_tc26_cipher_constants,1L

#define SN_id_tc26_gost_28147_param_Z           "id-tc26-gost-28147-param-Z"
#define LN_id_tc26_gost_28147_param_Z           "GOST 28147-89 TC26 parameter set"
#define NID_id_tc26_gost_28147_param_Z          1003
#define OBJ_id_tc26_gost_28147_param_Z          OBJ_id_tc26_gost_28147_constants,1L

#define SN_INN          "INN"
#define LN_INN          "INN"
#define NID_INN         1004
#define OBJ_INN         OBJ_member_body,643L,3L,131L,1L,1L

#define SN_OGRN         "OGRN"
#define LN_OGRN         "OGRN"
#define NID_OGRN                1005
#define OBJ_OGRN                OBJ_member_body,643L,100L,1L

#define SN_SNILS                "SNILS"
#define LN_SNILS                "SNILS"
#define NID_SNILS               1006
#define OBJ_SNILS               OBJ_member_body,643L,100L,3L

#define SN_OGRNIP               "OGRNIP"
#define LN_OGRNIP               "OGRNIP"
#define NID_OGRNIP              1226
#define OBJ_OGRNIP              OBJ_member_body,643L,100L,5L

#define SN_subjectSignTool              "subjectSignTool"
#define LN_subjectSignTool              "Signing Tool of Subject"
#define NID_subjectSignTool             1007
#define OBJ_subjectSignTool             OBJ_member_body,643L,100L,111L

#define SN_issuerSignTool               "issuerSignTool"
#define LN_issuerSignTool               "Signing Tool of Issuer"
#define NID_issuerSignTool              1008
#define OBJ_issuerSignTool              OBJ_member_body,643L,100L,112L

#define SN_classSignTool                "classSignTool"
#define LN_classSignTool                "Class of Signing Tool"
#define NID_classSignTool               1227
#define OBJ_classSignTool               OBJ_member_body,643L,100L,113L

#define SN_classSignToolKC1             "classSignToolKC1"
#define LN_classSignToolKC1             "Class of Signing Tool KC1"
#define NID_classSignToolKC1            1228
#define OBJ_classSignToolKC1            OBJ_member_body,643L,100L,113L,1L

#define SN_classSignToolKC2             "classSignToolKC2"
#define LN_classSignToolKC2             "Class of Signing Tool KC2"
#define NID_classSignToolKC2            1229
#define OBJ_classSignToolKC2            OBJ_member_body,643L,100L,113L,2L

#define SN_classSignToolKC3             "classSignToolKC3"
#define LN_classSignToolKC3             "Class of Signing Tool KC3"
#define NID_classSignToolKC3            1230
#define OBJ_classSignToolKC3            OBJ_member_body,643L,100L,113L,3L

#define SN_classSignToolKB1             "classSignToolKB1"
#define LN_classSignToolKB1             "Class of Signing Tool KB1"
#define NID_classSignToolKB1            1231
#define OBJ_classSignToolKB1            OBJ_member_body,643L,100L,113L,4L

#define SN_classSignToolKB2             "classSignToolKB2"
#define LN_classSignToolKB2             "Class of Signing Tool KB2"
#define NID_classSignToolKB2            1232
#define OBJ_classSignToolKB2            OBJ_member_body,643L,100L,113L,5L

#define SN_classSignToolKA1             "classSignToolKA1"
#define LN_classSignToolKA1             "Class of Signing Tool KA1"
#define NID_classSignToolKA1            1233
#define OBJ_classSignToolKA1            OBJ_member_body,643L,100L,113L,6L

#define SN_kuznyechik_ecb               "kuznyechik-ecb"
#define NID_kuznyechik_ecb              1012

#define SN_kuznyechik_ctr               "kuznyechik-ctr"
#define NID_kuznyechik_ctr              1013

#define SN_kuznyechik_ofb               "kuznyechik-ofb"
#define NID_kuznyechik_ofb              1014

#define SN_kuznyechik_cbc               "kuznyechik-cbc"
#define NID_kuznyechik_cbc              1015

#define SN_kuznyechik_cfb               "kuznyechik-cfb"
#define NID_kuznyechik_cfb              1016

#define SN_kuznyechik_mac               "kuznyechik-mac"
#define NID_kuznyechik_mac              1017

#define SN_magma_ecb            "magma-ecb"
#define NID_magma_ecb           1187

#define SN_magma_ctr            "magma-ctr"
#define NID_magma_ctr           1188

#define SN_magma_ofb            "magma-ofb"
#define NID_magma_ofb           1189

#define SN_magma_cbc            "magma-cbc"
#define NID_magma_cbc           1190

#define SN_magma_cfb            "magma-cfb"
#define NID_magma_cfb           1191

#define SN_magma_mac            "magma-mac"
#define NID_magma_mac           1192

#define SN_camellia_128_cbc             "CAMELLIA-128-CBC"
#define LN_camellia_128_cbc             "camellia-128-cbc"
#define NID_camellia_128_cbc            751
#define OBJ_camellia_128_cbc            1L,2L,392L,200011L,61L,1L,1L,1L,2L

#define SN_camellia_192_cbc             "CAMELLIA-192-CBC"
#define LN_camellia_192_cbc             "camellia-192-cbc"
#define NID_camellia_192_cbc            752
#define OBJ_camellia_192_cbc            1L,2L,392L,200011L,61L,1L,1L,1L,3L

#define SN_camellia_256_cbc             "CAMELLIA-256-CBC"
#define LN_camellia_256_cbc             "camellia-256-cbc"
#define NID_camellia_256_cbc            753
#define OBJ_camellia_256_cbc            1L,2L,392L,200011L,61L,1L,1L,1L,4L

#define SN_id_camellia128_wrap          "id-camellia128-wrap"
#define NID_id_camellia128_wrap         907
#define OBJ_id_camellia128_wrap         1L,2L,392L,200011L,61L,1L,1L,3L,2L

#define SN_id_camellia192_wrap          "id-camellia192-wrap"
#define NID_id_camellia192_wrap         908
#define OBJ_id_camellia192_wrap         1L,2L,392L,200011L,61L,1L,1L,3L,3L

#define SN_id_camellia256_wrap          "id-camellia256-wrap"
#define NID_id_camellia256_wrap         909
#define OBJ_id_camellia256_wrap         1L,2L,392L,200011L,61L,1L,1L,3L,4L

#define OBJ_ntt_ds              0L,3L,4401L,5L

#define OBJ_camellia            OBJ_ntt_ds,3L,1L,9L

#define SN_camellia_128_ecb             "CAMELLIA-128-ECB"
#define LN_camellia_128_ecb             "camellia-128-ecb"
#define NID_camellia_128_ecb            754
#define OBJ_camellia_128_ecb            OBJ_camellia,1L

#define SN_camellia_128_ofb128          "CAMELLIA-128-OFB"
#define LN_camellia_128_ofb128          "camellia-128-ofb"
#define NID_camellia_128_ofb128         766
#define OBJ_camellia_128_ofb128         OBJ_camellia,3L

#define SN_camellia_128_cfb128          "CAMELLIA-128-CFB"
#define LN_camellia_128_cfb128          "camellia-128-cfb"
#define NID_camellia_128_cfb128         757
#define OBJ_camellia_128_cfb128         OBJ_camellia,4L

#define SN_camellia_128_gcm             "CAMELLIA-128-GCM"
#define LN_camellia_128_gcm             "camellia-128-gcm"
#define NID_camellia_128_gcm            961
#define OBJ_camellia_128_gcm            OBJ_camellia,6L

#define SN_camellia_128_ccm             "CAMELLIA-128-CCM"
#define LN_camellia_128_ccm             "camellia-128-ccm"
#define NID_camellia_128_ccm            962
#define OBJ_camellia_128_ccm            OBJ_camellia,7L

#define SN_camellia_128_ctr             "CAMELLIA-128-CTR"
#define LN_camellia_128_ctr             "camellia-128-ctr"
#define NID_camellia_128_ctr            963
#define OBJ_camellia_128_ctr            OBJ_camellia,9L

#define SN_camellia_128_cmac            "CAMELLIA-128-CMAC"
#define LN_camellia_128_cmac            "camellia-128-cmac"
#define NID_camellia_128_cmac           964
#define OBJ_camellia_128_cmac           OBJ_camellia,10L

#define SN_camellia_192_ecb             "CAMELLIA-192-ECB"
#define LN_camellia_192_ecb             "camellia-192-ecb"
#define NID_camellia_192_ecb            755
#define OBJ_camellia_192_ecb            OBJ_camellia,21L

#define SN_camellia_192_ofb128          "CAMELLIA-192-OFB"
#define LN_camellia_192_ofb128          "camellia-192-ofb"
#define NID_camellia_192_ofb128         767
#define OBJ_camellia_192_ofb128         OBJ_camellia,23L

#define SN_camellia_192_cfb128          "CAMELLIA-192-CFB"
#define LN_camellia_192_cfb128          "camellia-192-cfb"
#define NID_camellia_192_cfb128         758
#define OBJ_camellia_192_cfb128         OBJ_camellia,24L

#define SN_camellia_192_gcm             "CAMELLIA-192-GCM"
#define LN_camellia_192_gcm             "camellia-192-gcm"
#define NID_camellia_192_gcm            965
#define OBJ_camellia_192_gcm            OBJ_camellia,26L

#define SN_camellia_192_ccm             "CAMELLIA-192-CCM"
#define LN_camellia_192_ccm             "camellia-192-ccm"
#define NID_camellia_192_ccm            966
#define OBJ_camellia_192_ccm            OBJ_camellia,27L

#define SN_camellia_192_ctr             "CAMELLIA-192-CTR"
#define LN_camellia_192_ctr             "camellia-192-ctr"
#define NID_camellia_192_ctr            967
#define OBJ_camellia_192_ctr            OBJ_camellia,29L

#define SN_camellia_192_cmac            "CAMELLIA-192-CMAC"
#define LN_camellia_192_cmac            "camellia-192-cmac"
#define NID_camellia_192_cmac           968
#define OBJ_camellia_192_cmac           OBJ_camellia,30L

#define SN_camellia_256_ecb             "CAMELLIA-256-ECB"
#define LN_camellia_256_ecb             "camellia-256-ecb"
#define NID_camellia_256_ecb            756
#define OBJ_camellia_256_ecb            OBJ_camellia,41L

#define SN_camellia_256_ofb128          "CAMELLIA-256-OFB"
#define LN_camellia_256_ofb128          "camellia-256-ofb"
#define NID_camellia_256_ofb128         768
#define OBJ_camellia_256_ofb128         OBJ_camellia,43L

#define SN_camellia_256_cfb128          "CAMELLIA-256-CFB"
#define LN_camellia_256_cfb128          "camellia-256-cfb"
#define NID_camellia_256_cfb128         759
#define OBJ_camellia_256_cfb128         OBJ_camellia,44L

#define SN_camellia_256_gcm             "CAMELLIA-256-GCM"
#define LN_camellia_256_gcm             "camellia-256-gcm"
#define NID_camellia_256_gcm            969
#define OBJ_camellia_256_gcm            OBJ_camellia,46L

#define SN_camellia_256_ccm             "CAMELLIA-256-CCM"
#define LN_camellia_256_ccm             "camellia-256-ccm"
#define NID_camellia_256_ccm            970
#define OBJ_camellia_256_ccm            OBJ_camellia,47L

#define SN_camellia_256_ctr             "CAMELLIA-256-CTR"
#define LN_camellia_256_ctr             "camellia-256-ctr"
#define NID_camellia_256_ctr            971
#define OBJ_camellia_256_ctr            OBJ_camellia,49L

#define SN_camellia_256_cmac            "CAMELLIA-256-CMAC"
#define LN_camellia_256_cmac            "camellia-256-cmac"
#define NID_camellia_256_cmac           972
#define OBJ_camellia_256_cmac           OBJ_camellia,50L

#define SN_camellia_128_cfb1            "CAMELLIA-128-CFB1"
#define LN_camellia_128_cfb1            "camellia-128-cfb1"
#define NID_camellia_128_cfb1           760

#define SN_camellia_192_cfb1            "CAMELLIA-192-CFB1"
#define LN_camellia_192_cfb1            "camellia-192-cfb1"
#define NID_camellia_192_cfb1           761

#define SN_camellia_256_cfb1            "CAMELLIA-256-CFB1"
#define LN_camellia_256_cfb1            "camellia-256-cfb1"
#define NID_camellia_256_cfb1           762

#define SN_camellia_128_cfb8            "CAMELLIA-128-CFB8"
#define LN_camellia_128_cfb8            "camellia-128-cfb8"
#define NID_camellia_128_cfb8           763

#define SN_camellia_192_cfb8            "CAMELLIA-192-CFB8"
#define LN_camellia_192_cfb8            "camellia-192-cfb8"
#define NID_camellia_192_cfb8           764

#define SN_camellia_256_cfb8            "CAMELLIA-256-CFB8"
#define LN_camellia_256_cfb8            "camellia-256-cfb8"
#define NID_camellia_256_cfb8           765

#define OBJ_aria                1L,2L,410L,200046L,1L,1L

#define SN_aria_128_ecb         "ARIA-128-ECB"
#define LN_aria_128_ecb         "aria-128-ecb"
#define NID_aria_128_ecb                1065
#define OBJ_aria_128_ecb                OBJ_aria,1L

#define SN_aria_128_cbc         "ARIA-128-CBC"
#define LN_aria_128_cbc         "aria-128-cbc"
#define NID_aria_128_cbc                1066
#define OBJ_aria_128_cbc                OBJ_aria,2L

#define SN_aria_128_cfb128              "ARIA-128-CFB"
#define LN_aria_128_cfb128              "aria-128-cfb"
#define NID_aria_128_cfb128             1067
#define OBJ_aria_128_cfb128             OBJ_aria,3L

#define SN_aria_128_ofb128              "ARIA-128-OFB"
#define LN_aria_128_ofb128              "aria-128-ofb"
#define NID_aria_128_ofb128             1068
#define OBJ_aria_128_ofb128             OBJ_aria,4L

#define SN_aria_128_ctr         "ARIA-128-CTR"
#define LN_aria_128_ctr         "aria-128-ctr"
#define NID_aria_128_ctr                1069
#define OBJ_aria_128_ctr                OBJ_aria,5L

#define SN_aria_192_ecb         "ARIA-192-ECB"
#define LN_aria_192_ecb         "aria-192-ecb"
#define NID_aria_192_ecb                1070
#define OBJ_aria_192_ecb                OBJ_aria,6L

#define SN_aria_192_cbc         "ARIA-192-CBC"
#define LN_aria_192_cbc         "aria-192-cbc"
#define NID_aria_192_cbc                1071
#define OBJ_aria_192_cbc                OBJ_aria,7L

#define SN_aria_192_cfb128              "ARIA-192-CFB"
#define LN_aria_192_cfb128              "aria-192-cfb"
#define NID_aria_192_cfb128             1072
#define OBJ_aria_192_cfb128             OBJ_aria,8L

#define SN_aria_192_ofb128              "ARIA-192-OFB"
#define LN_aria_192_ofb128              "aria-192-ofb"
#define NID_aria_192_ofb128             1073
#define OBJ_aria_192_ofb128             OBJ_aria,9L

#define SN_aria_192_ctr         "ARIA-192-CTR"
#define LN_aria_192_ctr         "aria-192-ctr"
#define NID_aria_192_ctr                1074
#define OBJ_aria_192_ctr                OBJ_aria,10L

#define SN_aria_256_ecb         "ARIA-256-ECB"
#define LN_aria_256_ecb         "aria-256-ecb"
#define NID_aria_256_ecb                1075
#define OBJ_aria_256_ecb                OBJ_aria,11L

#define SN_aria_256_cbc         "ARIA-256-CBC"
#define LN_aria_256_cbc         "aria-256-cbc"
#define NID_aria_256_cbc                1076
#define OBJ_aria_256_cbc                OBJ_aria,12L

#define SN_aria_256_cfb128              "ARIA-256-CFB"
#define LN_aria_256_cfb128              "aria-256-cfb"
#define NID_aria_256_cfb128             1077
#define OBJ_aria_256_cfb128             OBJ_aria,13L

#define SN_aria_256_ofb128              "ARIA-256-OFB"
#define LN_aria_256_ofb128              "aria-256-ofb"
#define NID_aria_256_ofb128             1078
#define OBJ_aria_256_ofb128             OBJ_aria,14L

#define SN_aria_256_ctr         "ARIA-256-CTR"
#define LN_aria_256_ctr         "aria-256-ctr"
#define NID_aria_256_ctr                1079
#define OBJ_aria_256_ctr                OBJ_aria,15L

#define SN_aria_128_cfb1                "ARIA-128-CFB1"
#define LN_aria_128_cfb1                "aria-128-cfb1"
#define NID_aria_128_cfb1               1080

#define SN_aria_192_cfb1                "ARIA-192-CFB1"
#define LN_aria_192_cfb1                "aria-192-cfb1"
#define NID_aria_192_cfb1               1081

#define SN_aria_256_cfb1                "ARIA-256-CFB1"
#define LN_aria_256_cfb1                "aria-256-cfb1"
#define NID_aria_256_cfb1               1082

#define SN_aria_128_cfb8                "ARIA-128-CFB8"
#define LN_aria_128_cfb8                "aria-128-cfb8"
#define NID_aria_128_cfb8               1083

#define SN_aria_192_cfb8                "ARIA-192-CFB8"
#define LN_aria_192_cfb8                "aria-192-cfb8"
#define NID_aria_192_cfb8               1084

#define SN_aria_256_cfb8                "ARIA-256-CFB8"
#define LN_aria_256_cfb8                "aria-256-cfb8"
#define NID_aria_256_cfb8               1085

#define SN_aria_128_ccm         "ARIA-128-CCM"
#define LN_aria_128_ccm         "aria-128-ccm"
#define NID_aria_128_ccm                1120
#define OBJ_aria_128_ccm                OBJ_aria,37L

#define SN_aria_192_ccm         "ARIA-192-CCM"
#define LN_aria_192_ccm         "aria-192-ccm"
#define NID_aria_192_ccm                1121
#define OBJ_aria_192_ccm                OBJ_aria,38L

#define SN_aria_256_ccm         "ARIA-256-CCM"
#define LN_aria_256_ccm         "aria-256-ccm"
#define NID_aria_256_ccm                1122
#define OBJ_aria_256_ccm                OBJ_aria,39L

#define SN_aria_128_gcm         "ARIA-128-GCM"
#define LN_aria_128_gcm         "aria-128-gcm"
#define NID_aria_128_gcm                1123
#define OBJ_aria_128_gcm                OBJ_aria,34L

#define SN_aria_192_gcm         "ARIA-192-GCM"
#define LN_aria_192_gcm         "aria-192-gcm"
#define NID_aria_192_gcm                1124
#define OBJ_aria_192_gcm                OBJ_aria,35L

#define SN_aria_256_gcm         "ARIA-256-GCM"
#define LN_aria_256_gcm         "aria-256-gcm"
#define NID_aria_256_gcm                1125
#define OBJ_aria_256_gcm                OBJ_aria,36L

#define SN_kisa         "KISA"
#define LN_kisa         "kisa"
#define NID_kisa                773
#define OBJ_kisa                OBJ_member_body,410L,200004L

#define SN_seed_ecb             "SEED-ECB"
#define LN_seed_ecb             "seed-ecb"
#define NID_seed_ecb            776
#define OBJ_seed_ecb            OBJ_kisa,1L,3L

#define SN_seed_cbc             "SEED-CBC"
#define LN_seed_cbc             "seed-cbc"
#define NID_seed_cbc            777
#define OBJ_seed_cbc            OBJ_kisa,1L,4L

#define SN_seed_cfb128          "SEED-CFB"
#define LN_seed_cfb128          "seed-cfb"
#define NID_seed_cfb128         779
#define OBJ_seed_cfb128         OBJ_kisa,1L,5L

#define SN_seed_ofb128          "SEED-OFB"
#define LN_seed_ofb128          "seed-ofb"
#define NID_seed_ofb128         778
#define OBJ_seed_ofb128         OBJ_kisa,1L,6L

#define SN_sm4_ecb              "SM4-ECB"
#define LN_sm4_ecb              "sm4-ecb"
#define NID_sm4_ecb             1133
#define OBJ_sm4_ecb             OBJ_sm_scheme,104L,1L

#define SN_sm4_cbc              "SM4-CBC"
#define LN_sm4_cbc              "sm4-cbc"
#define NID_sm4_cbc             1134
#define OBJ_sm4_cbc             OBJ_sm_scheme,104L,2L

#define SN_sm4_ofb128           "SM4-OFB"
#define LN_sm4_ofb128           "sm4-ofb"
#define NID_sm4_ofb128          1135
#define OBJ_sm4_ofb128          OBJ_sm_scheme,104L,3L

#define SN_sm4_cfb128           "SM4-CFB"
#define LN_sm4_cfb128           "sm4-cfb"
#define NID_sm4_cfb128          1137
#define OBJ_sm4_cfb128          OBJ_sm_scheme,104L,4L

#define SN_sm4_cfb1             "SM4-CFB1"
#define LN_sm4_cfb1             "sm4-cfb1"
#define NID_sm4_cfb1            1136
#define OBJ_sm4_cfb1            OBJ_sm_scheme,104L,5L

#define SN_sm4_cfb8             "SM4-CFB8"
#define LN_sm4_cfb8             "sm4-cfb8"
#define NID_sm4_cfb8            1138
#define OBJ_sm4_cfb8            OBJ_sm_scheme,104L,6L

#define SN_sm4_ctr              "SM4-CTR"
#define LN_sm4_ctr              "sm4-ctr"
#define NID_sm4_ctr             1139
#define OBJ_sm4_ctr             OBJ_sm_scheme,104L,7L

#define SN_sm4_gcm              "SM4-GCM"
#define LN_sm4_gcm              "sm4-gcm"
#define NID_sm4_gcm             1248
#define OBJ_sm4_gcm             OBJ_sm_scheme,104L,8L

#define SN_sm4_ccm              "SM4-CCM"
#define LN_sm4_ccm              "sm4-ccm"
#define NID_sm4_ccm             1249
#define OBJ_sm4_ccm             OBJ_sm_scheme,104L,9L

#define SN_sm4_xts              "SM4-XTS"
#define LN_sm4_xts              "sm4-xts"
#define NID_sm4_xts             1290
#define OBJ_sm4_xts             OBJ_sm_scheme,104L,10L

#define SN_hmac         "HMAC"
#define LN_hmac         "hmac"
#define NID_hmac                855

#define SN_cmac         "CMAC"
#define LN_cmac         "cmac"
#define NID_cmac                894

#define SN_rc4_hmac_md5         "RC4-HMAC-MD5"
#define LN_rc4_hmac_md5         "rc4-hmac-md5"
#define NID_rc4_hmac_md5                915

#define SN_aes_128_cbc_hmac_sha1                "AES-128-CBC-HMAC-SHA1"
#define LN_aes_128_cbc_hmac_sha1                "aes-128-cbc-hmac-sha1"
#define NID_aes_128_cbc_hmac_sha1               916

#define SN_aes_192_cbc_hmac_sha1                "AES-192-CBC-HMAC-SHA1"
#define LN_aes_192_cbc_hmac_sha1                "aes-192-cbc-hmac-sha1"
#define NID_aes_192_cbc_hmac_sha1               917

#define SN_aes_256_cbc_hmac_sha1                "AES-256-CBC-HMAC-SHA1"
#define LN_aes_256_cbc_hmac_sha1                "aes-256-cbc-hmac-sha1"
#define NID_aes_256_cbc_hmac_sha1               918

#define SN_aes_128_cbc_hmac_sha256              "AES-128-CBC-HMAC-SHA256"
#define LN_aes_128_cbc_hmac_sha256              "aes-128-cbc-hmac-sha256"
#define NID_aes_128_cbc_hmac_sha256             948

#define SN_aes_192_cbc_hmac_sha256              "AES-192-CBC-HMAC-SHA256"
#define LN_aes_192_cbc_hmac_sha256              "aes-192-cbc-hmac-sha256"
#define NID_aes_192_cbc_hmac_sha256             949

#define SN_aes_256_cbc_hmac_sha256              "AES-256-CBC-HMAC-SHA256"
#define LN_aes_256_cbc_hmac_sha256              "aes-256-cbc-hmac-sha256"
#define NID_aes_256_cbc_hmac_sha256             950

#define SN_chacha20_poly1305            "ChaCha20-Poly1305"
#define LN_chacha20_poly1305            "chacha20-poly1305"
#define NID_chacha20_poly1305           1018

#define SN_chacha20             "ChaCha20"
#define LN_chacha20             "chacha20"
#define NID_chacha20            1019

#define SN_dhpublicnumber               "dhpublicnumber"
#define LN_dhpublicnumber               "X9.42 DH"
#define NID_dhpublicnumber              920
#define OBJ_dhpublicnumber              OBJ_ISO_US,10046L,2L,1L

#define SN_brainpoolP160r1              "brainpoolP160r1"
#define NID_brainpoolP160r1             921
#define OBJ_brainpoolP160r1             1L,3L,36L,3L,3L,2L,8L,1L,1L,1L

#define SN_brainpoolP160t1              "brainpoolP160t1"
#define NID_brainpoolP160t1             922
#define OBJ_brainpoolP160t1             1L,3L,36L,3L,3L,2L,8L,1L,1L,2L

#define SN_brainpoolP192r1              "brainpoolP192r1"
#define NID_brainpoolP192r1             923
#define OBJ_brainpoolP192r1             1L,3L,36L,3L,3L,2L,8L,1L,1L,3L

#define SN_brainpoolP192t1              "brainpoolP192t1"
#define NID_brainpoolP192t1             924
#define OBJ_brainpoolP192t1             1L,3L,36L,3L,3L,2L,8L,1L,1L,4L

#define SN_brainpoolP224r1              "brainpoolP224r1"
#define NID_brainpoolP224r1             925
#define OBJ_brainpoolP224r1             1L,3L,36L,3L,3L,2L,8L,1L,1L,5L

#define SN_brainpoolP224t1              "brainpoolP224t1"
#define NID_brainpoolP224t1             926
#define OBJ_brainpoolP224t1             1L,3L,36L,3L,3L,2L,8L,1L,1L,6L

#define SN_brainpoolP256r1              "brainpoolP256r1"
#define NID_brainpoolP256r1             927
#define OBJ_brainpoolP256r1             1L,3L,36L,3L,3L,2L,8L,1L,1L,7L

#define SN_brainpoolP256r1tls13         "brainpoolP256r1tls13"
#define NID_brainpoolP256r1tls13                1285

#define SN_brainpoolP256t1              "brainpoolP256t1"
#define NID_brainpoolP256t1             928
#define OBJ_brainpoolP256t1             1L,3L,36L,3L,3L,2L,8L,1L,1L,8L

#define SN_brainpoolP320r1              "brainpoolP320r1"
#define NID_brainpoolP320r1             929
#define OBJ_brainpoolP320r1             1L,3L,36L,3L,3L,2L,8L,1L,1L,9L

#define SN_brainpoolP320t1              "brainpoolP320t1"
#define NID_brainpoolP320t1             930
#define OBJ_brainpoolP320t1             1L,3L,36L,3L,3L,2L,8L,1L,1L,10L

#define SN_brainpoolP384r1              "brainpoolP384r1"
#define NID_brainpoolP384r1             931
#define OBJ_brainpoolP384r1             1L,3L,36L,3L,3L,2L,8L,1L,1L,11L

#define SN_brainpoolP384r1tls13         "brainpoolP384r1tls13"
#define NID_brainpoolP384r1tls13                1286

#define SN_brainpoolP384t1              "brainpoolP384t1"
#define NID_brainpoolP384t1             932
#define OBJ_brainpoolP384t1             1L,3L,36L,3L,3L,2L,8L,1L,1L,12L

#define SN_brainpoolP512r1              "brainpoolP512r1"
#define NID_brainpoolP512r1             933
#define OBJ_brainpoolP512r1             1L,3L,36L,3L,3L,2L,8L,1L,1L,13L

#define SN_brainpoolP512r1tls13         "brainpoolP512r1tls13"
#define NID_brainpoolP512r1tls13                1287

#define SN_brainpoolP512t1              "brainpoolP512t1"
#define NID_brainpoolP512t1             934
#define OBJ_brainpoolP512t1             1L,3L,36L,3L,3L,2L,8L,1L,1L,14L

#define OBJ_x9_63_scheme                1L,3L,133L,16L,840L,63L,0L

#define OBJ_secg_scheme         OBJ_certicom_arc,1L

#define SN_dhSinglePass_stdDH_sha1kdf_scheme            "dhSinglePass-stdDH-sha1kdf-scheme"
#define NID_dhSinglePass_stdDH_sha1kdf_scheme           936
#define OBJ_dhSinglePass_stdDH_sha1kdf_scheme           OBJ_x9_63_scheme,2L

#define SN_dhSinglePass_stdDH_sha224kdf_scheme          "dhSinglePass-stdDH-sha224kdf-scheme"
#define NID_dhSinglePass_stdDH_sha224kdf_scheme         937
#define OBJ_dhSinglePass_stdDH_sha224kdf_scheme         OBJ_secg_scheme,11L,0L

#define SN_dhSinglePass_stdDH_sha256kdf_scheme          "dhSinglePass-stdDH-sha256kdf-scheme"
#define NID_dhSinglePass_stdDH_sha256kdf_scheme         938
#define OBJ_dhSinglePass_stdDH_sha256kdf_scheme         OBJ_secg_scheme,11L,1L

#define SN_dhSinglePass_stdDH_sha384kdf_scheme          "dhSinglePass-stdDH-sha384kdf-scheme"
#define NID_dhSinglePass_stdDH_sha384kdf_scheme         939
#define OBJ_dhSinglePass_stdDH_sha384kdf_scheme         OBJ_secg_scheme,11L,2L

#define SN_dhSinglePass_stdDH_sha512kdf_scheme          "dhSinglePass-stdDH-sha512kdf-scheme"
#define NID_dhSinglePass_stdDH_sha512kdf_scheme         940
#define OBJ_dhSinglePass_stdDH_sha512kdf_scheme         OBJ_secg_scheme,11L,3L

#define SN_dhSinglePass_cofactorDH_sha1kdf_scheme               "dhSinglePass-cofactorDH-sha1kdf-scheme"
#define NID_dhSinglePass_cofactorDH_sha1kdf_scheme              941
#define OBJ_dhSinglePass_cofactorDH_sha1kdf_scheme              OBJ_x9_63_scheme,3L

#define SN_dhSinglePass_cofactorDH_sha224kdf_scheme             "dhSinglePass-cofactorDH-sha224kdf-scheme"
#define NID_dhSinglePass_cofactorDH_sha224kdf_scheme            942
#define OBJ_dhSinglePass_cofactorDH_sha224kdf_scheme            OBJ_secg_scheme,14L,0L

#define SN_dhSinglePass_cofactorDH_sha256kdf_scheme             "dhSinglePass-cofactorDH-sha256kdf-scheme"
#define NID_dhSinglePass_cofactorDH_sha256kdf_scheme            943
#define OBJ_dhSinglePass_cofactorDH_sha256kdf_scheme            OBJ_secg_scheme,14L,1L

#define SN_dhSinglePass_cofactorDH_sha384kdf_scheme             "dhSinglePass-cofactorDH-sha384kdf-scheme"
#define NID_dhSinglePass_cofactorDH_sha384kdf_scheme            944
#define OBJ_dhSinglePass_cofactorDH_sha384kdf_scheme            OBJ_secg_scheme,14L,2L

#define SN_dhSinglePass_cofactorDH_sha512kdf_scheme             "dhSinglePass-cofactorDH-sha512kdf-scheme"
#define NID_dhSinglePass_cofactorDH_sha512kdf_scheme            945
#define OBJ_dhSinglePass_cofactorDH_sha512kdf_scheme            OBJ_secg_scheme,14L,3L

#define SN_dh_std_kdf           "dh-std-kdf"
#define NID_dh_std_kdf          946

#define SN_dh_cofactor_kdf              "dh-cofactor-kdf"
#define NID_dh_cofactor_kdf             947

#define SN_ct_precert_scts              "ct_precert_scts"
#define LN_ct_precert_scts              "CT Precertificate SCTs"
#define NID_ct_precert_scts             951
#define OBJ_ct_precert_scts             1L,3L,6L,1L,4L,1L,11129L,2L,4L,2L

#define SN_ct_precert_poison            "ct_precert_poison"
#define LN_ct_precert_poison            "CT Precertificate Poison"
#define NID_ct_precert_poison           952
#define OBJ_ct_precert_poison           1L,3L,6L,1L,4L,1L,11129L,2L,4L,3L

#define SN_ct_precert_signer            "ct_precert_signer"
#define LN_ct_precert_signer            "CT Precertificate Signer"
#define NID_ct_precert_signer           953
#define OBJ_ct_precert_signer           1L,3L,6L,1L,4L,1L,11129L,2L,4L,4L

#define SN_ct_cert_scts         "ct_cert_scts"
#define LN_ct_cert_scts         "CT Certificate SCTs"
#define NID_ct_cert_scts                954
#define OBJ_ct_cert_scts                1L,3L,6L,1L,4L,1L,11129L,2L,4L,5L

#define SN_jurisdictionLocalityName             "jurisdictionL"
#define LN_jurisdictionLocalityName             "jurisdictionLocalityName"
#define NID_jurisdictionLocalityName            955
#define OBJ_jurisdictionLocalityName            OBJ_ms_corp,60L,2L,1L,1L

#define SN_jurisdictionStateOrProvinceName              "jurisdictionST"
#define LN_jurisdictionStateOrProvinceName              "jurisdictionStateOrProvinceName"
#define NID_jurisdictionStateOrProvinceName             956
#define OBJ_jurisdictionStateOrProvinceName             OBJ_ms_corp,60L,2L,1L,2L

#define SN_jurisdictionCountryName              "jurisdictionC"
#define LN_jurisdictionCountryName              "jurisdictionCountryName"
#define NID_jurisdictionCountryName             957
#define OBJ_jurisdictionCountryName             OBJ_ms_corp,60L,2L,1L,3L

#define SN_id_scrypt            "id-scrypt"
#define LN_id_scrypt            "scrypt"
#define NID_id_scrypt           973
#define OBJ_id_scrypt           1L,3L,6L,1L,4L,1L,11591L,4L,11L

#define SN_tls1_prf             "TLS1-PRF"
#define LN_tls1_prf             "tls1-prf"
#define NID_tls1_prf            1021

#define SN_hkdf         "HKDF"
#define LN_hkdf         "hkdf"
#define NID_hkdf                1036

#define SN_sshkdf               "SSHKDF"
#define LN_sshkdf               "sshkdf"
#define NID_sshkdf              1203

#define SN_sskdf                "SSKDF"
#define LN_sskdf                "sskdf"
#define NID_sskdf               1205

#define SN_x942kdf              "X942KDF"
#define LN_x942kdf              "x942kdf"
#define NID_x942kdf             1207

#define SN_x963kdf              "X963KDF"
#define LN_x963kdf              "x963kdf"
#define NID_x963kdf             1206

#define SN_id_pkinit            "id-pkinit"
#define NID_id_pkinit           1031
#define OBJ_id_pkinit           1L,3L,6L,1L,5L,2L,3L

#define SN_pkInitClientAuth             "pkInitClientAuth"
#define LN_pkInitClientAuth             "PKINIT Client Auth"
#define NID_pkInitClientAuth            1032
#define OBJ_pkInitClientAuth            OBJ_id_pkinit,4L

#define SN_pkInitKDC            "pkInitKDC"
#define LN_pkInitKDC            "Signing KDC Response"
#define NID_pkInitKDC           1033
#define OBJ_pkInitKDC           OBJ_id_pkinit,5L

#define SN_X25519               "X25519"
#define NID_X25519              1034
#define OBJ_X25519              1L,3L,101L,110L

#define SN_X448         "X448"
#define NID_X448                1035
#define OBJ_X448                1L,3L,101L,111L

#define SN_ED25519              "ED25519"
#define NID_ED25519             1087
#define OBJ_ED25519             1L,3L,101L,112L

#define SN_ED448                "ED448"
#define NID_ED448               1088
#define OBJ_ED448               1L,3L,101L,113L

#define SN_kx_rsa               "KxRSA"
#define LN_kx_rsa               "kx-rsa"
#define NID_kx_rsa              1037

#define SN_kx_ecdhe             "KxECDHE"
#define LN_kx_ecdhe             "kx-ecdhe"
#define NID_kx_ecdhe            1038

#define SN_kx_dhe               "KxDHE"
#define LN_kx_dhe               "kx-dhe"
#define NID_kx_dhe              1039

#define SN_kx_ecdhe_psk         "KxECDHE-PSK"
#define LN_kx_ecdhe_psk         "kx-ecdhe-psk"
#define NID_kx_ecdhe_psk                1040

#define SN_kx_dhe_psk           "KxDHE-PSK"
#define LN_kx_dhe_psk           "kx-dhe-psk"
#define NID_kx_dhe_psk          1041

#define SN_kx_rsa_psk           "KxRSA_PSK"
#define LN_kx_rsa_psk           "kx-rsa-psk"
#define NID_kx_rsa_psk          1042

#define SN_kx_psk               "KxPSK"
#define LN_kx_psk               "kx-psk"
#define NID_kx_psk              1043

#define SN_kx_srp               "KxSRP"
#define LN_kx_srp               "kx-srp"
#define NID_kx_srp              1044

#define SN_kx_gost              "KxGOST"
#define LN_kx_gost              "kx-gost"
#define NID_kx_gost             1045

#define SN_kx_gost18            "KxGOST18"
#define LN_kx_gost18            "kx-gost18"
#define NID_kx_gost18           1218

#define SN_kx_any               "KxANY"
#define LN_kx_any               "kx-any"
#define NID_kx_any              1063

#define SN_auth_rsa             "AuthRSA"
#define LN_auth_rsa             "auth-rsa"
#define NID_auth_rsa            1046

#define SN_auth_ecdsa           "AuthECDSA"
#define LN_auth_ecdsa           "auth-ecdsa"
#define NID_auth_ecdsa          1047

#define SN_auth_psk             "AuthPSK"
#define LN_auth_psk             "auth-psk"
#define NID_auth_psk            1048

#define SN_auth_dss             "AuthDSS"
#define LN_auth_dss             "auth-dss"
#define NID_auth_dss            1049

#define SN_auth_gost01          "AuthGOST01"
#define LN_auth_gost01          "auth-gost01"
#define NID_auth_gost01         1050

#define SN_auth_gost12          "AuthGOST12"
#define LN_auth_gost12          "auth-gost12"
#define NID_auth_gost12         1051

#define SN_auth_srp             "AuthSRP"
#define LN_auth_srp             "auth-srp"
#define NID_auth_srp            1052

#define SN_auth_null            "AuthNULL"
#define LN_auth_null            "auth-null"
#define NID_auth_null           1053

#define SN_auth_any             "AuthANY"
#define LN_auth_any             "auth-any"
#define NID_auth_any            1064

#define SN_poly1305             "Poly1305"
#define LN_poly1305             "poly1305"
#define NID_poly1305            1061

#define SN_siphash              "SipHash"
#define LN_siphash              "siphash"
#define NID_siphash             1062

#define SN_ffdhe2048            "ffdhe2048"
#define NID_ffdhe2048           1126

#define SN_ffdhe3072            "ffdhe3072"
#define NID_ffdhe3072           1127

#define SN_ffdhe4096            "ffdhe4096"
#define NID_ffdhe4096           1128

#define SN_ffdhe6144            "ffdhe6144"
#define NID_ffdhe6144           1129

#define SN_ffdhe8192            "ffdhe8192"
#define NID_ffdhe8192           1130

#define SN_modp_1536            "modp_1536"
#define NID_modp_1536           1212

#define SN_modp_2048            "modp_2048"
#define NID_modp_2048           1213

#define SN_modp_3072            "modp_3072"
#define NID_modp_3072           1214

#define SN_modp_4096            "modp_4096"
#define NID_modp_4096           1215

#define SN_modp_6144            "modp_6144"
#define NID_modp_6144           1216

#define SN_modp_8192            "modp_8192"
#define NID_modp_8192           1217

#define SN_ISO_UA               "ISO-UA"
#define NID_ISO_UA              1150
#define OBJ_ISO_UA              OBJ_member_body,804L

#define SN_ua_pki               "ua-pki"
#define NID_ua_pki              1151
#define OBJ_ua_pki              OBJ_ISO_UA,2L,1L,1L,1L

#define SN_dstu28147            "dstu28147"
#define LN_dstu28147            "DSTU Gost 28147-2009"
#define NID_dstu28147           1152
#define OBJ_dstu28147           OBJ_ua_pki,1L,1L,1L

#define SN_dstu28147_ofb                "dstu28147-ofb"
#define LN_dstu28147_ofb                "DSTU Gost 28147-2009 OFB mode"
#define NID_dstu28147_ofb               1153
#define OBJ_dstu28147_ofb               OBJ_dstu28147,2L

#define SN_dstu28147_cfb                "dstu28147-cfb"
#define LN_dstu28147_cfb                "DSTU Gost 28147-2009 CFB mode"
#define NID_dstu28147_cfb               1154
#define OBJ_dstu28147_cfb               OBJ_dstu28147,3L

#define SN_dstu28147_wrap               "dstu28147-wrap"
#define LN_dstu28147_wrap               "DSTU Gost 28147-2009 key wrap"
#define NID_dstu28147_wrap              1155
#define OBJ_dstu28147_wrap              OBJ_dstu28147,5L

#define SN_hmacWithDstu34311            "hmacWithDstu34311"
#define LN_hmacWithDstu34311            "HMAC DSTU Gost 34311-95"
#define NID_hmacWithDstu34311           1156
#define OBJ_hmacWithDstu34311           OBJ_ua_pki,1L,1L,2L

#define SN_dstu34311            "dstu34311"
#define LN_dstu34311            "DSTU Gost 34311-95"
#define NID_dstu34311           1157
#define OBJ_dstu34311           OBJ_ua_pki,1L,2L,1L

#define SN_dstu4145le           "dstu4145le"
#define LN_dstu4145le           "DSTU 4145-2002 little endian"
#define NID_dstu4145le          1158
#define OBJ_dstu4145le          OBJ_ua_pki,1L,3L,1L,1L

#define SN_dstu4145be           "dstu4145be"
#define LN_dstu4145be           "DSTU 4145-2002 big endian"
#define NID_dstu4145be          1159
#define OBJ_dstu4145be          OBJ_dstu4145le,1L,1L

#define SN_uacurve0             "uacurve0"
#define LN_uacurve0             "DSTU curve 0"
#define NID_uacurve0            1160
#define OBJ_uacurve0            OBJ_dstu4145le,2L,0L

#define SN_uacurve1             "uacurve1"
#define LN_uacurve1             "DSTU curve 1"
#define NID_uacurve1            1161
#define OBJ_uacurve1            OBJ_dstu4145le,2L,1L

#define SN_uacurve2             "uacurve2"
#define LN_uacurve2             "DSTU curve 2"
#define NID_uacurve2            1162
#define OBJ_uacurve2            OBJ_dstu4145le,2L,2L

#define SN_uacurve3             "uacurve3"
#define LN_uacurve3             "DSTU curve 3"
#define NID_uacurve3            1163
#define OBJ_uacurve3            OBJ_dstu4145le,2L,3L

#define SN_uacurve4             "uacurve4"
#define LN_uacurve4             "DSTU curve 4"
#define NID_uacurve4            1164
#define OBJ_uacurve4            OBJ_dstu4145le,2L,4L

#define SN_uacurve5             "uacurve5"
#define LN_uacurve5             "DSTU curve 5"
#define NID_uacurve5            1165
#define OBJ_uacurve5            OBJ_dstu4145le,2L,5L

#define SN_uacurve6             "uacurve6"
#define LN_uacurve6             "DSTU curve 6"
#define NID_uacurve6            1166
#define OBJ_uacurve6            OBJ_dstu4145le,2L,6L

#define SN_uacurve7             "uacurve7"
#define LN_uacurve7             "DSTU curve 7"
#define NID_uacurve7            1167
#define OBJ_uacurve7            OBJ_dstu4145le,2L,7L

#define SN_uacurve8             "uacurve8"
#define LN_uacurve8             "DSTU curve 8"
#define NID_uacurve8            1168
#define OBJ_uacurve8            OBJ_dstu4145le,2L,8L

#define SN_uacurve9             "uacurve9"
#define LN_uacurve9             "DSTU curve 9"
#define NID_uacurve9            1169
#define OBJ_uacurve9            OBJ_dstu4145le,2L,9L

#define SN_aes_128_siv          "AES-128-SIV"
#define LN_aes_128_siv          "aes-128-siv"
#define NID_aes_128_siv         1198

#define SN_aes_192_siv          "AES-192-SIV"
#define LN_aes_192_siv          "aes-192-siv"
#define NID_aes_192_siv         1199

#define SN_aes_256_siv          "AES-256-SIV"
#define LN_aes_256_siv          "aes-256-siv"
#define NID_aes_256_siv         1200

#define SN_oracle               "oracle-organization"
#define LN_oracle               "Oracle organization"
#define NID_oracle              1282
#define OBJ_oracle              OBJ_joint_iso_itu_t,16L,840L,1L,113894L

#define SN_oracle_jdk_trustedkeyusage           "oracle-jdk-trustedkeyusage"
#define LN_oracle_jdk_trustedkeyusage           "Trusted key usage (Oracle)"
#define NID_oracle_jdk_trustedkeyusage          1283
#define OBJ_oracle_jdk_trustedkeyusage          OBJ_oracle,746875L,1L,1L

#define SN_brotli               "brotli"
#define LN_brotli               "Brotli compression"
#define NID_brotli              1288

#define SN_zstd         "zstd"
#define LN_zstd         "Zstandard compression"
#define NID_zstd                1289

#define SN_tcg          "tcg"
#define LN_tcg          "Trusted Computing Group"
#define NID_tcg         1324
#define OBJ_tcg         2L,23L,133L

#define SN_tcg_tcpaSpecVersion          "tcg-tcpaSpecVersion"
#define NID_tcg_tcpaSpecVersion         1325
#define OBJ_tcg_tcpaSpecVersion         OBJ_tcg,1L

#define SN_tcg_attribute                "tcg-attribute"
#define LN_tcg_attribute                "Trusted Computing Group Attributes"
#define NID_tcg_attribute               1326
#define OBJ_tcg_attribute               OBJ_tcg,2L

#define SN_tcg_protocol         "tcg-protocol"
#define LN_tcg_protocol         "Trusted Computing Group Protocols"
#define NID_tcg_protocol                1327
#define OBJ_tcg_protocol                OBJ_tcg,3L

#define SN_tcg_algorithm                "tcg-algorithm"
#define LN_tcg_algorithm                "Trusted Computing Group Algorithms"
#define NID_tcg_algorithm               1328
#define OBJ_tcg_algorithm               OBJ_tcg,4L

#define SN_tcg_platformClass            "tcg-platformClass"
#define LN_tcg_platformClass            "Trusted Computing Group Platform Classes"
#define NID_tcg_platformClass           1329
#define OBJ_tcg_platformClass           OBJ_tcg,5L

#define SN_tcg_ce               "tcg-ce"
#define LN_tcg_ce               "Trusted Computing Group Certificate Extensions"
#define NID_tcg_ce              1330
#define OBJ_tcg_ce              OBJ_tcg,6L

#define SN_tcg_kp               "tcg-kp"
#define LN_tcg_kp               "Trusted Computing Group Key Purposes"
#define NID_tcg_kp              1331
#define OBJ_tcg_kp              OBJ_tcg,8L

#define SN_tcg_ca               "tcg-ca"
#define LN_tcg_ca               "Trusted Computing Group Certificate Policies"
#define NID_tcg_ca              1332
#define OBJ_tcg_ca              OBJ_tcg,11L

#define SN_tcg_address          "tcg-address"
#define LN_tcg_address          "Trusted Computing Group Address Formats"
#define NID_tcg_address         1333
#define OBJ_tcg_address         OBJ_tcg,17L

#define SN_tcg_registry         "tcg-registry"
#define LN_tcg_registry         "Trusted Computing Group Registry"
#define NID_tcg_registry                1334
#define OBJ_tcg_registry                OBJ_tcg,18L

#define SN_tcg_traits           "tcg-traits"
#define LN_tcg_traits           "Trusted Computing Group Traits"
#define NID_tcg_traits          1335
#define OBJ_tcg_traits          OBJ_tcg,19L

#define SN_tcg_common           "tcg-common"
#define LN_tcg_common           "Trusted Computing Group Common"
#define NID_tcg_common          1336
#define OBJ_tcg_common          OBJ_tcg_platformClass,1L

#define SN_tcg_at_platformManufacturerStr               "tcg-at-platformManufacturerStr"
#define LN_tcg_at_platformManufacturerStr               "TCG Platform Manufacturer String"
#define NID_tcg_at_platformManufacturerStr              1337
#define OBJ_tcg_at_platformManufacturerStr              OBJ_tcg_common,1L

#define SN_tcg_at_platformManufacturerId                "tcg-at-platformManufacturerId"
#define LN_tcg_at_platformManufacturerId                "TCG Platform Manufacturer ID"
#define NID_tcg_at_platformManufacturerId               1338
#define OBJ_tcg_at_platformManufacturerId               OBJ_tcg_common,2L

#define SN_tcg_at_platformConfigUri             "tcg-at-platformConfigUri"
#define LN_tcg_at_platformConfigUri             "TCG Platform Configuration URI"
#define NID_tcg_at_platformConfigUri            1339
#define OBJ_tcg_at_platformConfigUri            OBJ_tcg_common,3L

#define SN_tcg_at_platformModel         "tcg-at-platformModel"
#define LN_tcg_at_platformModel         "TCG Platform Model"
#define NID_tcg_at_platformModel                1340
#define OBJ_tcg_at_platformModel                OBJ_tcg_common,4L

#define SN_tcg_at_platformVersion               "tcg-at-platformVersion"
#define LN_tcg_at_platformVersion               "TCG Platform Version"
#define NID_tcg_at_platformVersion              1341
#define OBJ_tcg_at_platformVersion              OBJ_tcg_common,5L

#define SN_tcg_at_platformSerial                "tcg-at-platformSerial"
#define LN_tcg_at_platformSerial                "TCG Platform Serial Number"
#define NID_tcg_at_platformSerial               1342
#define OBJ_tcg_at_platformSerial               OBJ_tcg_common,6L

#define SN_tcg_at_platformConfiguration         "tcg-at-platformConfiguration"
#define LN_tcg_at_platformConfiguration         "TCG Platform Configuration"
#define NID_tcg_at_platformConfiguration                1343
#define OBJ_tcg_at_platformConfiguration                OBJ_tcg_common,7L

#define SN_tcg_at_platformIdentifier            "tcg-at-platformIdentifier"
#define LN_tcg_at_platformIdentifier            "TCG Platform Identifier"
#define NID_tcg_at_platformIdentifier           1344
#define OBJ_tcg_at_platformIdentifier           OBJ_tcg_common,8L

#define SN_tcg_at_tpmManufacturer               "tcg-at-tpmManufacturer"
#define LN_tcg_at_tpmManufacturer               "TPM Manufacturer"
#define NID_tcg_at_tpmManufacturer              1345
#define OBJ_tcg_at_tpmManufacturer              OBJ_tcg_attribute,1L

#define SN_tcg_at_tpmModel              "tcg-at-tpmModel"
#define LN_tcg_at_tpmModel              "TPM Model"
#define NID_tcg_at_tpmModel             1346
#define OBJ_tcg_at_tpmModel             OBJ_tcg_attribute,2L

#define SN_tcg_at_tpmVersion            "tcg-at-tpmVersion"
#define LN_tcg_at_tpmVersion            "TPM Version"
#define NID_tcg_at_tpmVersion           1347
#define OBJ_tcg_at_tpmVersion           OBJ_tcg_attribute,3L

#define SN_tcg_at_securityQualities             "tcg-at-securityQualities"
#define LN_tcg_at_securityQualities             "Security Qualities"
#define NID_tcg_at_securityQualities            1348
#define OBJ_tcg_at_securityQualities            OBJ_tcg_attribute,10L

#define SN_tcg_at_tpmProtectionProfile          "tcg-at-tpmProtectionProfile"
#define LN_tcg_at_tpmProtectionProfile          "TPM Protection Profile"
#define NID_tcg_at_tpmProtectionProfile         1349
#define OBJ_tcg_at_tpmProtectionProfile         OBJ_tcg_attribute,11L

#define SN_tcg_at_tpmSecurityTarget             "tcg-at-tpmSecurityTarget"
#define LN_tcg_at_tpmSecurityTarget             "TPM Security Target"
#define NID_tcg_at_tpmSecurityTarget            1350
#define OBJ_tcg_at_tpmSecurityTarget            OBJ_tcg_attribute,12L

#define SN_tcg_at_tbbProtectionProfile          "tcg-at-tbbProtectionProfile"
#define LN_tcg_at_tbbProtectionProfile          "TBB Protection Profile"
#define NID_tcg_at_tbbProtectionProfile         1351
#define OBJ_tcg_at_tbbProtectionProfile         OBJ_tcg_attribute,13L

#define SN_tcg_at_tbbSecurityTarget             "tcg-at-tbbSecurityTarget"
#define LN_tcg_at_tbbSecurityTarget             "TBB Security Target"
#define NID_tcg_at_tbbSecurityTarget            1352
#define OBJ_tcg_at_tbbSecurityTarget            OBJ_tcg_attribute,14L

#define SN_tcg_at_tpmIdLabel            "tcg-at-tpmIdLabel"
#define LN_tcg_at_tpmIdLabel            "TPM ID Label"
#define NID_tcg_at_tpmIdLabel           1353
#define OBJ_tcg_at_tpmIdLabel           OBJ_tcg_attribute,15L

#define SN_tcg_at_tpmSpecification              "tcg-at-tpmSpecification"
#define LN_tcg_at_tpmSpecification              "TPM Specification"
#define NID_tcg_at_tpmSpecification             1354
#define OBJ_tcg_at_tpmSpecification             OBJ_tcg_attribute,16L

#define SN_tcg_at_tcgPlatformSpecification              "tcg-at-tcgPlatformSpecification"
#define LN_tcg_at_tcgPlatformSpecification              "TPM Platform Specification"
#define NID_tcg_at_tcgPlatformSpecification             1355
#define OBJ_tcg_at_tcgPlatformSpecification             OBJ_tcg_attribute,17L

#define SN_tcg_at_tpmSecurityAssertions         "tcg-at-tpmSecurityAssertions"
#define LN_tcg_at_tpmSecurityAssertions         "TPM Security Assertions"
#define NID_tcg_at_tpmSecurityAssertions                1356
#define OBJ_tcg_at_tpmSecurityAssertions                OBJ_tcg_attribute,18L

#define SN_tcg_at_tbbSecurityAssertions         "tcg-at-tbbSecurityAssertions"
#define LN_tcg_at_tbbSecurityAssertions         "TBB Security Assertions"
#define NID_tcg_at_tbbSecurityAssertions                1357
#define OBJ_tcg_at_tbbSecurityAssertions                OBJ_tcg_attribute,19L

#define SN_tcg_at_tcgCredentialSpecification            "tcg-at-tcgCredentialSpecification"
#define LN_tcg_at_tcgCredentialSpecification            "TCG Credential Specification"
#define NID_tcg_at_tcgCredentialSpecification           1358
#define OBJ_tcg_at_tcgCredentialSpecification           OBJ_tcg_attribute,23L

#define SN_tcg_at_tcgCredentialType             "tcg-at-tcgCredentialType"
#define LN_tcg_at_tcgCredentialType             "TCG Credential Type"
#define NID_tcg_at_tcgCredentialType            1359
#define OBJ_tcg_at_tcgCredentialType            OBJ_tcg_attribute,25L

#define SN_tcg_at_previousPlatformCertificates          "tcg-at-previousPlatformCertificates"
#define LN_tcg_at_previousPlatformCertificates          "TCG Previous Platform Certificates"
#define NID_tcg_at_previousPlatformCertificates         1360
#define OBJ_tcg_at_previousPlatformCertificates         OBJ_tcg_attribute,26L

#define SN_tcg_at_tbbSecurityAssertions_v3              "tcg-at-tbbSecurityAssertions-v3"
#define LN_tcg_at_tbbSecurityAssertions_v3              "TCG TBB Security Assertions V3"
#define NID_tcg_at_tbbSecurityAssertions_v3             1361
#define OBJ_tcg_at_tbbSecurityAssertions_v3             OBJ_tcg_attribute,27L

#define SN_tcg_at_cryptographicAnchors          "tcg-at-cryptographicAnchors"
#define LN_tcg_at_cryptographicAnchors          "TCG Cryptographic Anchors"
#define NID_tcg_at_cryptographicAnchors         1362
#define OBJ_tcg_at_cryptographicAnchors         OBJ_tcg_attribute,28L

#define SN_tcg_at_platformConfiguration_v1              "tcg-at-platformConfiguration-v1"
#define LN_tcg_at_platformConfiguration_v1              "Platform Configuration Version 1"
#define NID_tcg_at_platformConfiguration_v1             1363
#define OBJ_tcg_at_platformConfiguration_v1             OBJ_tcg_at_platformConfiguration,1L

#define SN_tcg_at_platformConfiguration_v2              "tcg-at-platformConfiguration-v2"
#define LN_tcg_at_platformConfiguration_v2              "Platform Configuration Version 2"
#define NID_tcg_at_platformConfiguration_v2             1364
#define OBJ_tcg_at_platformConfiguration_v2             OBJ_tcg_at_platformConfiguration,2L

#define SN_tcg_at_platformConfiguration_v3              "tcg-at-platformConfiguration-v3"
#define LN_tcg_at_platformConfiguration_v3              "Platform Configuration Version 3"
#define NID_tcg_at_platformConfiguration_v3             1365
#define OBJ_tcg_at_platformConfiguration_v3             OBJ_tcg_at_platformConfiguration,3L

#define SN_tcg_at_platformConfigUri_v3          "tcg-at-platformConfigUri-v3"
#define LN_tcg_at_platformConfigUri_v3          "Platform Configuration URI Version 3"
#define NID_tcg_at_platformConfigUri_v3         1366
#define OBJ_tcg_at_platformConfigUri_v3         OBJ_tcg_at_platformConfiguration,4L

#define SN_tcg_algorithm_null           "tcg-algorithm-null"
#define LN_tcg_algorithm_null           "TCG NULL Algorithm"
#define NID_tcg_algorithm_null          1367
#define OBJ_tcg_algorithm_null          OBJ_tcg_algorithm,1L

#define SN_tcg_kp_EKCertificate         "tcg-kp-EKCertificate"
#define LN_tcg_kp_EKCertificate         "Endorsement Key Certificate"
#define NID_tcg_kp_EKCertificate                1368
#define OBJ_tcg_kp_EKCertificate                OBJ_tcg_kp,1L

#define SN_tcg_kp_PlatformAttributeCertificate          "tcg-kp-PlatformAttributeCertificate"
#define LN_tcg_kp_PlatformAttributeCertificate          "Platform Attribute Certificate"
#define NID_tcg_kp_PlatformAttributeCertificate         1369
#define OBJ_tcg_kp_PlatformAttributeCertificate         OBJ_tcg_kp,2L

#define SN_tcg_kp_AIKCertificate                "tcg-kp-AIKCertificate"
#define LN_tcg_kp_AIKCertificate                "Attestation Identity Key Certificate"
#define NID_tcg_kp_AIKCertificate               1370
#define OBJ_tcg_kp_AIKCertificate               OBJ_tcg_kp,3L

#define SN_tcg_kp_PlatformKeyCertificate                "tcg-kp-PlatformKeyCertificate"
#define LN_tcg_kp_PlatformKeyCertificate                "Platform Key Certificate"
#define NID_tcg_kp_PlatformKeyCertificate               1371
#define OBJ_tcg_kp_PlatformKeyCertificate               OBJ_tcg_kp,4L

#define SN_tcg_kp_DeltaPlatformAttributeCertificate             "tcg-kp-DeltaPlatformAttributeCertificate"
#define LN_tcg_kp_DeltaPlatformAttributeCertificate             "Delta Platform Attribute Certificate"
#define NID_tcg_kp_DeltaPlatformAttributeCertificate            1372
#define OBJ_tcg_kp_DeltaPlatformAttributeCertificate            OBJ_tcg_kp,5L

#define SN_tcg_kp_DeltaPlatformKeyCertificate           "tcg-kp-DeltaPlatformKeyCertificate"
#define LN_tcg_kp_DeltaPlatformKeyCertificate           "Delta Platform Key Certificate"
#define NID_tcg_kp_DeltaPlatformKeyCertificate          1373
#define OBJ_tcg_kp_DeltaPlatformKeyCertificate          OBJ_tcg_kp,6L

#define SN_tcg_kp_AdditionalPlatformAttributeCertificate                "tcg-kp-AdditionalPlatformAttributeCertificate"
#define LN_tcg_kp_AdditionalPlatformAttributeCertificate                "Additional Platform Attribute Certificate"
#define NID_tcg_kp_AdditionalPlatformAttributeCertificate               1374
#define OBJ_tcg_kp_AdditionalPlatformAttributeCertificate               OBJ_tcg_kp,7L

#define SN_tcg_kp_AdditionalPlatformKeyCertificate              "tcg-kp-AdditionalPlatformKeyCertificate"
#define LN_tcg_kp_AdditionalPlatformKeyCertificate              "Additional Platform Key Certificate"
#define NID_tcg_kp_AdditionalPlatformKeyCertificate             1375
#define OBJ_tcg_kp_AdditionalPlatformKeyCertificate             OBJ_tcg_kp,8L

#define SN_tcg_ce_relevantCredentials           "tcg-ce-relevantCredentials"
#define LN_tcg_ce_relevantCredentials           "Relevant Credentials"
#define NID_tcg_ce_relevantCredentials          1376
#define OBJ_tcg_ce_relevantCredentials          OBJ_tcg_ce,2L

#define SN_tcg_ce_relevantManifests             "tcg-ce-relevantManifests"
#define LN_tcg_ce_relevantManifests             "Relevant Manifests"
#define NID_tcg_ce_relevantManifests            1377
#define OBJ_tcg_ce_relevantManifests            OBJ_tcg_ce,3L

#define SN_tcg_ce_virtualPlatformAttestationService             "tcg-ce-virtualPlatformAttestationService"
#define LN_tcg_ce_virtualPlatformAttestationService             "Virtual Platform Attestation Service"
#define NID_tcg_ce_virtualPlatformAttestationService            1378
#define OBJ_tcg_ce_virtualPlatformAttestationService            OBJ_tcg_ce,4L

#define SN_tcg_ce_migrationControllerAttestationService         "tcg-ce-migrationControllerAttestationService"
#define LN_tcg_ce_migrationControllerAttestationService         "Migration Controller Attestation Service"
#define NID_tcg_ce_migrationControllerAttestationService                1379
#define OBJ_tcg_ce_migrationControllerAttestationService                OBJ_tcg_ce,5L

#define SN_tcg_ce_migrationControllerRegistrationService                "tcg-ce-migrationControllerRegistrationService"
#define LN_tcg_ce_migrationControllerRegistrationService                "Migration Controller Registration Service"
#define NID_tcg_ce_migrationControllerRegistrationService               1380
#define OBJ_tcg_ce_migrationControllerRegistrationService               OBJ_tcg_ce,6L

#define SN_tcg_ce_virtualPlatformBackupService          "tcg-ce-virtualPlatformBackupService"
#define LN_tcg_ce_virtualPlatformBackupService          "Virtual Platform Backup Service"
#define NID_tcg_ce_virtualPlatformBackupService         1381
#define OBJ_tcg_ce_virtualPlatformBackupService         OBJ_tcg_ce,7L

#define SN_tcg_prt_tpmIdProtocol                "tcg-prt-tpmIdProtocol"
#define LN_tcg_prt_tpmIdProtocol                "TCG TPM Protocol"
#define NID_tcg_prt_tpmIdProtocol               1382
#define OBJ_tcg_prt_tpmIdProtocol               OBJ_tcg_protocol,1L

#define SN_tcg_address_ethernetmac              "tcg-address-ethernetmac"
#define LN_tcg_address_ethernetmac              "Ethernet MAC Address"
#define NID_tcg_address_ethernetmac             1383
#define OBJ_tcg_address_ethernetmac             OBJ_tcg_address,1L

#define SN_tcg_address_wlanmac          "tcg-address-wlanmac"
#define LN_tcg_address_wlanmac          "WLAN MAC Address"
#define NID_tcg_address_wlanmac         1384
#define OBJ_tcg_address_wlanmac         OBJ_tcg_address,2L

#define SN_tcg_address_bluetoothmac             "tcg-address-bluetoothmac"
#define LN_tcg_address_bluetoothmac             "Bluetooth MAC Address"
#define NID_tcg_address_bluetoothmac            1385
#define OBJ_tcg_address_bluetoothmac            OBJ_tcg_address,3L

#define SN_tcg_registry_componentClass          "tcg-registry-componentClass"
#define LN_tcg_registry_componentClass          "TCG Component Class"
#define NID_tcg_registry_componentClass         1386
#define OBJ_tcg_registry_componentClass         OBJ_tcg_registry,3L

#define SN_tcg_registry_componentClass_tcg              "tcg-registry-componentClass-tcg"
#define LN_tcg_registry_componentClass_tcg              "Trusted Computed Group Registry"
#define NID_tcg_registry_componentClass_tcg             1387
#define OBJ_tcg_registry_componentClass_tcg             OBJ_tcg_registry_componentClass,1L

#define SN_tcg_registry_componentClass_ietf             "tcg-registry-componentClass-ietf"
#define LN_tcg_registry_componentClass_ietf             "Internet Engineering Task Force Registry"
#define NID_tcg_registry_componentClass_ietf            1388
#define OBJ_tcg_registry_componentClass_ietf            OBJ_tcg_registry_componentClass,2L

#define SN_tcg_registry_componentClass_dmtf             "tcg-registry-componentClass-dmtf"
#define LN_tcg_registry_componentClass_dmtf             "Distributed Management Task Force Registry"
#define NID_tcg_registry_componentClass_dmtf            1389
#define OBJ_tcg_registry_componentClass_dmtf            OBJ_tcg_registry_componentClass,3L

#define SN_tcg_registry_componentClass_pcie             "tcg-registry-componentClass-pcie"
#define LN_tcg_registry_componentClass_pcie             "PCIE Component Class"
#define NID_tcg_registry_componentClass_pcie            1390
#define OBJ_tcg_registry_componentClass_pcie            OBJ_tcg_registry_componentClass,4L

#define SN_tcg_registry_componentClass_disk             "tcg-registry-componentClass-disk"
#define LN_tcg_registry_componentClass_disk             "Disk Component Class"
#define NID_tcg_registry_componentClass_disk            1391
#define OBJ_tcg_registry_componentClass_disk            OBJ_tcg_registry_componentClass,5L

#define SN_tcg_cap_verifiedPlatformCertificate          "tcg-cap-verifiedPlatformCertificate"
#define LN_tcg_cap_verifiedPlatformCertificate          "TCG Verified Platform Certificate CA Policy"
#define NID_tcg_cap_verifiedPlatformCertificate         1392
#define OBJ_tcg_cap_verifiedPlatformCertificate         OBJ_tcg_ca,4L

#define SN_tcg_tr_ID            "tcg-tr-ID"
#define LN_tcg_tr_ID            "TCG Trait Identifiers"
#define NID_tcg_tr_ID           1393
#define OBJ_tcg_tr_ID           OBJ_tcg_traits,1L

#define SN_tcg_tr_category              "tcg-tr-category"
#define LN_tcg_tr_category              "TCG Trait Categories"
#define NID_tcg_tr_category             1394
#define OBJ_tcg_tr_category             OBJ_tcg_traits,2L

#define SN_tcg_tr_registry              "tcg-tr-registry"
#define LN_tcg_tr_registry              "TCG Trait Registries"
#define NID_tcg_tr_registry             1395
#define OBJ_tcg_tr_registry             OBJ_tcg_traits,3L

#define SN_tcg_tr_ID_Boolean            "tcg-tr-ID-Boolean"
#define LN_tcg_tr_ID_Boolean            "Boolean Trait"
#define NID_tcg_tr_ID_Boolean           1396
#define OBJ_tcg_tr_ID_Boolean           OBJ_tcg_tr_ID,1L

#define SN_tcg_tr_ID_CertificateIdentifier              "tcg-tr-ID-CertificateIdentifier"
#define LN_tcg_tr_ID_CertificateIdentifier              "Certificate Identifier Trait"
#define NID_tcg_tr_ID_CertificateIdentifier             1397
#define OBJ_tcg_tr_ID_CertificateIdentifier             OBJ_tcg_tr_ID,2L

#define SN_tcg_tr_ID_CommonCriteria             "tcg-tr-ID-CommonCriteria"
#define LN_tcg_tr_ID_CommonCriteria             "Common Criteria Trait"
#define NID_tcg_tr_ID_CommonCriteria            1398
#define OBJ_tcg_tr_ID_CommonCriteria            OBJ_tcg_tr_ID,3L

#define SN_tcg_tr_ID_componentClass             "tcg-tr-ID-componentClass"
#define LN_tcg_tr_ID_componentClass             "Component Class Trait"
#define NID_tcg_tr_ID_componentClass            1399
#define OBJ_tcg_tr_ID_componentClass            OBJ_tcg_tr_ID,4L

#define SN_tcg_tr_ID_componentIdentifierV11             "tcg-tr-ID-componentIdentifierV11"
#define LN_tcg_tr_ID_componentIdentifierV11             "Component Identifier V1.1 Trait"
#define NID_tcg_tr_ID_componentIdentifierV11            1400
#define OBJ_tcg_tr_ID_componentIdentifierV11            OBJ_tcg_tr_ID,5L

#define SN_tcg_tr_ID_FIPSLevel          "tcg-tr-ID-FIPSLevel"
#define LN_tcg_tr_ID_FIPSLevel          "FIPS Level Trait"
#define NID_tcg_tr_ID_FIPSLevel         1401
#define OBJ_tcg_tr_ID_FIPSLevel         OBJ_tcg_tr_ID,6L

#define SN_tcg_tr_ID_ISO9000Level               "tcg-tr-ID-ISO9000Level"
#define LN_tcg_tr_ID_ISO9000Level               "ISO 9000 Level Trait"
#define NID_tcg_tr_ID_ISO9000Level              1402
#define OBJ_tcg_tr_ID_ISO9000Level              OBJ_tcg_tr_ID,7L

#define SN_tcg_tr_ID_networkMAC         "tcg-tr-ID-networkMAC"
#define LN_tcg_tr_ID_networkMAC         "Network MAC Trait"
#define NID_tcg_tr_ID_networkMAC                1403
#define OBJ_tcg_tr_ID_networkMAC                OBJ_tcg_tr_ID,8L

#define SN_tcg_tr_ID_OID                "tcg-tr-ID-OID"
#define LN_tcg_tr_ID_OID                "Object Identifier Trait"
#define NID_tcg_tr_ID_OID               1404
#define OBJ_tcg_tr_ID_OID               OBJ_tcg_tr_ID,9L

#define SN_tcg_tr_ID_PEN                "tcg-tr-ID-PEN"
#define LN_tcg_tr_ID_PEN                "Private Enterprise Number Trait"
#define NID_tcg_tr_ID_PEN               1405
#define OBJ_tcg_tr_ID_PEN               OBJ_tcg_tr_ID,10L

#define SN_tcg_tr_ID_platformFirmwareCapabilities               "tcg-tr-ID-platformFirmwareCapabilities"
#define LN_tcg_tr_ID_platformFirmwareCapabilities               "Platform Firmware Capabilities Trait"
#define NID_tcg_tr_ID_platformFirmwareCapabilities              1406
#define OBJ_tcg_tr_ID_platformFirmwareCapabilities              OBJ_tcg_tr_ID,11L

#define SN_tcg_tr_ID_platformFirmwareSignatureVerification              "tcg-tr-ID-platformFirmwareSignatureVerification"
#define LN_tcg_tr_ID_platformFirmwareSignatureVerification              "Platform Firmware Signature Verification Trait"
#define NID_tcg_tr_ID_platformFirmwareSignatureVerification             1407
#define OBJ_tcg_tr_ID_platformFirmwareSignatureVerification             OBJ_tcg_tr_ID,12L

#define SN_tcg_tr_ID_platformFirmwareUpdateCompliance           "tcg-tr-ID-platformFirmwareUpdateCompliance"
#define LN_tcg_tr_ID_platformFirmwareUpdateCompliance           "Platform Firmware Update Compliance Trait"
#define NID_tcg_tr_ID_platformFirmwareUpdateCompliance          1408
#define OBJ_tcg_tr_ID_platformFirmwareUpdateCompliance          OBJ_tcg_tr_ID,13L

#define SN_tcg_tr_ID_platformHardwareCapabilities               "tcg-tr-ID-platformHardwareCapabilities"
#define LN_tcg_tr_ID_platformHardwareCapabilities               "Platform Hardware Capabilities Trait"
#define NID_tcg_tr_ID_platformHardwareCapabilities              1409
#define OBJ_tcg_tr_ID_platformHardwareCapabilities              OBJ_tcg_tr_ID,14L

#define SN_tcg_tr_ID_RTM                "tcg-tr-ID-RTM"
#define LN_tcg_tr_ID_RTM                "Root of Trust for Measurement Trait"
#define NID_tcg_tr_ID_RTM               1410
#define OBJ_tcg_tr_ID_RTM               OBJ_tcg_tr_ID,15L

#define SN_tcg_tr_ID_status             "tcg-tr-ID-status"
#define LN_tcg_tr_ID_status             "Attribute Status Trait"
#define NID_tcg_tr_ID_status            1411
#define OBJ_tcg_tr_ID_status            OBJ_tcg_tr_ID,16L

#define SN_tcg_tr_ID_URI                "tcg-tr-ID-URI"
#define LN_tcg_tr_ID_URI                "Uniform Resource Identifier Trait"
#define NID_tcg_tr_ID_URI               1412
#define OBJ_tcg_tr_ID_URI               OBJ_tcg_tr_ID,17L

#define SN_tcg_tr_ID_UTF8String         "tcg-tr-ID-UTF8String"
#define LN_tcg_tr_ID_UTF8String         "UTF8String Trait"
#define NID_tcg_tr_ID_UTF8String                1413
#define OBJ_tcg_tr_ID_UTF8String                OBJ_tcg_tr_ID,18L

#define SN_tcg_tr_ID_IA5String          "tcg-tr-ID-IA5String"
#define LN_tcg_tr_ID_IA5String          "IA5String Trait"
#define NID_tcg_tr_ID_IA5String         1414
#define OBJ_tcg_tr_ID_IA5String         OBJ_tcg_tr_ID,19L

#define SN_tcg_tr_ID_PEMCertString              "tcg-tr-ID-PEMCertString"
#define LN_tcg_tr_ID_PEMCertString              "PEM-Encoded Certificate String Trait"
#define NID_tcg_tr_ID_PEMCertString             1415
#define OBJ_tcg_tr_ID_PEMCertString             OBJ_tcg_tr_ID,20L

#define SN_tcg_tr_ID_PublicKey          "tcg-tr-ID-PublicKey"
#define LN_tcg_tr_ID_PublicKey          "Public Key Trait"
#define NID_tcg_tr_ID_PublicKey         1416
#define OBJ_tcg_tr_ID_PublicKey         OBJ_tcg_tr_ID,21L

#define SN_tcg_tr_cat_platformManufacturer              "tcg-tr-cat-platformManufacturer"
#define LN_tcg_tr_cat_platformManufacturer              "Platform Manufacturer Trait Category"
#define NID_tcg_tr_cat_platformManufacturer             1417
#define OBJ_tcg_tr_cat_platformManufacturer             OBJ_tcg_tr_category,1L

#define SN_tcg_tr_cat_platformModel             "tcg-tr-cat-platformModel"
#define LN_tcg_tr_cat_platformModel             "Platform Model Trait Category"
#define NID_tcg_tr_cat_platformModel            1418
#define OBJ_tcg_tr_cat_platformModel            OBJ_tcg_tr_category,2L

#define SN_tcg_tr_cat_platformVersion           "tcg-tr-cat-platformVersion"
#define LN_tcg_tr_cat_platformVersion           "Platform Version Trait Category"
#define NID_tcg_tr_cat_platformVersion          1419
#define OBJ_tcg_tr_cat_platformVersion          OBJ_tcg_tr_category,3L

#define SN_tcg_tr_cat_platformSerial            "tcg-tr-cat-platformSerial"
#define LN_tcg_tr_cat_platformSerial            "Platform Serial Trait Category"
#define NID_tcg_tr_cat_platformSerial           1420
#define OBJ_tcg_tr_cat_platformSerial           OBJ_tcg_tr_category,4L

#define SN_tcg_tr_cat_platformManufacturerIdentifier            "tcg-tr-cat-platformManufacturerIdentifier"
#define LN_tcg_tr_cat_platformManufacturerIdentifier            "Platform Manufacturer Identifier Trait Category"
#define NID_tcg_tr_cat_platformManufacturerIdentifier           1421
#define OBJ_tcg_tr_cat_platformManufacturerIdentifier           OBJ_tcg_tr_category,5L

#define SN_tcg_tr_cat_platformOwnership         "tcg-tr-cat-platformOwnership"
#define LN_tcg_tr_cat_platformOwnership         "Platform Ownership Trait Category"
#define NID_tcg_tr_cat_platformOwnership                1422
#define OBJ_tcg_tr_cat_platformOwnership                OBJ_tcg_tr_category,6L

#define SN_tcg_tr_cat_componentClass            "tcg-tr-cat-componentClass"
#define LN_tcg_tr_cat_componentClass            "Component Class Trait Category"
#define NID_tcg_tr_cat_componentClass           1423
#define OBJ_tcg_tr_cat_componentClass           OBJ_tcg_tr_category,7L

#define SN_tcg_tr_cat_componentManufacturer             "tcg-tr-cat-componentManufacturer"
#define LN_tcg_tr_cat_componentManufacturer             "Component Manufacturer Trait Category"
#define NID_tcg_tr_cat_componentManufacturer            1424
#define OBJ_tcg_tr_cat_componentManufacturer            OBJ_tcg_tr_category,8L

#define SN_tcg_tr_cat_componentModel            "tcg-tr-cat-componentModel"
#define LN_tcg_tr_cat_componentModel            "Component Model Trait Category"
#define NID_tcg_tr_cat_componentModel           1425
#define OBJ_tcg_tr_cat_componentModel           OBJ_tcg_tr_category,9L

#define SN_tcg_tr_cat_componentSerial           "tcg-tr-cat-componentSerial"
#define LN_tcg_tr_cat_componentSerial           "Component Serial Trait Category"
#define NID_tcg_tr_cat_componentSerial          1426
#define OBJ_tcg_tr_cat_componentSerial          OBJ_tcg_tr_category,10L

#define SN_tcg_tr_cat_componentStatus           "tcg-tr-cat-componentStatus"
#define LN_tcg_tr_cat_componentStatus           "Component Status Trait Category"
#define NID_tcg_tr_cat_componentStatus          1427
#define OBJ_tcg_tr_cat_componentStatus          OBJ_tcg_tr_category,11L

#define SN_tcg_tr_cat_componentLocation         "tcg-tr-cat-componentLocation"
#define LN_tcg_tr_cat_componentLocation         "Component Location Trait Category"
#define NID_tcg_tr_cat_componentLocation                1428
#define OBJ_tcg_tr_cat_componentLocation                OBJ_tcg_tr_category,12L

#define SN_tcg_tr_cat_componentRevision         "tcg-tr-cat-componentRevision"
#define LN_tcg_tr_cat_componentRevision         "Component Revision Trait Category"
#define NID_tcg_tr_cat_componentRevision                1429
#define OBJ_tcg_tr_cat_componentRevision                OBJ_tcg_tr_category,13L

#define SN_tcg_tr_cat_componentFieldReplaceable         "tcg-tr-cat-componentFieldReplaceable"
#define LN_tcg_tr_cat_componentFieldReplaceable         "Component Field Replaceable Trait Category"
#define NID_tcg_tr_cat_componentFieldReplaceable                1430
#define OBJ_tcg_tr_cat_componentFieldReplaceable                OBJ_tcg_tr_category,14L

#define SN_tcg_tr_cat_EKCertificate             "tcg-tr-cat-EKCertificate"
#define LN_tcg_tr_cat_EKCertificate             "EK Certificate Trait Category"
#define NID_tcg_tr_cat_EKCertificate            1431
#define OBJ_tcg_tr_cat_EKCertificate            OBJ_tcg_tr_category,15L

#define SN_tcg_tr_cat_IAKCertificate            "tcg-tr-cat-IAKCertificate"
#define LN_tcg_tr_cat_IAKCertificate            "IAK Certificate Trait Category"
#define NID_tcg_tr_cat_IAKCertificate           1432
#define OBJ_tcg_tr_cat_IAKCertificate           OBJ_tcg_tr_category,16L

#define SN_tcg_tr_cat_IDevIDCertificate         "tcg-tr-cat-IDevIDCertificate"
#define LN_tcg_tr_cat_IDevIDCertificate         "IDevID Certificate Trait Category"
#define NID_tcg_tr_cat_IDevIDCertificate                1433
#define OBJ_tcg_tr_cat_IDevIDCertificate                OBJ_tcg_tr_category,17L

#define SN_tcg_tr_cat_DICECertificate           "tcg-tr-cat-DICECertificate"
#define LN_tcg_tr_cat_DICECertificate           "DICE Certificate Trait Category"
#define NID_tcg_tr_cat_DICECertificate          1434
#define OBJ_tcg_tr_cat_DICECertificate          OBJ_tcg_tr_category,18L

#define SN_tcg_tr_cat_SPDMCertificate           "tcg-tr-cat-SPDMCertificate"
#define LN_tcg_tr_cat_SPDMCertificate           "SPDM Certificate Trait Category"
#define NID_tcg_tr_cat_SPDMCertificate          1435
#define OBJ_tcg_tr_cat_SPDMCertificate          OBJ_tcg_tr_category,19L

#define SN_tcg_tr_cat_PEMCertificate            "tcg-tr-cat-PEMCertificate"
#define LN_tcg_tr_cat_PEMCertificate            "PEM Certificate Trait Category"
#define NID_tcg_tr_cat_PEMCertificate           1436
#define OBJ_tcg_tr_cat_PEMCertificate           OBJ_tcg_tr_category,20L

#define SN_tcg_tr_cat_PlatformCertificate               "tcg-tr-cat-PlatformCertificate"
#define LN_tcg_tr_cat_PlatformCertificate               "Platform Certificate Trait Category"
#define NID_tcg_tr_cat_PlatformCertificate              1437
#define OBJ_tcg_tr_cat_PlatformCertificate              OBJ_tcg_tr_category,21L

#define SN_tcg_tr_cat_DeltaPlatformCertificate          "tcg-tr-cat-DeltaPlatformCertificate"
#define LN_tcg_tr_cat_DeltaPlatformCertificate          "Delta Platform Certificate Trait Category"
#define NID_tcg_tr_cat_DeltaPlatformCertificate         1438
#define OBJ_tcg_tr_cat_DeltaPlatformCertificate         OBJ_tcg_tr_category,22L

#define SN_tcg_tr_cat_RebasePlatformCertificate         "tcg-tr-cat-RebasePlatformCertificate"
#define LN_tcg_tr_cat_RebasePlatformCertificate         "Rebase Platform Certificate Trait Category"
#define NID_tcg_tr_cat_RebasePlatformCertificate                1439
#define OBJ_tcg_tr_cat_RebasePlatformCertificate                OBJ_tcg_tr_category,23L

#define SN_tcg_tr_cat_genericCertificate                "tcg-tr-cat-genericCertificate"
#define LN_tcg_tr_cat_genericCertificate                "Generic Certificate Trait Category"
#define NID_tcg_tr_cat_genericCertificate               1440
#define OBJ_tcg_tr_cat_genericCertificate               OBJ_tcg_tr_category,24L

#define SN_tcg_tr_cat_CommonCriteria            "tcg-tr-cat-CommonCriteria"
#define LN_tcg_tr_cat_CommonCriteria            "Common Criteria Trait Category"
#define NID_tcg_tr_cat_CommonCriteria           1441
#define OBJ_tcg_tr_cat_CommonCriteria           OBJ_tcg_tr_category,25L

#define SN_tcg_tr_cat_componentIdentifierV11            "tcg-tr-cat-componentIdentifierV11"
#define LN_tcg_tr_cat_componentIdentifierV11            "Component Identifier V1.1 Trait Category"
#define NID_tcg_tr_cat_componentIdentifierV11           1442
#define OBJ_tcg_tr_cat_componentIdentifierV11           OBJ_tcg_tr_category,26L

#define SN_tcg_tr_cat_FIPSLevel         "tcg-tr-cat-FIPSLevel"
#define LN_tcg_tr_cat_FIPSLevel         "FIPS Level Trait Category"
#define NID_tcg_tr_cat_FIPSLevel                1443
#define OBJ_tcg_tr_cat_FIPSLevel                OBJ_tcg_tr_category,27L

#define SN_tcg_tr_cat_ISO9000           "tcg-tr-cat-ISO9000"
#define LN_tcg_tr_cat_ISO9000           "ISO 9000 Trait Category"
#define NID_tcg_tr_cat_ISO9000          1444
#define OBJ_tcg_tr_cat_ISO9000          OBJ_tcg_tr_category,28L

#define SN_tcg_tr_cat_networkMAC                "tcg-tr-cat-networkMAC"
#define LN_tcg_tr_cat_networkMAC                "Network MAC Trait Category"
#define NID_tcg_tr_cat_networkMAC               1445
#define OBJ_tcg_tr_cat_networkMAC               OBJ_tcg_tr_category,29L

#define SN_tcg_tr_cat_attestationProtocol               "tcg-tr-cat-attestationProtocol"
#define LN_tcg_tr_cat_attestationProtocol               "Attestation Protocol Trait Category"
#define NID_tcg_tr_cat_attestationProtocol              1446
#define OBJ_tcg_tr_cat_attestationProtocol              OBJ_tcg_tr_category,30L

#define SN_tcg_tr_cat_PEN               "tcg-tr-cat-PEN"
#define LN_tcg_tr_cat_PEN               "Private Enterprise Number Trait Category"
#define NID_tcg_tr_cat_PEN              1447
#define OBJ_tcg_tr_cat_PEN              OBJ_tcg_tr_category,31L

#define SN_tcg_tr_cat_platformFirmwareCapabilities              "tcg-tr-cat-platformFirmwareCapabilities"
#define LN_tcg_tr_cat_platformFirmwareCapabilities              "Platform Firmware Capabilities Trait Category"
#define NID_tcg_tr_cat_platformFirmwareCapabilities             1448
#define OBJ_tcg_tr_cat_platformFirmwareCapabilities             OBJ_tcg_tr_category,32L

#define SN_tcg_tr_cat_platformHardwareCapabilities              "tcg-tr-cat-platformHardwareCapabilities"
#define LN_tcg_tr_cat_platformHardwareCapabilities              "Platform Hardware Capabilities Trait Category"
#define NID_tcg_tr_cat_platformHardwareCapabilities             1449
#define OBJ_tcg_tr_cat_platformHardwareCapabilities             OBJ_tcg_tr_category,33L

#define SN_tcg_tr_cat_platformFirmwareSignatureVerification             "tcg-tr-cat-platformFirmwareSignatureVerification"
#define LN_tcg_tr_cat_platformFirmwareSignatureVerification             "Platform Firmware Signature Verification Trait Category"
#define NID_tcg_tr_cat_platformFirmwareSignatureVerification            1450
#define OBJ_tcg_tr_cat_platformFirmwareSignatureVerification            OBJ_tcg_tr_category,34L

#define SN_tcg_tr_cat_platformFirmwareUpdateCompliance          "tcg-tr-cat-platformFirmwareUpdateCompliance"
#define LN_tcg_tr_cat_platformFirmwareUpdateCompliance          "Platform Firmware Update Compliance Trait Category"
#define NID_tcg_tr_cat_platformFirmwareUpdateCompliance         1451
#define OBJ_tcg_tr_cat_platformFirmwareUpdateCompliance         OBJ_tcg_tr_category,35L

#define SN_tcg_tr_cat_RTM               "tcg-tr-cat-RTM"
#define LN_tcg_tr_cat_RTM               "Root of Trust of Measurement Trait Category"
#define NID_tcg_tr_cat_RTM              1452
#define OBJ_tcg_tr_cat_RTM              OBJ_tcg_tr_category,36L

#define SN_tcg_tr_cat_PublicKey         "tcg-tr-cat-PublicKey"
#define LN_tcg_tr_cat_PublicKey         "Public Key Trait Category"
#define NID_tcg_tr_cat_PublicKey                1453
#define OBJ_tcg_tr_cat_PublicKey                OBJ_tcg_tr_category,37L

#define OBJ_nistKems            OBJ_nistAlgorithms,4L

#define SN_ML_KEM_512           "id-alg-ml-kem-512"
#define LN_ML_KEM_512           "ML-KEM-512"
#define NID_ML_KEM_512          1454
#define OBJ_ML_KEM_512          OBJ_nistKems,1L

#define SN_ML_KEM_768           "id-alg-ml-kem-768"
#define LN_ML_KEM_768           "ML-KEM-768"
#define NID_ML_KEM_768          1455
#define OBJ_ML_KEM_768          OBJ_nistKems,2L

#define SN_ML_KEM_1024          "id-alg-ml-kem-1024"
#define LN_ML_KEM_1024          "ML-KEM-1024"
#define NID_ML_KEM_1024         1456
#define OBJ_ML_KEM_1024         OBJ_nistKems,3L

#endif /* OPENSSL_OBJ_MAC_H */

#ifndef OPENSSL_NO_DEPRECATED_3_0

#define SN_id_tc26_cipher_gostr3412_2015_magma_ctracpkm                 SN_magma_ctr_acpkm
#define NID_id_tc26_cipher_gostr3412_2015_magma_ctracpkm                NID_magma_ctr_acpkm
#define OBJ_id_tc26_cipher_gostr3412_2015_magma_ctracpkm                OBJ_magma_ctr_acpkm

#define SN_id_tc26_cipher_gostr3412_2015_magma_ctracpkm_omac            SN_magma_ctr_acpkm_omac
#define NID_id_tc26_cipher_gostr3412_2015_magma_ctracpkm_omac           NID_magma_ctr_acpkm_omac
#define OBJ_id_tc26_cipher_gostr3412_2015_magma_ctracpkm_omac           OBJ_magma_ctr_acpkm_omac

#define SN_id_tc26_cipher_gostr3412_2015_kuznyechik_ctracpkm            SN_kuznyechik_ctr_acpkm
#define NID_id_tc26_cipher_gostr3412_2015_kuznyechik_ctracpkm           NID_kuznyechik_ctr_acpkm
#define OBJ_id_tc26_cipher_gostr3412_2015_kuznyechik_ctracpkm           OBJ_kuznyechik_ctr_acpkm

#define SN_id_tc26_cipher_gostr3412_2015_kuznyechik_ctracpkm_omac               SN_kuznyechik_ctr_acpkm_omac
#define NID_id_tc26_cipher_gostr3412_2015_kuznyechik_ctracpkm_omac              NID_kuznyechik_ctr_acpkm_omac
#define OBJ_id_tc26_cipher_gostr3412_2015_kuznyechik_ctracpkm_omac              OBJ_kuznyechik_ctr_acpkm_omac

#define SN_id_tc26_wrap_gostr3412_2015_magma_kexp15             SN_magma_kexp15
#define NID_id_tc26_wrap_gostr3412_2015_magma_kexp15            NID_magma_kexp15
#define OBJ_id_tc26_wrap_gostr3412_2015_magma_kexp15            OBJ_magma_kexp15

#define SN_id_tc26_wrap_gostr3412_2015_kuznyechik_kexp15                SN_kuznyechik_kexp15
#define NID_id_tc26_wrap_gostr3412_2015_kuznyechik_kexp15               NID_kuznyechik_kexp15
#define OBJ_id_tc26_wrap_gostr3412_2015_kuznyechik_kexp15               OBJ_kuznyechik_kexp15

#define SN_grasshopper_ecb              SN_kuznyechik_ecb
#define NID_grasshopper_ecb             NID_kuznyechik_ecb

#define SN_grasshopper_ctr              SN_kuznyechik_ctr
#define NID_grasshopper_ctr             NID_kuznyechik_ctr

#define SN_grasshopper_ofb              SN_kuznyechik_ofb
#define NID_grasshopper_ofb             NID_kuznyechik_ofb

#define SN_grasshopper_cbc              SN_kuznyechik_cbc
#define NID_grasshopper_cbc             NID_kuznyechik_cbc

#define SN_grasshopper_cfb              SN_kuznyechik_cfb
#define NID_grasshopper_cfb             NID_kuznyechik_cfb

#define SN_grasshopper_mac              SN_kuznyechik_mac
#define NID_grasshopper_mac             NID_kuznyechik_mac

#endif  /* OPENSSL_NO_DEPRECATED_3_0 */
