﻿// ModuleDialog.cpp : 实现文件
//

#include "stdafx.h"
#include "FStation.h"
#include "ModuleDialog.h"
#include "afxdialogex.h"

#include "Sys.h"

#include "LogicMgr.h"

#include "DialogRobot.h"
#include "DialogRobotTeach.h"
#include "DialogRobotCalib.h"
#include "DialogTray.h"
#include "DialogFixture.h"
#include "DialogBelt.h"
#include "DialogMachine.h"

// CModuleDialog 对话框

IMPLEMENT_DYNAMIC(CModuleDialog, CDialogEx)

CModuleDialog::CModuleDialog(CPoint pt, CWnd* pParent /*=NULL*/)
	: CDialogEx(CModuleDialog::IDD, pParent)
	, m_bFlag(false)
{
	m_pt = pt;

	m_pImageWindow = CImageWindow::CreateInstance("Module");
}

CModuleDialog::~CModuleDialog()
{
	vector<vector<CDialogEx*>>::iterator itList = m_vecDialogList.begin();
	for (; itList != m_vecDialogList.end(); itList++)
	{
		vector<CDialogEx*>::iterator it = itList->begin();
		for (; it != itList->end(); it++)
		{
			delete *it;
		}
	}

	g_pRobot->SaveRobotPoint();

	delete m_pImageWindow;
}

void CModuleDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_BUTTON2, m_btn[0]);
	DDX_Control(pDX, IDC_BUTTON3, m_btn[1]);
	DDX_Control(pDX, IDC_BUTTON4, m_btn[2]);
	DDX_Control(pDX, IDC_BUTTON5, m_btn[3]);
	DDX_Control(pDX, IDC_BUTTON6, m_btn[4]);
	DDX_Control(pDX, IDC_BUTTON7, m_btn[5]);
	DDX_Control(pDX, IDC_BUTTON1, m_btn[6]);
	DDX_Control(pDX, IDC_LIST1, m_list1);
	DDX_Control(pDX, IDC_LIST2, m_list2);
	DDX_Control(pDX, IDC_LIST3, m_list3);
	DDX_Control(pDX, IDC_TAB1, m_tab);
	DDX_Control(pDX, IDC_EDIT1, m_edit);
}

BEGIN_MESSAGE_MAP(CModuleDialog, CDialogEx)
	ON_WM_PAINT()
//	ON_WM_CTLCOLOR()
	ON_WM_TIMER()
	ON_NOTIFY(NM_RCLICK, IDC_LIST1, &CModuleDialog::OnNMRClickList1)
	ON_NOTIFY(NM_DBLCLK, IDC_LIST1, &CModuleDialog::OnNMDblclkList1)
	ON_EN_KILLFOCUS(IDC_EDIT1, &CModuleDialog::OnEnKillfocusEdit1)
	ON_NOTIFY(NM_CLICK, IDC_LIST3, &CModuleDialog::OnNMClickList3)
	ON_NOTIFY(NM_DBLCLK, IDC_LIST3, &CModuleDialog::OnNMDblclkList3)
	ON_BN_CLICKED(IDC_BUTTON1, &CModuleDialog::OnBnClickedButton1)
	ON_BN_CLICKED(IDC_BUTTON2, &CModuleDialog::OnBnClickedButton2)
	ON_BN_CLICKED(IDC_BUTTON3, &CModuleDialog::OnBnClickedButton3)
	ON_BN_CLICKED(IDC_BUTTON4, &CModuleDialog::OnBnClickedButton4)
	ON_BN_CLICKED(IDC_BUTTON5, &CModuleDialog::OnBnClickedButton5)
	ON_BN_CLICKED(IDC_BUTTON6, &CModuleDialog::OnBnClickedButton6)
	ON_BN_CLICKED(IDC_BUTTON7, &CModuleDialog::OnBnClickedButton7)
	ON_NOTIFY(TCN_SELCHANGE, IDC_TAB1, &CModuleDialog::OnTcnSelchangeTab1)
END_MESSAGE_MAP()

void CModuleDialog::OnPaint()
{
	CPaintDC dc(this);

	if (!IsIconic())
	{
		CRect rect;
		GetClientRect(&rect);

		dc.FillSolidRect(rect, RGB(255, 255, 255));

		GetDlgItem(IDC_STATIC2)->GetWindowRect(rect);
		ScreenToClient(rect);

		dc.FillSolidRect(rect, RGB(153, 217, 234));

		CDialogEx::OnPaint();
	}
}

HBRUSH CModuleDialog::OnCtlColor(CDC* pDC, CWnd* pWnd, UINT nCtlColor)
{
	HBRUSH hbr = NULL;

	if (true) {
		hbr = reinterpret_cast<HBRUSH>(::GetStockObject(WHITE_BRUSH));
	}
	else {
		hbr = CDialogEx::OnCtlColor(pDC, pWnd, nCtlColor);
	}

	return hbr;
}

BOOL CModuleDialog::PreTranslateMessage( MSG* pMsg )
{
	if ((WM_SYSKEYDOWN == pMsg->message && VK_F4 == pMsg->wParam) || (pMsg->message == WM_KEYDOWN && pMsg->wParam == VK_ESCAPE)) { //屏蔽Alt + F4
		return TRUE;
	}

	return CDialogEx::PreTranslateMessage(pMsg);
}

static void ShowVCenter(CEdit &edit)
{
	CRect cRect = CRect(0, 0, 0, 0);
	edit.GetClientRect(&cRect);
	TEXTMETRIC textMetric;
	CDC *pcDC = edit.GetDC();
	pcDC->GetTextMetrics(&textMetric);
	int nFontHight = textMetric.tmHeight + textMetric.tmExternalLeading;
	int nOffsetY = (cRect.Height() - nFontHight)/2;//计算文字向下偏移量
	cRect.OffsetRect(0, nOffsetY);//设置向下偏移
	edit.SetRectNP(cRect);
}

BOOL CModuleDialog::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	CRect rc;

	GetWindowRect(&rc);

	MoveWindow(m_pt.x, m_pt.y, rc.Width(), rc.Height());

	m_imageList.Create(24, 24, TRUE, 2, 2);
	m_imageList.Add(theApp.LoadIcon(IDI_D));
	m_imageList.Add(theApp.LoadIcon(IDI_P));

	m_imageList1.Create(24, 24, TRUE, 2, 2);

	m_tab.SetItemSize(CSize(100, 30));
	
	//Node
	LONG lStyle = 0;
	lStyle = GetWindowLong(m_list1.m_hWnd, GWL_STYLE);//获取当前窗口样式
	lStyle &= ~LVS_TYPEMASK;//清除显位方式位
	lStyle |= LVS_REPORT;
	SetWindowLong(m_list1.m_hWnd, GWL_STYLE, lStyle);

	DWORD dwStyle = 0;
	dwStyle = m_list1.GetExtendedStyle();//获取当前窗口扩展样式
	dwStyle |= LVS_EX_FULLROWSELECT;//选中某行使整行高亮
	dwStyle |= LVS_EX_GRIDLINES;//网格线
	m_list1.SetExtendedStyle(dwStyle);

	m_list1.SetImageList(&m_imageList1, LVSIL_SMALL);

	CString sParam[] = { "", "名称", "值", "范围" };
	int		nParamLen[] = { 1, 218, 150, 150 };

	for (int i=0; i<sizeof(nParamLen) / sizeof(int); i++)
	{
		m_list1.InsertColumn(i, sParam[i], LVCFMT_LEFT, nParamLen[i]);
	}

	//Node
	lStyle = GetWindowLong(m_list2.m_hWnd, GWL_STYLE);//获取当前窗口样式
	lStyle &= ~LVS_TYPEMASK;//清除显位方式位
	lStyle |= LVS_REPORT;
	SetWindowLong(m_list2.m_hWnd, GWL_STYLE, lStyle);

	dwStyle = m_list2.GetExtendedStyle();//获取当前窗口扩展样式
	dwStyle |= LVS_EX_FULLROWSELECT;//选中某行使整行高亮
	dwStyle |= LVS_EX_GRIDLINES;//网格线
	m_list2.SetExtendedStyle(dwStyle);

	m_list2.SetImageList(&m_imageList, LVSIL_SMALL);

	CString sIn[] = { "", "输入信号" };
	int		nInLen[] = { 28, 210 };

	for (int i=0; i<sizeof(nInLen) / sizeof(int); i++)
	{
		m_list2.InsertColumn(i, sIn[i], LVCFMT_LEFT, nInLen[i]);
	}

	//Node
	lStyle = GetWindowLong(m_list3.m_hWnd, GWL_STYLE);//获取当前窗口样式
	lStyle &= ~LVS_TYPEMASK;//清除显位方式位
	lStyle |= LVS_REPORT;
	SetWindowLong(m_list3.m_hWnd, GWL_STYLE, lStyle);

	dwStyle = m_list3.GetExtendedStyle();//获取当前窗口扩展样式
	dwStyle |= LVS_EX_FULLROWSELECT;//选中某行使整行高亮
	dwStyle |= LVS_EX_GRIDLINES;//网格线
	m_list3.SetExtendedStyle(dwStyle);

	m_list3.SetImageList(&m_imageList, LVSIL_SMALL);

	CString sOut[] = { "", "输出信号" };
	int		nOutLen[] = { 28, 210 };

	for (int i=0; i<sizeof(nOutLen) / sizeof(int); i++)
	{
		m_list3.InsertColumn(i, sOut[i], LVCFMT_LEFT, nOutLen[i]);
	}
	
	CWnd *pTab = NULL;
	pTab = GetDlgItem(IDC_TAB1);

	CRect rTab, rItem;
	pTab->GetClientRect(&rTab);
	static_cast<CTabCtrl*>(pTab)->GetItemRect(0, &rItem);
	
	vector<CDialogEx*> vecDialog;

	CDialogEx* pDialog = NULL;

	pDialog = static_cast<CDialogEx *>(new CDialogRobot(CPoint(rTab.left + 2, rTab.top + rItem.Height() + 5), pTab));
	pDialog->Create(IDD_DIALOG_ROBOT, pTab);
	pDialog->ShowWindow(SW_HIDE);
	vecDialog.push_back(pDialog);

	pDialog = static_cast<CDialogEx *>(new CDialogRobotTeach(CPoint(rTab.left + 2, rTab.top + rItem.Height() + 5), pTab));
	pDialog->Create(IDD_DIALOG_ROBOT_TEACH, pTab);
	pDialog->ShowWindow(SW_HIDE);
	vecDialog.push_back(pDialog);

	pDialog = static_cast<CDialogEx *>(new CDialogRobotCalib(CPoint(rTab.left + 2, rTab.top + rItem.Height() + 5), m_pImageWindow, pTab));
	pDialog->Create(IDD_DIALOG_ROBOT_CALIB, pTab);
	pDialog->ShowWindow(SW_HIDE);
	vecDialog.push_back(pDialog);

	m_vecDialogList.push_back(vecDialog);
	vecDialog.clear();

	pDialog = static_cast<CDialogEx *>(new CDialogTray(CPoint(rTab.left + 2, rTab.top + rItem.Height() + 5), pTab));
	pDialog->Create(IDD_DIALOG_TRAY, pTab);
	pDialog->ShowWindow(SW_HIDE);
	vecDialog.push_back(pDialog);

	m_vecDialogList.push_back(vecDialog);
	vecDialog.clear();

	pDialog = static_cast<CDialogEx *>(new CDialogFixture(CPoint(rTab.left + 2, rTab.top + rItem.Height() + 5), pTab));
	pDialog->Create(IDD_DIALOG_FIXTURE, pTab);
	pDialog->ShowWindow(SW_HIDE);
	vecDialog.push_back(pDialog);

	m_vecDialogList.push_back(vecDialog);
	vecDialog.clear();

	pDialog = static_cast<CDialogEx *>(new CDialogBelt(CPoint(rTab.left + 2, rTab.top + rItem.Height() + 5), "BeltA", pTab));
	pDialog->Create(IDD_DIALOG_BELT, pTab);
	pDialog->ShowWindow(SW_HIDE);
	vecDialog.push_back(pDialog);

	m_vecDialogList.push_back(vecDialog);
	vecDialog.clear();

	pDialog = static_cast<CDialogEx *>(new CDialogBelt(CPoint(rTab.left + 2, rTab.top + rItem.Height() + 5), "BeltB", pTab));
	pDialog->Create(IDD_DIALOG_BELT, pTab);
	pDialog->ShowWindow(SW_HIDE);
	vecDialog.push_back(pDialog);

	m_vecDialogList.push_back(vecDialog);
	vecDialog.clear();

	pDialog = static_cast<CDialogEx *>(new CDialogMachine(CPoint(rTab.left + 2, rTab.top + rItem.Height() + 5), pTab));
	pDialog->Create(IDD_DIALOG_MACHINE, pTab);
	pDialog->ShowWindow(SW_HIDE);
	vecDialog.push_back(pDialog);

	m_vecDialogList.push_back(vecDialog);
	vecDialog.clear();

	SwitchModule(static_cast<CModule*>(g_pRobot), 0);

	SetTimer(0, 500, NULL);

	return TRUE;
}

// CModuleDialog 消息处理程序

void CModuleDialog::OnTimer(UINT_PTR nIDEvent)
{
	KillTimer(nIDEvent);

	if (nIDEvent == 0) {
		CRect rcWnd;
		GetDlgItem(IDC_STATIC2)->GetWindowRect(&rcWnd);
		ScreenToClient(rcWnd);
		m_pImageWindow->AttatchWindow(rcWnd);
		m_pImageWindow->SetDrawWnd(GetSafeHwnd());
		m_pImageWindow->Show(false);
		m_bFlag = true;
	}

	if (nIDEvent == 1) {
		UpdateList();

		static int nLastRight = CSys::m_nRight;
		if (nLastRight != CSys::m_nRight) {
			nLastRight = CSys::m_nRight;
			RefreshModule();
		}

		SetTimer(nIDEvent, 500, NULL);
	}

	CDialogEx::OnTimer(nIDEvent);
}

void CModuleDialog::OnNMDblclkList3(NMHDR *pNMHDR, LRESULT *pResult)
{
	LPNMITEMACTIVATE pNMItemActivate = reinterpret_cast<LPNMITEMACTIVATE>(pNMHDR);

	PREMISSION_CTRL();

	if (pNMItemActivate->iItem >= 0 && pNMItemActivate->iItem < m_list3.GetItemCount() && pNMItemActivate->iSubItem > 0)
	{
		CString sName;

		sName = m_list3.GetItemText(pNMItemActivate->iItem, 1);

		if (!OnSafeCheck(sName, NULL)) {
			*pResult = 0;
			return;
		}

		POUTINFO pOutInfo = NULL;
		
		pOutInfo = g_pInfo->GetOutInfoByName(sName);

		if (pOutInfo != NULL) {
			bool bNewState = pOutInfo->bStat ? false : true;
			g_pControl->WriteOutput(sName, bNewState);
		}
	}

	*pResult = 0;
}

void CModuleDialog::OnNMClickList3(NMHDR *pNMHDR, LRESULT *pResult)
{
	LPNMITEMACTIVATE pNMItemActivate = reinterpret_cast<LPNMITEMACTIVATE>(pNMHDR);

	if (pNMItemActivate->iItem >= 0 && pNMItemActivate->iItem < m_list3.GetItemCount() && pNMItemActivate->iSubItem == 0)
	{
		PREMISSION_CTRL();

		CString sName;

		sName = m_list3.GetItemText(pNMItemActivate->iItem, 1);

		if (!OnSafeCheck(sName, NULL)) {
			*pResult = 0;
			return;
		}

		POUTINFO pOutInfo = NULL;
		
		pOutInfo = g_pInfo->GetOutInfoByName(sName);

		if (pOutInfo != NULL) {
			bool bNewState = pOutInfo->bStat ? false : true;
			g_pControl->WriteOutput(sName, bNewState);
		}
	}

	*pResult = 0;
}

void CModuleDialog::OnNMRClickList1(NMHDR *pNMHDR, LRESULT *pResult)
{
	LPNMITEMACTIVATE pNMItemActivate = reinterpret_cast<LPNMITEMACTIVATE>(pNMHDR);
	// TODO: 在此添加控件通知处理程序代码
	*pResult = 0;
}

void CModuleDialog::OnNMDblclkList1(NMHDR *pNMHDR, LRESULT *pResult)
{
	LPNMITEMACTIVATE pNMItemActivate = reinterpret_cast<LPNMITEMACTIVATE>(pNMHDR);

	NM_LISTVIEW  *pEditCtrl = (NM_LISTVIEW *)pNMHDR;

	m_nRowForEdit = pNMItemActivate->iItem;//m_row选中行行号
	m_nColForEdit = pNMItemActivate->iSubItem;//m_col选中行列号

	CColorList &thisList = m_list1;

	CRect rc;
	thisList.GetSubItemRect(pNMItemActivate->iItem, pNMItemActivate->iSubItem, LVIR_LABEL, rc);//取得子项的矩形
	thisList.ClientToScreen(&rc);
	ScreenToClient(&rc);
	
	if (pEditCtrl->iItem >= 0 && (pEditCtrl->iSubItem == 2 || pEditCtrl->iSubItem == 3)) {//编辑框

		PREMISSION_CTRL();

		CString strVal;
		strVal = thisList.GetItemText(pNMItemActivate->iItem, pNMItemActivate->iSubItem);//取得子项的内容
		m_edit.SetWindowText(strVal);//将子项的内容显示到编辑框中
		m_edit.MoveWindow(&rc);//将编辑框移动到子项上面&#xff0c;覆盖在子项上
		m_edit.ShowWindow(SW_SHOW);//显示编辑框
		m_edit.SetFocus();//使编辑框取得焦点
		m_edit.CreateSolidCaret(1, rc.Height() - 12);//创建一个光标
		m_edit.ShowCaret();//显示光标
		m_edit.SetSel(0, -1);//使光标移到最后面
		ShowVCenter(m_edit);
	}

	*pResult = 0;
}

void CModuleDialog::OnEnKillfocusEdit1()
{
	CString sName, sVal;

	sName = m_list1.GetItemText(m_nRowForEdit, 1);

	GetDlgItemText(IDC_EDIT1, sVal);

	m_edit.ShowWindow(SW_HIDE);

	CString sRet, sLog;

	switch (m_nColForEdit)
	{
	case 2:
		sRet = m_pModule->m_mapParam[sName]->UpdateValue(sVal);
		break;
	case 3:
		if (CSys::m_nRight < 4) {
			AfxMessageBox("无修改权限!");
			break;
		}
		sRet = m_pModule->m_mapParam[sName]->UpdateRange(sVal);
		break;
	default:break;
	}

	if (!sRet.IsEmpty() && m_pModule->m_mapParam[sName]->NeedSave()) {
		SYSTEMTIME t = { 0 };
		GetLocalTime(&t);

		if (m_pModule->m_mapParam[sName]->IsValid(3)) {
			sLog.Format("%02d:%02d:%02d:%03d - [%s] - [%s] %s\n", t.wHour, t.wMinute, t.wSecond, t.wMilliseconds, CSys::m_strPro.c_str(), sName, sRet);
		}
		else {
			sLog.Format("%02d:%02d:%02d:%03d - [%s] %s\n", t.wHour, t.wMinute, t.wSecond, t.wMilliseconds, sName, sRet);
		}

		g_pDatLog->UpdateParameterLog(sLog);

		m_pModule->Save();
	}
}

void CModuleDialog::SwitchModule(CModule *pModule, int nBtnIndex)
{
	int nRowIndex = 0;

	KillTimer(1);

	m_nCurIndex = nBtnIndex;

	m_pModule = pModule;

	m_list1.DeleteAllItems();

	vector<CString>::iterator itParam = m_pModule->m_vecParam.begin();
	for (; itParam != m_pModule->m_vecParam.end(); itParam++)
	{
		if (!m_pModule->m_mapParam[*itParam]->IsValid(CSys::m_nRight)) {
			continue;
		}

		nRowIndex = m_list1.GetItemCount();
		m_list1.InsertItem(nRowIndex, "");
		m_list1.SetItem(nRowIndex, 0, LVIF_IMAGE, NULL, 0, 0, 0, 0);
		m_list1.SetItemText(nRowIndex, 1, *itParam);
		m_list1.SetItemText(nRowIndex, 2, m_pModule->m_mapParam[*itParam]->Value());
		m_list1.SetItemText(nRowIndex, 3, m_pModule->m_mapParam[*itParam]->Range());
	}

	m_list2.DeleteAllItems();

	vector<PININFO>::iterator itIn = g_pInfo->m_vInList.begin();
	for (; itIn != g_pInfo->m_vInList.end(); itIn++)
	{
		if ((*itIn)->nBelong != m_pModule->m_nModule) {
			continue;
		}

		nRowIndex = m_list2.GetItemCount();
		m_list2.InsertItem(nRowIndex, "");
		m_list2.SetItem(nRowIndex, 0, LVIF_IMAGE, NULL, 0, 0, 0, 0);
		m_list2.SetItemText(nRowIndex, 1, (*itIn)->sName);
	}

	m_list3.DeleteAllItems();

	vector<POUTINFO>::iterator itOut = g_pInfo->m_vOutList.begin();
	for (; itOut != g_pInfo->m_vOutList.end(); itOut++)
	{
		if ((*itOut)->nBelong != m_pModule->m_nModule) {
			continue;
		}

		nRowIndex = m_list3.GetItemCount();
		m_list3.InsertItem(nRowIndex, "");
		m_list3.SetItem(nRowIndex, 0, LVIF_IMAGE, NULL, 0, 0, 0, 0);
		m_list3.SetItemText(nRowIndex, 1, (*itOut)->sName);
	}

	for (int i=0; i<sizeof(m_btn) / sizeof(CColorButton); i++)
	{
		if (i == nBtnIndex) {
			m_btn[i].SetColor(RGB(255, 255, 0), RGB(0, 0, 0));
		}
		else {
			m_btn[i].SetColor(RGB(153, 217, 234), RGB(0, 0, 0));
		}
	}

	m_tab.DeleteAllItems();

	switch (nBtnIndex)
	{
	case 0:
		m_tab.InsertItem(0, "设备控制");
		m_tab.InsertItem(1, "机械手示教");
		if (CSys::m_nRight >= 4) {
			m_tab.InsertItem(2, "相机标定");
		}
		break;
	case 1:
		m_tab.InsertItem(0, "设备控制");
		break;
	case 2:
		m_tab.InsertItem(0, "设备控制");
		break;
	case 3:
		m_tab.InsertItem(0, "设备控制");
		break;
	case 4:
		m_tab.InsertItem(0, "设备控制");
		break;
	case 5:
		m_tab.InsertItem(0, "设备控制");
		break;
	default:break;
	}

	m_tab.SetCurSel(0);

	ShowDialog(nBtnIndex, 0);

	SetTimer(1, 200, NULL);
}

void CModuleDialog::RefreshModule()
{
	int nRowIndex = 0;

	KillTimer(1);

	m_list1.DeleteAllItems();

	vector<CString>::iterator itParam = m_pModule->m_vecParam.begin();
	for (; itParam != m_pModule->m_vecParam.end(); itParam++)
	{
		if (!m_pModule->m_mapParam[*itParam]->IsValid(CSys::m_nRight)) {
			continue;
		}

		nRowIndex = m_list1.GetItemCount();
		m_list1.InsertItem(nRowIndex, "");
		m_list1.SetItem(nRowIndex, 0, LVIF_IMAGE, NULL, 0, 0, 0, 0);
		m_list1.SetItemText(nRowIndex, 1, *itParam);
		m_list1.SetItemText(nRowIndex, 2, m_pModule->m_mapParam[*itParam]->Value());
		m_list1.SetItemText(nRowIndex, 3, m_pModule->m_mapParam[*itParam]->Range());
	}

	SetTimer(1, 200, NULL);
}

void CModuleDialog::UpdateList()
{
	bool bState = false;//IO状态

	for (int i=0; i<m_list2.GetItemCount(); i++)//输入
	{
		CString sName;
		sName = m_list2.GetItemText(i, 1);
		g_pControl->ReadInput(sName, bState);
		m_list2.SetItem(i, 0, LVIF_IMAGE, NULL, (bState ? 1 : 0), 0, 0, 0, 0);//界面显示
	}

	for (int i=0; i<m_list3.GetItemCount(); i++)//输出
	{
		CString sName;
		sName = m_list3.GetItemText(i, 1);
		g_pControl->ReadOutput(sName, bState);
		m_list3.SetItem(i, 0, LVIF_IMAGE, NULL, (bState ? 1 : 0), 0, 0, 0, 0);
	}

	vector<CString>::iterator it = m_pModule->m_vecParam.begin();
	for (int i=0; it != m_pModule->m_vecParam.end(); it++)
	{
		if (m_pModule->m_mapParam[*it] == NULL) {
			continue;
		}

		if (!m_pModule->m_mapParam[*it]->IsValid(CSys::m_nRight)) {
			continue;
		}

		m_list1.SetItemText(i, 2, m_pModule->m_mapParam[*it]->Value());
		m_list1.SetItemText(i, 3, m_pModule->m_mapParam[*it]->Range());

		i++;
	}
}

void CModuleDialog::ShowDialog(int nIndex, int nSubIndex)
{
	vector<vector<CDialogEx*>>::iterator itList = m_vecDialogList.begin();
	for (int i=0; itList != m_vecDialogList.end(); itList++, i++)
	{
		vector<CDialogEx*>::iterator it = itList->begin();
		for (int j=0; it != itList->end(); it++, j++)
		{
			if (i == nIndex && j == nSubIndex) {
				if (m_nCurIndex == 0 && nSubIndex == 2) {
					m_list2.ShowWindow(SW_HIDE);
					m_list3.ShowWindow(SW_HIDE);
					if (m_bFlag) {
						m_pImageWindow->Show(true);
					}
				}
				else {
					m_list2.ShowWindow(SW_NORMAL);
					m_list3.ShowWindow(SW_NORMAL);
					if (m_bFlag) {
						m_pImageWindow->Show(false);
					}
				}
				(*it)->ShowWindow(SW_NORMAL);
			}
			else {
				(*it)->ShowWindow(SW_HIDE);
			}
		}
	}
}

void CModuleDialog::OnTcnSelchangeTab1(NMHDR *pNMHDR, LRESULT *pResult)
{
	int nIndex = 0;

	nIndex = m_tab.GetCurSel();

	if (nIndex >= m_vecDialogList[m_nCurIndex].size() || nIndex < 0) {
		if (m_nCurSubIndex < m_vecDialogList[m_nCurIndex].size() && nIndex >= 0) {
			nIndex = m_nCurSubIndex;
			m_tab.SetCurSel(m_nCurSubIndex);
		}
		else {
			nIndex = 0;
			m_tab.SetCurSel(0);
		}
	}

	m_nCurSubIndex = nIndex;

	ShowDialog(m_nCurIndex, nIndex);

	*pResult = 0;
}

void CModuleDialog::OnBnClickedButton1()
{
	OnCancel();
}

void CModuleDialog::OnBnClickedButton2()
{
	SwitchModule(static_cast<CModule*>(g_pRobot), 0);
}

void CModuleDialog::OnBnClickedButton3()
{
	SwitchModule(static_cast<CModule*>(g_pTray), 1);
}

void CModuleDialog::OnBnClickedButton4()
{
	SwitchModule(static_cast<CModule*>(g_pFixture), 2);
}

void CModuleDialog::OnBnClickedButton5()
{
	SwitchModule(static_cast<CModule*>(g_pBeltA), 3);
}

void CModuleDialog::OnBnClickedButton6()
{
	SwitchModule(static_cast<CModule*>(g_pBeltB), 4);
}

void CModuleDialog::OnBnClickedButton7()
{
	SwitchModule(static_cast<CModule*>(g_pMachine), 5);
}
