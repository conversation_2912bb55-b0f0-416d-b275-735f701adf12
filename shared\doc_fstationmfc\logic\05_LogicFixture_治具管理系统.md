# LogicFixture - 治具管理系统

## 概述
LogicFixture负责治具的上传、分离和AGV交互操作，是生产线中重要的工装管理模块。

## 类结构
```cpp
class CLogicFixture : public CThreadBase {
private:
    CFixture*               m_pFixture;     // 治具设备对象
    CThreadFunc*            m_pFuncUpload;   // 上传子线程
    CThreadFunc*            m_pFuncSeparate; // 分离子线程  
    CThreadFunc*            m_pFuncAgv;      // AGV子线程
};
```

## 主要功能模块

### 1. 三个并发子线程
- **Upload线程**: 治具上传操作 (OnFixtureUpload系列)
- **Separate线程**: 治具分离操作 (OnFixtureSeparate系列)
- **AGV线程**: AGV交互操作 (OnFixtureAgv系列)

### 2. 状态机流程
```
OnFixture00() → 子线程协调
OnFixture01() → Upload线程运行
OnFixture02() → Separate和AGV线程运行
```

### 3. 关键操作序列

#### 治具上传 (20个状态)
```
OnFixtureUpload00-20 → 完整的治具上传流程
```

#### 治具分离 (11个状态)
```
OnFixtureSeparate00-11 → 治具分离和定位
```

#### AGV交互 (5个状态)
```
OnFixtureAgv00-04 → AGV装卸治具
```

## 设备控制接口
- 治具升降控制
- 分离机构控制
- 传输带控制
- AGV对接控制

## 与其他模块交互
- 与LogicFixtureTransport协作进行治具传输
- 与机器人系统协调治具状态
- 通过CLogicMgr统一管理

## 设计特点
- 三线程并发处理提高效率
- 模块化的操作序列
- 完整的安全检查机制
- 灵活的AGV对接能力 

# 治具管理系统线程分析

## 概述

FStation的治具管理系统由两个核心线程组成：LogicFixture（治具操作管理）和LogicFixtureTransport（治具传输控制）。该系统负责治具的上传、分离、AGV交互和传输控制，是生产线中产品装配和传输的关键环节。

## 系统架构图

```
治具管理系统
├── LogicFixture (21KB) - 治具操作管理线程
│   ├── 自检模块 (SelfCheck00-03)
│   ├── 治具主控 (Fixture00-02)
│   ├── 治具上传模块 (FixtureUpload00-20)
│   ├── 治具分离模块 (FixtureSeparate00-11)
│   ├── AGV交互模块 (FixtureAgv00-04)
│   ├── m_pFuncUpload - 上传子线程
│   ├── m_pFuncSeparate - 分离子线程
│   └── m_pFuncAgv - AGV子线程
└── LogicFixtureTransport (24KB) - 治具传输控制线程
    ├── 独立状态机
    ├── Y轴传输控制
    ├── Z轴升降控制
    └── 路径规划和协调
```

## LogicFixture - 治具操作管理线程

### 类结构分析
```cpp
class CLogicFixture : public CThreadBase
{
private:
    CFixture*                m_pFixture;        // 治具控制对象
    CString                  m_sRet;            // 返回结果字符串
    
    // 状态管理映射表
    map<CString, bool>       m_mapFlag;         // 标志映射
    map<CString, DWORD>      m_mapTick;         // 时间计数
    map<CString, double>     m_mapPos;          // 位置映射
    
    // 三个功能子线程
    CThreadFunc*             m_pFuncUpload;     // 治具上传子线程
    CThreadFunc*             m_pFuncSeparate;   // 治具分离子线程
    CThreadFunc*             m_pFuncAgv;        // AGV交互子线程
};
```

### 功能模块详细分析

#### 1. 系统自检模块 (SelfCheck00-03)
```cpp
CStatus OnSelfCheck00();  // 治具系统初始化检查
CStatus OnSelfCheck01();  // 上传机构自检
CStatus OnSelfCheck02();  // 分离机构自检
CStatus OnSelfCheck03();  // AGV接口自检
```

**自检流程作用**:
- 确保所有治具机构处于安全位置
- 检查传感器和执行器状态
- 验证AGV通信接口正常
- 为后续操作建立安全基准

#### 2. 治具主控模块 (Fixture00-02)
```cpp
CStatus OnFixture00();    // 治具系统总控制
CStatus OnFixture01();    // 治具状态协调
CStatus OnFixture02();    // 治具操作调度
```

**主控职责**:
- 协调三个子线程的工作
- 管理治具系统的整体状态
- 处理操作优先级和时序

#### 3. 治具上传模块 (FixtureUpload00-20)

治具上传是最复杂的功能模块，包含21个详细状态：

```cpp
// 上传准备阶段 (00-05)
CStatus OnFixtureUpload00();   // 上传流程启动
CStatus OnFixtureUpload01();   // 上传位置检查
CStatus OnFixtureUpload02();   // 上传机构初始化
CStatus OnFixtureUpload03();   // 上传传感器检查
CStatus OnFixtureUpload04();   // 上传安全确认
CStatus OnFixtureUpload05();   // 上传参数设置

// 上传执行阶段 (06-15)
CStatus OnFixtureUpload06();   // 治具夹紧准备
CStatus OnFixtureUpload07();   // 治具夹紧执行
CStatus OnFixtureUpload08();   // 夹紧状态确认
CStatus OnFixtureUpload09();   // 上传机构启动
CStatus OnFixtureUpload10();   // 上传运动控制
CStatus OnFixtureUpload11();   // 上传位置监控
CStatus OnFixtureUpload12();   // 上传到位检查
CStatus OnFixtureUpload13();   // 上传精度校正
CStatus OnFixtureUpload14();   // 上传完成确认
CStatus OnFixtureUpload15();   // 上传状态更新

// 上传完成阶段 (16-20)
CStatus OnFixtureUpload16();   // 治具松开准备
CStatus OnFixtureUpload17();   // 治具松开执行
CStatus OnFixtureUpload18();   // 松开状态确认
CStatus OnFixtureUpload19();   // 上传机构复位
CStatus OnFixtureUpload20();   // 上传流程结束
```

**上传流程特点**:
- **精确控制**: 21个状态确保每个步骤的精确执行
- **安全机制**: 多重检查和确认环节
- **容错设计**: 每个阶段都有状态验证

#### 4. 治具分离模块 (FixtureSeparate00-11)

```cpp
// 分离准备阶段
CStatus OnFixtureSeparate00();     // 分离流程启动
CStatus OnFixtureSeparate00_0();   // 分离预备状态1
CStatus OnFixtureSeparate00_1();   // 分离预备状态2
CStatus OnFixtureSeparate01();     // 分离位置检查
CStatus OnFixtureSeparate02();     // 分离机构准备

// 分离执行阶段
CStatus OnFixtureSeparate03();     // 分离动作启动
CStatus OnFixtureSeparate04();     // 分离运动控制
CStatus OnFixtureSeparate05();     // 分离位置监控
CStatus OnFixtureSeparate06();     // 分离到位确认

// 分离完成阶段
CStatus OnFixtureSeparate07();     // 分离状态确认
CStatus OnFixtureSeparate08();     // 分离机构复位
CStatus OnFixtureSeparate09();     // 分离参数更新
CStatus OnFixtureSeparate10();     // 分离安全检查
CStatus OnFixtureSeparate11();     // 分离流程结束
```

**分离流程特点**:
- **多级子状态**: OnFixtureSeparate00包含两个子状态
- **精确定位**: 确保分离后的治具位置精确
- **状态反馈**: 完整的状态反馈机制

#### 5. AGV交互模块 (FixtureAgv00-04)

```cpp
CStatus OnFixtureAgv00();   // AGV通信初始化
CStatus OnFixtureAgv01();   // AGV状态查询
CStatus OnFixtureAgv02();   // AGV装载协调
CStatus OnFixtureAgv03();   // AGV卸载协调
CStatus OnFixtureAgv04();   // AGV交互完成
```

**AGV交互特点**:
- **通信协议**: 与AGV系统的标准化通信
- **状态同步**: 确保AGV和治具系统状态同步
- **安全协调**: 安全的装卸载协调机制

### 三子线程并发机制

#### 子线程创建和初始化
```cpp
CLogicFixture::CLogicFixture()
{
    // 创建三个功能子线程
    m_pFuncUpload = CLogicMgr::m_ThreadFactory.CreateThreadFunc("治具上传");
    m_pFuncSeparate = CLogicMgr::m_ThreadFactory.CreateThreadFunc("治具分离");
    m_pFuncAgv = CLogicMgr::m_ThreadFactory.CreateThreadFunc("AGV交互");
}

CStatus CLogicFixture::OnRun()
{
    // 设置子线程的执行动作
    m_pFuncUpload->SetAction(static_cast<THREAD_FUNC>(&CLogicFixture::OnFixtureUpload00));
    m_pFuncSeparate->SetAction(static_cast<THREAD_FUNC>(&CLogicFixture::OnFixtureSeparate00));
    m_pFuncAgv->SetAction(static_cast<THREAD_FUNC>(&CLogicFixture::OnFixtureAgv00));
    
    RETURN_STATE(&CLogicFixture::OnSelfCheck00, true);
}
```

#### 并发执行控制
```cpp
// 在主状态机中轮询执行三个子线程
EnumStatus statusUpload = m_pFuncUpload->Run(this);
EnumStatus statusSeparate = m_pFuncSeparate->Run(this);
EnumStatus statusAgv = m_pFuncAgv->Run(this);

// 统一处理子线程状态
if (statusUpload == emStop || statusSeparate == emStop || statusAgv == emStop) {
    RETURN_STOP();
}
```

## LogicFixtureTransport - 治具传输控制线程

### 核心功能
```cpp
class CLogicFixtureTransport : public CThreadBase
{
    // 负责治具在生产线上的Y轴、Z轴传输控制
    // 包含复杂的路径规划和位置控制逻辑
};
```

### 主要职责

#### 1. Y轴传输控制
- **水平传输**: 控制治具在水平方向的移动
- **位置精度**: 确保治具移动到精确位置
- **速度控制**: 根据工艺要求调整移动速度
- **路径规划**: 优化移动路径，避免冲突

#### 2. Z轴升降控制
- **垂直运动**: 控制治具的升降动作
- **压力控制**: 精确控制治具的下压力度
- **安全保护**: 防止过压和碰撞
- **位置反馈**: 实时监控Z轴位置

#### 3. 协调控制
- **多轴同步**: Y轴和Z轴的协调运动
- **时序控制**: 严格的动作时序管理
- **状态同步**: 与LogicFixture的状态同步
- **异常处理**: 传输过程中的异常处理

## 两线程协作机制

### 1. 功能分工
```
LogicFixture (操作控制)        LogicFixtureTransport (传输控制)
├── 治具上传操作              ├── Y轴水平传输
├── 治具分离操作              ├── Z轴垂直升降
├── AGV接口管理               ├── 路径规划控制
├── 状态管理和协调            ├── 位置精度控制
└── 安全监控                  └── 运动同步协调
```

### 2. 数据交互
```cpp
// 通过全局变量进行状态通信
VAR_FIXTURE("治具上传完成标志")      // LogicFixture → LogicFixtureTransport
VAR_FIXTURE("治具传输位置")          // LogicFixtureTransport → LogicFixture
VAR_FIXTURE("传输系统忙标志")        // 双向状态通信
```

### 3. 时序协调
```cpp
// 典型的协作时序
if (治具上传完成 && 传输系统就绪) {
    启动传输控制();
} else if (传输完成 && 分离系统就绪) {
    启动分离操作();
}
```

## 安全机制设计

### 1. 多重安全检查
```cpp
// 每个操作前的安全确认
if (!治具位置安全() || !传输路径清空() || !AGV状态正常()) {
    RETURN_PAUSE("安全条件不满足");
}
```

### 2. 异常恢复机制
```cpp
EnumStatus CLogicFixture::OnPause()
{
    // 安全停止所有治具机构
    g_pFixture->UploadBeltOff();        // 停止上传传输
    g_pFixture->SeparateZStop();        // 停止分离Z轴
    g_pFixture->LidZStop();             // 停止盖板Z轴
    g_pFixture->TransportYStop();       // 停止传输Y轴
    g_pFixture->TransportZStop();       // 停止传输Z轴
    
    // 暂停所有子线程
    m_pFuncUpload->SetStatus(emPause);
    m_pFuncSeparate->SetStatus(emPause);
    m_pFuncAgv->SetStatus(emPause);
    
    return emRun;
}
```

### 3. 位置监控和保护
```cpp
// 实时位置监控
map<CString, double> m_mapPos;  // 记录关键位置信息
// 位置越界保护
if (位置超出安全范围) {
    紧急停止();
    REPORT("治具位置越界", emLogLevelError);
}
```

## 与其他线程的接口

### 1. 与LogicRobot的协作
- **工件交互**: 提供治具供机器人装配工件
- **位置协调**: 协调治具位置和机器人操作位置
- **状态同步**: 同步治具状态和机器人工作状态

### 2. 与LogicTray的协作
- **流程衔接**: 治具处理完成后与托盘系统的衔接
- **产品传递**: 协调产品在治具和托盘间的传递
- **状态通信**: 相互通知处理状态和进度

### 3. 与LogicContinueRun的协作
- **流程协调**: 在连续运行模式下的流程协调
- **资源调度**: 治具资源的统一调度管理
- **性能监控**: 治具系统性能数据的上报

## 性能优化特点

### 1. 并行处理能力
- **三路并行**: 上传、分离、AGV三个功能并行执行
- **流水线作业**: 不同治具可以在不同工位同时操作
- **资源复用**: 合理的资源分配和复用机制

### 2. 精确控制
- **多级状态机**: 每个操作都有详细的状态分解
- **位置反馈**: 实时的位置监控和反馈
- **误差补偿**: 自动的位置误差补偿机制

### 3. 智能调度
- **优先级管理**: 根据生产需要调整操作优先级
- **预测控制**: 预测性的准备和调度
- **自适应调整**: 根据实际情况自适应调整参数

## 维护建议

### 1. 定期校准
- **位置精度校准**: 定期校准Y轴、Z轴的位置精度
- **压力标定**: 校准治具夹紧和下压的力度
- **传感器检查**: 检查位置传感器的准确性

### 2. 状态监控
- **操作成功率**: 监控各种操作的成功率
- **异常统计**: 统计和分析异常情况
- **性能分析**: 分析治具系统的性能指标

### 3. 预防性维护
- **机械磨损检查**: 定期检查机械部件的磨损情况
- **润滑维护**: 定期对运动部件进行润滑维护
- **电气检查**: 检查电气连接和控制信号

## 总结

治具管理系统通过LogicFixture和LogicFixtureTransport两个线程的协作，实现了治具操作的精确控制和高效传输。LogicFixture通过三个子线程的并发处理，确保了上传、分离、AGV交互功能的独立性和并行性；LogicFixtureTransport专注于传输控制，保证了治具移动的精确性和安全性。

这种分工明确、协作紧密的设计模式，不仅提高了系统的处理效率，还增强了系统的可维护性和扩展性。对于理解和维护该系统，需要重点关注两个线程之间的协作机制和各自的状态管理逻辑。 