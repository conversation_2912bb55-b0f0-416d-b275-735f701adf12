﻿#pragma once

#include "ThreadBase.h"
using namespace yzBase;

#include "Tray.h"

class CLogicTray : public CThreadBase
{
public:
	CLogicTray();
	virtual ~CLogicTray();

public:
	// 基础控制函数
	EnumStatus OnSafeCheck();       // 安全检查：检查托盘系统安全状态
	EnumStatus OnStart();           // 启动控制：初始化托盘系统
	EnumStatus OnPause();           // 暂停控制：暂停托盘操作
	EnumStatus OnResume();          // 恢复控制：从暂停状态恢复托盘操作
	EnumStatus OnStop();            // 停止控制：停止托盘系统

public:
	// 主运行函数
	CStatus OnRun();                // 主状态机运行函数：协调托盘系统工作流程

	// ========== 自检流程 (OnSelfCheck00-OnSelfCheck03) ==========
	CStatus OnSelfCheck00();        // 自检准备：准备托盘系统自检
	CStatus OnSelfCheck01();        // 自检执行：执行托盘系统自检
	CStatus OnSelfCheck02();        // 自检验证：验证托盘系统自检结果
	CStatus OnSelfCheck03();        // 自检完成：完成托盘系统自检

	// ========== 托盘主流程 (OnTray00-OnTray02) ==========
	CStatus OnTray00();             // 托盘流程控制：控制托盘主流程
	CStatus OnTray01();             // 托盘状态检查：检查托盘状态
	CStatus OnTray02();             // 托盘流程分配：分配托盘流程

	// ========== 空托盘主流程 (OnEmptyTray00-OnEmptyTray01) ==========
	CStatus OnEmptyTray00();        // 空托盘流程控制：控制空托盘主流程
	CStatus OnEmptyTray01();        // 空托盘状态管理：管理空托盘状态

	// ========== 空托盘进料流程 (OnEmptyTrayFeed00-OnEmptyTrayFeed10) ==========
	CStatus OnEmptyTrayFeed00();    // 空托盘进料准备：准备空托盘进料
	CStatus OnEmptyTrayFeed01();    // 空托盘进料检测：检测空托盘进料状态
	CStatus OnEmptyTrayFeed02();    // 空托盘进料定位：定位空托盘进料位置
	CStatus OnEmptyTrayFeed03();    // 空托盘进料夹紧：夹紧空托盘
	CStatus OnEmptyTrayFeed04();    // 空托盘进料抬升：抬升空托盘到工作位置
	CStatus OnEmptyTrayFeed05();    // 空托盘进料验证：验证空托盘进料状态
	CStatus OnEmptyTrayFeed06();    // 空托盘进料完成：完成空托盘进料
	CStatus OnEmptyTrayFeed07();    // 空托盘进料后处理：空托盘进料后处理
	CStatus OnEmptyTrayFeed08();    // 空托盘进料清理：清理空托盘进料区域
	CStatus OnEmptyTrayFeed09();    // 空托盘进料记录：记录空托盘进料数据
	CStatus OnEmptyTrayFeed10();    // 空托盘进料流程结束：结束空托盘进料流程

	// ========== 空托盘回收流程 (OnEmptyTrayBack00-OnEmptyTrayBack13) ==========
	CStatus OnEmptyTrayBack00();    // 空托盘回收准备：准备空托盘回收
	CStatus OnEmptyTrayBack01();    // 空托盘回收检测：检测空托盘回收条件
	CStatus OnEmptyTrayBack02();    // 空托盘回收定位：定位空托盘回收位置
	CStatus OnEmptyTrayBack03();    // 空托盘回收下降：下降空托盘到回收位置
	CStatus OnEmptyTrayBack04();    // 空托盘回收松开：松开空托盘夹紧
	CStatus OnEmptyTrayBack05();    // 空托盘回收推出：推出空托盘
	CStatus OnEmptyTrayBack06();    // 空托盘回收验证：验证空托盘回收状态
	CStatus OnEmptyTrayBack07();    // 空托盘回收完成：完成空托盘回收
	CStatus OnEmptyTrayBack08();    // 空托盘回收后处理：空托盘回收后处理
	CStatus OnEmptyTrayBack09();    // 空托盘回收清理：清理空托盘回收区域
	CStatus OnEmptyTrayBack10();    // 空托盘回收记录：记录空托盘回收数据
	CStatus OnEmptyTrayBack11();    // 空托盘回收状态更新：更新空托盘回收状态
	CStatus OnEmptyTrayBack12();    // 空托盘回收流程验证：验证空托盘回收流程
	CStatus OnEmptyTrayBack13();    // 空托盘回收流程结束：结束空托盘回收流程

	// ========== 满托盘主流程 (OnFullTray00-OnFullTray01) ==========
	CStatus OnFullTray00();         // 满托盘流程控制：控制满托盘主流程
	CStatus OnFullTray01();         // 满托盘状态管理：管理满托盘状态

	// ========== 满托盘进料流程 (OnFullTrayFeed00-OnFullTrayFeed13) ==========
	CStatus OnFullTrayFeed00();     // 满托盘进料准备：准备满托盘进料
	CStatus OnFullTrayFeed01();     // 满托盘进料检测：检测满托盘进料状态
	CStatus OnFullTrayFeed02();     // 满托盘进料定位：定位满托盘进料位置
	CStatus OnFullTrayFeed03();     // 满托盘进料夹紧：夹紧满托盘
	CStatus OnFullTrayFeed03_0();   // 满托盘进料夹紧验证：验证满托盘夹紧状态
	CStatus OnFullTrayFeed04();     // 满托盘进料抬升：抬升满托盘到工作位置
	CStatus OnFullTrayFeed05();     // 满托盘进料验证：验证满托盘进料状态
	CStatus OnFullTrayFeed05_0();   // 满托盘进料状态检查：检查满托盘进料状态
	CStatus OnFullTrayFeed05_1();   // 满托盘进料位置检查：检查满托盘进料位置
	CStatus OnFullTrayFeed06();     // 满托盘进料完成：完成满托盘进料
	CStatus OnFullTrayFeed07();     // 满托盘进料后处理：满托盘进料后处理
	CStatus OnFullTrayFeed08();     // 满托盘进料清理：清理满托盘进料区域
	CStatus OnFullTrayFeed09();     // 满托盘进料记录：记录满托盘进料数据
	CStatus OnFullTrayFeed10();     // 满托盘进料状态更新：更新满托盘进料状态
	CStatus OnFullTrayFeed11();     // 满托盘进料流程验证：验证满托盘进料流程
	CStatus OnFullTrayFeed12();     // 满托盘进料流程完成：完成满托盘进料流程
	CStatus OnFullTrayFeed13();     // 满托盘进料流程结束：结束满托盘进料流程

	// ========== 满托盘回收流程 (OnFullTrayBack00-OnFullTrayBack11) ==========
	CStatus OnFullTrayBack00();     // 满托盘回收准备：准备满托盘回收
	CStatus OnFullTrayBack01();     // 满托盘回收检测：检测满托盘回收条件
	CStatus OnFullTrayBack02();     // 满托盘回收定位：定位满托盘回收位置
	CStatus OnFullTrayBack03();     // 满托盘回收下降：下降满托盘到回收位置
	CStatus OnFullTrayBack03_0();   // 满托盘回收下降验证：验证满托盘下降状态
	CStatus OnFullTrayBack03_1();   // 满托盘回收下降完成：完成满托盘下降
	CStatus OnFullTrayBack04();     // 满托盘回收松开：松开满托盘夹紧
	CStatus OnFullTrayBack04_0();   // 满托盘回收松开验证：验证满托盘松开状态
	CStatus OnFullTrayBack05();     // 满托盘回收推出：推出满托盘
	CStatus OnFullTrayBack06();     // 满托盘回收验证：验证满托盘回收状态
	CStatus OnFullTrayBack07();     // 满托盘回收完成：完成满托盘回收
	CStatus OnFullTrayBack08();     // 满托盘回收后处理：满托盘回收后处理
	CStatus OnFullTrayBack09();     // 满托盘回收清理：清理满托盘回收区域
	CStatus OnFullTrayBack10();     // 满托盘回收记录：记录满托盘回收数据
	CStatus OnFullTrayBack11();     // 满托盘回收流程结束：结束满托盘回收流程

private:
	CTray*					m_pTray;            // 托盘设备对象指针

	CString					m_sRet;             // 函数返回字符串结果

	map<CString, bool>		m_mapFlag;          // 标志映射表，记录各种状态标志
	map<CString, DWORD>		m_mapTick;          // 时间计时器映射表，用于超时控制
	map<CString, double>	m_mapPos;           // 位置映射表，记录各种位置信息

	CThreadFunc*			m_pFuncFullTray;    // 满托盘线程函数指针
	CThreadFunc*			m_pFuncEmptyTray;   // 空托盘线程函数指针
};
