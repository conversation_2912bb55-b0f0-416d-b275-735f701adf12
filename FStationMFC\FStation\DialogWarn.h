﻿#pragma once

#include "Resource.h"

#include "ThreadBase.h"

// CDialogWarn 对话框

class CDialogWarn : public CDialogEx
{
	DECLARE_DYNAMIC(CDialogWarn)

public:
	CDialogWarn(CString sMsg, CString sMsgEx, bool bWarn = false, CWnd* pParent = NULL);   // 标准构造函数
	virtual ~CDialogWarn();

// 对话框数据
	enum { IDD = IDD_DIALOG_WARN };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持
	virtual BOOL OnInitDialog();

	DECLARE_MESSAGE_MAP()
	afx_msg void OnPaint();
	afx_msg void OnDestroy();
	afx_msg void OnTimer(UINT_PTR nIDEvent);
	afx_msg HBRUSH OnCtlColor(CDC* pDC, CWnd* pWnd, UINT nCtlColor);

public:
	bool AutoExitCheck();
	bool GetResult();

private:
	CString m_sMsg;
	CString m_sMsgEx;

	bool	m_bWarn;

	bool	m_bResult;

	bool	m_bShowFlag;

	CFont	m_font;
};
