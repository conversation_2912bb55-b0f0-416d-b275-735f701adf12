{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/paho-mqttpp3-x64-windows-1.5.2-6c586939-7816-4156-ae6a-d7ff0def7ba3", "name": "paho-mqttpp3:x64-windows@1.5.2 b007d5291c8dfd0a13536928c2bc1560b13ebf8780d8468612fc9cf8d93d5193", "creationInfo": {"creators": ["Tool: vcpkg-2025-06-02-145689e84b7637525510e2c9b4ee603fda046b56"], "created": "2025-06-20T07:01:23Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "paho-mqttpp3", "SPDXID": "SPDXRef-port", "versionInfo": "1.5.2", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/paho-mqttpp3", "homepage": "https://github.com/eclipse/paho.mqtt.cpp", "licenseConcluded": "EPL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Paho project provides open-source C++ wrapper for Paho C library", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "paho-mqttpp3:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "b007d5291c8dfd0a13536928c2bc1560b13ebf8780d8468612fc9cf8d93d5193", "downloadLocation": "NONE", "licenseConcluded": "EPL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "eclipse/paho.mqtt.cpp", "downloadLocation": "git+https://github.com/eclipse/paho.mqtt.cpp@v1.5.2", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "2d7645c1a7681cdfb643bb77576412655220ee160eb18dbe6ffc4ed39711f2fc5fb9884e0d8244a7a21b34aec2e602042cb18b44159680d0b97823c418c23566"}]}], "files": [{"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "f9851f193500982410a907b4716b8e27ab10636273cbf7c54d44fca39c5343f1"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "e6b92ac64402b63a39ad729659590a3e150a641e5b6926552a3209c53eac12c0"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}