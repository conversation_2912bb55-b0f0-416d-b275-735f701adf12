﻿#include "StdAfx.h"
#include "ExcelEx.h"

CExcelEx::CExcelEx(void)
	: m_pApplication(NULL)
	, m_pWorkBook(NULL)
	, m_pWorkBooks(NULL)
	, m_pWorkSheet(NULL)
	, m_pWorkSheets(NULL)
	, m_nRowCount(0)
	, m_nColumnCount(0)
{
}

CExcelEx::~CExcelEx(void)
{
	if (m_pWorkSheet != NULL)
	{
		m_pWorkSheet->ReleaseDispatch();
		delete m_pWorkSheet;
		m_pWorkSheet = NULL;
	}
	if (m_pWorkSheets != NULL)
	{
		m_pWorkSheets->ReleaseDispatch();
		delete m_pWorkSheets;
		m_pWorkSheets = NULL;
	}
	if (m_pWorkBook != NULL)
	{
		m_pWorkBook->ReleaseDispatch();
		delete m_pWorkBook;
		m_pWorkBook = NULL;
	}
	if (m_pWorkBooks != NULL)
	{
		m_pWorkBooks->ReleaseDispatch();
		delete m_pWorkBooks;
		m_pWorkBooks = NULL;
	}
	if (m_pApplication)
	{
		m_pApplication->Quit();
		m_pApplication->ReleaseDispatch();
		delete m_pApplication;
		m_pApplication = NULL;
	}
}

bool CExcelEx::CreateInstance()
{
	bool bResult = true;
	if (m_pApplication != NULL)
		return bResult;
	m_pApplication = new CApplication;
	if (!m_pApplication->CreateDispatch("Excel.Application"))
	{
		MessageBox(NULL, "Error! Create Excel Application Server Fail!", "Create Excel Application Server", MB_ICONERROR);
		bResult = false;
	}
	return bResult;
}

bool CExcelEx::IsVersionOk()//判断当前Excel的版本是否是2010版本
{
	bool bResult = true;
	if (m_pApplication == NULL)
		return false;
	CString strExcelVersion = m_pApplication->get_Version();
	int nCurPos = 0;
	strExcelVersion = strExcelVersion.Tokenize(".", nCurPos);
	if (strExcelVersion != "14")
	{
		MessageBox(NULL, "Error! Office Version is not 2010", "Judge Office Version", MB_ICONERROR);
		bResult = false;
	}
	return bResult;
}

void CExcelEx::SetVisible(bool bVisible)
{
	if (m_pApplication != NULL)
	{
		m_pApplication->put_Visible(bVisible ? TRUE : FALSE);
		m_pApplication->put_UserControl(FALSE);
	}
}

bool CExcelEx::OpenExcel(CString sExcelName, bool bFlagCreate)
{
	//获取所有工作薄
	if (m_pApplication == NULL)
		return false;
	m_sExcelName = sExcelName;
	LPDISPATCH lpDisp = NULL;
	if (m_pWorkBooks == NULL)
	{
		m_pWorkBooks = new CWorkbooks;
		lpDisp = m_pApplication->get_Workbooks();
		m_pWorkBooks->AttachDispatch(lpDisp);
	}
	//关联工作薄
	bool bResult = true;
	try
	{
		lpDisp = m_pWorkBooks->Open(sExcelName, vtMissing, vtMissing, vtMissing, vtMissing, vtMissing, vtMissing, vtMissing, vtMissing, vtMissing, vtMissing, vtMissing, vtMissing, vtMissing, vtMissing);
	}
	catch (...)//失败则创建文件
	{
		if (bFlagCreate)
			lpDisp = m_pWorkBooks->Add(vtMissing);
		else
			bResult = false;
	}
	if (bResult)
	{
		if (m_pWorkBook == NULL)
			m_pWorkBook = new CWorkbook;
		else
			m_pWorkBook->ReleaseDispatch();
		m_pWorkBook->AttachDispatch(lpDisp);
	}
	return bResult;
}

bool CExcelEx::OpenSheet(CString sSheetName, bool bFlagCreate)
{
	if (m_pWorkBook == NULL)
		return false;
	LPDISPATCH lpDisp = NULL;
	//获取工作薄所有工作表
	if (m_pWorkSheets == NULL)
	{
		m_pWorkSheets = new CWorksheets;
	}
	else
	{
		m_pWorkSheets->ReleaseDispatch();
	}
	lpDisp = m_pWorkBook->get_Sheets();
	m_pWorkSheets->AttachDispatch(lpDisp);
	//获取工作表
	bool bResult = true;
	try  
	{  
		//打开一个已有的工作表(wooksheet)  
		lpDisp = m_pWorkSheets->get_Item(_variant_t(sSheetName));
		if (m_pWorkSheet == NULL)
		{
			m_pWorkSheet = new CWorksheet;
		}
		else
		{
			m_pWorkSheet->ReleaseDispatch();
		}
		m_pWorkSheet->AttachDispatch(lpDisp);  
	}  
	catch(...)  
	{  
		if (bFlagCreate)
		{
			lpDisp = m_pWorkSheets->Add(vtMissing, vtMissing, _variant_t((long)1), vtMissing);  
			if (m_pWorkSheet == NULL)
			{
				m_pWorkSheet = new CWorksheet;
			}
			else
			{
				m_pWorkSheet->ReleaseDispatch();
			}
			m_pWorkSheet->AttachDispatch(lpDisp);  
			m_pWorkSheet->put_Name(sSheetName);//创建工作表名
			SaveExcel(m_sExcelName, true);
		}
		else
		{
			bResult = false;
		}
	}
	
	if (bResult) {
		m_nRowCount = GetRowCount();
		m_nColumnCount = GetColumnCount();
	}

	return bResult;
}

void CExcelEx::SaveExcel(CString sExcelName, bool bSaveAs)
{
	if (m_pWorkBook == NULL)
		return;
	if (bSaveAs)
	{
		m_pApplication->put_DisplayAlerts(FALSE);
		m_pWorkBook->SaveAs(_variant_t(sExcelName), vtMissing, vtMissing, vtMissing, vtMissing, vtMissing, 0, vtMissing, vtMissing, vtMissing, vtMissing, vtMissing); 
		m_pApplication->put_DisplayAlerts(TRUE);
	}
	else
		m_pWorkBook->Save();
}

int CExcelEx::GetRowCount()
{
	if (m_pWorkSheet == NULL)
		return 0;
	CRange usedRange;
	CRange rowRange;
	//获得行句柄
	LPDISPATCH lpDisp  = NULL;
	lpDisp = m_pWorkSheet->get_UsedRange();
	usedRange.AttachDispatch(lpDisp);
	lpDisp = usedRange.get_Rows();
	rowRange.AttachDispatch(lpDisp, true);
	//获取行数
	int nRowCount = 0;
	nRowCount = rowRange.get_Count();//行数
	//释放资源
	rowRange.ReleaseDispatch();
	usedRange.ReleaseDispatch();
	return nRowCount;
}

int CExcelEx::GetColumnCount()
{
	if (m_pWorkSheet == NULL)
		return 0;
	CRange usedRange;
	CRange columnRange;
	//获得行数
	LPDISPATCH lpDisp  = NULL;
	lpDisp = m_pWorkSheet->get_UsedRange();
	usedRange.AttachDispatch(lpDisp);
	lpDisp = usedRange.get_Columns();
	columnRange.AttachDispatch(lpDisp, true);
	//获得列数
	int nColumnCount = 0;
	nColumnCount = columnRange.get_Count();//行数
	//释放资源
	columnRange.ReleaseDispatch();
	usedRange.ReleaseDispatch();
	return nColumnCount;
}

bool CExcelEx::GetValue(int nRowIndex, int nColumnIndex, CString &str)
{
	if (m_pWorkSheet == NULL)
		return false;
	bool bResult = true;
	LPDISPATCH lpDisp = NULL;
	//获取单元格
	CRange range;
	lpDisp = m_pWorkSheet->get_Cells();
	range.AttachDispatch(lpDisp);

	//获取单元格值
	range.AttachDispatch(range.get_Item (COleVariant((long)nRowIndex + 1),COleVariant((long)nColumnIndex + 1)).pdispVal);  
	COleVariant vResult = range.get_Value2();
	switch (vResult.vt)
	{
	case VT_EMPTY:
		str.Empty();
		break;
	case VT_BSTR:
		str = vResult.bstrVal;
		break;
	case VT_R8:
		str.Format("%d", (unsigned int)vResult.dblVal);
		break;
	default:bResult = false;
	}

	range.ReleaseDispatch();
	return bResult;
}

bool CExcelEx::GetValue(int nRowIndex, int nColumnIndex, double &dbValue)
{
	if (m_pWorkSheet == NULL)
		return false;
	bool bResult = true;
	LPDISPATCH lpDisp = NULL;
	//获取单元格
	CRange range;
	lpDisp = m_pWorkSheet->get_Cells();
	range.AttachDispatch(lpDisp);

	//获取单元格值
	range.AttachDispatch(range.get_Item (COleVariant((long)nRowIndex + 1),COleVariant((long)nColumnIndex + 1)).pdispVal);  
	COleVariant vResult = range.get_Value2();
	switch (vResult.vt)
	{
	case VT_EMPTY:
		dbValue = 0;
		break;
	case VT_R8:
		dbValue = (double)(vResult.dblVal);
		break;
	default:bResult = false;
	}
	range.ReleaseDispatch();
	return bResult;
}

bool CExcelEx::GetValue(int nRowIndex, int nColumnIndex, int &nValue)
{
	if (m_pWorkSheet == NULL)
		return false;
	bool bResult = true;
	LPDISPATCH lpDisp = NULL;
	//获取单元格
	CRange range;
	lpDisp = m_pWorkSheet->get_Cells();
	range.AttachDispatch(lpDisp);

	//获取单元格值
	range.AttachDispatch(range.get_Item (COleVariant((long)nRowIndex + 1),COleVariant((long)nColumnIndex + 1)).pdispVal);  
	COleVariant vResult = range.get_Value2();
	switch (vResult.vt)
	{
	case VT_EMPTY:
		nValue = 0;
		break;
	case VT_R8:
		nValue = (int)(vResult.dblVal);
		break;
	default:bResult = false;
	}
	range.ReleaseDispatch();
	return bResult;
}

bool CExcelEx::GetByTitle(CString sTitle, vector<ITEMINFO> &vItem)
{
	if (m_nRowCount < 1 || m_nColumnCount < 1)
		return false;
	CString strTemp;
	bool bResult = false;
	int nRow = 0, nColumn = 0;//标题所在行与列
	for (int i = 0; i < m_nRowCount; i++)
	{
		for (int j=0; j<m_nColumnCount; j++)
		{
			if (GetValue(i, j, strTemp))
			{
				if (strTemp == sTitle)
				{
					nRow = i;
					nColumn = j;
					bResult = true;
					break;
				}
			}
		}
		if (bResult)
			break;
	}

	if (bResult)
	{
		for (int i = nRow + 1; i < m_nRowCount; i++)
		{
			if (GetValue(i, nColumn, strTemp))
			{
				ITEMINFO stItem;
				stItem.nRow = i;
				stItem.nCol = nColumn;
				stItem.sValue = strTemp;

				vItem.push_back(stItem);
			}
		}
	}

	return bResult;
}

bool CExcelEx::ReadExcel(CString sExcel, CString sSheet, bool bAutoCreate)
{
	if (!CreateInstance())
		return false;
	if (!IsVersionOk())
		return false;
	SetVisible(false);
	if (!OpenExcel(sExcel, bAutoCreate))
		return false;
	if (!OpenSheet(sSheet, bAutoCreate))
		return false;
	return true;
}


void CExcelEx::SetValue(int nRowIndex, int nColumnIndex, CString str)
{
	if (m_pWorkSheet == NULL)
		return;
	LPDISPATCH lpDisp = NULL;
	//获取单元格
	CRange range;
	lpDisp = m_pWorkSheet->get_Cells();
	range.AttachDispatch(lpDisp);
	range.put_Item(_variant_t((LONG)(nRowIndex + 1)), _variant_t((LONG)(nColumnIndex + 1)), _variant_t(str));
	range.ReleaseDispatch();
	m_pWorkBook->Save();
}

void CExcelEx::SetValue(int nRowIndex, int nColumnIndex, int nVal)
{
	if (m_pWorkSheet == NULL)
		return;
	LPDISPATCH lpDisp = NULL;
	//获取单元格
	CRange range;
	lpDisp = m_pWorkSheet->get_Cells();
	range.AttachDispatch(lpDisp);
	range.put_Item(_variant_t((LONG)(nRowIndex + 1)), _variant_t((LONG)(nColumnIndex + 1)), _variant_t((LONG)nVal));
	range.ReleaseDispatch();
	m_pWorkBook->Save();
}

void CExcelEx::SetValue(int nRowIndex, int nColumnIndex, double dbVal)
{
	if (m_pWorkSheet == NULL)
		return;
	LPDISPATCH lpDisp = NULL;
	//获取单元格
	CRange range;
	lpDisp = m_pWorkSheet->get_Cells();
	range.AttachDispatch(lpDisp);
	range.put_Item(_variant_t((LONG)(nRowIndex + 1)), _variant_t((LONG)(nColumnIndex + 1)), _variant_t((LONG)dbVal));
	range.ReleaseDispatch();
	m_pWorkBook->Save();
}

void CExcelEx::ResetContent()
{
	if (m_pWorkSheet == NULL)
		return;
	LPDISPATCH lpDisp = NULL;
	//获取单元格
	CRange range;
	lpDisp = m_pWorkSheet->get_Cells();
	range.AttachDispatch(lpDisp);
	range.Clear();
	range.ReleaseDispatch();
	m_pWorkBook->Save();
}

void CExcelEx::SetColumnStyle(int nIndex, int nType)
{
	if (m_pWorkSheet == NULL)
		return;
	LPDISPATCH lpDisp = NULL;
	//获得所有列
	CRange range;
	lpDisp = m_pWorkSheet->get_Columns();
	range.AttachDispatch(lpDisp, true);
	//选择列
	range.AttachDispatch(range.get_Item(COleVariant((long)(nIndex + 1)),vtMissing).pdispVal, TRUE);
	int nVal = 1;
	switch (nType)
	{
	case -1:
		nVal = -4131;
		break;
	case 1:
		nVal = -4152;
		break;
	default:
		nVal = -4108;
		break;
	}
	range.put_HorizontalAlignment(COleVariant((long)nVal));
	range.ReleaseDispatch();
	m_pWorkBook->Save();
}
