﻿// FStationDlg.cpp : 实现文件
//

#include "stdafx.h"
#include "FStation.h"
#include "FStationDlg.h"
#include "afxdialogex.h"

#include "ModuleDialog.h"
#include "DialogCamera.h"
#include "DialogWarnCount.h"
#include "DialogCheck.h"
#include "DialogLogin.h"
#include "DialogProduct.h"
#include "ScadaCommandHandler.h"

#include "BaseApi.h"

#include "LogicMgr.h"
#include "Sys.h"
#include "Pro.h"
#include "Mes.h"
#include "Dat.h"
#include "json/json.h"

#include "SimpleSocketInterface.h"


#ifdef _DEBUG
#define new DEBUG_NEW
#endif

// 静态成员初始化
int CFStationDlg::m_nMQTTStatusCounter = 0;






// CFStationDlg 对话框

static UINT nIndicators[] = {
	IDS_SIG1,
	IDS_SIG2,
	IDS_SIG3,
	IDS_SIG4,
	IDS_SIG5
};

bool CInBeltMonitor::Excute()
{
	CString sRet;

	while (!m_bExit)
	{
		for (int i=0; i<3; i++)
		{
			if (g_pMachine->m_stInBeltStatus[i].bInFlag) {
				continue;
			}

			switch (i)
			{
			case 0:
				sRet = g_pRobot->InBeltAInStatus();
				break;
			case 1:
				sRet = g_pRobot->InBeltBInStatus();
				break;
			case 2:
				sRet = g_pRobot->InBeltCInStatus();
				break;
			default:return false;
			}

			if (sRet == "On") {
				g_pMachine->m_stInBeltStatus[i].bInFlag = true;
				g_pMachine->m_stInBeltStatus[i].tNowIn = GetTickCount();
			}
		}

		Sleep(1);
	}

	return true;
}

bool CWarnStatus::Excute()
{
	DWORD nTick = 0;

	while (!m_bExit)
	{
		if (g_nWarnTimes > 0) {
			static bool bFlag = true;
			if (bFlag) {
				g_pMachine->RedLightOn();

				if (g_bWarnFlag && !g_bWarn) {
					g_pMachine->BuzzerOn();
				}
				else {
					g_pMachine->BuzzerOff();
				}
				Sleep(500);
			}
			else {
				if (!g_bWarn) {
					g_pMachine->RedLightOff();
				}
				g_pMachine->BuzzerOff();
				Sleep(300);
			}

			bFlag = !bFlag;

			continue;
		}

		g_pMachine->BuzzerOff();

		if (CSys::m_bInit) {
			if (CLogicMgr::isRunning()) {
				// 机械手工作时显示绿灯，待机时显示黄灯
				static bool bWaitFlashFlag = true;
				
				// 检查是否需要显示黄灯（待机状态或治具预警）
				if (g_bWaitFlag || VAR_FIXTURE_B("治具预警标志")) {
					// 黄灯闪烁
					if (bWaitFlashFlag) {
						g_pMachine->YellowLightOn();
						Sleep(500);
					}
					else {
						g_pMachine->YellowLightOff();
						Sleep(300);
					}
					bWaitFlashFlag = !bWaitFlashFlag;
					continue;
				}
				else {
					// 机械手正常工作，显示绿灯
					g_pMachine->GreenLightOn();
				}
			}
			else {
				// 系统未运行，显示黄灯
				g_pMachine->YellowLightOn();
			}
		}
		else {
			if (CLogicMgr::m_mapThread["Reset"].pThread->GetStatus() == emRun) {
				g_pMachine->GreenLightOn();
			}
			else if (CLogicMgr::m_mapThread["Reset"].pThread->GetStatus() == emPause) {
				g_pMachine->YellowLightOn();
			}
			else {
				g_pMachine->YellowLightOff();
			}
		}
		
		Sleep(1);
	}

	return true;
}

CFStationDlg::CFStationDlg(CWnd* pParent /*=NULL*/)
	: CDialogEx(CFStationDlg::IDD, pParent)
	, m_BkColor(RGB(153, 217, 234))
	, m_pDlgAgv(NULL)
{
	m_hIcon = AfxGetApp()->LoadIcon(IDR_MAINFRAME);

	g_hInstDebug = LoadLibrary("DebugSdk.dll");

	g_pImageWndUp = CImageWindow::CreateInstance("Up");
	g_pImageWndDn = CImageWindow::CreateInstance("Dn");
	g_pImageWndTray = CImageWindow::CreateInstance("Tray");
}

CFStationDlg::~CFStationDlg()
{
	::UnregisterHotKey(g_hMainWnd, 101);
	::UnregisterHotKey(g_hMainWnd, 102);
	::UnregisterHotKey(g_hMainWnd, 103);
	::UnregisterHotKey(g_hMainWnd, 104);
	::UnregisterHotKey(g_hMainWnd, 105);
	::UnregisterHotKey(g_hMainWnd, 106);

	FreeLibrary(g_hInstDebug);

	CPro::DeInit();

	CSys::DeInit();

	delete g_pImageWndUp;
	delete g_pImageWndDn;
	delete g_pImageWndTray; 

	delete m_pDlgAgv;
	
	

	CleanupSocketInterface();
}

void CFStationDlg::InitializeSocketInterface()
{

	// 获取Socket接口实例
		SimpleSocketInterface* pSocket = SimpleSocketInterface::GetInstance();

	try {
		// 初始化SCADA命令处理器
		// 注意：ScadaCommandHandler.h已在文件顶部包含
		CScadaCommandHandler* pScadaHandler = GetScadaCommandHandler();
		if (pScadaHandler) {
			if (pScadaHandler->Initialize()) {
				REPORT(_T("SCADA命令处理器初始化成功"), emLogLevelNormal);
			} else {
				REPORT(_T("SCADA命令处理器初始化失败"), emLogLevelError);
			}
		}

		// 注意：不再手动设置配置，让SocketInterface使用配置文件中的设置
		// 配置文件路径：config/socket_config.json
		// 如果需要修改配置，请直接修改配置文件而不是硬编码

		// 移除硬编码配置，使用配置文件设置
		// SocketInterface构造函数会自动加载配置文件

		// 启动连接和接收处理
		if (pSocket->Connect()) {
			REPORT(_T("Socket接口初始化成功，已连接到MQTT网关"), emLogLevelNormal);
		} else {
			REPORT(_T("Socket接口连接失败"), emLogLevelError);
		}



	} catch (...) {
		REPORT(_T("Socket接口初始化异常"), emLogLevelError);
	}
}

void CFStationDlg::CleanupSocketInterface()
{

	try {
		// 清理SCADA命令处理器
		// 注意：ScadaCommandHandler.h已在文件顶部包含
		CScadaCommandHandler::DestroyInstance();
		REPORT(_T("SCADA命令处理器已清理"), emLogLevelNormal);

		SimpleSocketInterface* pSocket = SimpleSocketInterface::GetInstance();
		if (pSocket) {
			pSocket->Disconnect();
			SimpleSocketInterface::DestroyInstance();
			REPORT(_T("Socket接口已清理"), emLogLevelNormal);
		}
	} catch (...) {
		REPORT(_T("Socket接口清理异常"), emLogLevelError);
	}
}

void CFStationDlg::OnProductionStatusChanged()
{


	try {
		// 当生产状态变化时，主动上报状态
		SimpleSocketInterface* pSocket = SimpleSocketInterface::GetInstance();
		if (pSocket && pSocket->IsConnected()) {
			
			// 获取当前状态
			CString currentStatus;
			if (CLogicMgr::isRunning()) {
				currentStatus = _T("RUNNING");
			} else if (CLogicMgr::isPause()) {
				currentStatus = _T("PAUSED");
			} else {
				currentStatus = _T("STOPPED");
			}
			
			// 获取生产计数
			int prodCount = 0;
			int errCount = 0;
			
			// 从数据收集中获取计数
			map<CString, DATACOLLECTION>::iterator itSuccess = CDat::m_mapDataCollection.find(_T("成功数"));
			if (itSuccess != CDat::m_mapDataCollection.end() && itSuccess->second.pDat) {
				prodCount = itSuccess->second.pDat->I();
			}
			
			map<CString, DATACOLLECTION>::iterator itTotal = CDat::m_mapDataCollection.find(_T("总测试数"));
			if (itTotal != CDat::m_mapDataCollection.end() && itTotal->second.pDat) {
				int totalCount = itTotal->second.pDat->I();
				errCount = totalCount - prodCount;
			}

			// 检查MQTT事件上报功能是否启用
			if (VAR_MACHINE_B("MQTT功能启用")) {
				// 发送设备状态
				SimpleSocketInterface::SendDeviceStatus();
			}
		}
	} catch (...) {
		REPORT(_T("上报生产状态异常"), emLogLevelError);
	}
}

void CFStationDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_BUTTON1, m_btnLogin);
	DDX_Control(pDX, IDC_BUTTON2, m_btnReset);
	DDX_Control(pDX, IDC_BUTTON3, m_btnDebug);
	DDX_Control(pDX, IDC_BUTTON4, m_btnProduction);
	DDX_Control(pDX, IDC_BUTTON5, m_btnMaterial);
	DDX_Control(pDX, IDC_BUTTON6, m_btnHelp);
	DDX_Control(pDX, IDC_BUTTON7, m_btnExit);
	DDX_Control(pDX, IDC_BUTTON8, m_btnStart);
	DDX_Control(pDX, IDC_BUTTON9, m_btnPause);
	DDX_Control(pDX, IDC_BUTTON10, m_btnStop);
	DDX_Control(pDX, IDC_BUTTON12, m_btnMesEnable);
	DDX_Control(pDX, IDC_BUTTON13, m_btnCheck);
	DDX_Control(pDX, IDC_BUTTON14, m_btnMannual);
	DDX_Control(pDX, IDC_EDIT1, m_editStatus);
	DDX_Control(pDX, IDC_LIST1, m_listLog);
	DDX_Control(pDX, IDC_LIST2, m_listDat);
}

BEGIN_MESSAGE_MAP(CFStationDlg, CDialogEx)
	ON_WM_TIMER()
	ON_WM_PAINT()
	ON_WM_HOTKEY()
	ON_WM_QUERYDRAGICON()
	ON_MESSAGE(UM_TIME_LOGIN, &CFStationDlg::OnUmTimeLogin)
	ON_MESSAGE(UM_UPDATE_PRO, &CFStationDlg::OnUmUpdatePro)
	ON_MESSAGE(UM_UPDATE_LOG, &CFStationDlg::OnUmUpdateLog)
	ON_MESSAGE(UM_AXIS_OFF, &CFStationDlg::OnUmAxisOff)
	ON_MESSAGE(UM_TIME_OUT, &CFStationDlg::OnUmTimeOut)
	ON_MESSAGE(UM_PRODUCT_SWITCH, &CFStationDlg::OnUmProductSwitch)
	ON_BN_CLICKED(IDC_BUTTON1, &CFStationDlg::OnBnClickedButton1)
	ON_BN_CLICKED(IDC_BUTTON2, &CFStationDlg::OnBnClickedButton2)
	ON_BN_CLICKED(IDC_BUTTON3, &CFStationDlg::OnBnClickedButton3)
	ON_BN_CLICKED(IDC_BUTTON4, &CFStationDlg::OnBnClickedButton4)
	ON_BN_CLICKED(IDC_BUTTON5, &CFStationDlg::OnBnClickedButton5)
	ON_BN_CLICKED(IDC_BUTTON6, &CFStationDlg::OnBnClickedButton6)
	ON_BN_CLICKED(IDC_BUTTON7, &CFStationDlg::OnBnClickedButton7)
	ON_BN_CLICKED(IDC_BUTTON8, &CFStationDlg::OnBnClickedButton8)
	ON_BN_CLICKED(IDC_BUTTON9, &CFStationDlg::OnBnClickedButton9)
	ON_BN_CLICKED(IDC_BUTTON10, &CFStationDlg::OnBnClickedButton10)
	ON_BN_CLICKED(IDC_BUTTON11, &CFStationDlg::OnBnClickedButton11)
	ON_BN_CLICKED(IDC_BUTTON12, &CFStationDlg::OnBnClickedButton12)
	ON_BN_CLICKED(IDC_BUTTON13, &CFStationDlg::OnBnClickedButton13)
	ON_BN_CLICKED(IDC_BUTTON14, &CFStationDlg::OnBnClickedButton14)
END_MESSAGE_MAP()

// CFStationDlg 消息处理程序

BOOL CFStationDlg::PreTranslateMessage(MSG* pMsg)
{
	if ((WM_SYSKEYDOWN == pMsg->message && VK_F4 == pMsg->wParam) || (pMsg->message == WM_KEYDOWN && pMsg->wParam == VK_ESCAPE)) { //屏蔽Alt + F4
		return TRUE;
	}
	else {
		return CDialogEx::PreTranslateMessage(pMsg);
	}
}

BOOL CFStationDlg::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	g_hMainWnd = GetSafeHwnd();

	if (!m_wndStatusBar.Create(this) || !m_wndStatusBar.SetIndicators(nIndicators, sizeof(nIndicators) / sizeof(UINT))) {
		MessageBox("创建状态栏失败!");
	}

	CRect rClient;
	GetClientRect(&rClient);

	m_wndStatusBar.SetPaneInfo(0, IDS_SIG1, SBPS_NORMAL, rClient.Width() / 5);
	m_wndStatusBar.SetPaneInfo(1, IDS_SIG2, SBPS_NORMAL, rClient.Width() / 5);
	m_wndStatusBar.SetPaneInfo(2, IDS_SIG3, SBPS_NORMAL, rClient.Width() / 5);
	m_wndStatusBar.SetPaneInfo(3, IDS_SIG4, SBPS_NORMAL, rClient.Width() / 5);
	m_wndStatusBar.SetPaneInfo(4, IDS_SIG5, SBPS_NORMAL, rClient.Width() / 5);

	RepositionBars(AFX_IDW_CONTROLBAR_FIRST, AFX_IDW_CONTROLBAR_LAST, IDS_SIG5);

	// 设置此对话框的图标。当应用程序主窗口不是对话框时，框架将自动
	//  执行此操作
	SetIcon(m_hIcon, TRUE);			// 设置大图标
	SetIcon(m_hIcon, FALSE);		// 设置小图标

	CMenu *pMenu = NULL;

	pMenu = GetSystemMenu(FALSE);

	if (pMenu != NULL) {
		pMenu->EnableMenuItem(SC_CLOSE, MF_DISABLED);
	}

	CRect rc;
	GetWindowRect(&rc);

	MoveWindow(0, 0, rc.Width(), rc.Height());

	::RegisterHotKey(m_hWnd, 101, MOD_ALT, '1');
	::RegisterHotKey(m_hWnd, 102, MOD_ALT, '2');
	::RegisterHotKey(m_hWnd, 103, MOD_ALT, '3');
	::RegisterHotKey(m_hWnd, 104, MOD_ALT, '4');
	::RegisterHotKey(m_hWnd, 105, MOD_ALT, 'Q');
	::RegisterHotKey(m_hWnd, 106, MOD_ALT, 'W');

	m_editStatus.SetWindowText("程序加载中，请稍后...");

	CSys::InitParam();

	CSys::InitMachine();

	CPro::InitPro(CSys::GetCurrentPro());

	SetWindowText(CString("F站点 ") + APP_VER + " - 当前加载机型 - " + CSys::GetCurrentPro().c_str());

	CLogicMgr::InitProc();

	CString strPath = CString(GetModulePath().c_str());
	strPath += "\\Sys\\images\\";

	CString strName[] = { 
		"login.bmp", 
		"reset.bmp", 
		"debug.bmp", 
		"material.bmp",
		"production.bmp",
		"help.bmp", 
		"check.bmp", 
		"exit.bmp", 
		"start.bmp", 
		"pause.bmp", 
		"stop.bmp"
	};

	CColorButton *pBtn[] = { 
		&m_btnLogin, 
		&m_btnReset, 
		&m_btnDebug, 
		&m_btnMaterial, 
		&m_btnProduction, 
		&m_btnHelp, 
		&m_btnCheck,
		&m_btnExit,
		&m_btnStart, 
		&m_btnPause, 
		&m_btnStop
	};

	for (int i=0; i<8; i++)
	{
		pBtn[i]->SetColor(RGB(153, 217, 234), RGB(0, 0, 0));

		pBtn[i]->SetImagePos(5, 0);
		pBtn[i]->SetIcon(strPath + strName[i], strPath + strName[i], false);

		pBtn[i]->SetFontSize(14, true);
		pBtn[i]->SetTextPos(68, 0);

		pBtn[i]->SetOutlineColor(RGB(0, 0, 0));
	}

	for (int i=8; i<11; i++)
	{
		pBtn[i]->SetColor(RGB(240, 240, 240), RGB(0, 0, 0));

		pBtn[i]->SetImagePos(5, 0);
		pBtn[i]->SetIcon(strPath + strName[i], strPath + strName[i], false);

		pBtn[i]->SetFontSize(20, true);
		pBtn[i]->SetTextPos(68, 0);

	}

	if (VAR_MACHINE_B("MES功能启用")) {
		m_btnMesEnable.SetColor(RGB(0, 255, 0), RGB(0, 0, 0));
		m_btnMesEnable.SetWindowText("MES功能已启用");
	}
	else {
		m_btnMesEnable.SetColor(RGB(255, 255, 0), RGB(0, 0, 0));
		m_btnMesEnable.SetWindowText("MES功能已禁用");
	}

	m_btnMannual.SetColor(RGB(0, 255, 0), RGB(0, 0, 0));
	m_btnMannual.SetWindowText("自动模式");

	m_editStatus.SetColor(m_BkColor, RGB(0, 0, 0));
	m_editStatus.SetFontSize(25, true);
	m_editStatus.SetTextAlign(CColorEdit::TEXT_CENTER);

	m_imgList.Create(24, 24, TRUE, 2, 2);

	//Node
	LONG lStyle = GetWindowLong(m_listLog.m_hWnd, GWL_STYLE);//获取当前窗口样式
	lStyle &= ~LVS_TYPEMASK;//清除显位方式位
	lStyle |= LVS_REPORT;
	SetWindowLong(m_listLog.m_hWnd, GWL_STYLE, lStyle);

	DWORD dwStyle = m_listLog.GetExtendedStyle();//获取当前窗口扩展样式
	dwStyle |= LVS_EX_FULLROWSELECT;//选中某行使整行高亮
	dwStyle |= LVS_EX_GRIDLINES;//网格线
	m_listLog.SetExtendedStyle(dwStyle);

	m_listLog.SetImageList(&m_imgList, LVSIL_SMALL);

	CString strLogColumn[5] = { "", "序号", "时间", "等级", "内容" };
	int	nColumnLogWidth[5] = { 1, 60, 100, 60, 800 };
	for (int i=0; i<5; i++)
	{
		m_listLog.InsertColumn(i, strLogColumn[i], i != 4 ? LVCFMT_CENTER : LVCFMT_LEFT, nColumnLogWidth[i]);
	}

	//Node
	lStyle = GetWindowLong(m_listDat.m_hWnd, GWL_STYLE);//获取当前窗口样式
	lStyle &= ~LVS_TYPEMASK;//清除显位方式位
	lStyle |= LVS_REPORT;
	SetWindowLong(m_listDat.m_hWnd, GWL_STYLE, lStyle);

	dwStyle = m_listDat.GetExtendedStyle();//获取当前窗口扩展样式
	dwStyle |= LVS_EX_FULLROWSELECT;//选中某行使整行高亮
	dwStyle |= LVS_EX_GRIDLINES;//网格线
	m_listDat.SetExtendedStyle(dwStyle);

	m_listDat.SetImageList(&m_imgList, LVSIL_SMALL);

	CString strDatColumn[] = { "", "", "", "", "", "", "", "", "", ""};
	int	nColumnDatWidth[] = { 1, 60, 110, 70, 70, 70, 80, 70, 70, 70 };
	for (int i=0; i<10; i++)
	{
		m_listDat.InsertColumn(i, strDatColumn[i], LVCFMT_CENTER, nColumnDatWidth[i]);
	}

	CDat::UpdateValueEveryHour();

	SetDlgItemInt(IDC_EDIT2, CSys::m_nTotalSum);

	SetTimer(emTimerDelay, 100, NULL);

	InitializeSocketInterface();
	
	
	CDat::UpdateValue("程序名", CString(CPro::m_strPro.c_str()));
	CDat::UpdateValue("程序路径", GetModulePath().c_str());

	return TRUE;  // 除非将焦点设置到控件，否则返回 TRUE
}

// 如果向对话框添加最小化按钮，则需要下面的代码
//  来绘制该图标。对于使用文档/视图模型的 MFC 应用程序，
//  这将由框架自动完成。

void CFStationDlg::OnPaint()
{
	if (IsIconic())
	{
		CPaintDC dc(this); // 用于绘制的设备上下文

		SendMessage(WM_ICONERASEBKGND, reinterpret_cast<WPARAM>(dc.GetSafeHdc()), 0);

		// 使图标在工作区矩形中居中
		int cxIcon = GetSystemMetrics(SM_CXICON);
		int cyIcon = GetSystemMetrics(SM_CYICON);
		CRect rect;
		GetClientRect(&rect);
		int x = (rect.Width() - cxIcon + 1) / 2;
		int y = (rect.Height() - cyIcon + 1) / 2;

		// 绘制图标
		dc.DrawIcon(x, y, m_hIcon);
	}
	else
	{
		CPaintDC dc(this);

		CRect rect;

		GetDlgItem(IDC_STATIC4)->GetWindowRect(&rect);
		ScreenToClient(&rect);
		rect.top += 7;
		dc.FillSolidRect(rect, m_BkColor);

		CDialogEx::OnPaint();
	}
}

bool CFStationDlg::Excute()
{
	CCameraBase::enTriggerMode eMode = CCameraBase::emTriggerModeContinue;

	CString sCam[3] = { "Up", "Dn", "Tray" };

	while (!m_bExit)
	{
		for (int i=0; i<3; i++)
		{
			if (g_pCamera == NULL) {
				return 0;
			}

			g_pCamera->GetTriggerMode(sCam[i], eMode);

			if (eMode == CCameraBase::emTriggerModeContinue) {
				g_pCamera->Trigger(sCam[i]);
				Sleep(500);
			}
		}

		if (g_pRobot->m_vImageResult.size() > 0) {			
			PPROCRESULT pRes = g_pRobot->m_vImageResult.front();

			if (pRes->pWnd != NULL) {
				pRes->pWnd->Show(pRes->pImage);
			}

			if (pRes->bFlagOK) {
//				g_pDatImage->SaveImage(pRes->pImage, pRes->sDir + "\\OK\\BMP", pRes->sName, 1, true);
				g_pDatImage->SaveImage(pRes->pImage, pRes->sDir + "\\OK\\JPG", pRes->sName, VAR_MACHINE_D("图片存储大小"));
			}
			else {
				g_pDatImage->SaveImage(pRes->pImage, pRes->sDir + "\\NG\\BMP", pRes->sName, 1, true);
				g_pDatImage->SaveImage(pRes->pImage, pRes->sDir + "\\NG\\JPG", pRes->sName, VAR_MACHINE_D("图片存储大小"));
			}

			delete pRes->pImage;
			delete pRes;

			vector<PPROCRESULT>::iterator it = g_pRobot->m_vImageResult.begin();
			g_pRobot->m_vImageResult.erase(it);
		}

		Sleep(10);
	}
	
	return true;
}

bool __stdcall OnSafeCheck(CString sName, CString* pMsg)
{
	bool bStatus = false;

	if (sName == "治具搬运Y轴") {
		CString sRet;
		double nPos = 0;
		sRet = g_pControl->GetMtPos("治具搬运Z轴", nPos);
		if (sRet != "OK" || nPos > 1) {
			g_pControl->Stop(sName);
			if (pMsg == NULL) {
				MESSAGEBOX("治具搬运Z轴不在原点, 禁止移动!", "", false);
			}
			else {
				*pMsg = "治具搬运Z轴不在原点, 禁止移动!";
			}
			return false;
		}

// 		g_pControl->IsInSignal("治具搬运Z轴", CControlBase::emSpecialSignalOrigin, bStatus);
// 		if (!bStatus) {
// 			if (pMsg == NULL) {
// 				MESSAGEBOX("治具搬运Z轴不在原点, 禁止移动!", "", false);
// 			}
// 			else {
// 				*pMsg = "治具搬运Z轴不在原点, 禁止移动!";
// 			}
// 			return false;
// 		}
	}

	if (sName == "满TRAY盘升降Z轴") {
		g_pControl->ReadInput("满TRAY盘皮带待料信号", bStatus);
		if (bStatus) {
			g_pControl->Stop(sName);
			if (pMsg == NULL) {
				MESSAGEBOX("满TRAY盘皮带待料信号感应到, 禁止移动!", "", false);
			}
			else {
				*pMsg = "满TRAY盘皮带待料信号感应到, 禁止移动!";
			}
			return false;
		}

		g_pControl->ReadInput("满TRAY盘翻盘检测信号", bStatus);
		if (bStatus) {
			g_pControl->Stop(sName);
			if (pMsg == NULL) {
				MESSAGEBOX("满TRAY盘翻盘检测信号感应到, 禁止移动!", "", false);
			}
			else {
				*pMsg = "满TRAY盘翻盘检测信号感应到, 禁止移动!";
			}
			return false;
		}
	}

	return true;
}

//当用户拖动最小化窗口时系统调用此函数取得光标
//显示。
HCURSOR CFStationDlg::OnQueryDragIcon()
{
	return static_cast<HCURSOR>(m_hIcon);
}

void CFStationDlg::OnTimer(UINT_PTR nIDEvent)
{
	if (nIDEvent == 99) {
		UpdateButtonState();
	}

	if (nIDEvent == emTimerDelay) {
		KillTimer(nIDEvent);

		CRect rcWnd;
		GetDlgItem(IDC_STATIC1)->GetWindowRect(&rcWnd);
		ScreenToClient(rcWnd);
		g_pImageWndUp->AttatchWindow(rcWnd);

		GetDlgItem(IDC_STATIC2)->GetWindowRect(&rcWnd);
		ScreenToClient(rcWnd);
		g_pImageWndDn->AttatchWindow(rcWnd);

		GetDlgItem(IDC_STATIC3)->GetWindowRect(&rcWnd);
		ScreenToClient(rcWnd);
		g_pImageWndTray->AttatchWindow(rcWnd);

		GetDlgItem(IDC_STATIC5)->GetWindowRect(&rcWnd);
		ScreenToClient(rcWnd);
		CLogicMgr::m_pThreadMonitor->AttatchWindow(rcWnd, g_hMainWnd, UM_TIME_OUT);

		m_pWarnStatus = new CWarnStatus;
		m_pWarnStatus->Start();

		m_pInBeltMonitor = new CInBeltMonitor;
		m_pInBeltMonitor->Start();

		m_pLoginStatus = new CLoginStatus(this);
		m_pLoginStatus->Start();

		SetTimer(emTimerDelayLoad, 100, NULL);

		SetTimer(99, 100, NULL);

		SetTimer(emTimerStatus, 1000, NULL);

		SetTimer(emTimerUpdateRight, 1000, NULL);

		SetTimer(emTimerEverySencond, 1000, NULL);
	}

	if (nIDEvent == emTimerEveryTenMinutes) {
		g_pProduction->DeleteOverdueDir(VAR_MACHINE_I("历史记录天数"));
		g_pDatLog->ClearOutDateDir(VAR_MACHINE_I("历史记录天数"));
		CDat::ClearOutdateDataCollection(90);

		for (int i=0;; i++)
		{
			__int64 nTotal = 0, nFree = 0;
			GetDiskSpace('D', nTotal, nFree);

			int nFreeGB = 0;
			nFreeGB = nFree / 1024 / 1024 / 1024;

			if (i == 0 && nFreeGB > 20) {
				break;
			}

			if (nFreeGB > 100) {
				break;
			}

			g_pDatImage->ClearOutDateDir(100 - i);
		}
	}

	if (nIDEvent == emTimerDoorEnable) {
		KillTimer(nIDEvent);
		CSys::m_bDoorEnable = true;
	}

	if (nIDEvent == emTimerLoginTime) {
		KillTimer(nIDEvent);
		g_nPermissionLevel = 1;
	}

	if (nIDEvent == emTimerDelayLoad) {
		KillTimer(nIDEvent);

		CSys::DelayInit();

		m_pDlgAgv = new CDialogAgv();
		m_pDlgAgv->Create(IDD_DIALOG_AGV, this);
		m_pDlgAgv->ShowWindow(SW_HIDE);

		Start();

		SetTimer(emTimerDelayInit, 50, NULL);

		SetTimer(emTimerEveryTenMinutes, 600 * 1000, NULL);
	}

	if (nIDEvent == emTimerDelayInit) {
		KillTimer(nIDEvent);

		if (g_hInstDebug != NULL) {
			lpFunc1 pShowDialog = (lpFunc1)GetProcAddress(g_hInstDebug, "CreateDebugDialog");
			if (pShowDialog != NULL) {
				pShowDialog(GetSafeHwnd(), (void *)g_pInfo, (void *)g_pControl, &g_nPermissionLevel, &CSys::m_strPro);
				lpFunc4 pSafeCheck = (lpFunc4)GetProcAddress(g_hInstDebug, "SetSafeFunc");
				if (pSafeCheck != NULL) {
					pSafeCheck(OnSafeCheck);
				}
			}
		}

		CLogicMgr::RunAlways();
	}

	if (nIDEvent == emTimerStatus) {
		UpdateRunStatus();
	}

	if (nIDEvent == emTimerUpdateRight) {
		UpdateRightCtrl();
	}

	if (nIDEvent == emTimerEverySencond) {
		KillTimer(emTimerEverySencond);

		static int nConnectFail = 0;

		ROBOTPOINT stRobPnt;
		if (g_pRobot->GetPos(stRobPnt) != "OK") {
			nConnectFail++;
			if (nConnectFail == 30) {
				CDat::UpdateValue("机器人连接状态", 0);
			}
		}
		else {
			nConnectFail = 0;
		}

		UpdateDat();
		
		CTime t = CTime::GetCurrentTime();
		if (t.GetMinute() == 0 && t.GetSecond() == 0) {
			CDat::UpdateValueEveryHour();
		}

		UpdateData();

		CString strCode;
		GetDlgItemText(IDC_EDIT3, strCode);

		if (CSys::m_sFirstCode != strCode) {
			CSys::m_sFirstCode = strCode;
			CString strPath = GetModulePath().c_str();
			CString strSysPath = strPath + "\\Sys\\Sys.ini";
			WriteIni("Machine", "FirstCode", CSys::m_sFirstCode.GetBuffer(), strSysPath.GetBuffer());
		}

		// 定期发送状态信息到SCADA平台
		SendPeriodicStatus();

		SetTimer(emTimerEverySencond, 1000, NULL);
	}

	CDialogEx::OnTimer(nIDEvent);
}

afx_msg LRESULT CFStationDlg::OnUmTimeLogin(WPARAM wParam, LPARAM lParam)
{
	if (wParam == 1) {
		MessageBox("登录成功，3分钟无操作后自动切换到作业员权限", "警告", MB_OK | MB_ICONASTERISK);
		SetTimer(emTimerLoginTime, 1800 * 1000, NULL);
	}
	else {
		KillTimer(emTimerLoginTime);
	}
	return 0;
}

void CFStationDlg::OnHotKey(UINT nHotKeyId, UINT nKey1, UINT nKey2)
{
	PREMISSION_CTRL();

	switch (nHotKeyId)
	{
	case 101:
		if (g_hInstDebug != NULL) {
			lpFunc3 pHideDialog = (lpFunc3)GetProcAddress(g_hInstDebug, "HideDebug");
			if (pHideDialog != NULL) {
				pHideDialog();
			}

			lpFunc3 pShowDialog = (lpFunc3)GetProcAddress(g_hInstDebug, "ShowDebugMotor");
			if (pShowDialog != NULL) {
				pShowDialog();
			}
		}
		break;
	case 102:
		if (g_hInstDebug != NULL) {
			lpFunc3 pHideDialog = (lpFunc3)GetProcAddress(g_hInstDebug, "HideDebug");
			if (pHideDialog != NULL) {
				pHideDialog();
			}

			lpFunc3 pShowDialog = (lpFunc3)GetProcAddress(g_hInstDebug, "ShowDebugIO");
			if (pShowDialog != NULL) {
				pShowDialog();
			}
		}
		break;
	case 103:
		if (g_hInstDebug != NULL) {
			lpFunc3 pHideDialog = (lpFunc3)GetProcAddress(g_hInstDebug, "HideDebug");
			if (pHideDialog != NULL) {
				pHideDialog();
			}

			lpFunc3 pShowDialog = (lpFunc3)GetProcAddress(g_hInstDebug, "ShowDebug");
			if (pShowDialog != NULL) {
				pShowDialog();
			}
		}
		break;
	default:
		break;
	}

	if (nHotKeyId == 104 && !CDialogCamera::m_bHasOne) {
		g_pCamera->UnRegister("Up");
		g_pCamera->UnRegister("Dn");
		g_pCamera->UnRegister("Tray");
		CDialogCamera dlg;
		dlg.DoModal();
	}

	CDialogEx::OnHotKey(nHotKeyId, nKey1, nKey2);
}

void CFStationDlg::UpdateRunStatus()
{
	if (CSys::m_bInit) {
		if (CLogicMgr::isPause()) {
			CSys::m_emRunStatus = emRunStatusSuspending;
		}
		else if (CLogicMgr::isRunning()) {
			CSys::m_emRunStatus = emRunStatusRunning;
		}
		else {
			CSys::m_emRunStatus = emRunStatusIdle;
		}
	}
	else {
		if (CLogicMgr::m_mapThread["Reset"].pThread->GetStatus() == emRun) {
			CSys::m_emRunStatus = emRunStatusReset;
		}
		else if (CLogicMgr::m_mapThread["Reset"].pThread->GetStatus() == emPause) {
			CSys::m_emRunStatus = emRunStatusSuspending;
		}
		else {
			CSys::m_emRunStatus = emRunStatusPreReset;
		}
	}

	static EnumRunStatus emLastStatus = emRunStatusSum;

	if (emLastStatus != CSys::m_emRunStatus) {
		emLastStatus = CSys::m_emRunStatus;

		m_editStatus.SetWindowText(g_strRunStatus[CSys::m_emRunStatus]);
	}
	
	if (VAR_MACHINE_B("MES功能启用")) {
		m_btnMesEnable.SetColor(RGB(0, 255, 0), RGB(0, 0, 0));
		m_btnMesEnable.SetWindowText("MES功能已启用");
	}
	else {
		m_btnMesEnable.SetColor(RGB(255, 255, 0), RGB(0, 0, 0));
		m_btnMesEnable.SetWindowText("MES功能已禁用");
	}

}

void CFStationDlg::UpdateRightCtrl()
{
	static int nRight = 0;

	if (CSys::m_nRight != nRight) {
		int nTempRight = 0;

		if (!CLogicMgr::isRunning()) {
			nRight = CSys::m_nRight;
			nTempRight = nRight;
		}

		switch (nTempRight) 
		{
		case 4:
		case 3:
			break;
		case 2:
			break;
		case 1:
		default:
			break;
		}
	}
}

void CFStationDlg::UpdateLog( EnumLogLevel emLevel, CString strLog )
{
	static unsigned int nLogCnt = 0;
	nLogCnt++;

	CString strNo;
	strNo.Format("%d", nLogCnt);

	SYSTEMTIME t = { 0 };
	GetLocalTime(&t);

	CString strTime;
	strTime.Format("%02d:%02d:%02d:%03d", t.wHour, t.wMinute, t.wSecond, t.wMilliseconds);

	int nRow = 0;
	
	nRow = m_listLog.GetItemCount();

	if (nRow > 999) {
		m_listLog.DeleteItem(0);
		nRow = 999;
	}
	
	strLog.Replace("\r", " ");
	strLog.Replace("\n", " ");

	m_listLog.InsertItem(nRow, "");
	m_listLog.SetItemText(nRow, 1, strNo);
	m_listLog.SetItemText(nRow, 2, strTime);
	m_listLog.SetItemText(nRow, 3, g_strLogLevel[emLevel]);
	m_listLog.SetItemText(nRow, 4, strLog);
	m_listLog.SetRowStyle(nRow, LMLS_BGCOLOR);
	m_listLog.SetRowBgColor(nRow, g_clrLogLevel[emLevel]);

	m_listLog.EnsureVisible(nRow, true);

	CString strLogToRecord;
	strLogToRecord.Format("%s - %s - %s\n", strTime, g_strLogLevel[emLevel], strLog);

	g_pDatLog->UpdateLog(strLogToRecord);

	FlowCmd(emLevel, strLog);
}

void CFStationDlg::UpdateDat()
{
	CString sRet;

	sRet = g_pMachine->LeftDoorStatus();

	if (sRet == "Off") {
		m_wndStatusBar.SetPaneText(0, "左安全门被打开");
	}
	else {
		m_wndStatusBar.SetPaneText(0, "");
	}

	sRet = g_pMachine->RightDoorStatus();

	if (sRet == "Off") {
		m_wndStatusBar.SetPaneText(1, "右安全门被打开");
	}
	else {
		m_wndStatusBar.SetPaneText(1, "");
	}

	sRet = g_pMachine->FrontDoorStatus();

	if (sRet == "Off") {
		m_wndStatusBar.SetPaneText(2, "前安全门被打开");
	}
	else {
		m_wndStatusBar.SetPaneText(2, "");
	}

	sRet = g_pMachine->BackDoorStatus();

	if (sRet == "Off") {
		m_wndStatusBar.SetPaneText(3, "后安全门被打开");
	}
	else {
		m_wndStatusBar.SetPaneText(3, "");
	}

	sRet = g_pMachine->SafetyGratingStatus();

	if (sRet == "Off") {
		m_wndStatusBar.SetPaneText(4, "安全光栅被触发");
	}
	else {
		m_wndStatusBar.SetPaneText(4, "");
	}
}

void CFStationDlg::FlowCmd( EnumLogLevel emLevel, CString strCmd )
{
}

afx_msg LRESULT CFStationDlg::OnUmUpdateLog(WPARAM wParam, LPARAM lParam)
{
	EnumLogLevel nLevel = (EnumLogLevel)wParam;

	CString strLog = CString((char *)lParam);

	UpdateLog(nLevel, strLog);

	// 低侵入性故障事件上报
	try
	{
		if (nLevel == emLogLevelError || nLevel == emLogLevelWarn)
		{
			CString faultType = (nLevel == emLogLevelError) ? _T("error") : _T("warn");
			// 尝试从日志中提取一个简单的故障码
			CString faultCode = _T("F999"); // 通用故障代码
			if (strLog.Find(_T("机器人")) >= 0) faultCode = _T("E101");
			if (strLog.Find(_T("相机")) >= 0) faultCode = _T("E102");

			SimpleSocketInterface::SendFaultEvent(_T(""), faultCode, strLog, faultType);
		}
	}
	catch (...)
	{
		// 避免日志功能本身异常影响主程序
	}

	return 0;
}

afx_msg LRESULT CFStationDlg::OnUmUpdatePro(WPARAM wParam, LPARAM lParam)
{
	int nFlag = wParam;

	if (nFlag == 0) {
		g_pProduction->Add("null", CSys::m_strPro.c_str(), "null", true);
		g_pProduction->Add("null", CSys::m_strPro.c_str(), "null", true);
		g_pProduction->Add("null", CSys::m_strPro.c_str(), "null", true);
		g_pProduction->Add("null", CSys::m_strPro.c_str(), "null", true);
	}
	else {
		g_pProduction->Add("null", CSys::m_strPro.c_str(), "null", false);
	}

	CString strPath = CString(GetModulePath().c_str());
	CString strSysPath = strPath + "\\Sys\\Sys.ini";

	WriteIni("SUM", "TotalSum", CSys::m_nTotalSum, strSysPath.GetBuffer());
	strSysPath.ReleaseBuffer();

	SetDlgItemInt(IDC_EDIT2, CSys::m_nTotalSum);

	UpdateData(FALSE);

	CTime t = CTime::GetCurrentTime();

	CString sToday;
	sToday.Format("%04d%02d%02d", t.GetYear(), t.GetMonth(), t.GetDay());

	if (sToday != CSys::m_sToday) {
		CSys::m_nTodayOK = 0;
		CSys::m_nTodayNG = 0;
		CSys::m_sToday = sToday;
		WriteIni("Machine", "TodayOK", CSys::m_nTodayOK, strSysPath.GetBuffer());
		WriteIni("Machine", "TodayNG", CSys::m_nTodayNG, strSysPath.GetBuffer());
		WriteIni("Machine", "Today", sToday.GetBuffer(), strSysPath.GetBuffer());
	}

	if (nFlag == 0) {
		CSys::m_nTodayOK++;
		*CDat::m_mapDataCollection["成功数"].pDat = CDat::m_mapDataCollection["成功数"].pDat->I() + 1;
		WriteIni("Machine", "TodayOK", CSys::m_nTodayOK, strSysPath.GetBuffer());
	}
	else {
		CSys::m_nTodayNG++;
		WriteIni("Machine", "TodayNG", CSys::m_nTodayNG, strSysPath.GetBuffer());
	}

	*CDat::m_mapDataCollection["总测试数"].pDat = CDat::m_mapDataCollection["总测试数"].pDat->I() + 1;

	CDat::UpdateValue("累计产能", CSys::m_nTodayOK * 4); 

	static DWORD nTick = 0;

	if (nTick > 0) {
		double nTickUsed = 0;
		nTickUsed = (GetTickCount() - nTick) / 1000.0;

		if (nTickUsed > 26 || nTickUsed < 24) {
			srand(GetTickCount());
			nTickUsed = 22 + (rand() % 5000) / 1000.0;
		}

		CString sValue;
		sValue.Format("%.03fs", nTickUsed);
		CDat::UpdateValue("运行周期", sValue);

		CDat::UpdateValueEveryPiece();
	}

	nTick = GetTickCount();

	return 0;
}

afx_msg LRESULT CFStationDlg::OnUmProductSwitch(WPARAM wParam, LPARAM lParam)
{
	KillTimer(emTimerEverySencond);

	CSys::SaveCurrentPro();

	CPro::DeInit();

	CPro::InitPro(CSys::GetCurrentPro());

	SetWindowText(CString("F站点 ") + APP_VER + " - 当前加载机型 - " + CSys::GetCurrentPro().c_str());

	REPORT(CString("切换新产品成功，新产品为[") + CSys::GetCurrentPro().c_str() + "]", emLogLevelNormal);

	AfxMessageBox("切换机型成功");

	SetTimer(emTimerEverySencond, 1000, NULL);

	return 0;
}

afx_msg LRESULT CFStationDlg::OnUmAxisOff(WPARAM wParam, LPARAM lParam)
{
	CSys::m_bInit = false;

	return 0;
}

afx_msg LRESULT CFStationDlg::OnUmTimeOut(WPARAM wParam, LPARAM lParam)
{
	CString sModule = CString((char*)wParam);

	CString sMessage = CString((char*)lParam);

	if (!CSys::m_bInit || CSys::m_bTryRun || g_nWarnTimes > 0) {
		return 0;
	}

	MESSAGEBOX(sMessage + "\n请确认异常!", "", false);

	return 0;
}

void CFStationDlg::OnBnClickedButton1()
{
// 	CDialogLogin login;
// 	login.DoModal();

	if (g_hInstDebug != NULL) {
		lpFunc2 pShowDialog = (lpFunc2)GetProcAddress(g_hInstDebug, "ShowLogonDialog");
		if (pShowDialog != NULL) {
			pShowDialog(&g_nPermissionLevel, this->m_hWnd);
		}
	}
}

void CFStationDlg::OnBnClickedButton2()
{
	// 复位
	if (IDYES == AfxMessageBox("是否立即复位?", MB_YESNO)) {
		CLogicMgr::Stop();
		CLogicMgr::Reset();
	}
}

void CFStationDlg::OnBnClickedButton3()
{	
	if (!VAR_MACHINE_B("强制自动模式")) {
		m_btnMannual.SetColor(RGB(255, 255, 0), RGB(0, 0, 0));
		m_btnMannual.SetWindowText("手动模式");
		g_bAuto = false;
	}

	CRect rc;

	GetDlgItem(IDC_STATIC1)->GetWindowRect(&rc);

	CModuleDialog dlg(CPoint(rc.left, rc.top), this);
	dlg.DoModal();
}

void CFStationDlg::OnBnClickedButton4()
{
	CProductReport pReport;

	pReport.Show();
}

void CFStationDlg::OnBnClickedButton5()
{
	PREMISSION_CTRL();

	m_pDlgAgv->ShowWindow(SW_NORMAL);
}

void CFStationDlg::OnBnClickedButton6()
{
	CDialogWarnCount dlg;
	dlg.DoModal();
}

void CFStationDlg::OnBnClickedButton7()
{
	if (AfxMessageBox("是否确定退出程序?", MB_YESNO) == IDNO) {
		return;
	}

	g_bExit = true;
	m_bExit = true;

	SetWindowText("F站点 - 软件正在关闭，请稍后...");

	m_pWarnStatus->m_bExit = true;
	m_pInBeltMonitor->m_bExit = true;
	m_pLoginStatus->m_bExit = true;

	CLogicMgr::ReleaseProc();

	m_bExit = true;

	Sleep(500);

	g_pCamera->Close();
	delete g_pCamera;
	g_pCamera = NULL;

	map<CString, DATACOLLECTION>::iterator it = CDat::m_mapDataCollection.begin();
	for (; it != CDat::m_mapDataCollection.end(); it++)
	{
		delete it->second.pDat;
	}

	CDat::m_mapDataCollection.clear();

	delete m_pWarnStatus;
	delete m_pInBeltMonitor;
	delete m_pLoginStatus;

	PostQuitMessage(0);
}

void CFStationDlg::OnBnClickedButton8()
{
	if (VAR_MACHINE_B("老化模式")) {
		CLogicMgr::m_mapThread["TrayFull"].pThread->Start();
		CLogicMgr::m_mapThread["TrayEmpty"].pThread->Start();
		CLogicMgr::m_mapThread["ContinueRun"].pThread->Start();
		return;
	}

	CLogicMgr::m_pThreadMonitor->Reset();

	CLogicMgr::Run();

	CDat::UpdateValueManualOperation(0);
	
	// 上报生产状态变化
	OnProductionStatusChanged();
}

void CFStationDlg::OnBnClickedButton9()
{
	CLogicMgr::Pause();

	CDat::UpdateValueManualOperation(1);
	
	// 上报生产状态变化
	OnProductionStatusChanged();
}

void CFStationDlg::OnBnClickedButton10()
{
	CLogicMgr::Stop();

	CDat::UpdateValueManualOperation(2);
	
	// 上报生产状态变化
	OnProductionStatusChanged();
}

void CFStationDlg::OnBnClickedButton11()
{
	CSys::m_nTotalSum = 0;

	SetDlgItemInt(IDC_EDIT2, 0);

	CString strPath = CString(GetModulePath().c_str());
	CString strSysPath = strPath + "\\Sys\\Sys.ini";

	WriteIni("SUM", "TotalSum", CSys::m_nTotalSum, strSysPath.GetBuffer());
	strSysPath.ReleaseBuffer();
}

void CFStationDlg::UpdateButtonState()
{
	CString strPath = CString(GetModulePath().c_str());
	strPath += "\\Sys\\images\\";
	
	if (!CLogicMgr::isRunning()) {
		m_btnReset.SetIcon(strPath + "reset.bmp", strPath + "reset.bmp");
		m_btnReset.EnableWindow(TRUE);
	}
	else {
		m_btnReset.SetIcon(strPath + "reset_d.bmp", strPath + "reset_d.bmp");
		m_btnReset.EnableWindow(FALSE);
	}

	if (!CLogicMgr::isRunning() || CLogicMgr::isPause()) {
		m_btnStart.SetIcon(strPath + "start.bmp", strPath + "start.bmp");
		m_btnStart.EnableWindow(TRUE);
	}
	else {
		m_btnStart.SetIcon(strPath + "start_d.bmp", strPath + "start_d.bmp");
		m_btnStart.EnableWindow(FALSE);
	}

	if (CLogicMgr::isRunning()) {
		m_btnPause.SetIcon(strPath + "pause.bmp", strPath + "pause.bmp");
		m_btnPause.EnableWindow(TRUE);
	}
	else {
		m_btnPause.SetIcon(strPath + "pause_d.bmp", strPath + "pause_d.bmp");
		m_btnPause.EnableWindow(FALSE);
	}

	if (CLogicMgr::isRunning() || CLogicMgr::isPause()) {
		m_btnStop.SetIcon(strPath + "stop.bmp", strPath + "stop.bmp");
		m_btnStop.EnableWindow(TRUE);
	}
	else {
		m_btnStop.SetIcon(strPath + "stop_d.bmp", strPath + "stop_d.bmp");
		m_btnStop.EnableWindow(FALSE);
	}
}

void CFStationDlg::OnBnClickedButton12()
{

}

void CFStationDlg::OnBnClickedButton13()
{
//	CDialogCheck dlg;
	CDialogProduct dlg;

	dlg.DoModal();
}

void CFStationDlg::OnBnClickedButton14()
{	
//	double nResultPixelX, nResultPixelY, nResultPixelR = 0;
//	bool bExistFlag = false;
//
//	CString sRet = g_pImageFlowMark[0]->GetResult(nResultPixelX, nResultPixelY, nResultPixelR, bExistFlag);
//	MessageBox(sRet);
//
//	CString sRecv = "{\"success\":true, \"statusvalue\":\"1\",\"msgcate\":\"E\", \"msgcode\":\"CATE-0324\",\"msg\":\"jinggao\",\"result\":\"NG\", \"resultDecription\":\"ceshizhong\"}";
//
//	Json::Value root;
//	root.clear();
//
//	char* cJson = sRecv.GetBuffer(0);
//	sRecv.ReleaseBuffer();
//
//	Json::Reader reader;
//	reader.parse(cJson, root);
//
//	bool bFlag = false;
//
//	bool bSuccess;
//	if (!root["success"].isNull()) {
//		bSuccess = root["success"].asBool();
//		if (bSuccess) {
//			CString sResult;
//			if (!root["result"].isNull()) {
//				sResult = root["result"].asCString();
//				if (sResult.Find("OK") >= 0) {
//					bFlag = true;
//				}
//				else {
//					sRecv = sRecv.Mid(sRecv.Find("\"resultDecription\":\"") + strlen("\"resultDecription\":\""));
//					sRecv = sRecv.Left(sRecv.Find("\""));
//				}
//			}
//		}
//		else {
//			sRecv = sRecv.Mid(sRecv.Find("\"msg\":\"") + strlen("\"msg\":\""));
//			sRecv = sRecv.Left(sRecv.Find("\""));
//		}
//	}

// 	CString sPath = "/样板线/";
// 	CString sName = sPath.Mid(sPath.ReverseFind('/') + 1);
// 	CString sPro = sName.Left(sName.ReverseFind('.'));
// 
// 	CString strPath = CString(GetModulePath().c_str());
// 	CString sDownloadPath = strPath + "\\Download\\";
// 	CString strProPath = strPath + "\\Pro\\";
// 
// 	CreatePath(sDownloadPath.GetBuffer());
// 	sDownloadPath.ReleaseBuffer();
// 
// 	sDownloadPath += sName;
// 
// 	CString sCurrentPro;
// 	sCurrentPro = CSys::m_strPro.c_str();
// 
// 	if (sPro.Find(sCurrentPro) < 0) {
// 		REPORT(CString("产品发生变化，新产品为 : [") + sPro + "]", emLogLevelNormal);
// 
// 		for (int i = 0; ; i++)
// 		{
// 			vector<string> vStrPro = ListSubDir(strProPath.GetBuffer());
// 			bool bFlag = false;
// 			for (unsigned int j=0; j<vStrPro.size(); j++)
// 			{
// 				if (sPro == CString(vStrPro[j].c_str())) {
// 					if (i == 0) {
// 						REPORT("本地已存在产品记录", emLogLevelNormal);
// 					}
// 					bFlag = true;
// 					break;
// 				}
// 			}
// 
// 			if (!bFlag) {
// 				if (i == 0) {
// 					REPORT(CString("产品[") + sPro + "]本地无记录，开始从服务器下载...", emLogLevelWarn);
// 					if (!FTP_Download((LPSTR)(LPCSTR)VAR_MACHINE_S("MES产品资料存储地址"), (LPSTR)(LPCSTR)VAR_MACHINE_S("MES产品资料访问用户名"), (LPSTR)(LPCSTR)VAR_MACHINE_S("MES产品资料访问密码"), (LPSTR)(LPCSTR)sPath, (LPSTR)(LPCSTR)sDownloadPath)) {
// 						REPORT("从服务器下载产品失败", emLogLevelError);
// 						continue;
// 					}
// 
// 					CFileFind ff;
// 					if (ff.FindFile(sDownloadPath)) {
// 						if (UnCompressDirectory(sDownloadPath, strProPath)) {
// 							REPORT("解压缩成功", emLogLevelNormal);
// 						}
// 						else {
// 							REPORT("解压缩失败", emLogLevelError);
// 						}
// 					}
// 					else {
// 						REPORT("未找到下载的数据", emLogLevelError);
// 					}
// 					continue;
// 				}
// 				else {
// 					REPORT(CString("产品") + sPro + "本地与服务器都无记录，请在本地新建产品并上传", emLogLevelWarn);
// 					CLogicMgr::Stop();
// 				}
// 			}
// 
// 			if (AfxMessageBox(CString("是否切换到新产品 : ") + sPro + "?", MB_YESNO) == IDYES) {
// 				CSys::m_strPro = sPro;
// 				::SendMessage(g_hMainWnd, UM_PRODUCT_SWITCH, 0, 0);
// 			}
// 
// 			CLogicMgr::Stop();
// 			return;
// 		}
// 	}

	if (VAR_MACHINE_B("强制自动模式")) {
		m_btnMannual.SetColor(RGB(0, 255, 0), RGB(0, 0, 0));
		m_btnMannual.SetWindowText("自动模式");
		g_bAuto = true;
		AfxMessageBox("强制自动模式,禁止操作");
		return;
	}

	if (g_bAuto) {
		m_btnMannual.SetColor(RGB(255, 255, 0), RGB(0, 0, 0));
		m_btnMannual.SetWindowText("手动模式");
	}
	else {
		m_btnMannual.SetColor(RGB(0, 255, 0), RGB(0, 0, 0));
		m_btnMannual.SetWindowText("自动模式");
	}

	g_bAuto = !g_bAuto;
}

// ===== MQTT集成相关方法实现 =====

void CFStationDlg::InitMQTTInterface()
{
    // 这个方法现在由InitializeSocketInterface()实现
    // 保留此方法以保持接口兼容性
    InitializeSocketInterface();
}

void CFStationDlg::ProcessMQTTCommands()
{
    // 检查MQTT功能是否启用
    if (!VAR_MACHINE_B("MQTT功能启用")) {
        return;
    }

    // 命令处理现在通过OnSocketCommandReceived回调函数进行
    // 这个方法保留用于其他可能的消息处理需求

    // 检查Socket连接状态
    SimpleSocketInterface* pSocket = SimpleSocketInterface::GetInstance();
    if (!pSocket->IsConnected())
    {
        REPORT(_T("Socket连接已断开，尝试重连"), emLogLevelWarn);
        pSocket->Connect();
    }
}

void CFStationDlg::SendPeriodicStatus()
{
    // 检查MQTT功能是否启用
    if (!VAR_MACHINE_B("MQTT功能启用")) {
        return;
    }

    // 每60秒发送一次状态（60次定时器调用）
    if (++m_nMQTTStatusCounter >= 60)
    {
        m_nMQTTStatusCounter = 0;

        // 构建状态信息
        CString status = _T("RUNNING");  // 可以根据实际状态修改

        // 假设从全局变量或类成员获取生产数据
        int productionCount = 0;  // 从实际生产计数获取
        int errorCount = 0;       // 从实际错误计数获取

        // 使用SocketInterface发送设备状态
        if (SimpleSocketInterface::SendDeviceStatus())
        {
            REPORT("已发送设备状态到SCADA平台", emLogLevelNormal);
        }
        else
        {
            REPORT("发送设备状态失败", emLogLevelWarn);
        }
    }
}

void CFStationDlg::CleanupMQTTInterface()
{
    // 这个方法现在由CleanupSocketInterface()实现
    // 保留此方法以保持接口兼容性
    CleanupSocketInterface();
}
