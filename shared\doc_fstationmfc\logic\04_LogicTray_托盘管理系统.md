# LogicTray - 托盘管理系统

## 概述
托盘管理系统是FStation中的重要物料管理模块，负责协调空托盘和满托盘的流转。该系统采用分层管理架构，包含一个主控制器和两个专用子系统。

## 托盘系统架构

### 1. 三层架构设计
```
LogicTray (主控制器)
├── LogicTrayEmpty (空托盘处理)
└── LogicTrayFull (满托盘处理)
```

### 2. 并发子线程管理
```cpp
class CLogicTray : public CThreadBase {
private:
    CThreadFunc* m_pFuncFullTray;    // 满托盘子线程
    CThreadFunc* m_pFuncEmptyTray;   // 空托盘子线程
};
```

## LogicTray - 主控制器

### 类结构分析
```cpp
class CLogicTray : public CThreadBase {
private:
    CTray*                  m_pTray;        // 托盘设备对象
    CString                 m_sRet;         // 返回值缓存
    map<CString, bool>      m_mapFlag;      // 状态标志
    map<CString, DWORD>     m_mapTick;      // 时间戳
    map<CString, double>    m_mapPos;       // 位置参数
    CThreadFunc*            m_pFuncFullTray; // 满托盘线程
    CThreadFunc*            m_pFuncEmptyTray;// 空托盘线程
};
```

### 主要功能

#### 1. 托盘流转协调
```cpp
CStatus CLogicTray::OnTray00() {
    // 处理机器人的托盘请求
    if (g_pRobot->m_mapParam["TRAY盘进料标志"]->B()) {
        *g_pRobot->m_mapParam["TRAY盘进料标志"] = false;
        PARAM("空TRAY盘退料标志") = true;  // 提供空托盘
    }
    
    if (g_pRobot->m_mapParam["TRAY盘退料标志"]->B()) {
        *g_pRobot->m_mapParam["TRAY盘退料标志"] = false;
        PARAM("满TRAY盘退料标志") = true;  // 回收满托盘
    }
}
```

#### 2. 子线程管理
主控制器在两个子线程间轮询执行：
```cpp
OnTray01() → 运行满托盘子线程
OnTray02() → 运行空托盘子线程
OnTray00() → 处理请求和协调
```

#### 3. 参数管理
```cpp
// 托盘流转控制标志
PARAM("空TRAY盘进料标志") = false;
PARAM("空TRAY盘退料标志") = false;  
PARAM("满TRAY盘进料标志") = false;
PARAM("满TRAY盘退料标志") = false;
```

## LogicTrayEmpty - 空托盘处理

### 功能概述
专门处理空托盘的进料、传输和分离操作。

### 关键状态流程

#### 1. 主循环
```
OnEmptyTray00() → 检查进料请求
OnEmptyTray01() → 检查退料请求
```

#### 2. 进料流程 (OnEmptyTrayFeed系列)
```
OnEmptyTrayFeed00() → Z轴移动到支撑位
OnEmptyTrayFeed01() → 等待Z轴到位
OnEmptyTrayFeed02() → 启动进料皮带
...
OnEmptyTrayFeed10() → 完成进料操作
```

#### 3. 退料流程 (OnEmptyTrayBack系列)
```
OnEmptyTrayBack00() → 准备退料
OnEmptyTrayBack01() → 启动退料皮带
...
OnEmptyTrayBack13() → 完成退料操作
```

### 设备控制接口
```cpp
// Z轴控制
m_pTray->EmptyTraySeparateZMove(位置);
m_pTray->IsEmptyTraySeparateZInPos(位置);

// 皮带控制  
m_pTray->EmptyTrayBeltOn(方向);
m_pTray->EmptyTrayBeltOff();

// Y轴传输控制
m_pTray->EmptyTrayTransportYMove(位置);
m_pTray->EmptyTrayTransportYStop();
```

## LogicTrayFull - 满托盘处理

### 功能概述
专门处理满托盘的接收、存储和输出操作。

### 关键状态流程

#### 1. 主循环
```
OnFullTray00() → 检查进料请求
OnFullTray01() → 检查退料请求  
OnFullTray02() → 状态维护
```

#### 2. 进料流程 (OnFullTrayFeed系列)
```
OnFullTrayFeed00() → 准备接收满托盘
OnFullTrayFeed01() → 托盘定位检测
OnFullTrayFeed02() → 升降Z轴控制
...
OnFullTrayFeed13() → 完成进料存储
```

#### 3. 退料流程 (OnFullTrayBack系列)
```
OnFullTrayBack00() → 准备输出满托盘
OnFullTrayBack01() → Z轴升降控制
...
OnFullTrayBack10() → 完成退料输出
```

### 特殊功能

#### 1. 精确定位系统
满托盘系统包含更复杂的定位逻辑：
```cpp
OnFullTrayFeed01_0() → 粗定位
OnFullTrayFeed01_1() → 精定位  
OnFullTrayFeed01_2() → 位置确认
OnFullTrayFeed01_3() → 锁定定位
```

#### 2. 多级安全检查
```cpp
OnFullTrayFeed03_0() → 第一级安全检查
OnFullTrayFeed03_1() → 第二级安全检查
OnFullTrayFeed03_2() → 第三级安全检查
OnFullTrayFeed03_3() → 最终确认
```

## 托盘系统协作机制

### 1. 与机器人系统交互
```cpp
// 机器人请求空托盘
g_pRobot->m_mapParam["TRAY盘进料标志"] = true;

// 机器人归还满托盘  
g_pRobot->m_mapParam["TRAY盘退料标志"] = true;
```

### 2. 与连续运行系统协作
```cpp
// LogicContinueRun控制托盘系统启停
CLogicMgr::m_mapThread["TrayFull"].pThread->Start();
CLogicMgr::m_mapThread["TrayEmpty"].pThread->Start();
```

### 3. 状态同步机制
通过参数映射表实现状态同步：
```cpp
PARAM_BOOL("空TRAY盘进料标志")
PARAM_BOOL("满TRAY盘退料标志")  
PARAM_DOUBLE("托盘位置参数")
```

## 安全和容错机制

### 1. 生命周期安全控制
```cpp
EnumStatus OnPause() {
    m_pTray->FullTrayBeltOff();      // 停止所有皮带
    m_pTray->FullTrayUpDnZStop();    // 停止Z轴运动
    m_pTray->EmptyTrayBeltOff();     // 停止空托盘皮带
    m_pTray->EmptyTrayTransportYStop(); // 停止Y轴运动
    return emRun;
}
```

### 2. 位置检测和确认
每个关键位置都有多重检测：
- 传感器检测
- 位置反馈确认
- 超时保护
- 异常处理

### 3. 运动安全保护
- 运动前的安全检查
- 运动过程的监控
- 异常情况的自动停止

## 设计特点

### 1. 分层管理
- 主控制器协调整体流程
- 子系统专注具体功能
- 清晰的职责分离

### 2. 并发处理  
- 空托盘和满托盘可以并行处理
- 提高系统整体效率
- 减少等待时间

### 3. 状态驱动
- 基于参数标志的状态控制
- 事件驱动的流程切换
- 灵活的流程配置

### 4. 高可靠性
- 多重安全检查
- 完整的错误处理
- 自动恢复机制

托盘管理系统体现了现代自动化系统中物料管理的复杂性和重要性，为整个生产线的高效运行提供了稳定的物料供应保障。 

# 托盘管理系统线程分析

## 概述

FStation的托盘管理系统由三个相关线程组成：LogicTray（总控）、LogicTrayEmpty（空托盘处理）、LogicTrayFull（满托盘处理）。该系统采用三层架构设计，实现了空满托盘的自动化流转、精确定位和智能调度，确保生产线的连续供料和成品输出。

## 系统架构图

```
托盘管理系统
├── LogicTray (29KB) - 托盘总控线程
│   ├── m_pFuncFullTray - 满托盘子线程控制器
│   ├── m_pFuncEmptyTray - 空托盘子线程控制器
│   ├── 自检流程 (SelfCheck00-03)
│   ├── 托盘状态管理 (Tray00-02)
│   ├── 空托盘流程 (EmptyTray系列)
│   └── 满托盘流程 (FullTray系列)
├── LogicTrayEmpty (17KB) - 空托盘处理线程
│   ├── 独立状态机
│   ├── 空托盘上料控制
│   └── 空托盘定位和准备
└── LogicTrayFull (26KB) - 满托盘处理线程
    ├── 独立状态机
    ├── 满托盘下料控制
    └── 成品托盘输出
```

## LogicTray - 托盘总控线程分析

### 类结构
```cpp
class CLogicTray : public CThreadBase
{
private:
    CTray*                   m_pTray;           // 托盘控制对象
    CString                  m_sRet;            // 返回结果字符串
    
    // 状态管理映射表
    map<CString, bool>       m_mapFlag;         // 标志映射
    map<CString, DWORD>      m_mapTick;         // 时间计数
    map<CString, double>     m_mapPos;          // 位置映射
    
    // 子线程控制器
    CThreadFunc*             m_pFuncFullTray;   // 满托盘子线程
    CThreadFunc*             m_pFuncEmptyTray;  // 空托盘子线程
};
```

### 主要功能模块

#### 1. 系统自检模块 (SelfCheck00-03)
```cpp
CStatus OnSelfCheck00();  // 托盘系统初始化检查
CStatus OnSelfCheck01();  // 空托盘传输检查
CStatus OnSelfCheck02();  // 满托盘传输检查
CStatus OnSelfCheck03();  // 位置传感器检查
```

#### 2. 托盘状态管理 (Tray00-02)
```cpp
CStatus OnTray00();       // 托盘系统总体状态检查
CStatus OnTray01();       // 空满托盘状态协调
CStatus OnTray02();       // 托盘流转状态管理
```

#### 3. 空托盘流程控制

**主流程控制**:
```cpp
CStatus OnEmptyTray00();  // 空托盘流程启动
CStatus OnEmptyTray01();  // 空托盘状态检查
```

**进给流程控制**:
```cpp
CStatus OnEmptyTrayFeed00();   // 空托盘进给准备
CStatus OnEmptyTrayFeed01();   // 空托盘位置检测
CStatus OnEmptyTrayFeed02();   // 空托盘传输启动
CStatus OnEmptyTrayFeed03();   // 空托盘移动控制
CStatus OnEmptyTrayFeed04();   // 空托盘到位检查
CStatus OnEmptyTrayFeed05();   // 空托盘定位精调
CStatus OnEmptyTrayFeed06();   // 空托盘夹紧操作
CStatus OnEmptyTrayFeed07();   // 空托盘状态确认
CStatus OnEmptyTrayFeed08();   // 空托盘进给完成
CStatus OnEmptyTrayFeed09();   // 空托盘参数更新
CStatus OnEmptyTrayFeed10();   // 空托盘流程结束
```

**回退流程控制**:
```cpp
CStatus OnEmptyTrayBack00();   // 空托盘回退准备
CStatus OnEmptyTrayBack01();   // 空托盘松开操作
CStatus OnEmptyTrayBack02();   // 空托盘回退启动
CStatus OnEmptyTrayBack03();   // 空托盘回退控制
CStatus OnEmptyTrayBack04();   // 空托盘回退检查
CStatus OnEmptyTrayBack05();   // 空托盘到位确认
CStatus OnEmptyTrayBack06();   // 空托盘回退完成
CStatus OnEmptyTrayBack07();   // 空托盘状态复位
CStatus OnEmptyTrayBack08();   // 空托盘清理操作
CStatus OnEmptyTrayBack09();   // 空托盘参数复位
CStatus OnEmptyTrayBack10();   // 空托盘传输停止
CStatus OnEmptyTrayBack11();   // 空托盘系统复位
CStatus OnEmptyTrayBack12();   // 空托盘状态更新
CStatus OnEmptyTrayBack13();   // 空托盘回退结束
```

#### 4. 满托盘流程控制

**主流程控制**:
```cpp
CStatus OnFullTray00();        // 满托盘流程启动
CStatus OnFullTray01();        // 满托盘状态检查
```

**进给流程控制**:
```cpp
CStatus OnFullTrayFeed00();    // 满托盘进给准备
CStatus OnFullTrayFeed01();    // 满托盘位置检测
CStatus OnFullTrayFeed02();    // 满托盘传输启动
CStatus OnFullTrayFeed03();    // 满托盘移动控制
CStatus OnFullTrayFeed03_0();  // 满托盘移动子状态
CStatus OnFullTrayFeed04();    // 满托盘到位检查
CStatus OnFullTrayFeed05();    // 满托盘定位操作
CStatus OnFullTrayFeed05_0();  // 满托盘定位子状态1
CStatus OnFullTrayFeed05_1();  // 满托盘定位子状态2
CStatus OnFullTrayFeed06();    // 满托盘夹紧操作
CStatus OnFullTrayFeed07();    // 满托盘状态确认
CStatus OnFullTrayFeed08();    // 满托盘进给完成
CStatus OnFullTrayFeed09();    // 满托盘参数更新
CStatus OnFullTrayFeed10();    // 满托盘上升操作
CStatus OnFullTrayFeed11();    // 满托盘准备就绪
CStatus OnFullTrayFeed12();    // 满托盘等待操作
CStatus OnFullTrayFeed13();    // 满托盘进给结束
```

**回退流程控制**:
```cpp
CStatus OnFullTrayBack00();    // 满托盘回退准备
CStatus OnFullTrayBack01();    // 满托盘下降操作
CStatus OnFullTrayBack02();    // 满托盘松开操作
CStatus OnFullTrayBack03();    // 满托盘回退启动
CStatus OnFullTrayBack03_0();  // 满托盘回退子状态1
CStatus OnFullTrayBack03_1();  // 满托盘回退子状态2
CStatus OnFullTrayBack04();    // 满托盘回退控制
CStatus OnFullTrayBack04_0();  // 满托盘回退微调
CStatus OnFullTrayBack05();    // 满托盘到位确认
CStatus OnFullTrayBack06();    // 满托盘回退完成
CStatus OnFullTrayBack07();    // 满托盘状态复位
CStatus OnFullTrayBack08();    // 满托盘清理操作
CStatus OnFullTrayBack09();    // 满托盘参数复位
CStatus OnFullTrayBack10();    // 满托盘传输停止
CStatus OnFullTrayBack11();    // 满托盘回退结束
```

### 子线程管理机制

#### 子线程创建和配置
```cpp
// 构造函数中创建子线程控制器
CLogicTray::CLogicTray()
{
    m_pFuncFullTray = CLogicMgr::m_ThreadFactory.CreateThreadFunc("满托盘");
    m_pFuncEmptyTray = CLogicMgr::m_ThreadFactory.CreateThreadFunc("空托盘");
}

// 主运行函数中设置子线程动作
CStatus CLogicTray::OnRun()
{
    // 通过LogicMgr启动独立的满空托盘线程
    CLogicMgr::m_mapThread["TrayFull"].pThread->Start();
    CLogicMgr::m_mapThread["TrayEmpty"].pThread->Start();
    
    // 设置子线程控制器的动作
    m_pFuncFullTray->SetAction(static_cast<THREAD_FUNC>(&CLogicTray::OnFullTray00));
    m_pFuncEmptyTray->SetAction(static_cast<THREAD_FUNC>(&CLogicTray::OnEmptyTray00));
    
    RETURN_STATE(&CLogicTray::OnSelfCheck00, true);
}
```

#### 子线程状态同步
```cpp
// 在主状态机中轮询子线程状态
EnumStatus statusFull = m_pFuncFullTray->Run(this);
EnumStatus statusEmpty = m_pFuncEmptyTray->Run(this);

// 统一处理子线程状态
switch (statusFull) {
    case emRun:   break;
    case emStop:  RETURN_STOP();
    case emPause: RETURN_PAUSE(NULL);
}
```

## LogicTrayEmpty - 空托盘处理线程

### 核心功能
```cpp
class CLogicTrayEmpty : public CThreadBase
{
    // 专门负责空托盘的进给、定位、夹紧等操作
    // 与LogicTray协调工作，但拥有独立的状态机
};
```

### 主要流程
1. **空托盘检测**: 检测空托盘的存在和位置
2. **传输控制**: 控制空托盘传输机构
3. **精确定位**: 确保空托盘精确到位
4. **状态反馈**: 向总控线程报告处理状态

## LogicTrayFull - 满托盘处理线程

### 核心功能
```cpp
class CLogicTrayFull : public CThreadBase
{
    // 专门负责满托盘的收集、输出、传输等操作
    // 与LogicTray协调工作，处理成品托盘
};
```

### 主要流程
1. **满托盘收集**: 收集完成装配的产品
2. **质量确认**: 确认托盘中产品的质量状态
3. **输出控制**: 控制满托盘的输出传输
4. **状态管理**: 管理满托盘的各种状态标志

## 三线程协作机制

### 1. 分层控制架构
```
LogicTray (总控层)
├── 协调空满托盘的整体流程
├── 管理系统级状态和时序
├── 处理异常和安全控制
└── 统一对外接口

LogicTrayEmpty (执行层)     LogicTrayFull (执行层)
├── 专注空托盘操作         ├── 专注满托盘操作
├── 独立状态机             ├── 独立状态机
├── 精确控制               ├── 精确控制
└── 状态反馈               └── 状态反馈
```

### 2. 数据流协作
```cpp
// 通过全局变量进行状态通信
VAR_TRAY("空托盘就绪标志")    // LogicTrayEmpty → LogicTray
VAR_TRAY("满托盘完成标志")    // LogicTrayFull → LogicTray
VAR_TRAY("托盘系统忙标志")    // LogicTray → 其他线程
```

### 3. 时序协调机制
```cpp
// LogicTray中的时序控制
if (空托盘操作完成 && 满托盘操作完成) {
    // 进入下一个处理周期
    RETURN_STATE(&CLogicTray::OnTray00, true);
}
```

## 安全和异常处理

### 1. 安全检查机制
```cpp
// 每个子线程都有独立的安全检查
EnumStatus OnSafeCheck() {
    // 检查托盘传输安全
    // 检查位置传感器状态
    // 检查夹紧机构状态
    return emRun;
}
```

### 2. 异常恢复机制
```cpp
// 暂停处理 - 安全停止所有托盘机构
EnumStatus OnPause() {
    g_pTray->EmptyTrayBeltOff();        // 停止空托盘传输
    g_pTray->EmptyTrayTransportYStop(); // 停止Y轴运动
    g_pTray->FullTrayBeltOff();         // 停止满托盘传输
    g_pTray->FullTrayUpDnZStop();       // 停止Z轴运动
    
    m_pFuncFullTray->SetStatus(emPause);
    m_pFuncEmptyTray->SetStatus(emPause);
    
    return emRun;
}
```

### 3. 位置和状态管理
```cpp
// 使用映射表管理关键位置和状态
map<CString, double> m_mapPos;    // 托盘位置信息
map<CString, bool>   m_mapFlag;   // 托盘状态标志
map<CString, DWORD>  m_mapTick;   // 时间计数器
```

## 与其他线程的接口

### 1. 与LogicRobot的协作
- **产品供给**: 提供空托盘供机器人抓取原料
- **成品收集**: 接收机器人装配完成的产品
- **状态同步**: 协调机器人工作节拍和托盘操作

### 2. 与LogicContinueRun的协作
- **流程协调**: 在连续运行模式下协调托盘操作
- **状态报告**: 向总协调器报告托盘系统状态
- **参数共享**: 通过参数映射表共享关键信息

## 性能优化特点

### 1. 并行处理
- 空满托盘可以同时进行操作
- 减少等待时间，提高处理效率
- 独立的状态机避免相互阻塞

### 2. 精确控制
- 多级定位确保托盘精确到位
- 分步骤的进给和回退流程
- 完善的位置反馈和校正

### 3. 智能调度
- 根据生产节拍动态调整托盘操作
- 预测性准备减少等待时间
- 自适应的流程控制

## 维护建议

### 1. 定期校准
- 检查托盘位置传感器精度
- 校准夹紧机构的夹紧力度
- 验证传输机构的运动精度

### 2. 状态监控
- 监控托盘操作的成功率
- 分析异常停机的原因
- 跟踪托盘流转的效率

### 3. 参数优化
- 根据实际生产调整传输速度
- 优化定位精度和时间平衡
- 调整安全检查的时间间隔

## 总结

托盘管理系统通过三线程协作的架构设计，实现了高效、精确的托盘自动化管理。LogicTray作为总控核心负责整体协调，LogicTrayEmpty和LogicTrayFull各自专注于特定功能，既保证了系统的模块化，又实现了高效的并行处理。

这种设计模式体现了工控软件中"分而治之"的设计思想，通过合理的功能划分和有效的协作机制，确保了托盘系统的稳定运行和高效生产。对于理解和维护该系统，需要重点关注三个线程之间的协作关系和数据流向。 