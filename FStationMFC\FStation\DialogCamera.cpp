﻿// DialogCamera.cpp : 实现文件
//

#include "stdafx.h"
#include "FStation.h"
#include "DialogCamera.h"
#include "afxdialogex.h"

#include "Sys.h"

#include "LogicMgr.h"

static bool bInitFlag = false;

// CDialogCamera 对话框

static void __stdcall OnCapture( BYTE *pBuffer, void *pUserData1, void *pUserData2, void *pUserData3, void *pUserData4, void *pUserData5, void *pUserData6)
{
	unsigned short* pWidth = (unsigned short *)pUserData1;
	unsigned short* pHeight = (unsigned short *)pUserData2;

	CDialogCamera *pDlg = (CDialogCamera *)pUserData3;

	bool* pTranspose = (bool*)pUserData4;
	bool* pFlipX = (bool*)pUserData5;
	bool* pFlipY = (bool*)pUserData6;

	if (!bInitFlag) {
		return;
	}

	CCvImage* pImage = CCvImage::CreateInstance();

	pImage->SetImage(pBuffer, (int)(*pWidth), (int)(*pHeight), *pTranspose, *pFlipX, *pFlipY);

	pDlg->m_pImageWindow->Show(pImage);

	delete pImage;
}

bool CDialogCamera::m_bHasOne = false;

IMPLEMENT_DYNAMIC(CDialogCamera, CDialogEx)

CDialogCamera::CDialogCamera(CWnd* pParent /*=NULL*/)
	: CDialogEx(CDialogCamera::IDD, pParent)
	, m_nCamera(0)
{
	m_bHasOne = true;
	m_pImageWindow = CImageWindow::CreateInstance("Camera");
}

CDialogCamera::~CDialogCamera()
{
	delete m_pImageWindow;
	m_bHasOne = false;
}

void CDialogCamera::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_SLIDER1, m_slider1);
	DDX_Control(pDX, IDC_SLIDER2, m_slider2);
	DDX_Control(pDX, IDC_LIST1, m_list);
	DDX_Control(pDX, IDC_EDIT4, m_edit);
}

BEGIN_MESSAGE_MAP(CDialogCamera, CDialogEx)
	ON_WM_TIMER()
	ON_WM_HSCROLL()
	ON_NOTIFY(NM_DBLCLK, IDC_LIST1, &CDialogCamera::OnNMDblclkList1)
	ON_NOTIFY(NM_CLICK, IDC_LIST1, &CDialogCamera::OnNMClickList1)
	ON_EN_KILLFOCUS(IDC_EDIT4, &CDialogCamera::OnEnKillfocusEdit4)
	ON_MESSAGE(UM_RIGHT_MOVE, &CDialogCamera::OnUmRightMove)
	ON_BN_CLICKED(IDC_RADIO1, &CDialogCamera::OnBnClickedRadio1)
	ON_BN_CLICKED(IDC_RADIO2, &CDialogCamera::OnBnClickedRadio2)
	ON_BN_CLICKED(IDC_RADIO3, &CDialogCamera::OnBnClickedRadio3)
	ON_BN_CLICKED(IDC_BUTTON1, &CDialogCamera::OnBnClickedButton1)
	ON_MESSAGE(UM_DRAW_FINISH, &CDialogCamera::OnUmDrawFinish)
	ON_BN_CLICKED(IDC_BUTTON3, &CDialogCamera::OnBnClickedButton3)
	ON_BN_CLICKED(IDC_BUTTON2, &CDialogCamera::OnBnClickedButton2)
	ON_BN_CLICKED(IDC_BUTTON7, &CDialogCamera::OnBnClickedButton7)
	ON_BN_CLICKED(IDC_BUTTON6, &CDialogCamera::OnBnClickedButton6)
	ON_BN_CLICKED(IDC_BUTTON5, &CDialogCamera::OnBnClickedButton5)
	ON_BN_CLICKED(IDC_BUTTON4, &CDialogCamera::OnBnClickedButton4)
	ON_BN_CLICKED(IDC_BUTTON8, &CDialogCamera::OnBnClickedButton8)
	ON_BN_CLICKED(IDC_BUTTON9, &CDialogCamera::OnBnClickedButton9)
	ON_BN_CLICKED(IDC_BUTTON10, &CDialogCamera::OnBnClickedButton10)
	ON_BN_CLICKED(IDC_BUTTON11, &CDialogCamera::OnBnClickedButton11)
	ON_BN_CLICKED(IDC_BUTTON12, &CDialogCamera::OnBnClickedButton12)
	ON_BN_CLICKED(IDC_BUTTON13, &CDialogCamera::OnBnClickedButton13)
	ON_BN_CLICKED(IDC_BUTTON14, &CDialogCamera::OnBnClickedButton14)
	ON_BN_CLICKED(IDC_RADIO4, &CDialogCamera::OnBnClickedRadio4)
	ON_BN_CLICKED(IDC_RADIO5, &CDialogCamera::OnBnClickedRadio5)
	ON_BN_CLICKED(IDC_BUTTON15, &CDialogCamera::OnBnClickedButton15)
	ON_BN_CLICKED(IDC_RADIO6, &CDialogCamera::OnBnClickedRadio6)
END_MESSAGE_MAP()

// CDialogCamera 消息处理程序

BOOL CDialogCamera::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	m_imageList.Create(24, 24, TRUE, 2, 2);

	//Node
	LONG lStyle = 0;
	lStyle = GetWindowLong(m_list.m_hWnd, GWL_STYLE);//获取当前窗口样式
	lStyle &= ~LVS_TYPEMASK;//清除显位方式位
	lStyle |= LVS_REPORT;
	SetWindowLong(m_list.m_hWnd, GWL_STYLE, lStyle);

	DWORD dwStyle = 0;
	dwStyle = m_list.GetExtendedStyle();//获取当前窗口扩展样式
	dwStyle |= LVS_EX_FULLROWSELECT;//选中某行使整行高亮
	dwStyle |= LVS_EX_GRIDLINES;//网格线
	m_list.SetExtendedStyle(dwStyle);

	m_list.SetImageList(&m_imageList, LVSIL_SMALL);

	CString sParam[] = { "", "序号", "名称", "x", "y" , "z", "r", "speed"};
	int		nParamLen[] = { 1, 60, 235, 60, 60, 60, 60, 60 };

	for (int i=0; i<sizeof(nParamLen) / sizeof(int); i++)
	{
		if (i == 2) {
			m_list.InsertColumn(i, sParam[i], LVCFMT_LEFT, nParamLen[i]);
		}
		else {
			m_list.InsertColumn(i, sParam[i], LVCFMT_CENTER, nParamLen[i]);
		}
	}

	int nIndex = 0;

	CString str;

	vector<CString>::iterator it = g_pRobot->m_vecRobotPoint.begin();
	for (; it != g_pRobot->m_vecRobotPoint.end(); it++)
	{
		nIndex = m_list.GetItemCount();
		m_list.InsertItem(nIndex, "");
		str.Format("P%02d", nIndex + 1);
		m_list.SetItemText(nIndex, 1, str);
		m_list.SetItemText(nIndex, 2, *it);
		m_list.SetItemText(nIndex, 3, g_pRobot->m_mapRobotPoint[*it]->x);
		m_list.SetItemText(nIndex, 4, g_pRobot->m_mapRobotPoint[*it]->y);
		m_list.SetItemText(nIndex, 5, g_pRobot->m_mapRobotPoint[*it]->z);
		m_list.SetItemText(nIndex, 6, g_pRobot->m_mapRobotPoint[*it]->r);
		m_list.SetItemText(nIndex, 7, g_pRobot->m_mapRobotPoint[*it]->speed);
	}

	m_slider1.SetRange(1000, 50000);
	m_slider1.SetTicFreq(1);

	m_slider2.SetRange(0, 100);
	m_slider2.SetTicFreq(1);

	SetDlgItemInt(IDC_EDIT3, 1);

	SetTimer(1, 100, NULL);

	return TRUE;
}

void CDialogCamera::OnTimer(UINT_PTR nIDEvent)
{
	KillTimer(nIDEvent);

	CRect rcWnd;
	GetDlgItem(IDC_STATIC1)->GetWindowRect(&rcWnd);
	ScreenToClient(rcWnd);
	m_pImageWindow->AttatchWindow(rcWnd);

	m_pImageWindow->SetDrawWnd(GetSafeHwnd());

	bInitFlag = true;

	CDialogEx::OnTimer(nIDEvent);
}

afx_msg LRESULT CDialogCamera::OnUmDrawFinish(WPARAM wParam, LPARAM lParam)
{
	POINT2D *p1 = NULL, *p2 = NULL;

	p1 = (POINT2D *)wParam;
	p2 = (POINT2D *)lParam;

	double nPixelX = (p1->x + p2->x) / 2.0;
	double nPixelY = (p1->y + p2->y) / 2.0;

	PostMessage(UM_RIGHT_MOVE, nPixelX, nPixelY);

	return 0;
}

afx_msg LRESULT CDialogCamera::OnUmRightMove(WPARAM wParam, LPARAM lParam)
{
	double nPixelX = (double)wParam;
	double nPixelY = (double)lParam;

	int nWidth = 0, nHeight = 0;

	switch (m_nCamera)
	{
	case 0:
		nWidth = 2 * g_pRobot->m_mapParam["上相机中心坐标X"]->D();
		nHeight = 2 * g_pRobot->m_mapParam["上相机中心坐标Y"]->D();
		break;
	case 1:
		nWidth = 2 * g_pRobot->m_mapParam["下相机中心坐标X"]->D();
		nHeight = 2 * g_pRobot->m_mapParam["下相机中心坐标Y"]->D();
		break;
	case 2:
		nWidth = 2 * g_pRobot->m_mapParam["TRAY相机中心坐标X"]->D();
		nHeight = 2 * g_pRobot->m_mapParam["TRAY相机中心坐标Y"]->D();
		break;
	case 3:
		nWidth = 2 * g_pRobot->m_mapParam["上相机中心坐标X"]->D();
		nHeight = 2 * g_pRobot->m_mapParam["上相机中心坐标Y"]->D();
		break;
	case 4:
		nWidth = 2 * g_pRobot->m_mapParam["下相机中心坐标X"]->D();
		nHeight = 2 * g_pRobot->m_mapParam["下相机中心坐标Y"]->D();
		break;
	case 5:
		nWidth = 2 * g_pRobot->m_mapParam["上相机中心坐标X"]->D();
		nHeight = 2 * g_pRobot->m_mapParam["上相机中心坐标Y"]->D();
		break;
	default:
		return 0;
	}

	if (nPixelX < 0 || nPixelX >= nWidth || nPixelY < 0 || nPixelY >= nHeight) {
		return 0;
	}

	CImageCalibrate *pImageCalib = NULL;

	switch (m_nCamera)
	{
	case 0:
	case 3:
	case 5:
		pImageCalib = g_pImageCalibrateUp;
		break;
	case 1:
	case 4:
		pImageCalib = g_pImageCalibrateDn;
		break;
//	case 2:
//		pImageCalib = g_pImageCalibrateTray;
//		break;
	default:
		return 0;
	}

	double nOffX = 0, nOffY = 0;

	double nMx1 = 0, nMy1 = 0, nMx2 = 0, nMy2 = 0;

	pImageCalib->TransToMachine(nPixelX, nPixelY, nMx1, nMy1);
	pImageCalib->TransToMachine(nWidth / 2, nHeight / 2, nMx2, nMy2);

	nOffX = nMx1 - nMx2;
	nOffY = nMy1 - nMy2;

	ROBOTPOINT stRobPnt;

	if (g_pRobot->GetPos(stRobPnt) != "OK") {
		return 0;
	}

	switch (m_nCamera)
	{
	case 0:
	case 3:
	case 5:
		stRobPnt.x -= nOffX;
		stRobPnt.y -= nOffY;
		break;
	case 1:
	case 4:
		stRobPnt.x -= nOffX;
		stRobPnt.y -= nOffY;
		break;
//	case 2:
//		stRobPnt.x -= nOffX;
//		stRobPnt.y -= nOffY;
//		break;
	default:
		return 0;
	}

	stRobPnt.speed = 30;

	g_pRobot->Move(stRobPnt, false);

	switch (m_nCamera)
	{
	case 0:
	case 3:
	case 5:
		g_pCamera->SetTriggerMode("Up", CCameraBase::emTriggerModeContinue);
		break;
	case 1:
	case 4:
		g_pCamera->SetTriggerMode("Dn", CCameraBase::emTriggerModeContinue);
		break;
	case 2:
		g_pCamera->SetTriggerMode("Tray", CCameraBase::emTriggerModeContinue);
		break;
	default:break;
	}

	return 0;
}

void CDialogCamera::OnHScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar)
{
	UpdateData();

	switch (m_nCamera)
	{
	case 0:
		VAR_ROBOT("上相机(治具)曝光时间") = m_slider1.GetPos();
		VAR_ROBOT("上相机(治具)增益") = m_slider2.GetPos();

		g_pCamera->SetExposureTime("Up", VAR_ROBOT_I("上相机(治具)曝光时间"));
		g_pCamera->SetGain("Up", VAR_ROBOT_I("上相机(治具)增益"));
		break;
	case 1:
		VAR_ROBOT("下相机曝光时间") = m_slider1.GetPos();
		VAR_ROBOT("下相机增益") = m_slider2.GetPos();

		g_pCamera->SetExposureTime("Dn", VAR_ROBOT_I("下相机曝光时间"));
		g_pCamera->SetGain("Dn", VAR_ROBOT_I("下相机增益"));
		break;
	case 2:
		VAR_ROBOT("TRAY相机曝光时间") = m_slider1.GetPos();
		VAR_ROBOT("TRAY相机增益") = m_slider2.GetPos();

		g_pCamera->SetExposureTime("Tray", VAR_ROBOT_I("TRAY相机曝光时间"));
		g_pCamera->SetGain("Tray", VAR_ROBOT_I("TRAY相机增益"));
		break;
	case 3:
		VAR_ROBOT("上相机(皮带)曝光时间") = m_slider1.GetPos();
		VAR_ROBOT("上相机(皮带)增益") = m_slider2.GetPos();

		g_pCamera->SetExposureTime("Up", VAR_ROBOT_I("上相机(皮带)曝光时间"));
		g_pCamera->SetGain("Up", VAR_ROBOT_I("上相机(皮带)增益"));
		break;
	case 4:
		VAR_ROBOT("下相机(参考位)曝光时间") = m_slider1.GetPos();
		VAR_ROBOT("下相机(参考位)增益") = m_slider2.GetPos();

		g_pCamera->SetExposureTime("Dn", m_slider1.GetPos());
		g_pCamera->SetGain("Dn", m_slider2.GetPos());
		break;
	case 5:
		VAR_ROBOT("上相机(治具二维码)曝光时间") = m_slider1.GetPos();
		VAR_ROBOT("上相机(治具二维码)增益") = m_slider2.GetPos();

		g_pCamera->SetExposureTime("Up", VAR_ROBOT_I("上相机(治具二维码)曝光时间"));
		g_pCamera->SetGain("Up", VAR_ROBOT_I("上相机(治具二维码)增益"));
		break;
	default:break;
	}

	SetDlgItemInt(IDC_EDIT1, m_slider1.GetPos());
	SetDlgItemInt(IDC_EDIT2, m_slider2.GetPos());
	
	g_pRobot->Save();

	CDialogEx::OnHScroll(nSBCode, nPos, pScrollBar);
}

void CDialogCamera::OnNMClickList1(NMHDR *pNMHDR, LRESULT *pResult)
{
	LPNMITEMACTIVATE pNMItemActivate = reinterpret_cast<LPNMITEMACTIVATE>(pNMHDR);

	NM_LISTVIEW  *pEditCtrl = (NM_LISTVIEW *)pNMHDR;

	m_nRowForEdit = pNMItemActivate->iItem;//m_row选中行行号
	m_nColForEdit = pNMItemActivate->iSubItem;//m_col选中行列号

	*pResult = 0;
}

void CDialogCamera::OnNMDblclkList1(NMHDR *pNMHDR, LRESULT *pResult)
{
	LPNMITEMACTIVATE pNMItemActivate = reinterpret_cast<LPNMITEMACTIVATE>(pNMHDR);

	NM_LISTVIEW  *pEditCtrl = (NM_LISTVIEW *)pNMHDR;

	m_nRowForEdit = pNMItemActivate->iItem;//m_row选中行行号
	m_nColForEdit = pNMItemActivate->iSubItem;//m_col选中行列号

	CColorList &thisList = m_list;

	CRect rc;
	thisList.GetSubItemRect(pNMItemActivate->iItem, pNMItemActivate->iSubItem, LVIR_LABEL, rc);//取得子项的矩形
	thisList.ClientToScreen(&rc);
	ScreenToClient(&rc);

	if (pEditCtrl->iItem >= 0 && pEditCtrl->iSubItem > 2 && pEditCtrl->iSubItem < 8) {//编辑框
		CString strVal;
		strVal = thisList.GetItemText(pNMItemActivate->iItem, pNMItemActivate->iSubItem);//取得子项的内容
		m_edit.SetWindowText(strVal);//将子项的内容显示到编辑框中
		m_edit.MoveWindow(&rc);//将编辑框移动到子项上面&#xff0c;覆盖在子项上
		m_edit.ShowWindow(SW_SHOW);//显示编辑框
		m_edit.SetFocus();//使编辑框取得焦点
		m_edit.CreateSolidCaret(1, rc.Height() - 5);//创建一个光标
		m_edit.ShowCaret();//显示光标
		m_edit.SetSel(0, -1);//使光标移到最后面
	}

	*pResult = 0;
}

void CDialogCamera::OnEnKillfocusEdit4()
{
	if (CSys::m_nRight < 4) {
		AfxMessageBox("无修改权限!");
		return;
	}

	CString sName, sVal;

	sName = m_list.GetItemText(m_nRowForEdit, 2);

	GetDlgItemText(IDC_EDIT4, sVal);

	m_list.SetItemText(m_nRowForEdit, m_nColForEdit, sVal);

	m_edit.ShowWindow(SW_HIDE);

	switch (m_nColForEdit)
	{
	case 3:
		g_pRobot->m_mapRobotPoint[sName]->x = atof(sVal);
		break;
	case 4:
		g_pRobot->m_mapRobotPoint[sName]->y = atof(sVal);
		break;
	case 5:
		g_pRobot->m_mapRobotPoint[sName]->z = atof(sVal);
		break;
	case 6:
		g_pRobot->m_mapRobotPoint[sName]->r = atof(sVal);
		break;
	case 7:
		g_pRobot->m_mapRobotPoint[sName]->speed = atoi(sVal);
		break;
	default:break;
	}

	g_pRobot->SaveRobotPoint(sName);
}

void CDialogCamera::OnBnClickedRadio1()
{
	m_nCamera = 0;

	g_pCamera->UnRegister("Dn");
	g_pCamera->UnRegister("Tray");
	g_pCamera->Register("Up", (CAMERACAPTUREFUNC)(&OnCapture), this);

	g_pRobot->UpCameraLightOn();
	g_pRobot->DnCameraLightOff();
	g_pRobot->TrayCameraLightOff();

	m_slider1.SetPos(VAR_ROBOT_I("上相机(治具)曝光时间"));
	m_slider2.SetPos(VAR_ROBOT_I("上相机(治具)增益"));

	SetDlgItemInt(IDC_EDIT1, m_slider1.GetPos());
	SetDlgItemInt(IDC_EDIT2, m_slider2.GetPos());

	g_pCamera->SetExposureTime("Up", VAR_ROBOT_I("上相机(治具)曝光时间"));
	g_pCamera->SetGain("Up", VAR_ROBOT_I("上相机(治具)增益"));

	g_pCamera->SetTriggerMode("Dn", CCameraBase::emTriggerModeSoftware);
	g_pCamera->SetTriggerMode("Tray", CCameraBase::emTriggerModeSoftware);
	g_pCamera->SetTriggerMode("Up", CCameraBase::emTriggerModeContinue);

}

void CDialogCamera::OnBnClickedRadio2()
{
	m_nCamera = 1;

	g_pCamera->UnRegister("Up");
	g_pCamera->UnRegister("Tray");
	g_pCamera->Register("Dn", (CAMERACAPTUREFUNC)(&OnCapture), this);

	g_pRobot->UpCameraLightOff();
	g_pRobot->DnCameraLightOn();
	g_pRobot->TrayCameraLightOff();
	m_slider1.SetPos(VAR_ROBOT_I("下相机曝光时间"));
	m_slider2.SetPos(VAR_ROBOT_I("下相机增益"));

	SetDlgItemInt(IDC_EDIT1, m_slider1.GetPos());
	SetDlgItemInt(IDC_EDIT2, m_slider2.GetPos());

	g_pCamera->SetExposureTime("Dn", VAR_ROBOT_I("下相机曝光时间"));
	g_pCamera->SetGain("Dn", VAR_ROBOT_I("下相机增益"));

	g_pCamera->SetTriggerMode("Up", CCameraBase::emTriggerModeSoftware);
	g_pCamera->SetTriggerMode("Tray", CCameraBase::emTriggerModeSoftware);
	g_pCamera->SetTriggerMode("Dn", CCameraBase::emTriggerModeContinue);
}

void CDialogCamera::OnBnClickedRadio3()
{
	m_nCamera = 2;

	g_pCamera->UnRegister("Up");
	g_pCamera->UnRegister("Dn");
	g_pCamera->Register("Tray", (CAMERACAPTUREFUNC)(&OnCapture), this);

	g_pRobot->UpCameraLightOff();
	g_pRobot->DnCameraLightOff();
	g_pRobot->TrayCameraLightOn();

	m_slider1.SetPos(VAR_ROBOT_I("TRAY相机曝光时间"));
	m_slider2.SetPos(VAR_ROBOT_I("TRAY相机增益"));

	SetDlgItemInt(IDC_EDIT1, m_slider1.GetPos());
	SetDlgItemInt(IDC_EDIT2, m_slider2.GetPos());

	g_pCamera->SetExposureTime("Tray", VAR_ROBOT_I("TRAY相机曝光时间"));
	g_pCamera->SetGain("Tray", VAR_ROBOT_I("TRAY相机增益"));

	g_pCamera->SetTriggerMode("Up", CCameraBase::emTriggerModeSoftware);
	g_pCamera->SetTriggerMode("Dn", CCameraBase::emTriggerModeSoftware);
	g_pCamera->SetTriggerMode("Tray", CCameraBase::emTriggerModeContinue);
}

void CDialogCamera::OnBnClickedRadio4()
{
	m_nCamera = 3;

	g_pCamera->UnRegister("Dn");
	g_pCamera->UnRegister("Tray");
	g_pCamera->Register("Up", (CAMERACAPTUREFUNC)(&OnCapture), this);

	g_pRobot->UpCameraLightOn();
	g_pRobot->DnCameraLightOff();
	g_pRobot->TrayCameraLightOff();

	m_slider1.SetPos(VAR_ROBOT_I("上相机(皮带)曝光时间"));
	m_slider2.SetPos(VAR_ROBOT_I("上相机(皮带)增益"));

	SetDlgItemInt(IDC_EDIT1, m_slider1.GetPos());
	SetDlgItemInt(IDC_EDIT2, m_slider2.GetPos());

	g_pCamera->SetExposureTime("Up", VAR_ROBOT_I("上相机(皮带)曝光时间"));
	g_pCamera->SetGain("Up", VAR_ROBOT_I("上相机(皮带)增益"));

	g_pCamera->SetTriggerMode("Dn", CCameraBase::emTriggerModeSoftware);
	g_pCamera->SetTriggerMode("Tray", CCameraBase::emTriggerModeSoftware);
	g_pCamera->SetTriggerMode("Up", CCameraBase::emTriggerModeContinue);
}

void CDialogCamera::OnBnClickedRadio5()
{
	m_nCamera = 4;

	g_pCamera->UnRegister("Up");
	g_pCamera->UnRegister("Tray");
	g_pCamera->Register("Dn", (CAMERACAPTUREFUNC)(&OnCapture), this);

	g_pRobot->UpCameraLightOff();
	g_pRobot->DnCameraLightOn();
	g_pRobot->TrayCameraLightOff();
	m_slider1.SetPos(VAR_ROBOT_I("下相机(参考位)曝光时间"));
	m_slider2.SetPos(VAR_ROBOT_I("下相机(参考位)增益"));

	SetDlgItemInt(IDC_EDIT1, m_slider1.GetPos());
	SetDlgItemInt(IDC_EDIT2, m_slider2.GetPos());

	g_pCamera->SetExposureTime("Dn", VAR_ROBOT_I("下相机(参考位)曝光时间"));
	g_pCamera->SetGain("Dn", VAR_ROBOT_I("下相机(参考位)增益"));

	g_pCamera->SetTriggerMode("Up", CCameraBase::emTriggerModeSoftware);
	g_pCamera->SetTriggerMode("Tray", CCameraBase::emTriggerModeSoftware);
	g_pCamera->SetTriggerMode("Dn", CCameraBase::emTriggerModeContinue);
}

void CDialogCamera::OnBnClickedRadio6()
{
	m_nCamera = 5;

	g_pCamera->UnRegister("Dn");
	g_pCamera->UnRegister("Tray");
	g_pCamera->Register("Up", (CAMERACAPTUREFUNC)(&OnCapture), this);

	g_pRobot->UpCameraLightOn();
	g_pRobot->DnCameraLightOff();
	g_pRobot->TrayCameraLightOff();

	m_slider1.SetPos(VAR_ROBOT_I("上相机(治具二维码)曝光时间"));
	m_slider2.SetPos(VAR_ROBOT_I("上相机(治具二维码)增益"));

	SetDlgItemInt(IDC_EDIT1, m_slider1.GetPos());
	SetDlgItemInt(IDC_EDIT2, m_slider2.GetPos());

	g_pCamera->SetExposureTime("Up", VAR_ROBOT_I("上相机(治具二维码)曝光时间"));
	g_pCamera->SetGain("Up", VAR_ROBOT_I("上相机(治具二维码)增益"));

	g_pCamera->SetTriggerMode("Dn", CCameraBase::emTriggerModeSoftware);
	g_pCamera->SetTriggerMode("Tray", CCameraBase::emTriggerModeSoftware);
	g_pCamera->SetTriggerMode("Up", CCameraBase::emTriggerModeContinue);
}

void CDialogCamera::OnBnClickedButton1()
{
	g_pCamera->SetTriggerMode("Up", CCameraBase::emTriggerModeSoftware);
	g_pCamera->SetTriggerMode("Dn", CCameraBase::emTriggerModeSoftware);
	g_pCamera->SetTriggerMode("Tray", CCameraBase::emTriggerModeSoftware);

	Sleep(200);

	m_pImageWindow->DrawCircle();
}

void CDialogCamera::OnBnClickedButton3()
{
	ROBOTPOINT stRobPnt;

	CString sRet, sVal;

	sRet = g_pRobot->GetPos(stRobPnt);

	if (sRet != "OK") {
		return;
	}

	stRobPnt.speed = 30;

	UpdateData();

	GetDlgItemText(IDC_EDIT3, sVal);

	if (sVal.IsEmpty()) {
		return;
	} 

	double nVal = 0;

	nVal = atof(sVal);

	if (fabs(nVal) > 30) {
		AfxMessageBox("步长限制30°，超出限制!");
		return;
	}

	stRobPnt.r += nVal;

	g_pRobot->Move(stRobPnt, false);
}

void CDialogCamera::OnBnClickedButton2()
{
	ROBOTPOINT stRobPnt;

	CString sRet, sVal;

	sRet = g_pRobot->GetPos(stRobPnt);

	if (sRet != "OK") {
		return;
	}

	stRobPnt.speed = 30;

	UpdateData();

	GetDlgItemText(IDC_EDIT3, sVal);

	if (sVal.IsEmpty()) {
		return;
	} 

	double nVal = 0;

	nVal = atof(sVal);

	if (fabs(nVal) > 30) {
		AfxMessageBox("步长限制30°，超出限制!");
		return;
	}

	stRobPnt.r -= nVal;

	g_pRobot->Move(stRobPnt, false);
}


void CDialogCamera::OnBnClickedButton7()
{
	ROBOTPOINT stRobPnt;

	CString sRet, sVal;

	sRet = g_pRobot->GetPos(stRobPnt);

	if (sRet != "OK") {
		return;
	}

	stRobPnt.speed = 30;

	UpdateData();

	GetDlgItemText(IDC_EDIT3, sVal);

	if (sVal.IsEmpty()) {
		return;
	} 

	double nVal = 0;

	nVal = atof(sVal);

	if (fabs(nVal) > 30) {
		AfxMessageBox("步长限制30，超出限制!");
		return;
	}

	stRobPnt.x += nVal;

	sRet.Empty();

	g_pRobot->Move(stRobPnt, false, true, &sRet);

	if (!sRet.IsEmpty()) {
		AfxMessageBox(sRet);
	}
}


void CDialogCamera::OnBnClickedButton6()
{
	ROBOTPOINT stRobPnt;

	CString sRet, sVal;

	sRet = g_pRobot->GetPos(stRobPnt);

	if (sRet != "OK") {
		return;
	}

	stRobPnt.speed = 30;

	UpdateData();

	GetDlgItemText(IDC_EDIT3, sVal);

	if (sVal.IsEmpty()) {
		return;
	} 

	double nVal = 0;

	nVal = atof(sVal);

	if (fabs(nVal) > 30) {
		AfxMessageBox("步长限制30，超出限制!");
		return;
	}

	stRobPnt.x -= nVal;

	sRet.Empty();

	g_pRobot->Move(stRobPnt, false, true, &sRet);

	if (!sRet.IsEmpty()) {
		AfxMessageBox(sRet);
	}
}


void CDialogCamera::OnBnClickedButton5()
{
	ROBOTPOINT stRobPnt;

	CString sRet, sVal;

	sRet = g_pRobot->GetPos(stRobPnt);

	if (sRet != "OK") {
		return;
	}

	stRobPnt.speed = 30;

	UpdateData();

	GetDlgItemText(IDC_EDIT3, sVal);

	if (sVal.IsEmpty()) {
		return;
	} 

	double nVal = 0;

	nVal = atof(sVal);

	if (fabs(nVal) > 30) {
		AfxMessageBox("步长限制30，超出限制!");
		return;
	}

	stRobPnt.y += nVal;

	sRet.Empty();

	g_pRobot->Move(stRobPnt, false, true, &sRet);

	if (!sRet.IsEmpty()) {
		AfxMessageBox(sRet);
	}
}

void CDialogCamera::OnBnClickedButton4()
{
	ROBOTPOINT stRobPnt;

	CString sRet, sVal;

	sRet = g_pRobot->GetPos(stRobPnt);

	if (sRet != "OK") {
		return;
	}

	stRobPnt.speed = 30;

	UpdateData();

	GetDlgItemText(IDC_EDIT3, sVal);

	if (sVal.IsEmpty()) {
		return;
	} 

	double nVal = 0;

	nVal = atof(sVal);

	if (fabs(nVal) > 30) {
		AfxMessageBox("步长限制30，超出限制!");
		return;
	}

	stRobPnt.y -= nVal;

	sRet.Empty();

	g_pRobot->Move(stRobPnt, false, true, &sRet);

	if (!sRet.IsEmpty()) {
		AfxMessageBox(sRet);
	}
}

void CDialogCamera::OnBnClickedButton8()
{
	if (CSys::m_nRight < 3) {
		AfxMessageBox("无修改权限!");
		return;
	}

	ROBOTPOINT stRobPnt;

	if (g_pRobot->GetPos(stRobPnt) != "OK") {
		AfxMessageBox("获取机械手当前点位失败");
		return;
	}

	CString sName;

	sName = m_list.GetItemText(m_nRowForEdit, 2);

	if (sName.IsEmpty()) {
		return;
	}

	CString sRet;

	sRet.Format("示教点位数据: [%s], 是否继续?", sName);

	if (AfxMessageBox(sRet, MB_YESNO) == IDNO) {
		return;
	}

	g_pRobot->m_mapRobotPoint[sName]->x = stRobPnt.x;
	g_pRobot->m_mapRobotPoint[sName]->y = stRobPnt.y;
	g_pRobot->m_mapRobotPoint[sName]->z = stRobPnt.z;
	g_pRobot->m_mapRobotPoint[sName]->r = stRobPnt.r;
	g_pRobot->m_mapRobotPoint[sName]->cf1 = stRobPnt.cf1;
	g_pRobot->m_mapRobotPoint[sName]->cf4 = stRobPnt.cf4;
	g_pRobot->m_mapRobotPoint[sName]->cf6 = stRobPnt.cf6;
	g_pRobot->m_mapRobotPoint[sName]->cfx = stRobPnt.cfx;

	m_list.SetItemText(m_nRowForEdit, 3, g_pRobot->m_mapRobotPoint[sName]->x);
	m_list.SetItemText(m_nRowForEdit, 4, g_pRobot->m_mapRobotPoint[sName]->y);
	m_list.SetItemText(m_nRowForEdit, 5, g_pRobot->m_mapRobotPoint[sName]->z);
	m_list.SetItemText(m_nRowForEdit, 6, g_pRobot->m_mapRobotPoint[sName]->r);

	g_pRobot->SaveRobotPoint(sName);
}

void CDialogCamera::OnBnClickedButton9()
{
	CString sName;

	sName = m_list.GetItemText(m_nRowForEdit, 2);

	if (sName.IsEmpty()) {
		return;
	}

	CString sRet;

	sRet.Empty();

	g_pRobot->Move(sName, true, true, &sRet);

	if (!sRet.IsEmpty()) {
		AfxMessageBox(sRet);
	}
}

void CDialogCamera::OnBnClickedButton10()
{
	ROBOTPOINT stRobPnt;

	CString sRet, sVal;

	sRet = g_pRobot->GetPos(stRobPnt);

	if (sRet != "OK") {
		return;
	}

	stRobPnt.speed = 30;

	UpdateData();

	GetDlgItemText(IDC_EDIT3, sVal);

	if (sVal.IsEmpty()) {
		return;
	} 

	double nVal = 0;

	nVal = atof(sVal);

	if (fabs(nVal) > 30) {
		AfxMessageBox("步长限制30，超出限制!");
		return;
	}

	stRobPnt.z += nVal;

	sRet.Empty();

	g_pRobot->Move(stRobPnt, false, true, &sRet);

	if (!sRet.IsEmpty()) {
		AfxMessageBox(sRet);
	}
}

void CDialogCamera::OnBnClickedButton11()
{
	ROBOTPOINT stRobPnt;

	CString sRet, sVal;

	sRet = g_pRobot->GetPos(stRobPnt);

	if (sRet != "OK") {
		return;
	}

	stRobPnt.speed = 30;

	UpdateData();

	GetDlgItemText(IDC_EDIT3, sVal);

	if (sVal.IsEmpty()) {
		return;
	} 

	double nVal = 0;

	nVal = atof(sVal);

	if (fabs(nVal) > 30) {
		AfxMessageBox("步长限制30，超出限制!");
		return;
	}

	stRobPnt.z -= nVal;

	sRet.Empty();

	g_pRobot->Move(stRobPnt, false, true, &sRet);

	if (!sRet.IsEmpty()) {
		AfxMessageBox(sRet);
	}
}

void CDialogCamera::OnBnClickedButton12()
{
	double nCalibMachineX = 0, nCalibMachineY = 0, nCalibCenterPixelX = 0, nCalibCenterPixelY = 0;

	g_pImageCalibrateDn->GetCalibrateCoordinate(nCalibMachineX, nCalibMachineY);

	g_pImageCalibrateDn->GetCalibrateCenter(nCalibCenterPixelX, nCalibCenterPixelY);

	double nCenterOffX = 0, nCenterOffY = 0, nCalibCenterMachineX = 0, nCalibCenterMachineY = 0, nCamCenterMachineX = 0, nCamCenterMachineY = 0;

	g_pImageCalibrateDn->TransToMachine(nCalibCenterPixelX, nCalibCenterPixelY, nCalibCenterMachineX, nCalibCenterMachineY);
	g_pImageCalibrateDn->TransToMachine(g_pRobot->m_mapParam["下相机中心坐标X"]->D(), g_pRobot->m_mapParam["下相机中心坐标Y"]->D(), nCamCenterMachineX, nCamCenterMachineY);

	VAR_ROBOT("吸嘴1基于旋转中心坐标X") = nCamCenterMachineX - nCalibCenterMachineX + nCalibMachineX - g_pRobot->m_mapRobotPoint["下相机吸嘴1取料参考位"]->x;
	VAR_ROBOT("吸嘴1基于旋转中心坐标Y") = nCamCenterMachineY - nCalibCenterMachineY + nCalibMachineY - g_pRobot->m_mapRobotPoint["下相机吸嘴1取料参考位"]->y;

	VAR_ROBOT("吸嘴2基于旋转中心坐标X") = nCamCenterMachineX - nCalibCenterMachineX + nCalibMachineX - g_pRobot->m_mapRobotPoint["下相机吸嘴2取料参考位"]->x;
	VAR_ROBOT("吸嘴2基于旋转中心坐标Y") = nCamCenterMachineY - nCalibCenterMachineY + nCalibMachineY - g_pRobot->m_mapRobotPoint["下相机吸嘴2取料参考位"]->y;

	VAR_ROBOT("吸嘴3基于旋转中心坐标X") = nCamCenterMachineX - nCalibCenterMachineX + nCalibMachineX - g_pRobot->m_mapRobotPoint["下相机吸嘴3取料参考位"]->x;
	VAR_ROBOT("吸嘴3基于旋转中心坐标Y") = nCamCenterMachineY - nCalibCenterMachineY + nCalibMachineY - g_pRobot->m_mapRobotPoint["下相机吸嘴3取料参考位"]->y;

	VAR_ROBOT("吸嘴4基于旋转中心坐标X") = nCamCenterMachineX - nCalibCenterMachineX + nCalibMachineX - g_pRobot->m_mapRobotPoint["下相机吸嘴4取料参考位"]->x;
	VAR_ROBOT("吸嘴4基于旋转中心坐标Y") = nCamCenterMachineY - nCalibCenterMachineY + nCalibMachineY - g_pRobot->m_mapRobotPoint["下相机吸嘴4取料参考位"]->y;

	g_pRobot->Save();
	g_pRobot->SaveRobotPoint();
}

void CDialogCamera::OnBnClickedButton13()
{
	g_pCamera->SetTriggerMode("Up", CCameraBase::emTriggerModeSoftware);
	g_pCamera->SetTriggerMode("Dn", CCameraBase::emTriggerModeSoftware);
	g_pCamera->SetTriggerMode("Tray", CCameraBase::emTriggerModeSoftware);

	Sleep(200);

	m_pImageWindow->DrawRect();
}

void CDialogCamera::OnBnClickedButton14()
{
	if (CSys::m_nRight < 3) {
		AfxMessageBox("无修改权限!");
		return;
	}

	ROBOTPOINT stRobPnt;

	if (g_pRobot->GetPos(stRobPnt) != "OK") {
		AfxMessageBox("获取机械手当前点位失败");
		return;
	}

	double nDistance[2] = { 0 };
	CString sName[2] = { "A轨左下角Mark拍照位", "B轨左下角Mark拍照位" };

	for (int i=0; i<2; i++)
	{
		nDistance[i] = (stRobPnt.x - g_pRobot->m_mapRobotPoint[sName[i]]->x) * (stRobPnt.x - g_pRobot->m_mapRobotPoint[sName[i]]->x) + (stRobPnt.y - g_pRobot->m_mapRobotPoint[sName[i]]->y) * (stRobPnt.y - g_pRobot->m_mapRobotPoint[sName[i]]->y);
	}

	if (nDistance[0] < nDistance[1]) {
		VAR_ROBOT("治具二维码位置偏移X") = stRobPnt.x - g_pRobot->m_mapRobotPoint[sName[0]]->x;
		VAR_ROBOT("治具二维码位置偏移Y") = g_pRobot->m_mapRobotPoint[sName[0]]->y - stRobPnt.y;
	}
	else {
		VAR_ROBOT("治具二维码位置偏移X") = stRobPnt.x - g_pRobot->m_mapRobotPoint[sName[1]]->x;
		VAR_ROBOT("治具二维码位置偏移Y") = g_pRobot->m_mapRobotPoint[sName[1]]->y - stRobPnt.y;
	}

	g_pRobot->Save();
}

void CDialogCamera::OnBnClickedButton15()
{
	if (CSys::m_nRight < 3) {
		AfxMessageBox("无修改权限!");
		return;
	}

	ROBOTPOINT stRobPnt;

	if (g_pRobot->GetPos(stRobPnt) != "OK") {
		AfxMessageBox("获取机械手当前点位失败");
		return;
	}

	double nDistance[2] = { 0 };
	CString sName[2] = { "A轨左下角Mark拍照位", "B轨左下角Mark拍照位" };

	for (int i=0; i<2; i++)
	{
		nDistance[i] = (stRobPnt.x - g_pRobot->m_mapRobotPoint[sName[i]]->x) * (stRobPnt.x - g_pRobot->m_mapRobotPoint[sName[i]]->x) + (stRobPnt.y - g_pRobot->m_mapRobotPoint[sName[i]]->y) * (stRobPnt.y - g_pRobot->m_mapRobotPoint[sName[i]]->y);
	}

	if (nDistance[0] < nDistance[1]) {
		VAR_ROBOT("主板回拍位置偏移X") = stRobPnt.x - g_pRobot->m_mapRobotPoint[sName[0]]->x;
		VAR_ROBOT("主板回拍位置偏移Y") = g_pRobot->m_mapRobotPoint[sName[0]]->y - stRobPnt.y;
	}
	else {
		VAR_ROBOT("主板回拍位置偏移X") = stRobPnt.x - g_pRobot->m_mapRobotPoint[sName[1]]->x;
		VAR_ROBOT("主板回拍位置偏移Y") = g_pRobot->m_mapRobotPoint[sName[1]]->y - stRobPnt.y;
	}

	g_pRobot->Save();
}

BOOL CDialogCamera::DestroyWindow()
{
	g_pRobot->UpCameraLightOff();
	g_pRobot->DnCameraLightOff();
	g_pRobot->TrayCameraLightOff();

	g_pCamera->SetTriggerMode("Up", CCameraBase::emTriggerModeSoftware);
	g_pCamera->SetTriggerMode("Dn", CCameraBase::emTriggerModeSoftware);
	g_pCamera->SetTriggerMode("Tray", CCameraBase::emTriggerModeSoftware);

	Sleep(1000);

	bInitFlag = false;

	return CDialogEx::DestroyWindow();
}
