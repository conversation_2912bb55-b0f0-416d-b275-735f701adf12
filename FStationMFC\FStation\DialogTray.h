﻿#pragma once

#include "Resource.h"

#include "ColorButton.h"

#include "ThreadBase.h"
using namespace yzBase;

// CDialogTray 对话框

class CDialogTray : public CDialogEx, public CParallel
{
	DECLARE_DYNAMIC(CDialogTray)

public:
	CDialogTray(CPoint pt, CWnd* pParent = NULL);   // 标准构造函数
	virtual ~CDialogTray();

// 对话框数据
	enum { IDD = IDD_DIALOG_TRAY };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持
	virtual BOOL PreTranslateMessage(MSG* pMsg);
	virtual void OnOK() {};
	virtual BOOL OnInitDialog();
	virtual bool Excute();

	DECLARE_MESSAGE_MAP()
	afx_msg void OnTimer(UINT_PTR nIDEvent);
	afx_msg void OnBnClickedButton1();
	afx_msg void OnBnClickedButton2();
	afx_msg void OnBnClickedButton3();
	afx_msg void OnBnClickedButton4();
	afx_msg void OnBnClickedButton5();
	afx_msg void OnBnClickedButton6();
	afx_msg void OnBnClickedButton7();
	afx_msg void OnBnClickedButton8();
	afx_msg void OnBnClickedButton9();
	afx_msg void OnBnClickedButton10();
	afx_msg void OnBnClickedButton11();
	afx_msg void OnBnClickedButton12();
	afx_msg void OnBnClickedButton13();
	afx_msg void OnBnClickedButton14();
	afx_msg void OnBnClickedButton15();
	afx_msg void OnBnClickedButton16();
	afx_msg void OnBnClickedButton17();
	afx_msg void OnBnClickedButton18();
	afx_msg void OnBnClickedButton19();
	afx_msg void OnBnClickedButton20();
	afx_msg void OnBnClickedButton21();
	afx_msg void OnBnClickedButton22();

public:
	void UpdateButtonState();

protected:
	CColorButton	m_btnReset;
	CColorButton	m_btnStart;
	CColorButton	m_btnPause;
	CColorButton	m_btnStop;

private:
	CPoint			m_pt;

	DWORD			m_nResetTimer;
};
