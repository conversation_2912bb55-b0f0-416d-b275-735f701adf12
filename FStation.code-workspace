{"folders": [{"name": "FStationMFC", "path": "./FStationMFC"}, {"name": "MQTTServices", "path": "./MQTTServices"}, {"name": "Shared", "path": "./shared"}], "settings": {"files.autoGuessEncoding": true, "files.exclude": {"**/build/": true, "**/build_vscode/": true, "**/.vs/": true, "**/Debug/": true, "**/Release/": true, "**/x64/": true, "**/*.vcxproj.user": true, "**/*.suo": true, "**/*.sdf": true}}, "extensions": {"recommendations": ["ms-vscode.cpptools", "ms-vscode.cmake-tools", "ms-vscode.cpptools-extension-pack"]}}