{"serverIP": "127.0.0.1", "serverPort": 8888, "connectTimeout": 5000, "sendTimeout": 3000, "receiveTimeout": 3000, "autoReconnect": true, "retryInterval": 10000, "maxRetryCount": 0, "backoffMultiplier": 2, "maxRetryInterval": 60000, "forceReconnectOnError": true, "_comments": {"serverIP": "MQTT网关服务器IP地址", "serverPort": "MQTT网关服务器端口", "connectTimeout": "连接超时时间(毫秒)", "sendTimeout": "发送超时时间(毫秒)", "receiveTimeout": "接收超时时间(毫秒)", "autoReconnect": "是否启用自动重连 (true/false)", "retryInterval": "初始重连间隔(毫秒)", "maxRetryCount": "最大重试次数 (0表示无限重连，推荐设置为0)", "backoffMultiplier": "退避倍数 (失败后间隔乘以此倍数)", "maxRetryInterval": "最大重连间隔(毫秒，60秒)", "forceReconnectOnError": "在任何网络错误时强制重连 (true/false)"}}