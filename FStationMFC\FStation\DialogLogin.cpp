﻿// DialogLogin.cpp : 实现文件
//

#include "stdafx.h"
#include "FStation.h"
#include "DialogLogin.h"
#include "afxdialogex.h"

#include "ExcelEx.h"


// CDialogLogin 对话框

IMPLEMENT_DYNAMIC(CDialogLogin, CDialogEx)

CDialogLogin::CDialogLogin(CWnd* pParent /*=NULL*/)
	: CDialogEx(CDialogLogin::IDD, pParent)
{

}

CDialogLogin::~CDialogLogin()
{
}

void CDialogLogin::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_COMBO1, m_comboUserName);
	DDX_Control(pDX, IDC_COMBO2, m_comboLevel);
}

BEGIN_MESSAGE_MAP(CDialogLogin, CDialogEx)
	ON_BN_CLICKED(IDC_BN_LOG_ON, &CDialogLogin::OnBnClickedBnLogOn)
	ON_BN_CLICKED(IDC_BN_LOG_OUT, &CDialogLogin::OnBnClickedBnLogOut)
	ON_BN_CLICKED(IDC_BUTTON1, &CDialogLogin::OnBnClickedButton1)
	ON_BN_CLICKED(IDC_BUTTON2, &CDialogLogin::OnBnClickedButton2)
	ON_CBN_SELCHANGE(IDC_COMBO1, &CDialogLogin::OnCbnSelchangeCombo1)
END_MESSAGE_MAP()

BOOL CDialogLogin::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	for (unsigned int i=0; i<g_vUser.size(); i++)
	{
		m_comboUserName.AddString(g_vUser[i].sName);
	}

	if (m_comboUserName.GetCount() > 0) {
		m_comboUserName.SetCurSel(0);
		m_comboLevel.SetCurSel(3 - g_vUser[0].nLevel);
	}

	return TRUE;
}

// CDialogLogin 消息处理程序

void CDialogLogin::OnCbnSelchangeCombo1()
{
	int nSel = 0;
	nSel = m_comboUserName.GetCurSel();

	if (nSel >= 0 && nSel < m_comboUserName.GetCount()) {
		m_comboLevel.SetCurSel(3 - g_vUser[nSel].nLevel);
	}
}

void CDialogLogin::OnBnClickedBnLogOn()
{
	UpdateData();

	bool bFlag = true;

	CString strValue;
	GetDlgItemText(IDC_SEC_CODE, strValue);

	int nSel = 0;
	nSel = m_comboUserName.GetCurSel();

	if (nSel < 0 || nSel >= g_vUser.size()) {
		AfxMessageBox("用户不存在");
		return;
	}

	if (g_vUser[nSel].sKey == strValue) {
		g_nPermissionLevel = g_vUser[nSel].nLevel;
		AfxMessageBox("登录成功");
		OnOK();
	}
	else {
		AfxMessageBox("密码不正确");
	}
}

void CDialogLogin::OnBnClickedBnLogOut()
{
	// TODO: 在此添加控件通知处理程序代码
}

void CDialogLogin::OnBnClickedButton1()
{
	CFileDialog fd(TRUE, NULL, NULL, OFN_FILEMUSTEXIST | OFN_PATHMUSTEXIST | OFN_READONLY, "支持的图像文件 (*.xlsx;...)|*.xlsx|Office Excel(*.xlsx)|*.xlsx|所有文件(*.*)|*.*||", NULL);
	fd.m_ofn.nFilterIndex = 1;
	fd.m_ofn.hwndOwner = NULL;
	fd.m_ofn.lStructSize = sizeof(OPENFILENAME);
	fd.m_ofn.lpstrTitle= "打开Excel文件...";
	fd.m_ofn.nMaxFile = MAX_PATH;

	if (fd.DoModal() != IDOK) {
		return;
	}

	CString strFile = fd.GetPathName();

	CExcelEx erw;

	if (!erw.CreateInstance()) {
		AfxMessageBox("创建EXCEL示例失败");
		return;
	}

	if (!erw.IsVersionOk()) {
		AfxMessageBox("Office版本非2010版");
		return;
	}

	erw.SetVisible(false);

	if (!erw.OpenExcel(strFile, false)) {
		AfxMessageBox("打开EXCEL文件失败");
		return;
	}

	if (!erw.OpenSheet("Sheet1", false)) {
		AfxMessageBox("打开Sheet1信息失败");
		return;
	}

	g_vUser.clear();

	bool bFlag = false;

	for (int i=0; ; i++)
	{
		USER user;

		bFlag = erw.GetValue(i + 1, 0, user.sName);
		if (!bFlag || user.sName.IsEmpty()) {
			break;
		}

		bFlag = erw.GetValue(i + 1, 1, user.sKey);
		if (!bFlag) {
			break;
		}

		bFlag = erw.GetValue(i + 1, 2, user.nLevel);
		if (!bFlag) {
			break;
		}

		g_vUser.push_back(user);

		m_comboUserName.AddString(user.sName);
	}

	if (m_comboUserName.GetCount() > 0) {
		m_comboUserName.SetCurSel(0);
		m_comboLevel.SetCurSel(3 - g_vUser[0].nLevel);
	}
}

void CDialogLogin::OnBnClickedButton2()
{
	// TODO: 在此添加控件通知处理程序代码
}
