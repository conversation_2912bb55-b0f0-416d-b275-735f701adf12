# LogicRobot - 机器人控制线程分析

## 概述
LogicRobot是FStation系统中最复杂、最关键的业务线程，代码量达179KB，包含66个主要状态和多个子状态。它负责整个生产流程的核心控制，包括机器人运动、视觉识别、产品装配、质量检测等关键功能。

## 类结构分析

### 继承关系
```cpp
class CLogicRobot : public CThreadBase
```

### 核心数据结构

#### 1. 头部处理结果结构
```cpp
typedef struct _HEAD_PROC_RESULT_
{
    bool    bHeadPickOk;        // 头部抓取成功标志
    bool    bHeadThrowFlag;     // 头部丢弃标志
    bool    bMesNgFlag;         // MES系统NG标志
    bool    bHeadProcOk;        // 头部处理完成标志
    int     nProcTimes;         // 处理次数
    bool    bFromTray;          // 是否来自托盘
    
    // 像素坐标
    double  nHeadPixelX;
    double  nHeadPixelY;
    double  nHeadPixelR;
    
    // 机器坐标
    double  nHeadMachineX;
    double  nHeadMachineY;
    double  nHeadMachineR;
    
    CString sCode2D;            // 二维码信息
} HEADPROCRESULT;
```

#### 2. 托盘主板信息结构
```cpp
typedef struct _TRAY_MAIN_BOARD_INFO_
{
    double  nPixelX, nPixelY, nPixelR;      // 像素坐标
    bool    bExistFlag;                     // 存在标志
    bool    bProcOk;                        // 处理完成标志
    int     nProcTimes;                     // 处理次数
    bool    bMesNgFlag;                     // MES NG标志
    bool    bBadCheckNgFlag;                // 坏品检查NG标志
    bool    bHasMaterialFlag;               // 有物料标志
    CString sCode2D;                        // 二维码
} TRAYMAINBOARDINFO;
```

#### 3. 标记信息结构
```cpp
typedef struct _MARK_INFO_
{
    bool    bAssembleOk;                    // 装配完成标志
    bool    bMarkProcOk;                    // 标记处理完成
    bool    bLookBackProcOk;                // 回视处理完成
    int     nProcTimes;                     // 处理次数
    
    // 标记像素坐标
    double  nMarkPixelX, nMarkPixelY, nMarkPixelR;
    
    // 标记机器坐标
    double  nMarkMachineX, nMarkMachineY, nMarkMachineR;
    
    // 实际坐标和旋转
    double  nRotateR;
    double  nRealMachineX, nRealMachineY, nRealMachineR;
    double  nLookBackMachineX, nLookBackMachineY, nLookBackMachineR;
    
    bool    bCalcFeedFlag;                  // 计算进给标志
    bool    bFromTray;                      // 来自托盘标志
} MARKINFO;
```

### 核心成员变量
```cpp
private:
    CRobot*                 m_pRobot;           // 机器人控制对象
    CString                 m_sRet;             // 返回结果字符串
    
    // 状态管理映射表
    map<CString, DWORD>     m_mapTick;          // 时间计数
    map<CString, int>       m_mapIndex;         // 索引映射
    map<CString, bool>      m_mapFlag;          // 标志映射
    
    // 机器人位置信息
    ROBOTPOINT              m_stDstRobPnt;      // 目标机器人点位
    ROBOTPOINT              m_stLastDstRobPnt;  // 上次目标点位
    
    // 业务数据数组
    HEADPROCRESULT          m_stHeadProcResult[4];      // 头部处理结果数组
    TRAYMAINBOARDINFO       m_stTrayMainBoardInfo[64];  // 托盘主板信息数组
    MARKINFO                m_stMarkInfo[4];            // 标记信息数组
    
    // 托盘和传送带数据
    PTRAYMAINBOARDINFO      m_pMainBoardInTray;             // 托盘主板信息（动态分配）
    INMAINBOARDINFO         m_stInMainBoardInBelt[3];       // 传送带主板信息
    
    // 控制标志
    bool                    m_bTrayCameraCaptureFinishFlag; // 托盘相机拍照完成标志
    bool                    m_bExcuteOnce;                  // 单次执行标志
    
    // 统计信息
    int                     m_nHeadBackCnt;                 // 头部回退计数
    int                     m_nTrayExistSum;                // 托盘存在总数
    int                     m_nPickSum;                     // 取料总数
    
    // 治具代码
    CString                 m_sFixtureCodeA;                // 治具A代码
    CString                 m_sFixtureCodeB;                // 治具B代码
```

## 状态机设计

### 主状态机概览
LogicRobot包含66个主状态（OnRobot00 ~ OnRobot66），每个状态可能包含多个子状态。

#### 状态编号规律
```cpp
// 主状态
CStatus OnRobot00();    // 初始化状态
CStatus OnRobot01();    // 准备状态
...
CStatus OnRobot66();    // 结束状态

// 子状态示例
CStatus OnRobot00_0();  // 00状态的子状态0
CStatus OnRobot00_1();  // 00状态的子状态1
CStatus OnRobot00_2();  // 00状态的子状态2
```

### 典型状态流程分析

#### 1. 初始化阶段 (Robot00-05)
- **Robot00**: 系统初始化和自检
- **Robot01**: 机器人归位
- **Robot02**: 参数加载和校验
- **Robot03**: 视觉系统初始化
- **Robot04**: 工具初始化
- **Robot05**: 准备进入工作循环

#### 2. 视觉识别阶段 (Robot06-15)
- **Robot06**: 启动相机，准备视觉识别
- **Robot07**: 图像采集
- **Robot08**: 图像处理和分析
- **Robot09**: 坐标计算
- **Robot10**: 识别结果验证

#### 3. 抓取阶段 (Robot16-25)
- **Robot16**: 移动到抓取预备位置
- **Robot17**: 精确定位
- **Robot18**: 下降到抓取位置
- **Robot19**: 执行抓取动作
- **Robot20**: 抓取结果检查

#### 4. 装配阶段 (Robot26-45)
- **Robot26**: 移动到装配位置
- **Robot27**: 装配前检查
- **Robot28**: 精确对位
- **Robot29**: 执行装配动作
- **Robot30**: 装配质量检查

## 关键业务流程

### 1. 视觉处理流程

#### 坐标转换逻辑
```cpp
// 像素坐标到机器坐标的转换
nMachineX = CalculateMachineX(nPixelX, nPixelY);
nMachineY = CalculateMachineY(nPixelX, nPixelY);
nMachineR = CalculateMachineR(nPixelR);
```

#### 视觉识别流程
1. 触发相机拍照
2. 等待图像处理完成
3. 获取识别结果
4. 坐标转换和校准
5. 结果验证和筛选

### 2. 机器人运动控制

#### 点位管理
```cpp
// 设置目标点位
m_stDstRobPnt.x = nTargetX;
m_stDstRobPnt.y = nTargetY;
m_stDstRobPnt.z = nTargetZ;
m_stDstRobPnt.rx = nTargetRX;
```

#### 运动执行
```cpp
// 执行机器人运动
RUN_STOP_IF_ERROR(m_pRobot->MoveTo(m_stDstRobPnt));

// 等待运动完成
while (!m_pRobot->IsMoveDone()) {
    Sleep(1);
}
```

### 3. 质量检测流程

#### MES系统集成
```cpp
// 向MES系统报告处理结果
if (bProcessOK) {
    MESReport(sProductCode, "OK");
} else {
    MESReport(sProductCode, "NG");
    m_stHeadProcResult[nIndex].bMesNgFlag = true;
}
```

#### 多重质量检查
1. **视觉检查**: 通过图像识别检查产品外观
2. **尺寸检查**: 检查产品尺寸是否符合规格
3. **装配检查**: 检查装配质量和完整性
4. **MES检查**: 与MES系统集成，记录产品质量信息

## 并发处理机制

### 1. 多头并行处理
```cpp
// 4个头部同时处理
for (int i = 0; i < 4; i++) {
    if (m_stHeadProcResult[i].bNeedProcess) {
        ProcessHead(i);  // 并行处理每个头部
    }
}
```

### 2. 流水线处理
- **预处理阶段**: 准备下一个产品的处理
- **主处理阶段**: 当前产品的主要操作
- **后处理阶段**: 完成品的质量检查和输出

### 3. 异步等待机制
```cpp
// 异步等待机器人运动完成
if (!m_pRobot->IsMoveDone()) {
    RETURN_SELF("等待机器人运动完成", false);
}

// 异步等待视觉处理完成
if (!IsVisionComplete()) {
    RETURN_SELF("等待视觉处理完成", false);
}
```

## 异常处理机制

### 1. 分级异常处理
```cpp
// 错误级别异常 - 立即停止
if (nErrorLevel == ERROR_CRITICAL) {
    REPORT("严重错误：" + sErrorMsg, emLogLevelError);
    RETURN_STOP();
}

// 警告级别异常 - 暂停等待
if (nErrorLevel == ERROR_WARNING) {
    REPORT("警告：" + sErrorMsg, emLogLevelWarn);
    RETURN_PAUSE("等待人工处理");
}
```

### 2. 重试机制
```cpp
// 操作重试逻辑
m_stHeadProcResult[nIndex].nProcTimes++;
if (m_stHeadProcResult[nIndex].nProcTimes > MAX_RETRY_TIMES) {
    // 超过最大重试次数，标记为NG
    m_stHeadProcResult[nIndex].bMesNgFlag = true;
} else {
    // 重新尝试处理
    RETURN_STATE(&CLogicRobot::OnRobot16, true);
}
```

### 3. 自动恢复机制
```cpp
// 自动恢复到安全位置
if (bEmergencyStop) {
    m_pRobot->MoveToSafePosition();
    ResetAllFlags();
    RETURN_STATE(&CLogicRobot::OnRobot00, true);
}
```

## 数据管理

### 1. 数组索引管理
```cpp
// 通过映射表管理数组索引
m_mapIndex["当前处理头部"] = nCurrentHead;
m_mapIndex["当前托盘位置"] = nCurrentTrayPos;
m_mapIndex["当前标记位置"] = nCurrentMarkPos;
```

### 2. 状态标志管理
```cpp
// 关键状态标志
m_mapFlag["视觉识别完成"] = bVisionComplete;
m_mapFlag["抓取动作完成"] = bPickComplete;
m_mapFlag["装配动作完成"] = bAssembleComplete;
m_mapFlag["质量检查完成"] = bQualityCheckComplete;
```

### 3. 时间管理
```cpp
// 关键时间点记录
m_mapTick["视觉处理开始时间"] = GetTickCount();
m_mapTick["机器人运动开始时间"] = GetTickCount();
m_mapTick["质量检查开始时间"] = GetTickCount();
```

## 与其他线程的交互

### 1. 与LogicTray的交互
- 从托盘获取产品信息
- 向托盘报告处理结果
- 协调托盘的进出料时序

### 2. 与LogicFixture的交互
- 协调治具的装载和卸载
- 同步治具状态和机器人状态
- 处理治具异常情况

### 3. 与LogicMachine的交互
- 接收安全状态信息
- 响应安全停机指令
- 报告机器人状态信息

## 性能优化点

### 1. 并行处理优化
- 多头并行操作减少单次处理时间
- 流水线作业提高整体效率
- 异步等待避免阻塞

### 2. 视觉处理优化
- 预处理图像减少识别时间
- 缓存识别结果避免重复计算
- 多线程视觉处理

### 3. 运动优化
- 路径规划避免不必要的运动
- 运动插补提高运动平滑性
- 预测性定位减少定位时间

## 维护建议

### 1. 状态机维护
- 定期检查状态转换逻辑
- 优化状态机流程，去除冗余状态
- 添加状态监控和调试功能

### 2. 数据结构优化
- 定期清理不必要的数据结构
- 优化数组大小和内存使用
- 添加数据有效性检查

### 3. 性能监控
- 监控关键操作的执行时间
- 分析性能瓶颈并优化
- 记录性能统计数据

## 总结

LogicRobot作为FStation系统的核心业务线程，通过复杂的状态机设计、完善的数据管理和强大的异常处理机制，实现了高精度、高效率的工业自动化生产控制。其设计体现了工控软件的复杂性和专业性，是整个生产系统的核心控制单元。

理解和维护这个线程需要深入的工控知识和丰富的调试经验，建议在修改时要充分测试，确保系统的稳定性和可靠性。 