﻿// MainDialog.cpp : 实现文件
//

#include "stdafx.h"
#include "FStation.h"
#include "MainDialog.h"
#include "afxdialogex.h"

// CMainDialog 对话框

#include "LogicMgr.h"
#include "Sys.h"
#include "Pro.h"

IMPLEMENT_DYNAMIC(CMainDialog, CDialogEx)

CMainDialog::CMainDialog(CPoint pt, CWnd* pParent /*=NULL*/)
: CDialogEx(CMainDialog::IDD, pParent)
{
	m_pt = pt;
}

CMainDialog::~CMainDialog()
{
}

void CMainDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_BUTTON1, m_btnStart);
	DDX_Control(pDX, IDC_BUTTON2, m_btnPause);
	DDX_Control(pDX, IDC_BUTTON3, m_btnStop);
	DDX_Control(pDX, IDC_EDIT1, m_editStatus);
	DDX_Control(pDX, IDC_LIST1, m_listLog);
	DDX_Control(pDX, IDC_LIST2, m_listDat);
}

BEGIN_MESSAGE_MAP(CMainDialog, CDialogEx)
	ON_WM_TIMER()
	ON_WM_PAINT()
	ON_MESSAGE(UM_UPDATE_PRO, &CMainDialog::OnUmUpdatePro)
	ON_MESSAGE(UM_UPDATE_LOG, &CMainDialog::OnUmUpdateLog)
	ON_MESSAGE(UM_AXIS_OFF, &CMainDialog::OnUmAxisOff)
	ON_MESSAGE(UM_TIME_OUT, &CMainDialog::OnUmTimeOut)
END_MESSAGE_MAP()

// CMainDialog 消息处理程序

BOOL CMainDialog::PreTranslateMessage(MSG* pMsg)
{
	if ((WM_SYSKEYDOWN == pMsg->message && VK_F4 == pMsg->wParam) || (pMsg->message == WM_KEYDOWN && pMsg->wParam == VK_ESCAPE)) { //屏蔽Alt + F4
		return TRUE;
	}
	else {
		return CDialogEx::PreTranslateMessage(pMsg);
	}
}

BOOL CMainDialog::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	CRect rect;
	GetWindowRect(&rect);
	MoveWindow(m_pt.x, m_pt.y, rect.Width(), rect.Height());

	g_hMainWnd = GetSafeHwnd();

	SetTimer(emTimerDelay, 10, NULL);

	return TRUE;
}

void CMainDialog::OnPaint()
{
	if (!IsIconic()) {
		CPaintDC dc(this);

		CRect rect;

		GetDlgItem(IDC_STATIC4)->GetWindowRect(&rect);
		ScreenToClient(&rect);
		rect.top += 7;
		dc.FillSolidRect(rect, m_BkColor);
				
		CDialogEx::OnPaint();
	}
}

void CMainDialog::OnTimer(UINT_PTR nIDEvent)
{
	if (nIDEvent == emTimerDelay) {
		KillTimer(nIDEvent);

		CRect rcWnd;
		GetDlgItem(IDC_STATIC1)->GetWindowRect(&rcWnd);
		ScreenToClient(rcWnd);
		rcWnd.bottom += m_pt.y;
		rcWnd.top += m_pt.y;
		rcWnd.right += m_pt.x;
		rcWnd.left += m_pt.x;
		g_pImageWndUp->AttatchWindow(rcWnd);

		GetDlgItem(IDC_STATIC2)->GetWindowRect(&rcWnd);
		ScreenToClient(rcWnd);
		rcWnd.bottom += m_pt.y;
		rcWnd.top += m_pt.y;
		rcWnd.right += m_pt.x;
		rcWnd.left += m_pt.x;
		g_pImageWndDn->AttatchWindow(rcWnd);
		g_pImageWndDn->SetDrawWnd(g_hMainWnd);

		GetDlgItem(IDC_STATIC3)->GetWindowRect(&rcWnd);
		ScreenToClient(rcWnd);
		rcWnd.bottom += m_pt.y;
		rcWnd.top += m_pt.y;
		rcWnd.right += m_pt.x;
		rcWnd.left += m_pt.x;
		g_pImageWndTray->AttatchWindow(rcWnd);

		GetDlgItem(IDC_STATIC5)->GetWindowRect(&rcWnd);
		ScreenToClient(rcWnd);
		rcWnd.bottom += m_pt.y;
		rcWnd.top += m_pt.y;
		rcWnd.right += m_pt.x;
		rcWnd.left += m_pt.x;
		CLogicMgr::m_pThreadMonitor->AttatchWindow(rcWnd, g_hMainWnd, UM_TIME_OUT);

		SetTimer(emTimerStatus, 1000, NULL);

		SetTimer(emTimerUpdateRight, 1000, NULL);

		SetTimer(emTimerEverySencond, 1000, NULL);
	}

	CDialogEx::OnTimer(nIDEvent);
}

void CMainDialog::UpdateRunStatus()
{
	if (CSys::m_bInit) {
		if (CLogicMgr::isPause()) {
			CSys::m_emRunStatus = emRunStatusSuspending;
		}
		else if (CLogicMgr::isRunning()) {
			CSys::m_emRunStatus = emRunStatusRunning;
		}
		else {
			CSys::m_emRunStatus = emRunStatusIdle;
		}
	}
	else {
// 		if (CLogicMgr::m_mapThread["Reset"].pThread->GetStatus() == /*EnumStatus::*/emRun) {
// 			CSys::m_emRunStatus = emRunStatusReset;
// 		}
// 		else if (CLogicMgr::m_mapThread["Reset"].pThread->GetStatus() == /*EnumStatus::*/emPause) {
// 			CSys::m_emRunStatus = emRunStatusSuspending;
// 		}
// 		else {
// 			CSys::m_emRunStatus = emRunStatusPreReset;
// 		}
	}

	static EnumRunStatus emLastStatus = emRunStatusSum;

	if (emLastStatus != CSys::m_emRunStatus) {
		emLastStatus = CSys::m_emRunStatus;
	}
}
