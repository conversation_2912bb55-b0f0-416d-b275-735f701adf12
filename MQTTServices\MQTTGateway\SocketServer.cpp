#include "SocketServer.h"
#include <QDebug>
#include <QJsonParseError>
#include <thread>
#include <chrono>

Q_LOGGING_CATEGORY(socketServer, "SocketServer")

SocketServer::SocketServer(int port)
    : m_port(port), m_listenSocket(INVALID_SOCKET),
      m_clientSocket(INVALID_SOCKET), m_running{false} {
#ifdef _WIN32
    WSADATA wsaData;
    WSAStartup(MAKEWORD(2, 2), &wsaData);
#endif
}

SocketServer::~SocketServer() {
    Stop();
#ifdef _WIN32
    WSACleanup();
#endif
}

bool SocketServer::Initialize(int port) {
    m_port = port;
    
    m_listenSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (m_listenSocket == INVALID_SOCKET) {
        qCCritical(socketServer) << "创建监听socket失败";
        return false;
    }

    sockaddr_in addr;
    addr.sin_family = AF_INET;
    addr.sin_addr.s_addr = INADDR_ANY;
    addr.sin_port = htons(static_cast<u_short>(m_port));

    if (::bind(m_listenSocket, (sockaddr*)&addr, sizeof(addr)) == SOCKET_ERROR) {
        qCCritical(socketServer) << "绑定端口" << m_port << "失败";
        return false;
    }

    if (listen(m_listenSocket, 1) == SOCKET_ERROR) {
        qCCritical(socketServer) << "开始监听失败";
        return false;
    }

    qCInfo(socketServer) << "Socket服务器在端口" << m_port << "上启动成功";
    return true;
}

void SocketServer::Run() {
    m_running = true;

    while (m_running) {
        if (m_clientSocket == INVALID_SOCKET) {
            WaitForClient();
        }

        if (m_clientSocket != INVALID_SOCKET) {
            HandleClient();
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

void SocketServer::Stop() {
    m_running = false;
    if (m_clientSocket != INVALID_SOCKET) {
        closesocket(m_clientSocket);
        m_clientSocket = INVALID_SOCKET;
    }
    if (m_listenSocket != INVALID_SOCKET) {
        closesocket(m_listenSocket);
        m_listenSocket = INVALID_SOCKET;
    }
}

bool SocketServer::SendMessage(const SocketMessage& msg) {
    if (m_clientSocket == INVALID_SOCKET)
        return false;

    QJsonObject json;
    json["type"] = QString::fromStdString(msg.type);
    json["data"] = QString::fromStdString(msg.data);
    json["timestamp"] = QString::fromStdString(msg.timestamp);
    
    if (!msg.requestId.empty()) {
        json["requestId"] = QString::fromStdString(msg.requestId);
    }

    QJsonDocument doc(json);
    QByteArray jsonData = doc.toJson(QJsonDocument::Compact);
    
    // 添加消息长度前缀
    uint32_t messageLength = static_cast<uint32_t>(jsonData.size());
    QByteArray packet;
    packet.append(reinterpret_cast<const char*>(&messageLength), sizeof(messageLength));
    packet.append(jsonData);

    int result = send(m_clientSocket, packet.constData(), packet.size(), 0);
    return result != SOCKET_ERROR;
}

bool SocketServer::ReceiveMessage(SocketMessage& msg) {
    if (m_clientSocket == INVALID_SOCKET)
        return false;

    // 首先接收消息长度
    uint32_t messageLength = 0;
    int received = recv(m_clientSocket, reinterpret_cast<char*>(&messageLength), sizeof(messageLength), 0);
    
    if (received <= 0) {
        return false;
    }

    // 接收消息内容
    QByteArray buffer;
    buffer.resize(messageLength);
    
    uint32_t totalReceived = 0;
    while (totalReceived < messageLength) {
        int toReceive = messageLength - totalReceived;
        received = recv(m_clientSocket, buffer.data() + totalReceived, toReceive, 0);
        
        if (received <= 0) {
            return false;
        }
        
        totalReceived += received;
    }

    std::string receivedData = buffer.toStdString();
    return ParseMessage(receivedData, msg);
}

void SocketServer::WaitForClient() {
    fd_set readSet;
    FD_ZERO(&readSet);
    FD_SET(m_listenSocket, &readSet);

    timeval timeout = {1, 0}; // 1秒超时
    int result = select(0, &readSet, nullptr, nullptr, &timeout);

    if (result > 0 && FD_ISSET(m_listenSocket, &readSet)) {
        m_clientSocket = accept(m_listenSocket, nullptr, nullptr);
        if (m_clientSocket != INVALID_SOCKET) {
            qCInfo(socketServer) << "客户端连接成功";
        }
    }
}

void SocketServer::HandleClient() {
    // 检查客户端是否还连接
    fd_set readSet;
    FD_ZERO(&readSet);
    FD_SET(m_clientSocket, &readSet);

    timeval timeout = {0, 0}; // 非阻塞检查
    int result = select(0, &readSet, nullptr, nullptr, &timeout);

    if (result == SOCKET_ERROR) {
        qCWarning(socketServer) << "客户端连接断开";
        closesocket(m_clientSocket);
        m_clientSocket = INVALID_SOCKET;
    }
}

bool SocketServer::ParseMessage(const std::string& rawData, SocketMessage& msg) {
    try {
        QString jsonStr = QString::fromUtf8(rawData.c_str());
        
        QJsonParseError error;
        QJsonDocument doc = QJsonDocument::fromJson(jsonStr.toUtf8(), &error);

        if (error.error != QJsonParseError::NoError) {
            qCWarning(socketServer) << "JSON解析失败:" << error.errorString();
            return false;
        }

        if (!doc.isObject()) {
            qCWarning(socketServer) << "JSON不是对象格式";
            return false;
        }

        QJsonObject json = doc.object();

        msg.type = json["type"].toString().toStdString();

        // 处理data字段 - 可能是字符串或JSON对象
        if (json["data"].isString()) {
            msg.data = json["data"].toString().toStdString();
        } else if (json["data"].isObject() || json["data"].isArray()) {
            // 将JSON对象/数组转换为字符串
            QJsonDocument dataDoc(json["data"].toObject());
            msg.data = dataDoc.toJson(QJsonDocument::Compact).toStdString();
        } else {
            msg.data = "";
        }

        msg.timestamp = json["timestamp"].toString().toStdString();
        
        if (json.contains("requestId")) {
            msg.requestId = json["requestId"].toString().toStdString();
        }

        return true;
        
    } catch (const std::exception& e) {
        qCCritical(socketServer) << "解析消息异常:" << e.what();
        return false;
    }
}

bool SocketServer::IsConnected() const {
    return m_clientSocket != INVALID_SOCKET;
}
