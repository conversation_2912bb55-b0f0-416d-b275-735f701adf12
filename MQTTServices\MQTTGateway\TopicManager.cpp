#include "TopicManager.h"
#include <QDebug>
#include <algorithm>

TopicManager::TopicManager() {
}

bool TopicManager::Initialize(const SimpleConfigManager& configManager) {
    // 复制配置
    m_subscriptions = configManager.GetSubscriptions();
    m_publishTopics = configManager.GetPublishTopics();
    
    // 设置设备ID
    m_deviceId = configManager.GetGatewayConfig().device.deviceId;
    
    // 初始化Topic模式
    InitializeTopicPatterns();
    
    qInfo() << "TopicManager初始化成功 - DeviceId:" << QString::fromStdString(m_deviceId);
    return true;
}

void TopicManager::SetDeviceId(const std::string& deviceId) {
    m_deviceId = deviceId;
    // 重新初始化Topic模式
    InitializeTopicPatterns();
}

std::vector<std::string> TopicManager::GetSubscriptionTopics() const {
    std::vector<std::string> topics;
    
    for (const auto& subscription : m_subscriptions) {
        if (subscription.enabled) {
            std::string topic = subscription.topicTemplate;
            // 替换deviceId
            topic = std::regex_replace(topic, std::regex("\\{deviceId\\}"), m_deviceId);
            topics.push_back(topic);
        }
    }
    
    return topics;
}

int TopicManager::GetSubscriptionQoS(const std::string& topicPattern) const {
    for (const auto& subscription : m_subscriptions) {
        std::string pattern = subscription.topicTemplate;
        pattern = std::regex_replace(pattern, std::regex("\\{deviceId\\}"), m_deviceId);
        
        if (pattern == topicPattern) {
            return subscription.qos;
        }
    }
    return 1; // 默认QoS
}

std::string TopicManager::GetMessageHandler(const std::string& topic) const {
    return DetermineMessageHandler(topic);
}

bool TopicManager::IsSubscriptionEnabled(const std::string& name) const {
    auto it = std::find_if(m_subscriptions.begin(), m_subscriptions.end(),
                          [&name](const SubscriptionConfig& config) {
                              return config.name == name;
                          });
    return (it != m_subscriptions.end()) ? it->enabled : false;
}

std::string TopicManager::BuildPublishTopic(const std::string& topicName, 
                                           const std::map<std::string, std::string>& params) const {
    auto it = std::find_if(m_publishTopics.begin(), m_publishTopics.end(),
                          [&topicName](const PublishTopicConfig& config) {
                              return config.name == topicName;
                          });
    
    if (it == m_publishTopics.end()) {
        qWarning() << "未找到发布主题配置:" << QString::fromStdString(topicName);
        return "";
    }
    
    return ReplaceTopicPlaceholders(it->topicTemplate, params);
}

int TopicManager::GetPublishQoS(const std::string& topicName) const {
    auto it = std::find_if(m_publishTopics.begin(), m_publishTopics.end(),
                          [&topicName](const PublishTopicConfig& config) {
                              return config.name == topicName;
                          });
    return (it != m_publishTopics.end()) ? it->qos : 1;
}

bool TopicManager::GetRetainFlag(const std::string& topicName) const {
    auto it = std::find_if(m_publishTopics.begin(), m_publishTopics.end(),
                          [&topicName](const PublishTopicConfig& config) {
                              return config.name == topicName;
                          });
    return (it != m_publishTopics.end()) ? it->retain : false;
}

std::string TopicManager::GetMessageType(const std::string& topicName) const {
    auto it = std::find_if(m_publishTopics.begin(), m_publishTopics.end(),
                          [&topicName](const PublishTopicConfig& config) {
                              return config.name == topicName;
                          });
    return (it != m_publishTopics.end()) ? it->messageType : "";
}

bool TopicManager::IsTopicMatch(const std::string& topic, const std::string& pattern) const {
    auto it = m_topicPatterns.find(pattern);
    if (it != m_topicPatterns.end()) {
        return std::regex_match(topic, it->second);
    }
    
    // 如果没有预编译的正则表达式，临时创建一个
    std::string regexPattern = ConvertTopicPatternToRegex(pattern);
    std::regex regex(regexPattern);
    return std::regex_match(topic, regex);
}

std::string TopicManager::ExtractRequestId(const std::string& topic) const {
    // 从Topic中提取request_id
    std::regex requestIdRegex("request_id=([^/]+)");
    std::smatch match;

    if (std::regex_search(topic, match, requestIdRegex)) {
        return match[1].str();
    }

    return "";
}

std::string TopicManager::DetermineMessageHandler(const std::string& topic) const {
    // 根据Topic确定消息处理器
    for (const auto& subscription : m_subscriptions) {
        std::string pattern = subscription.topicTemplate;

        // 使用字符串替换避免正则表达式问题
        std::string deviceIdPlaceholder = "{deviceId}";
        size_t pos = 0;
        while ((pos = pattern.find(deviceIdPlaceholder, pos)) != std::string::npos) {
            pattern.replace(pos, deviceIdPlaceholder.length(), m_deviceId);
            pos += m_deviceId.length();
        }

        if (IsTopicMatch(topic, pattern)) {
            return subscription.handler;
        }
    }

    return "UnknownHandler";
}

std::string TopicManager::ReplaceTopicPlaceholders(const std::string& topicTemplate,
                                                  const std::map<std::string, std::string>& params) const {
    std::string topic = topicTemplate;

    // 替换deviceId - 使用字符串替换避免正则表达式问题
    std::string deviceIdPlaceholder = "{deviceId}";
    size_t pos = 0;
    while ((pos = topic.find(deviceIdPlaceholder, pos)) != std::string::npos) {
        topic.replace(pos, deviceIdPlaceholder.length(), m_deviceId);
        pos += m_deviceId.length();
    }

    // 替换其他参数 - 使用字符串替换避免正则表达式问题
    for (const auto& param : params) {
        std::string placeholder = "{" + param.first + "}";
        pos = 0;
        while ((pos = topic.find(placeholder, pos)) != std::string::npos) {
            topic.replace(pos, placeholder.length(), param.second);
            pos += param.second.length();
        }
    }

    return topic;
}

bool TopicManager::ValidateTopicFormat(const std::string& topic) const {
    // 基本的Topic格式验证
    if (topic.empty()) {
        return false;
    }

    // 检查是否包含非法字符
    if (topic.find('#') != std::string::npos && topic.back() != '#') {
        return false; // # 只能在末尾
    }

    return true;
}

void TopicManager::InitializeTopicPatterns() {
    m_topicPatterns.clear();

    // 为每个订阅主题预编译正则表达式
    for (const auto& subscription : m_subscriptions) {
        std::string pattern = subscription.topicTemplate;
        pattern = std::regex_replace(pattern, std::regex("\\{deviceId\\}"), m_deviceId);

        std::string regexPattern = ConvertTopicPatternToRegex(pattern);
        try {
            m_topicPatterns[pattern] = std::regex(regexPattern);
        } catch (const std::regex_error& e) {
            qWarning() << "正则表达式编译失败:" << QString::fromStdString(pattern)
                      << "错误:" << e.what();
        }
    }
}

std::string TopicManager::ConvertTopicPatternToRegex(const std::string& mqttPattern) const {
    std::string regexPattern = mqttPattern;

    // 使用字符串替换来转义特殊字符，避免正则表达式语法错误
    // 替换 $ 为 \$
    size_t pos = 0;
    while ((pos = regexPattern.find("$", pos)) != std::string::npos) {
        regexPattern.replace(pos, 1, "\\$");
        pos += 2;
    }

    // 替换 . 为 \.
    pos = 0;
    while ((pos = regexPattern.find(".", pos)) != std::string::npos) {
        regexPattern.replace(pos, 1, "\\.");
        pos += 2;
    }

    // 替换 ( 为 \(
    pos = 0;
    while ((pos = regexPattern.find("(", pos)) != std::string::npos) {
        regexPattern.replace(pos, 1, "\\(");
        pos += 2;
    }

    // 替换 ) 为 \)
    pos = 0;
    while ((pos = regexPattern.find(")", pos)) != std::string::npos) {
        regexPattern.replace(pos, 1, "\\)");
        pos += 2;
    }

    // 转换MQTT通配符为正则表达式
    regexPattern = std::regex_replace(regexPattern, std::regex("\\+"), "[^/]+");  // + 匹配单层
    regexPattern = std::regex_replace(regexPattern, std::regex("#"), ".*");       // # 匹配多层

    return regexPattern;
}
