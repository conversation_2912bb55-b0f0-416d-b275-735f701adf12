﻿#pragma once

#include "ColorList.h"
using namespace yzBase;

// CDialogProduct 对话框

class CDialogProduct : public CDialogEx
{
	DECLARE_DYNAMIC(CDialogProduct)

public:
	CDialogProduct(CWnd* pParent = NULL);   // 标准构造函数
	virtual ~CDialogProduct();

// 对话框数据
	enum { IDD = IDD_DIALOG_PRODUCT };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持
	virtual BOOL OnInitDialog();
	virtual void OnOK() {}

	DECLARE_MESSAGE_MAP()
	afx_msg void OnNMClickList1(NMHDR *pNMHDR, LRESULT *pResult);
	afx_msg void OnBnClickedButton1();
	afx_msg void OnBnClickedButton2();
	afx_msg void OnBnClickedButton3();
	afx_msg void OnBnClickedButton4();
	afx_msg void OnBnClickedButton5();

private:
	CColorList m_list1;
	CImageList m_imageList;

	CString	   m_sPro;
	int		   m_nRowIndex;
};
