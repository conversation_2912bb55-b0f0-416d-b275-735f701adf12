﻿// DialogMachine.cpp : 实现文件
//

#include "stdafx.h"
#include "FStation.h"
#include "DialogMachine.h"
#include "afxdialogex.h"

#include "Mes.h"
#include "Sys.h"
#include "SimpleSocketInterface.h"

#include "LogicMgr.h"

// CDialogMachine 对话框

IMPLEMENT_DYNAMIC(CDialogMachine, CDialogEx)

CDialogMachine::CDialogMachine(CPoint pt, CWnd* pParent /*=NULL*/)
	: CDialogEx(CDialogMachine::IDD, pParent)
{
	m_pt = pt;
}

CDialogMachine::~CDialogMachine()
{
}

void CDialogMachine::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_BUTTON1, m_btnReset);
	DDX_Control(pDX, IDC_BUTTON2, m_btnStart);
	DDX_Control(pDX, IDC_BUTTON3, m_btnPause);
	DDX_Control(pDX, IDC_BUTTON4, m_btnStop);
}

BEGIN_MESSAGE_MAP(CDialogMachine, CDialogEx)
	ON_BN_CLICKED(IDC_BUTTON1, &CDialogMachine::OnBnClickedButton1)
	ON_BN_CLICKED(IDC_BUTTON2, &CDialogMachine::OnBnClickedButton2)
	ON_BN_CLICKED(IDC_BUTTON3, &CDialogMachine::OnBnClickedButton3)
	ON_BN_CLICKED(IDC_BUTTON4, &CDialogMachine::OnBnClickedButton4)
	ON_BN_CLICKED(IDC_BUTTON10, &CDialogMachine::OnBnClickedButton10)
	ON_BN_CLICKED(IDC_BUTTON11, &CDialogMachine::OnBnClickedButton11)
	ON_BN_CLICKED(IDC_BUTTON12, &CDialogMachine::OnBnClickedButton12)
	ON_BN_CLICKED(IDC_BUTTON_SN_IN, &CDialogMachine::OnBnClickedButtonSnIn)
	ON_BN_CLICKED(IDC_BUTTON_SN_OUT, &CDialogMachine::OnBnClickedButtonSnOut)
	ON_BN_CLICKED(IDC_BUTTON_SN_OUT_REQ, &CDialogMachine::OnBnClickedButtonSnOutReq)	
	ON_BN_CLICKED(IDC_BUTTON_EVENT_BOP_DELIVER_COMPLETED, &CDialogMachine::OnBnClickedButtonEventBopDeliverCompleted)
	ON_BN_CLICKED(IDC_BUTTON_EVENT_PAUSE, &CDialogMachine::OnBnClickedButtonEventPause)
END_MESSAGE_MAP()

BOOL CDialogMachine::PreTranslateMessage( MSG* pMsg )
{
	if ((WM_SYSKEYDOWN == pMsg->message && VK_F4 == pMsg->wParam) || (pMsg->message == WM_KEYDOWN && pMsg->wParam == VK_ESCAPE)) { //屏蔽Alt + F4
		return TRUE;
	}

	return CDialogEx::PreTranslateMessage(pMsg);
}

BOOL CDialogMachine::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	CRect rc;

	GetWindowRect(&rc);

	MoveWindow(m_pt.x, m_pt.y, rc.Width(), rc.Height());

	CString strPath = CString(GetModulePath().c_str());
	strPath += "\\Sys\\images\\";

	CString strName[] = { 
		"resetbk.bmp", 
		"start.bmp", 
		"pause.bmp", 
		"stop.bmp"
	};

	CColorButton *pBtn[] = { 
		&m_btnReset, 
		&m_btnStart, 
		&m_btnPause, 
		&m_btnStop
	};

	for (int i=0; i<4; i++)
	{
		pBtn[i]->SetColor(RGB(240, 240, 240), RGB(0, 0, 0));

		pBtn[i]->SetImagePos(5, 0);
		pBtn[i]->SetIcon(strPath + strName[i], strPath + strName[i], false);

		pBtn[i]->SetFontSize(20, true);
		pBtn[i]->SetTextPos(68, 0);

	}

	SetDlgItemText(IDC_BUTTON10, VAR_MACHINE_B("MES功能启用") ? "设置\nMES功能-关" : "设置\nMES功能-开");

	SetDlgItemText(IDC_BUTTON11, VAR_MACHINE_B("弹片检测功能启用") ? "设置\n弹片检测功能-关" : "设置\n弹片检测功能-开");

	return TRUE;
}

// CDialogMachine 消息处理程序

void CDialogMachine::OnBnClickedButton1()
{
}

void CDialogMachine::OnBnClickedButton2()
{
	CLogicMgr::m_mapThread["Machine"].pThread->Start();
}

void CDialogMachine::OnBnClickedButton3()
{
	CLogicMgr::m_mapThread["Machine"].pThread->Pause();
}

void CDialogMachine::OnBnClickedButton4()
{
	CLogicMgr::m_mapThread["Machine"].pThread->Stop();
}


void CDialogMachine::OnBnClickedButton10()
{
	PREMISSION_CTRL();

	CString str;

	GetDlgItemText(IDC_BUTTON10, str);

	if (str.Find("开") >= 0) {
		VAR_MACHINE("MES功能启用") = true;
		SetDlgItemText(IDC_BUTTON10, "设置\nMES功能-关");
	}
	else {
		VAR_MACHINE("MES功能启用") = false;
		SetDlgItemText(IDC_BUTTON10, "设置\nMES功能-开");
	}

	g_pMachine->Save();
}

void CDialogMachine::OnBnClickedButton11()
{
	PREMISSION_CTRL();

	CString str;

	GetDlgItemText(IDC_BUTTON11, str);

	if (str.Find("开") >= 0) {
		VAR_MACHINE("弹片检测功能启用") = true;
		SetDlgItemText(IDC_BUTTON11, "设置\n弹片功能-关");
	}
	else {
		VAR_MACHINE("弹片检测功能启用") = false;
		SetDlgItemText(IDC_BUTTON11, "设置\n弹片功能-开");
	}

	g_pMachine->Save();
}


void CDialogMachine::OnBnClickedButton12()
{
	UpdateData();

	CString sCode2D;
	GetDlgItemText(IDC_EDIT1, sCode2D);

	CString sMesRes;
	CMes::HttpPostPassStation(VAR_MACHINE_S("MES过站服务器地址"), 80, sCode2D, VAR_MACHINE_S("MES过站拉线ID"), VAR_MACHINE_S("MES过站工序"), VAR_MACHINE_S("MES过站用户名"), VAR_MACHINE_S("MES过站密码"), VAR_MACHINE_S("MES过站工厂"), &sMesRes);

	AfxMessageBox(sMesRes);
}


void CDialogMachine::OnBnClickedButtonSnIn()
{
	CString sn;
	GetDlgItemText(IDC_EDIT_sn_in_out, sn);
	if (sn.IsEmpty())
	{
		sn = _T("SIM_SN_IN_2024102701"); // Default SN if empty
	}
	
	CString model = CSys::GetCurrentPro().c_str();
	REPORT(_T("模拟入站事件，SN: ") + sn, emLogLevelNormal);
	SimpleSocketInterface::SendSnInEvent(sn, model);
}


void CDialogMachine::OnBnClickedButtonSnOut()
{
	CString sn;
	GetDlgItemText(IDC_EDIT_sn_in_out, sn);
	if (sn.IsEmpty())
	{
		sn = _T("SIM_SN_OUT_2024102702"); // Default SN if empty
	}

	CString model = CSys::GetCurrentPro().c_str();
	REPORT(_T("模拟出站事件，SN: ") + sn, emLogLevelNormal);
	SimpleSocketInterface::SendSnOutEvent(sn, model);
}


void CDialogMachine::OnBnClickedButtonSnOutReq()
{
	CString sn;
	GetDlgItemText(IDC_EDIT_sn_in_out, sn);
	if (sn.IsEmpty())
	{
		sn = _T("SIM_SN_OUT_REQ_2024102703"); // Default SN if empty
	}

	CString model = CSys::GetCurrentPro().c_str();
	REPORT(_T("模拟出站询问事件，SN: ") + sn, emLogLevelNormal);
	SimpleSocketInterface::SendSnOutReqEvent(sn, model);
}



void CDialogMachine::OnBnClickedButtonEventBopDeliverCompleted()
{
	// TODO: 在此添加控件通知处理程序代码
	CString requestId = _T("BOP_2024102705");
	REPORT(_T("模拟转产完成事件，RequestId: ") + requestId, emLogLevelNormal);
	SimpleSocketInterface::SendBopDeliverCompletedEvent(requestId);
}


void CDialogMachine::OnBnClickedButtonEventPause()
{
	// TODO: 在此添加控件通知处理程序代码
	CString sn;
	GetDlgItemText(IDC_EDIT_sn_in_out, sn);
	if (sn.IsEmpty())
	{
		sn = _T("SIM_PAUSE_2024102704"); // Default SN if empty
	}

	CString model = CSys::GetCurrentPro().c_str();
	CString pauseMsg = _T("设备暂停生产");
	REPORT(_T("模拟暂停事件，SN: ") + sn, emLogLevelNormal);
	SimpleSocketInterface::SendPauseEvent(sn, pauseMsg, model);
}
