{"version": "1.0", "description": "SCADA协议事件配置 - 基于v1.13协议标准", "events": [{"type": "EVENT_SN_IN", "name": "入站事件", "description": "产品进入本工位", "qos": 2, "requires_response": true, "timeout_ms": 10000, "max_retries": 1, "properties": ["sn", "production_model", "profiles"], "requires_realtime_data": true}, {"type": "EVENT_SN_OUT", "name": "出站事件", "description": "产品完成本工位作业并离开", "qos": 2, "requires_response": true, "timeout_ms": 10000, "max_retries": 1, "properties": ["sn", "production_model", "profiles"], "requires_realtime_data": true}, {"type": "EVENT_SN_OUT_REQ", "name": "过站询问事件", "description": "在出站前向平台询问是否可以出站", "qos": 2, "requires_response": true, "timeout_ms": 10000, "max_retries": 1, "properties": ["sn", "production_model", "profiles"], "requires_realtime_data": true}, {"type": "EVENT_BOP_DELIVER_COMPLETED", "name": "转产完成事件", "description": "设备已成功应用平台下发的转产参数", "qos": 2, "requires_response": true, "timeout_ms": 10000, "max_retries": 1, "properties": [], "requires_realtime_data": true, "note": "request_id为平台下发指令时的request_id"}, {"type": "EVENT_PAUSE", "name": "暂停事件", "description": "设备进入暂停状态", "qos": 2, "requires_response": true, "timeout_ms": 10000, "max_retries": 1, "properties": ["sn", "pause_msg", "production_model", "profiles"], "requires_realtime_data": true}, {"type": "EVENT_FAULT", "name": "故障事件", "description": "设备发生故障", "qos": 2, "requires_response": true, "timeout_ms": 10000, "max_retries": 1, "properties": ["sn", "production_model", "profiles", "fault_code", "fault_type", "fault_msg"], "requires_realtime_data": true, "fault_types": ["warn", "error"]}]}