﻿#pragma once

#include "ThreadBase.h"
using namespace yzBase;

#include "Belt.h"

class CLogicBelt : public CThreadBase
{
public:
	CLogicBelt(CString sTrack);     // 构造函数：初始化皮带逻辑类，sTrack为轨道标识（A轨/B轨）
	virtual ~CLogicBelt();          // 析构函数：清理皮带逻辑类资源

public:
	// 基础控制函数
	EnumStatus OnSafeCheck();       // 安全检查：检查皮带系统安全状态
	EnumStatus OnStart();           // 启动控制：初始化皮带系统
	EnumStatus OnPause();           // 暂停控制：暂停皮带操作
	EnumStatus OnResume();          // 恢复控制：从暂停状态恢复皮带操作
	EnumStatus OnStop();            // 停止控制：停止皮带系统

public:
	// 主运行函数
	CStatus OnRun();                // 主状态机运行函数：协调皮带系统工作流程

	// ========== 自检流程 (OnSelfCheck00-OnSelfCheck03) ==========
	CStatus OnSelfCheck00();        // 自检准备：准备皮带系统自检
	CStatus OnSelfCheck01();        // 自检执行：执行皮带系统自检
	CStatus OnSelfCheck02();        // 自检验证：验证皮带系统自检结果
	CStatus OnSelfCheck03();        // 自检完成：完成皮带系统自检
	
	// ========== 皮带运行流程 (OnBelt00-OnBelt18) ==========
	CStatus OnBelt00();             // 皮带流程控制：控制皮带主流程
	CStatus OnBelt01();             // 皮带状态检查：检查皮带运行状态
	CStatus OnBelt02();             // 皮带来料检测：检测皮带来料状态
	CStatus OnBelt02_0();           // 皮带来料检测子流程：执行皮带来料检测详细操作
	CStatus OnBelt03();             // 皮带运行准备：准备皮带运行
	CStatus OnBelt04();             // 皮带运行启动：启动皮带运行
	CStatus OnBelt05();             // 皮带运行监控：监控皮带运行状态
	CStatus OnBelt06();             // 皮带运行验证：验证皮带运行状态
	CStatus OnBelt07();             // 皮带运行完成：完成皮带运行
	CStatus OnBelt08();             // 皮带运行后处理：皮带运行后处理
	CStatus OnBelt09();             // 皮带运行清理：清理皮带运行区域
	CStatus OnBelt10();             // 皮带运行记录：记录皮带运行数据
	CStatus OnBelt11();             // 皮带运行状态更新：更新皮带运行状态
	CStatus OnBelt11_0();           // 皮带运行状态更新子流程：执行皮带运行状态更新详细操作
	CStatus OnBelt12();             // 皮带运行流程验证：验证皮带运行流程
	CStatus OnBelt13();             // 皮带运行流程完成：完成皮带运行流程
	CStatus OnBelt14();             // 皮带运行流程结束：结束皮带运行流程
	CStatus OnBelt15();             // 皮带运行最终检查：最终检查皮带运行状态
	CStatus OnBelt16();             // 皮带运行最终验证：最终验证皮带运行结果
	CStatus OnBelt17();             // 皮带运行最终完成：最终完成皮带运行
	CStatus OnBelt18();             // 皮带运行最终结束：最终结束皮带运行

private:
	CString				m_sTrack;           // 轨道标识字符串（A轨或B轨）

	CBelt*				m_pBelt;            // 皮带设备对象指针

	CString				m_sRet;             // 函数返回字符串结果

	map<CString, DWORD>	m_mapTick;          // 时间计时器映射表，用于超时控制
};
