cmake 3.30.1
features core;ssl
openssl 253bb4bf9b62871c6de32cf1f4c52f2ae7a93f34adc6df60993705d9b45216d6
paho-mqtt e04391966b12706516130ee4c3638115c5aea5fff27cbc92a9901830db5167b9
portfile.cmake f9851f193500982410a907b4716b8e27ab10636273cbf7c54d44fca39c5343f1
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
powershell 7.2.24
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
vcpkg-cmake 8112fdacac7427b4feea02a5a660bd2fb97e5b898a970d195ff6370d43a794fb
vcpkg-cmake-config 8b1ae8f4be6cff022eadef50d38cffca5e75f23d30fc7e280b912b164b7e8ea1
vcpkg.json e6b92ac64402b63a39ad729659590a3e150a641e5b6926552a3209c53eac12c0
vcpkg_check_features 943b217e0968d64cf2cb9c272608e6a0b497377e792034f819809a79e1502c2b
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
