# FStationMFC 项目规范（低侵入性维护）

## 🚨 核心原则：最小化变更，保持稳定

这是一个使用 VS2010 Visual C++ 和 MFC 框架开发的成熟工业控制系统。**任何修改都必须遵循低侵入性原则**。

## 📋 维护规范

### ✅ 允许的操作
- **配置文件修改**：仅修改 `config/` 目录下的配置文件
- **日志增强**：添加非关键路径的日志输出
- **注释补充**：为复杂逻辑添加说明注释
- **文档更新**：更新 `doc_fstationmfc/` 目录下的文档

### ❌ 禁止的操作
- **核心逻辑修改**：不得修改 `Logic/` 目录下的状态机逻辑
- **硬件接口变更**：不得修改 `Module/` 目录下的硬件抽象层
- **线程架构调整**：不得修改11个核心线程的架构
- **MFC框架升级**：保持VS2010和MFC版本不变

## 🔧 关键信息

- **项目文件**: `FStationMFC/Frame.sln` (VS2010)
- **架构文档**: `doc_fstationmfc/机器运行机制总览.md`
- **核心模块**:
  - `Logic/LogicRobot.cpp` (5914行，机器人控制核心)
  - `Module/` (7个硬件模块类)
  - `Global/LogicMgr.cpp` (线程管理器)

## 📝 代码修改指南

### 新增功能时
```cpp
// ✅ 推荐：通过配置文件扩展
// config/new_feature.json

// ✅ 推荐：添加可选的日志
#ifdef DEBUG_NEW_FEATURE
    TRACE("新功能调试信息: %s", info);
#endif

// ❌ 避免：修改核心状态机
// 不要修改 OnRobot00(), OnRobot01() 等核心方法
```

### 集成外部系统时
```cpp
// ✅ 推荐：通过Socket/MQTT接口
// 使用现有的 socket_config.json 配置

// ✅ 推荐：添加独立的通信模块
class CExternalComm : public CModule {
    // 继承现有模块架构，不破坏原有设计
};
```

## 🔍 故障排查原则

1. **优先查看日志**：检查现有日志输出
2. **参考文档**：查阅 `doc_fstationmfc/` 详细文档
3. **最小化测试**：在测试环境验证后再应用
4. **保留备份**：任何修改前都要备份原始文件

## 📊 MQTT数采集成规范

### 🚨 重要：基于SCADA平台协议的数采功能
FStationMFC需要集成MQTT数采功能，与SCADA平台进行数据交换。**必须严格遵循低侵入性原则**。

#### ✅ 推荐的集成方式
```cpp
// 1. 通过独立的通信模块集成
class CMQTTDataCollector : public CModule {
private:
    // 使用现有的socket_config.json配置
    bool LoadMQTTConfig();

    // 数据采集接口，不修改原有逻辑
    void CollectDeviceData();
    void CollectProductionData();
    void CollectFaultData();

public:
    // 继承CModule架构，保持一致性
    virtual CStatus OnRun() override;
    virtual void OnStop() override;
};
```

#### 📋 数据点映射（科瑞F站数采清单）
```cpp
// ✅ 推荐：通过配置文件映射，避免硬编码
// config/fstation_datapoints.json
{
  "device_status": {
    "A00002": "m_bProductionMode",      // 正常生产状态
    "A00003": "m_bPauseMode",           // 运行暂停状态
    "A00004": "m_bFaultMode",           // 设备故障状态
    "A00006": "m_bReadyMode"            // 待机状态
  },
  "production_data": {
    "C00001": "m_strCurrentSN",         // 主板SN号
    "C00002": "m_strDeviceSerial",      // 设备资产编码
    "C00003": "m_nCurrentLane",         // 轨道号
    "C00004": "m_strCurrentSide",       // 面别 (T/B)
    "C00005": "m_strProgramName"        // 程序名
  }
}
```

#### 🔧 集成实现指南
```cpp
// ✅ 推荐：通过全局数据访问，不修改核心逻辑
class CMQTTDataCollector {
private:
    // 访问现有全局数据，不破坏原有架构
    bool GetDeviceStatus() {
        // 通过Sys、Pro、Dat等全局对象获取数据
        bool isProduction = Sys.m_LogicMgr.IsProductionMode();
        bool isFault = Sys.m_LogicMgr.IsFaultMode();
        return true;
    }

    // 获取机器人数据，不修改LogicRobot.cpp
    bool GetRobotData() {
        // 通过现有接口获取机器人状态
        CLogicRobot* pRobot = Sys.m_LogicMgr.GetLogicRobot();
        if (pRobot) {
            // 获取当前SN、程序名等信息
        }
        return true;
    }
};
```

#### 📝 配置文件扩展
```json
// ✅ 推荐：扩展现有socket_config.json
{
  "socket": {
    "port": 8080,
    "timeout": 30000
  },
  "mqtt_datacollection": {
    "enable": true,
    "broker": "172.16.247.78",
    "port": 1883,
    "deviceId": "F_STATION_001",
    "reportInterval": 300,
    "dataPointsConfig": "config/fstation_datapoints.json"
  }
}
```

#### ❌ 严格禁止的操作
```cpp
// ❌ 禁止：修改核心状态机
void CLogicRobot::OnRobot00() {
    // 不要在这里添加MQTT相关代码
    // 不要修改现有的状态转换逻辑
}

// ❌ 禁止：修改硬件模块接口
class CRobot : public CModule {
    // 不要在硬件抽象层添加MQTT功能
    // 不要修改现有的MakePair参数定义
};

// ❌ 禁止：修改线程架构
class CLogicMgr {
    // 不要添加新的线程
    // 不要修改现有11个线程的管理逻辑
};
```

#### 🔍 数据采集策略
```cpp
// ✅ 推荐：定时采集，不影响实时性
class CMQTTDataCollector {
private:
    QTimer* m_pCollectionTimer;

    void StartDataCollection() {
        // 每5分钟采集一次，不影响生产节拍
        m_pCollectionTimer->start(300000);
    }

    void OnCollectionTimer() {
        // 快速采集，避免阻塞主线程
        CollectAndSendData();
    }
};
```

#### 📊 事件上报机制
```cpp
// ✅ 推荐：通过观察者模式，不修改原有代码
class CMQTTEventReporter {
public:
    // 监听现有的事件，不修改事件源
    void OnProductionEvent(const ProductionEvent& event) {
        if (event.type == EVENT_SN_IN) {
            ReportSNInEvent(event.sn, event.programName);
        }
    }

    void OnFaultEvent(const FaultEvent& event) {
        ReportFaultEvent(event.faultCode, event.faultMessage);
    }
};
```

## 📚 必读文档
- `doc_fstationmfc/机器运行机制总览.md` - 系统整体架构
- `doc_fstationmfc/机器人控制详细分析.md` - 核心控制逻辑
- `doc_fstationmfc/硬件模块接口分析.md` - 硬件抽象层
- `doc/重要/SCADA平台MQTT协议设备接入指南.md` - MQTT协议规范
- `doc/重要/科瑞F站--数采清单.md` - 数据点定义
