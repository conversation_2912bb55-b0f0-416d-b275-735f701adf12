﻿// DialogRobotTeach.cpp : 实现文件
//

#include "stdafx.h"
#include "FStation.h"
#include "DialogRobotTeach.h"
#include "afxdialogex.h"

#include "Sys.h"

#include "LogicMgr.h"

// CDialogRobotTeach 对话框

IMPLEMENT_DYNAMIC(CDialogRobotTeach, CDialogEx)

CDialogRobotTeach::CDialogRobotTeach(CPoint pt, CWnd* pParent /*=NULL*/)
	: CDialogEx(CDialogRobotTeach::IDD, pParent)
{
	m_pt = pt;
}

CDialogRobotTeach::~CDialogRobotTeach()
{
}

void CDialogRobotTeach::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_LIST1, m_list);
	DDX_Control(pDX, IDC_EDIT5, m_edit);
}

BEGIN_MESSAGE_MAP(CDialogRobotTeach, CDialogEx)
	ON_WM_TIMER()
	ON_WM_DESTROY()
	ON_NOTIFY(NM_DBLCLK, IDC_LIST1, &CDialogRobotTeach::OnNMDblclkList1)
	ON_EN_KILLFOCUS(IDC_EDIT5, &CDialogRobotTeach::OnEnKillfocusEdit5)
	ON_BN_CLICKED(IDC_BUTTON1, &CDialogRobotTeach::OnBnClickedButton1)
	ON_BN_CLICKED(IDC_BUTTON2, &CDialogRobotTeach::OnBnClickedButton2)
	ON_NOTIFY(NM_CLICK, IDC_LIST1, &CDialogRobotTeach::OnNMClickList1)
	ON_BN_CLICKED(IDC_BUTTON3, &CDialogRobotTeach::OnBnClickedButton3)
	ON_BN_CLICKED(IDC_BUTTON4, &CDialogRobotTeach::OnBnClickedButton4)
	ON_BN_CLICKED(IDC_BUTTON5, &CDialogRobotTeach::OnBnClickedButton5)
	ON_BN_CLICKED(IDC_BUTTON6, &CDialogRobotTeach::OnBnClickedButton6)
	ON_BN_CLICKED(IDC_BUTTON7, &CDialogRobotTeach::OnBnClickedButton7)
	ON_BN_CLICKED(IDC_BUTTON8, &CDialogRobotTeach::OnBnClickedButton8)
	ON_BN_CLICKED(IDC_BUTTON9, &CDialogRobotTeach::OnBnClickedButton9)
	ON_BN_CLICKED(IDC_BUTTON10, &CDialogRobotTeach::OnBnClickedButton10)
END_MESSAGE_MAP()

BOOL CDialogRobotTeach::PreTranslateMessage( MSG* pMsg )
{
	if ((WM_SYSKEYDOWN == pMsg->message && VK_F4 == pMsg->wParam) || (pMsg->message == WM_KEYDOWN && pMsg->wParam == VK_ESCAPE)) { //屏蔽Alt + F4
		return TRUE;
	}

	return CDialogEx::PreTranslateMessage(pMsg);
}

BOOL CDialogRobotTeach::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	CRect rc;

	GetWindowRect(&rc);

	MoveWindow(m_pt.x, m_pt.y, rc.Width(), rc.Height());

	m_imageList.Create(24, 24, TRUE, 2, 2);

	//Node
	LONG lStyle = 0;
	lStyle = GetWindowLong(m_list.m_hWnd, GWL_STYLE);//获取当前窗口样式
	lStyle &= ~LVS_TYPEMASK;//清除显位方式位
	lStyle |= LVS_REPORT;
	SetWindowLong(m_list.m_hWnd, GWL_STYLE, lStyle);

	DWORD dwStyle = 0;
	dwStyle = m_list.GetExtendedStyle();//获取当前窗口扩展样式
	dwStyle |= LVS_EX_FULLROWSELECT;//选中某行使整行高亮
	dwStyle |= LVS_EX_GRIDLINES;//网格线
	m_list.SetExtendedStyle(dwStyle);

	m_list.SetImageList(&m_imageList, LVSIL_SMALL);

	CString sParam[] = { "", "名称", "x", "y" , "z", "r", "speed", "cf1", "cf4", "cf6", "cfx", "confj"};
	int		nParamLen[] = { 1, 195, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60 };

	for (int i=0; i<sizeof(nParamLen) / sizeof(int); i++)
	{
		if (i == 1) {
			m_list.InsertColumn(i, sParam[i], LVCFMT_LEFT, nParamLen[i]);
		}
		else {
			m_list.InsertColumn(i, sParam[i], LVCFMT_CENTER, nParamLen[i]);
		}
	}

	int nIndex = 0;

	vector<CString>::iterator it = g_pRobot->m_vecRobotPoint.begin();
	for (; it != g_pRobot->m_vecRobotPoint.end(); it++)
	{
		nIndex = m_list.GetItemCount();
		m_list.InsertItem(nIndex, "");
		m_list.SetItemText(nIndex, 1, *it);
		m_list.SetItemText(nIndex, 2, g_pRobot->m_mapRobotPoint[*it]->x);
		m_list.SetItemText(nIndex, 3, g_pRobot->m_mapRobotPoint[*it]->y);
		m_list.SetItemText(nIndex, 4, g_pRobot->m_mapRobotPoint[*it]->z);
		m_list.SetItemText(nIndex, 5, g_pRobot->m_mapRobotPoint[*it]->r);
		m_list.SetItemText(nIndex, 6, g_pRobot->m_mapRobotPoint[*it]->speed);
		m_list.SetItemText(nIndex, 7, g_pRobot->m_mapRobotPoint[*it]->cf1);
		m_list.SetItemText(nIndex, 8, g_pRobot->m_mapRobotPoint[*it]->cf4);
		m_list.SetItemText(nIndex, 9, g_pRobot->m_mapRobotPoint[*it]->cf6);
		m_list.SetItemText(nIndex, 10, g_pRobot->m_mapRobotPoint[*it]->cfx);
		m_list.SetItemText(nIndex, 11, g_pRobot->m_mapRobotPoint[*it]->confj);
	}

	SetDlgItemInt(IDC_EDIT6, 1);

	Start();

	return TRUE;
}

bool CDialogRobotTeach::Excute()
{
	ROBOTPOINT stRobPnt;

	CString sRet;

	while (!m_bExit) 
	{
		if (!IsWindowVisible()) {
			Sleep(100);
			continue;
		}

		sRet = g_pRobot->GetPos(stRobPnt);

		if (sRet == "OK") {
			m_curRobPnt = stRobPnt;
			SetTimer(1, 100, NULL);
		}

		Sleep(200);
	}

	return true;
}

// CDialogRobotTeach 消息处理程序

void CDialogRobotTeach::OnTimer(UINT_PTR nIDEvent)
{
	KillTimer(nIDEvent);

	CString sRet;

	sRet.Format("%.03f", m_curRobPnt.x);
	SetDlgItemText(IDC_EDIT1, sRet);

	sRet.Format("%.03f", m_curRobPnt.y);
	SetDlgItemText(IDC_EDIT2, sRet);

	sRet.Format("%.03f", m_curRobPnt.z);
	SetDlgItemText(IDC_EDIT3, sRet);

	sRet.Format("%.03f", m_curRobPnt.r);
	SetDlgItemText(IDC_EDIT4, sRet);

	CDialogEx::OnTimer(nIDEvent);
}

void CDialogRobotTeach::OnDestroy()
{
	Stop();

	CDialogEx::OnDestroy();
}

void CDialogRobotTeach::OnNMClickList1(NMHDR *pNMHDR, LRESULT *pResult)
{
	LPNMITEMACTIVATE pNMItemActivate = reinterpret_cast<LPNMITEMACTIVATE>(pNMHDR);

	NM_LISTVIEW  *pEditCtrl = (NM_LISTVIEW *)pNMHDR;

	m_nRowForEdit = pNMItemActivate->iItem;//m_row选中行行号
	m_nColForEdit = pNMItemActivate->iSubItem;//m_col选中行列号

	*pResult = 0;
}

void CDialogRobotTeach::OnNMDblclkList1(NMHDR *pNMHDR, LRESULT *pResult)
{
	LPNMITEMACTIVATE pNMItemActivate = reinterpret_cast<LPNMITEMACTIVATE>(pNMHDR);

	NM_LISTVIEW  *pEditCtrl = (NM_LISTVIEW *)pNMHDR;

	m_nRowForEdit = pNMItemActivate->iItem;//m_row选中行行号
	m_nColForEdit = pNMItemActivate->iSubItem;//m_col选中行列号

	CColorList &thisList = m_list;

	CRect rc;
	thisList.GetSubItemRect(pNMItemActivate->iItem, pNMItemActivate->iSubItem, LVIR_LABEL, rc);//取得子项的矩形
	thisList.ClientToScreen(&rc);
	ScreenToClient(&rc);

	if (pEditCtrl->iItem >= 0 && pEditCtrl->iSubItem >= 2 && pEditCtrl->iSubItem <= 6) {//编辑框

		PREMISSION_CTRL();

		CString strVal;
		strVal = thisList.GetItemText(pNMItemActivate->iItem, pNMItemActivate->iSubItem);//取得子项的内容
		m_edit.SetWindowText(strVal);//将子项的内容显示到编辑框中
		m_edit.MoveWindow(&rc);//将编辑框移动到子项上面&#xff0c;覆盖在子项上
		m_edit.ShowWindow(SW_SHOW);//显示编辑框
		m_edit.SetFocus();//使编辑框取得焦点
		m_edit.CreateSolidCaret(1, rc.Height() - 5);//创建一个光标
		m_edit.ShowCaret();//显示光标
		m_edit.SetSel(0, -1);//使光标移到最后面
	}

	*pResult = 0;
}

void CDialogRobotTeach::OnEnKillfocusEdit5()
{
	if (CSys::m_nRight < 4) {
		AfxMessageBox("无修改权限!");
		return;
	}

	CString sName, sVal;

	sName = m_list.GetItemText(m_nRowForEdit, 1);

	GetDlgItemText(IDC_EDIT5, sVal);

	m_list.SetItemText(m_nRowForEdit, m_nColForEdit, sVal);

	m_edit.ShowWindow(SW_HIDE);

	switch (m_nColForEdit)
	{
	case 2:
		g_pRobot->m_mapRobotPoint[sName]->x = atof(sVal);
		break;
	case 3:
		g_pRobot->m_mapRobotPoint[sName]->y = atof(sVal);
		break;
	case 4:
		g_pRobot->m_mapRobotPoint[sName]->z = atof(sVal);
		break;
	case 5:
		g_pRobot->m_mapRobotPoint[sName]->r = atof(sVal);
		break;
	case 6:
		g_pRobot->m_mapRobotPoint[sName]->speed = atoi(sVal);
		break;
	case 11:
		g_pRobot->m_mapRobotPoint[sName]->confj = sVal == "1" ? 1 : 0;
		break;
	default:break;
	}

	g_pRobot->SaveRobotPoint(sName);
}

void CDialogRobotTeach::OnBnClickedButton1()
{
	PREMISSION_CTRL();

	if (CSys::m_nRight < 4) {
		AfxMessageBox("无修改权限!");
		return;
	}

	CString sName;

	sName = m_list.GetItemText(m_nRowForEdit, 1);

	if (sName.IsEmpty()) {
		return;
	}

	CString sRet;

	sRet.Format("示教点位数据: [%s], 是否继续?", sName);

	if (AfxMessageBox(sRet, MB_YESNO) == IDNO) {
		return;
	}

	g_pRobot->m_mapRobotPoint[sName]->x = m_curRobPnt.x;
	g_pRobot->m_mapRobotPoint[sName]->y = m_curRobPnt.y;
	g_pRobot->m_mapRobotPoint[sName]->z = m_curRobPnt.z;
	g_pRobot->m_mapRobotPoint[sName]->r = m_curRobPnt.r;
	g_pRobot->m_mapRobotPoint[sName]->cf1 = m_curRobPnt.cf1;
	g_pRobot->m_mapRobotPoint[sName]->cf4 = m_curRobPnt.cf4;
	g_pRobot->m_mapRobotPoint[sName]->cf6 = m_curRobPnt.cf6;
	g_pRobot->m_mapRobotPoint[sName]->cfx = m_curRobPnt.cfx;

	m_list.SetItemText(m_nRowForEdit, 2, g_pRobot->m_mapRobotPoint[sName]->x);
	m_list.SetItemText(m_nRowForEdit, 3, g_pRobot->m_mapRobotPoint[sName]->y);
	m_list.SetItemText(m_nRowForEdit, 4, g_pRobot->m_mapRobotPoint[sName]->z);
	m_list.SetItemText(m_nRowForEdit, 5, g_pRobot->m_mapRobotPoint[sName]->r);
	m_list.SetItemText(m_nRowForEdit, 6, g_pRobot->m_mapRobotPoint[sName]->speed);
	m_list.SetItemText(m_nRowForEdit, 7, g_pRobot->m_mapRobotPoint[sName]->cf1);
	m_list.SetItemText(m_nRowForEdit, 8, g_pRobot->m_mapRobotPoint[sName]->cf4);
	m_list.SetItemText(m_nRowForEdit, 9, g_pRobot->m_mapRobotPoint[sName]->cf6);
	m_list.SetItemText(m_nRowForEdit, 10, g_pRobot->m_mapRobotPoint[sName]->cfx);
	m_list.SetItemText(m_nRowForEdit, 11, g_pRobot->m_mapRobotPoint[sName]->confj);

	g_pRobot->SaveRobotPoint(sName);
}

void CDialogRobotTeach::OnBnClickedButton2()
{
	PREMISSION_CTRL();

	CString sName;

	sName = m_list.GetItemText(m_nRowForEdit, 1);

	if (sName.IsEmpty()) {
		return;
	}

	CString sRet;

	sRet.Empty();

	g_pRobot->Move(sName, true, true, &sRet);

	if (!sRet.IsEmpty()) {
		AfxMessageBox(sRet);
	}
}

void CDialogRobotTeach::OnBnClickedButton3()
{
	PREMISSION_CTRL();

	ROBOTPOINT stRobPnt;

	CString sRet, sVal;

	sRet = g_pRobot->GetPos(stRobPnt);

	if (sRet != "OK") {
		return;
	}

	stRobPnt.speed = 30;

	UpdateData();

	GetDlgItemText(IDC_EDIT6, sVal);

	if (sVal.IsEmpty()) {
		return;
	} 

	double nVal = 0;

	nVal = atof(sVal);

	if (fabs(nVal) > 50) {
		AfxMessageBox("步长限制50mm，超出限制!");
		return;
	}

	stRobPnt.x -= nVal;

	sRet.Empty();

	g_pRobot->Move(stRobPnt, false, true, &sRet);

	if (!sRet.IsEmpty()) {
		AfxMessageBox(sRet);
	}
}

void CDialogRobotTeach::OnBnClickedButton4()
{
	PREMISSION_CTRL();

	ROBOTPOINT stRobPnt;

	CString sRet, sVal;

	sRet = g_pRobot->GetPos(stRobPnt);

	if (sRet != "OK") {
		return;
	}

	stRobPnt.speed = 30;

	UpdateData();

	GetDlgItemText(IDC_EDIT6, sVal);

	if (sVal.IsEmpty()) {
		return;
	} 

	double nVal = 0;

	nVal = atof(sVal);

	if (fabs(nVal) > 50) {
		AfxMessageBox("步长限制50mm，超出限制!");
		return;
	}

	stRobPnt.x += nVal;

	sRet.Empty();

	g_pRobot->Move(stRobPnt, false, true, &sRet);

	if (!sRet.IsEmpty()) {
		AfxMessageBox(sRet);
	}
}

void CDialogRobotTeach::OnBnClickedButton5()
{
	PREMISSION_CTRL();

	ROBOTPOINT stRobPnt;

	CString sRet, sVal;

	sRet = g_pRobot->GetPos(stRobPnt);

	if (sRet != "OK") {
		return;
	}

	stRobPnt.speed = 30;

	UpdateData();

	GetDlgItemText(IDC_EDIT6, sVal);

	if (sVal.IsEmpty()) {
		return;
	} 

	double nVal = 0;

	nVal = atof(sVal);

	if (fabs(nVal) > 50) {
		AfxMessageBox("步长限制50mm，超出限制!");
		return;
	}

	stRobPnt.y -= nVal;

	sRet.Empty();

	g_pRobot->Move(stRobPnt, false, true, &sRet);

	if (!sRet.IsEmpty()) {
		AfxMessageBox(sRet);
	}
}

void CDialogRobotTeach::OnBnClickedButton6()
{
	PREMISSION_CTRL();

	ROBOTPOINT stRobPnt;

	CString sRet, sVal;

	sRet = g_pRobot->GetPos(stRobPnt);

	if (sRet != "OK") {
		return;
	}

	stRobPnt.speed = 30;

	UpdateData();

	GetDlgItemText(IDC_EDIT6, sVal);

	if (sVal.IsEmpty()) {
		return;
	} 

	double nVal = 0;

	nVal = atof(sVal);

	if (fabs(nVal) > 50) {
		AfxMessageBox("步长限制50mm，超出限制!");
		return;
	}

	stRobPnt.y += nVal;

	sRet.Empty();

	g_pRobot->Move(stRobPnt, false, true, &sRet);

	if (!sRet.IsEmpty()) {
		AfxMessageBox(sRet);
	}
}

void CDialogRobotTeach::OnBnClickedButton7()
{
	PREMISSION_CTRL();

	ROBOTPOINT stRobPnt;

	CString sRet, sVal;

	sRet = g_pRobot->GetPos(stRobPnt);

	if (sRet != "OK") {
		return;
	}

	stRobPnt.speed = 30;

	UpdateData();

	GetDlgItemText(IDC_EDIT6, sVal);

	if (sVal.IsEmpty()) {
		return;
	} 

	double nVal = 0;

	nVal = atof(sVal);

	if (fabs(nVal) > 50) {
		AfxMessageBox("步长限制50mm，超出限制!");
		return;
	}

	stRobPnt.z -= nVal;

	sRet.Empty();

	g_pRobot->Move(stRobPnt, false, true, &sRet);

	if (!sRet.IsEmpty()) {
		AfxMessageBox(sRet);
	}
}

void CDialogRobotTeach::OnBnClickedButton8()
{
	PREMISSION_CTRL();

	ROBOTPOINT stRobPnt;

	CString sRet, sVal;

	sRet = g_pRobot->GetPos(stRobPnt);

	if (sRet != "OK") {
		return;
	}

	stRobPnt.speed = 30;

	UpdateData();

	GetDlgItemText(IDC_EDIT6, sVal);

	if (sVal.IsEmpty()) {
		return;
	} 

	double nVal = 0;

	nVal = atof(sVal);

	if (fabs(nVal) > 50) {
		AfxMessageBox("步长限制50mm，超出限制!");
		return;
	}

	stRobPnt.z += nVal;

	sRet.Empty();

	g_pRobot->Move(stRobPnt, false, true, &sRet);

	if (!sRet.IsEmpty()) {
		AfxMessageBox(sRet);
	}
}

void CDialogRobotTeach::OnBnClickedButton9()
{
	PREMISSION_CTRL();

	ROBOTPOINT stRobPnt;

	CString sRet, sVal;

	sRet = g_pRobot->GetPos(stRobPnt);

	if (sRet != "OK") {
		return;
	}

	stRobPnt.speed = 30;

	UpdateData();

	GetDlgItemText(IDC_EDIT6, sVal);

	if (sVal.IsEmpty()) {
		return;
	} 

	double nVal = 0;

	nVal = atof(sVal);

	if (fabs(nVal) > 30) {
		AfxMessageBox("步长限制30°，超出限制!");
		return;
	}

	stRobPnt.r -= nVal;

	sRet.Empty();

	g_pRobot->Move(stRobPnt, false, true, &sRet);

	if (!sRet.IsEmpty()) {
		AfxMessageBox(sRet);
	}
}

void CDialogRobotTeach::OnBnClickedButton10()
{
	PREMISSION_CTRL();

	ROBOTPOINT stRobPnt;

	CString sRet, sVal;

	sRet = g_pRobot->GetPos(stRobPnt);

	if (sRet != "OK") {
		return;
	}

	stRobPnt.speed = 30;

	UpdateData();

	GetDlgItemText(IDC_EDIT6, sVal);

	if (sVal.IsEmpty()) {
		return;
	} 

	double nVal = 0;

	nVal = atof(sVal);

	if (fabs(nVal) > 30) {
		AfxMessageBox("步长限制30°，超出限制!");
		return;
	}

	stRobPnt.r += nVal;

	sRet.Empty();

	g_pRobot->Move(stRobPnt, false, true, &sRet);

	if (!sRet.IsEmpty()) {
		AfxMessageBox(sRet);
	}
}
