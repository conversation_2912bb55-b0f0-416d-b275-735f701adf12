﻿#include "stdafx.h"
#include "Pro.h"

#include "BaseApi.h"
using namespace yzBase;

string CPro::m_strPro;

void CPro::InitPro(string strPro)
{
	if (strPro.size() <= 0) {
		TRACE("产品名称为空\n");
		return;
	}

	if (g_pInfo == NULL) {
		return;
	}

	m_strPro = strPro;

	CString strPath = CString(GetModulePath().c_str());
	CString strProPath, strProDbFile, strProCfgFile;

	strProPath.Format("%s\\Pro\\%s",  strPath.GetBuffer(), strPro.c_str());

	strProDbFile.Format("%s\\Pos.mdb", strProPath.GetBuffer());
	g_pInfo->InitProInfo(strProDbFile, "123");

	SwitchPro();
}

void CPro::DeInit()
{
	for (int i=0; i<4; i++)
	{
		delete g_pImageFlowUp[i];
		delete g_pImageFlowDn[i];
		delete g_pImageFlowMark[i];
		delete g_pImageFlowScanCodeMainBoard[i];
	}

	delete g_pImageFlowTray;

	delete g_pImageFlowScanCodeFixture;
}

void CPro::SavePro()
{
	CString strPath = CString(GetModulePath().c_str());
	CString strProPath;

	strProPath.Format("%s\\Pro\\%s\\Pro.ini",  strPath.GetBuffer(), m_strPro.c_str());
}

void CPro::SwitchPro()
{
	string strPro;
	strPro = m_strPro;

	if (strPro.size() <= 0) {
		TRACE("产品名称为空\n");
		return;
	}

	CString strPath = CString(GetModulePath().c_str());

	CString strProPath;
	strProPath.Format("%s\\Pro\\%s",  strPath.GetBuffer(), strPro.c_str());

	for (int i=0; i<4; i++)
	{
		g_pImageFlowUp[i] = CImageFlow::CreateInstance("上相机图像处理流程", strProPath, g_pCamera, "Up");
		g_pImageFlowDn[i] = CImageFlow::CreateInstance("下相机图像处理流程", strProPath, g_pCamera, "Dn");
		g_pImageFlowMark[i] = CImageFlow::CreateInstance("上相机Mark图像处理流程", strProPath, g_pCamera, "Up");
		g_pImageFlowScanCodeMainBoard[i] = CImageFlow::CreateInstance("主板二维码扫描图像处理流程", strProPath, g_pCamera, "Up");
	}
	
	g_pImageFlowTray = CImageFlow::CreateInstance("Tray相机图像处理流程", strProPath, g_pCamera, "Tray");
	
	g_pImageFlowScanCodeFixture = CImageFlow::CreateInstance("治具二维码扫描图像处理流程", strProPath, g_pCamera, "Up");

	g_pRobot->LoadRobotPoint();

	g_pRobot->Load();

	g_pRobot->LoadMainBoardBackPos();

	g_pTray->Load();

	g_pFixture->Load();

	g_pBeltA->Load();

	g_pBeltB->Load();

	g_pMachine->Load();
}
