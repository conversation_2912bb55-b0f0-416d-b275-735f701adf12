[
    // 声明/实现跳转
    {
        "key": "f12",
        "command": "C_Cpp.GoToDeclaration",
        "when": "editorTextFocus && editorLangId == cpp"
    },
    {
        "key": "ctrl+f12",
        "command": "C_Cpp.GoToDefinition",
        "when": "editorTextFocus && editorLangId == cpp"
    },
    {
        "key": "alt+f12",
        "command": "editor.action.goToImplementation",
        "when": "editorTextFocus && editorLangId == cpp"
    },
    
    // 查找所有引用
    {
        "key": "shift+f12",
        "command": "editor.action.findReferences",
        "when": "editorTextFocus && editorLangId == cpp"
    },
    
    // 符号搜索
    {
        "key": "ctrl+,",
        "command": "workbench.action.showAllSymbols"
    },
    {
        "key": "ctrl+shift+o",
        "command": "workbench.action.gotoSymbol"
    },
    
    // 文件导航
    {
        "key": "ctrl+shift+t",
        "command": "workbench.action.quickOpen"
    },
    {
        "key": "alt+o",
        "command": "C_Cpp.SwitchHeaderSource",
        "when": "editorTextFocus && editorLangId == cpp"
    },
    
    // CMake 快捷键
    {
        "key": "ctrl+shift+p",
        "command": "cmake.build",
        "when": "!terminalFocus"
    },
    {
        "key": "f7",
        "command": "cmake.build"
    },
    {
        "key": "ctrl+f7",
        "command": "cmake.buildAll"
    },
    {
        "key": "shift+f7",
        "command": "cmake.clean"
    },
    {
        "key": "f5",
        "command": "cmake.debugTarget"
    },
    {
        "key": "ctrl+f5",
        "command": "cmake.launchTarget"
    },
    
    // 手动格式化选项
    {
        "key": "ctrl+k ctrl+f",
        "command": "editor.action.formatSelection",
        "when": "editorTextFocus && editorHasSelection && editorLangId == cpp"
    },
    {
        "key": "ctrl+k ctrl+d",
        "command": "editor.action.formatDocument",
        "when": "editorTextFocus && editorLangId == cpp"
    },
    {
        "key": "ctrl+shift+i",
        "command": "editor.action.formatDocument",
        "when": "editorTextFocus && editorLangId == cpp"
    }
]
