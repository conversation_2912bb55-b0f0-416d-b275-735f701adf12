﻿#pragma once

#include "ThreadBase.h"
using namespace yzBase;

#include "Machine.h"

class CLogicMachine : public CThreadBase
{
public:
	CLogicMachine();
	virtual ~CLogicMachine();

public:
	// 基础控制函数
	EnumStatus OnSafeCheck();       // 安全检查：检查机器安全状态，包括安全光栅、安全门、急停等
	EnumStatus OnStart();           // 启动控制：初始化机器系统
	EnumStatus OnPause();           // 暂停控制：暂停机器操作，关闭NG皮带
	EnumStatus OnResume();          // 恢复控制：从暂停状态恢复机器操作
	EnumStatus OnStop();            // 停止控制：停止机器系统，关闭NG皮带

public:
	// 主运行函数
	CStatus OnRun();                // 主状态机运行函数：协调机器系统工作流程

	// ========== 机器主流程 (OnMachine00-OnMachine02) ==========
	CStatus OnMachine00();          // 机器主流程控制：控制机器主流程，监控按钮状态
	CStatus OnMachine01();          // 机器状态检查：检查机器运行状态
	CStatus OnMachine02();          // 机器流程分配：分配机器流程

	// ========== NG皮带控制流程 (OnNGBelt00-OnNGBelt05) ==========
	CStatus OnNGBelt00();           // NG皮带流程控制：控制NG皮带主流程
	CStatus OnNGBelt01();           // NG皮带等待：等待NG皮带放料完成
	CStatus OnNGBelt02();           // NG皮带检测：检测NG皮带尾端是否有NG主板
	CStatus OnNGBelt03();           // NG皮带运转：运转NG皮带直到检测到主板
	CStatus OnNGBelt04();           // NG皮带停止：停止NG皮带运转
	CStatus OnNGBelt05();           // NG皮带完成：完成NG皮带操作

	// ========== 按钮和安全检测流程 (OnButton00-OnButton08) ==========
	CStatus OnButton00();           // 急停检测：检测急停按钮状态，紧急停止系统
	CStatus OnButton01();           // 安全光栅检测：检测安全光栅状态，触发时暂停机械手
	CStatus OnButton02();           // 左安全门检测：检测左安全门状态，打开时暂停机械手
	CStatus OnButton03();           // 右安全门检测：检测右安全门状态，打开时暂停机械手
	CStatus OnButton04();           // 前安全门检测：检测前安全门状态，打开时暂停机械手
	CStatus OnButton05();           // 后安全门检测：检测后安全门状态，打开时暂停机械手
	CStatus OnButton06();           // 启动按钮检测：检测启动按钮状态，按下时启动系统
	CStatus OnButton07();           // 按钮检测预留：预留按钮检测功能
	CStatus OnButton08();           // NG皮带按钮控制：控制NG皮带启动和停止

private:
	CMachine*		m_pMachine;         // 机器设备对象指针

	CString			m_sRet;             // 函数返回字符串结果

	CThreadFunc*	m_pFuncNGBelt;      // NG皮带线程函数指针
	CThreadFunc*	m_pFuncButton;      // 按钮检测线程函数指针

	map<CString, bool> m_mapFlag;       // 标志映射表，记录各种安全状态标志
	map<CString, DWORD> m_mapTick;      // 时间计时器映射表，用于超时控制
};
