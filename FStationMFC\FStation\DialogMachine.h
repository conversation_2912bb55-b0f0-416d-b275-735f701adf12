﻿#pragma once

#include "Resource.h"

#include "ColorButton.h"

// CDialogMachine 对话框

class CDialogMachine : public CDialogEx
{
	DECLARE_DYNAMIC(CDialogMachine)

public:
	CDialogMachine(CPoint pt, CWnd* pParent = NULL);   // 标准构造函数
	virtual ~CDialogMachine();

// 对话框数据
	enum { IDD = IDD_DIALOG_MACHINE };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持
	virtual BOOL PreTranslateMessage(MSG* pMsg);
	virtual void OnOK() {};
	virtual BOOL OnInitDialog();

	DECLARE_MESSAGE_MAP()
	afx_msg void OnBnClickedButton1();
	afx_msg void OnBnClickedButton2();
	afx_msg void OnBnClickedButton3();
	afx_msg void OnBnClickedButton4();
	afx_msg void OnBnClickedButton10();
	afx_msg void OnBnClickedButton11();
	afx_msg void OnBnClickedButton12();

protected:
	CColorButton	m_btnReset;
	CColorButton	m_btnStart;
	CColorButton	m_btnPause;
	CColorButton	m_btnStop;

private:
	CPoint			m_pt;
public:
	afx_msg void OnBnClickedButtonSnIn();
	afx_msg void OnBnClickedButtonSnOut();
	afx_msg void OnBnClickedButtonSnOutReq();
	afx_msg void OnBnClickedButtonEventBopDeliverCompleted();
	afx_msg void OnBnClickedButtonEventPause();
};
