cmake_minimum_required(VERSION 3.16)
project(MQTTGateway VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# 告诉vcpkg在构建时自动复制依赖的DLL到输出目录
if(MSVC)
    set(VCPKG_APPLOCAL_DEPS ON)
endif()

# --- 查找依赖项 ---
# vcpkg工具链会根据构建类型(Debug/Release)自动查找正确的库
find_package(Qt6 REQUIRED COMPONENTS Core Network)
find_package(OpenSSL REQUIRED)
find_package(PahoMqttCpp CONFIG REQUIRED)
find_package(eclipse-paho-mqtt-c CONFIG REQUIRED)

# --- 创建核心静态库（极简架构）---
add_library(MQTTGatewayLib STATIC
    SimpleMQTTGateway.cpp
    SimpleMQTTGateway.h
    SimpleConfigManager.cpp
    SimpleConfigManager.h
    TopicManager.cpp
    TopicManager.h
    SocketServer.cpp
    SocketServer.h
    RequestIdManager.cpp
    RequestIdManager.h
    CommandManager.cpp
    CommandManager.h
    mqtt_fix.cpp
)

# 生成版本头文件
configure_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/version.h.in"
    "${CMAKE_CURRENT_BINARY_DIR}/version.h"
    @ONLY
)

# --- 配置库 ---
target_include_directories(MQTTGatewayLib PUBLIC
    "$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>"
    "$<BUILD_INTERFACE:${CMAKE_SOURCE_DIR}/Common/Protocol>"
    "$<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}>" # for version.h
    "$<INSTALL_INTERFACE:include>" 
)

target_link_libraries(MQTTGatewayLib PUBLIC
    Qt6::Core
    Qt6::Network
    OpenSSL::SSL
    OpenSSL::Crypto
    PahoMqttCpp::paho-mqttpp3
    eclipse-paho-mqtt-c::paho-mqtt3as
)

if(MSVC)
    target_compile_definitions(MQTTGatewayLib PUBLIC
        _CRT_SECURE_NO_WARNINGS
        WIN32_LEAN_AND_MEAN
        NOMINMAX
    )
endif()

# --- 创建可执行文件 ---
add_executable(MQTTGateway main.cpp)

target_link_libraries(MQTTGateway PRIVATE
    MQTTGatewayLib
    ws2_32 # for Windows sockets
)

# --- 创建测试Socket网关 ---
add_executable(TestSocketGateway
    test_main.cpp
    TestSocketGateway.cpp
    TestSocketGateway.h
    SocketServer.cpp
    SocketServer.h
)
target_link_libraries(TestSocketGateway PRIVATE Qt6::Core Qt6::Network ws2_32)

# --- 配置Visual Studio调试环境 ---
if(MSVC)
    # 获取Qt的bin目录
    get_target_property(QT_BIN_DIR Qt6::Core IMPORTED_LOCATION_RELEASE)
    get_filename_component(QT_BIN_DIR ${QT_BIN_DIR} DIRECTORY)

    # vcpkg的bin目录，vcpkg工具链会自动处理Debug/Release
    set(VCPKG_BIN_DIR "$<TARGET_FILE_DIR:MQTTGateway>/$<CONFIG>")
    
    set_target_properties(MQTTGateway PROPERTIES
        VS_DEBUGGER_ENVIRONMENT "PATH=${QT_BIN_DIR};%PATH%"
        VS_DEBUGGER_WORKING_DIRECTORY "$<TARGET_FILE_DIR:MQTTGateway>"
    )
    message(STATUS "Configured VS_DEBUGGER_ENVIRONMENT for MQTTGateway.")
endif()

# --- 构建后和安装步骤 ---
function(add_post_build_config_copy target_name)
    if(NOT WIN32)
        return()
    endif()
    set(config_source_dir "${CMAKE_CURRENT_SOURCE_DIR}/config")
    set(config_target_dir "$<TARGET_FILE_DIR:${target_name}>/config")
    
    # 确保目标目录存在
    add_custom_command(TARGET ${target_name} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E make_directory "${config_target_dir}"
        COMMENT "Creating config directory for ${target_name}"
    )
    
    # 复制新的配置文件
    set(config_files
        "gateway.json"
        "data_points.json"
        "events.json"
        "commands.json"
        "subscriptions.json"
        "publish_topics.json"
    )

    foreach(config_file ${config_files})
        add_custom_command(TARGET ${target_name} POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
            "${config_source_dir}/${config_file}"
            "${config_target_dir}/${config_file}"
            COMMENT "Copying ${config_file} for ${target_name}"
        )
    endforeach()
    
    # 如果有其他配置文件，也可以在这里添加
    # add_custom_command(TARGET ${target_name} POST_BUILD
    #     COMMAND ${CMAKE_COMMAND} -E copy_if_different
    #     "${config_source_dir}/other_config.json"
    #     "${config_target_dir}/other_config.json"
    #     COMMENT "Copying other_config.json for ${target_name}"
    # )
endfunction()

add_post_build_config_copy(MQTTGateway)

# 使用GNUInstallDirs来获取标准的安装路径
include(GNUInstallDirs)

# 安装可执行文件
install(TARGETS MQTTGateway
    RUNTIME DESTINATION "${CMAKE_INSTALL_BINDIR}"
    COMPONENT Runtime
)

# 安装库文件，并导出目标供其他项目使用
install(TARGETS MQTTGatewayLib
    EXPORT FStationTargets
    ARCHIVE DESTINATION "${CMAKE_INSTALL_LIBDIR}"
    LIBRARY DESTINATION "${CMAKE_INSTALL_LIBDIR}"
    COMPONENT Development
)

# 安装开发头文件（极简架构）
install(FILES
    SimpleMQTTGateway.h
    SimpleConfigManager.h
    TopicManager.h
    "${CMAKE_CURRENT_BINARY_DIR}/version.h"
    DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}/fstation/mqttgateway"
    COMPONENT Development
)

# 注意：配置文件的安装现在由根目录的InstallConfig.cmake统一处理 