﻿#include "stdafx.h"
#include "LogicRobot.h"
#include "Pro.h"
#include "Sys.h"
#include "LogicMgr.h"
#include "Mes.h"
#include "json/json.h"

CLogicRobot::CLogicRobot()
{
	m_pRobot = g_pRobot;

	m_pMainBoardInTray = NULL;
}

CLogicRobot::~CLogicRobot()
{
}

EnumStatus CLogicRobot::OnSafeCheck()
{
	if (!CSys::m_bInit) {
		return emStop;
	}

	return emRun;
}

EnumStatus CLogicRobot::OnStart()
{
	double nCalibMachineX = 0, nCalibMachineY = 0, nCalibCenterPixelX = 0, nCalibCenterPixelY = 0;

	g_pImageCalibrateDn->GetCalibrateCoordinate(nCalibMachineX, nCalibMachineY);

	g_pImageCalibrateDn->GetCalibrateCenter(nCalibCenterPixelX, nCalibCenterPixelY);

	double nCenterOffX = 0, nCenterOffY = 0, nCalibCenterMachineX = 0, nCalibCenterMachineY = 0, nCamCenterMachineX = 0, nCamCenterMachineY = 0;

	g_pImageCalibrateDn->TransToMachine(nCalibCenterPixelX, nCalibCenterPixelY, nCalibCenterMachineX, nCalibCenterMachineY);
	g_pImageCalibrateDn->TransToMachine(g_pRobot->m_mapParam["下相机中心坐标X"]->D(), g_pRobot->m_mapParam["下相机中心坐标Y"]->D(), nCamCenterMachineX, nCamCenterMachineY);
	
	VAR_ROBOT("吸嘴1基于旋转中心坐标X") = nCamCenterMachineX - nCalibCenterMachineX + nCalibMachineX - g_pRobot->m_mapRobotPoint["下相机吸嘴1取料参考位"]->x;
	VAR_ROBOT("吸嘴1基于旋转中心坐标Y") = nCamCenterMachineY - nCalibCenterMachineY + nCalibMachineY - g_pRobot->m_mapRobotPoint["下相机吸嘴1取料参考位"]->y;

	VAR_ROBOT("吸嘴2基于旋转中心坐标X") = nCamCenterMachineX - nCalibCenterMachineX + nCalibMachineX - g_pRobot->m_mapRobotPoint["下相机吸嘴2取料参考位"]->x;
	VAR_ROBOT("吸嘴2基于旋转中心坐标Y") = nCamCenterMachineY - nCalibCenterMachineY + nCalibMachineY - g_pRobot->m_mapRobotPoint["下相机吸嘴2取料参考位"]->y;

	VAR_ROBOT("吸嘴3基于旋转中心坐标X") = nCamCenterMachineX - nCalibCenterMachineX + nCalibMachineX - g_pRobot->m_mapRobotPoint["下相机吸嘴3取料参考位"]->x;
	VAR_ROBOT("吸嘴3基于旋转中心坐标Y") = nCamCenterMachineY - nCalibCenterMachineY + nCalibMachineY - g_pRobot->m_mapRobotPoint["下相机吸嘴3取料参考位"]->y;

	VAR_ROBOT("吸嘴4基于旋转中心坐标X") = nCamCenterMachineX - nCalibCenterMachineX + nCalibMachineX - g_pRobot->m_mapRobotPoint["下相机吸嘴4取料参考位"]->x;
	VAR_ROBOT("吸嘴4基于旋转中心坐标Y") = nCamCenterMachineY - nCalibCenterMachineY + nCalibMachineY - g_pRobot->m_mapRobotPoint["下相机吸嘴4取料参考位"]->y;

	g_pRobot->Save();
	
	if (VAR_BELTA_B("允许机械手装配主板标志")) {
		MESSAGEBOX("请确认A轨（2轨）治具上是否有主板？如有，请手动拿走，否则可能导致叠板", "", false);
	}

	if (VAR_BELTB_B("允许机械手装配主板标志")) {
		MESSAGEBOX("请确认B轨（1轨）治具上是否有主板？如有，请手动拿走，否则可能导致叠板", "", false);
	}

	return emRun;
}

EnumStatus CLogicRobot::OnPause()
{
	return emRun;
}

EnumStatus CLogicRobot::OnResume()
{
	g_pRobot->RegisterCamera();

	m_bTrayCameraCaptureFinishFlag = false;

	map<CString, DWORD>::iterator it = m_mapTick.begin();
	for (; it!=m_mapTick.end(); it++)
	{
		it->second = GetTickCount();
	}

	return emRun;
}

EnumStatus CLogicRobot::OnStop()
{
	return emRun;
}

CStatus CLogicRobot::OnRun()
{
 	for (int i=0; i<4; i++)
	{
		m_stHeadProcResult[i].bFromTray = false;
		m_stHeadProcResult[i].bHeadPickOk = false;
		m_stHeadProcResult[i].nProcTimes = 0;
		m_stHeadProcResult[i].bHeadThrowFlag = false;
		m_stHeadProcResult[i].bMesNgFlag = false;
		m_stHeadProcResult[i].bHeadProcOk = false;
		m_stHeadProcResult[i].bFromTray = false;
		m_stHeadProcResult[i].nHeadPixelX = 0;
		m_stHeadProcResult[i].nHeadPixelY = 0;
		m_stHeadProcResult[i].nHeadPixelR = 0;
		m_stHeadProcResult[i].nHeadMachineX = 0;
		m_stHeadProcResult[i].nHeadMachineY = 0;
		m_stHeadProcResult[i].nHeadMachineR = 0;
	}

	if (m_pMainBoardInTray != NULL) {
		delete []m_pMainBoardInTray;
	}

	int nNumber = 0;

	nNumber = VAR_ROBOT_I("TRAY盘主板行数") * VAR_ROBOT_I("TRAY盘主板列数");

	m_pMainBoardInTray = new TRAYMAINBOARDINFO[nNumber];

	for (int i=0; i<nNumber; i++)
	{
		m_pMainBoardInTray[i].nPixelX = 0;
		m_pMainBoardInTray[i].nPixelY = 0;
		m_pMainBoardInTray[i].nPixelR = 0;
		m_pMainBoardInTray[i].bExistFlag = false;
		m_pMainBoardInTray[i].bProcOk = false;
		m_pMainBoardInTray[i].nProcTimes = 0;
		m_pMainBoardInTray[i].bMesNgFlag = false;
		m_pMainBoardInTray[i].bBadCheckNgFlag = false;
		m_pMainBoardInTray[i].bHasMaterialFlag = false;
		m_pMainBoardInTray[i].sCode2D.Empty();
	}

	for (int i=0; i<3; i++)
	{
		m_stInMainBoardInBelt[i].nPixelX = 0;
		m_stInMainBoardInBelt[i].nPixelY = 0;
		m_stInMainBoardInBelt[i].nPixelR = 0;
		m_stInMainBoardInBelt[i].bExistFlag = false;
		m_stInMainBoardInBelt[i].bProcOk = false;
		m_stInMainBoardInBelt[i].nProcTimes = 0;
		m_stInMainBoardInBelt[i].bMesNgFlag = false;
		m_stInMainBoardInBelt[i].bBadCheckNgFlag = false;
		m_stInMainBoardInBelt[i].bHasMaterialFlag = false;
		m_stInMainBoardInBelt[i].sCode2D.Empty();
	}

	memset(&m_stMarkInfo, 0, 8 * sizeof(MARKINFO));

	m_mapIndex["当前来料索引"] = 0;
	m_mapIndex["当前吸嘴索引"] = 0;

	if (VAR_BELTB_B("允许机械手装配主板标志")) {
		m_mapIndex["当前治具索引"] = 4;
	}
	else {
		m_mapIndex["当前治具索引"] = 0;
	}

	m_mapIndex["上一次取料吸嘴"] = 0;
	m_mapIndex["重复取料计数"] = 0;

	m_mapIndex["重复拍照计数"] = 0;

	m_nDnCamCaptureSequence[0] = 3;
	m_nDnCamCaptureSequence[1] = 2;
	m_nDnCamCaptureSequence[2] = 1;
	m_nDnCamCaptureSequence[3] = 0;

	m_pRobot->RegisterCamera();

	g_pCamera->SetExposureTime("Dn", VAR_ROBOT_I("下相机曝光时间"));
	g_pCamera->SetGain("Dn", VAR_ROBOT_I("下相机增益"));

	g_pCamera->SetExposureTime("Tray", VAR_ROBOT_I("TRAY相机曝光时间"));
	g_pCamera->SetGain("Tray", VAR_ROBOT_I("TRAY相机增益"));

	m_bTrayCameraCaptureFinishFlag = false;;

	m_bExcuteOnce = false;

	m_nHeadBackCnt = 0;

	VAR_MACHINE("托盘优先模式") = false;

	for (int i=0; i<3; i++)
	{
		g_pMachine->m_stInBeltStatus[i].nIndex = i;
		g_pMachine->m_stInBeltStatus[i].bInFlag = false;
		g_pMachine->m_stInBeltStatus[i].tLastPick = 0;
		g_pMachine->m_stInBeltStatus[i].tNowIn = 0;
		g_pMachine->m_stInBeltStatus[i].tNowPick = 0;
	}

	g_pMachine->m_stProductionCircle.bFree = false;

	g_pMachine->m_listInBeltStatus.clear();
	g_pMachine->m_listProductionCycle.clear();

	for (int i=0; i<4; i++)
	{
		RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumStatus(i));
		if (m_sRet == "On") {
			m_stHeadProcResult[i].bHeadPickOk = true;
			m_stHeadProcResult[i].bFromTray = true;
		}
		else {
			m_stHeadProcResult[i].bHeadPickOk = false;
			m_stHeadProcResult[i].bFromTray = false;
		}
		m_stHeadProcResult[i].bHeadThrowFlag = false;
		m_stHeadProcResult[i].bHeadProcOk = false;
		m_stHeadProcResult[i].bMesNgFlag = false;
	}

	m_mapIndex["待料计数"] = 0;

	VAR_ROBOT("主板回放标志") = false;

	VAR_ROBOT("TRAY盘进料标志") = false;

	VAR_ROBOT("TRAY盘退料标志") = false;

	m_mapFlag["皮带取料吸嘴标志"] = false;

	m_mapFlag["吸嘴气缸伸出标志"] = false;

	m_mapFlag["主板位置需手动调整报警"] = true;

	m_mapTick["纯收板模式收尾计时"] = 0;

	if (VAR_ROBOT_B("纯收板模式")) {
		RETURN_STATE(&CLogicRobot::OnRobot00, true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot25, true);
}

CStatus CLogicRobot::OnRobot00()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move("空闲等待位"));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos("空闲等待位"));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到空闲等待位", true);
	}

	m_mapIndex["待料计数"]++;

	if (m_mapIndex["待料计数"] > 2) {
		if (GetTickCount() - m_mapTick["待料计时"] > (DWORD)VAR_MACHINE_I("机台待机变灯时间")) {
			g_bWaitFlag = true;
		}
	}
	else {
		m_mapTick["待料计时"] = GetTickCount();
		g_bWaitFlag = false;
	}

	RETURN_STATE(&CLogicRobot::OnRobot00_0, true);
}

CStatus CLogicRobot::OnRobot00_0()
{
	int nCnt = 0;

	RUN_STOP_IF_ERROR(m_pRobot->InBeltAInStatus());

	if (m_sRet == "On") {
		nCnt++;
	}

	RUN_STOP_IF_ERROR(m_pRobot->InBeltBInStatus());

	if (m_sRet == "On") {
		nCnt++;
	}

	RUN_STOP_IF_ERROR(m_pRobot->InBeltCInStatus());

	if (m_sRet == "On") {
		nCnt++;
	}

	int nPickCnt = 0;

	for (int i=0; i<4; i++)
	{
		if (m_stHeadProcResult[i].bHeadPickOk) {
			nPickCnt++;
		}
	}
	
	if (VAR_ROBOT_B("纯收板模式") && nPickCnt >= 2) {
		m_nHeadBackCnt = 2;
		VAR_ROBOT("主板回放标志") = true;
		RETURN_STATE(&CLogicRobot::OnRobot56, true);
	}

	if (VAR_ROBOT_B("双主板装配模式") && nPickCnt == 2) {
		if (!VAR_BELTA_B("允许机械手装配主板标志") && !VAR_BELTB_B("允许机械手装配主板标志")) {
			if (nCnt > 0) {
				m_nHeadBackCnt = 2;
				VAR_ROBOT("主板回放标志") = true;
				RETURN_STATE(&CLogicRobot::OnRobot56, true);
			}
			else {
				RETURN_STATE(&CLogicRobot::OnRobot00, true);
			}
		}
		else {
			RETURN_STATE(&CLogicRobot::OnRobot30, true);
		}
	}

	if (nPickCnt == 4) {
		if (!VAR_BELTA_B("允许机械手装配主板标志") && !VAR_BELTB_B("允许机械手装配主板标志")) {
			if (nCnt > 0) {
				m_nHeadBackCnt = 2;
				VAR_ROBOT("主板回放标志") = true;
				RETURN_STATE(&CLogicRobot::OnRobot56, true);
			}
			else {
				RETURN_STATE(&CLogicRobot::OnRobot00, true);
			}
		}
		else {
			RETURN_STATE(&CLogicRobot::OnRobot30, true);
		}
	}
	
	if (nCnt == 0) {
		if (VAR_ROBOT_B("纯收板模式")) {
			if (m_mapTick["纯收板模式收尾计时"] == 0) {
				if (nPickCnt == 1) {
					m_mapTick["纯收板模式收尾计时"] = GetTickCount();
				}
				else {
					m_mapTick["纯收板模式收尾计时"] = 0;
				}
			}
			else {
				if (GetTickCount() - m_mapTick["纯收板模式收尾计时"] > 10000) {
					m_mapTick["纯收板模式收尾计时"] = 0;
					m_nHeadBackCnt = 1;
					VAR_ROBOT("主板回放标志") = true;
					RETURN_STATE(&CLogicRobot::OnRobot56, true);
				}
			}
			RETURN_STATE(&CLogicRobot::OnRobot00, true);
		}

		if (!VAR_MACHINE_B("托盘优先模式")) {
			m_mapTick["等待来料计时"] = GetTickCount();
			RETURN_STATE(&CLogicRobot::OnRobot00_1, true);
		}
		else {
			RETURN_STATE(&CLogicRobot::OnRobot12, true);
		}
	}
	else {
		m_mapTick["纯收板模式收尾计时"] = 0;
		RETURN_STATE(&CLogicRobot::OnRobot00_2, true);
	}
}

CStatus CLogicRobot::OnRobot00_1()
{
	int nCnt = 0;

	RUN_STOP_IF_ERROR(m_pRobot->InBeltAInStatus());

	if (m_sRet == "On") {
		nCnt++;
	}

	RUN_STOP_IF_ERROR(m_pRobot->InBeltBInStatus());

	if (m_sRet == "On") {
		nCnt++;
	}

	RUN_STOP_IF_ERROR(m_pRobot->InBeltCInStatus());

	if (m_sRet == "On") {
		nCnt++;
	}

	if (nCnt <= 0) {
		if (GetTickCount() - m_mapTick["等待来料计时"] < (DWORD)VAR_MACHINE_I("皮带待料超时")) {
			Sleep(10);
			RETURN_SELF("等待皮带来料", false);
		}
		else {
			if (!g_pMachine->m_stProductionCircle.bFree) {
				g_pMachine->m_stProductionCircle.bFree = true;
			}
			RETURN_STATE(&CLogicRobot::OnRobot12, true);
		}
	}

	RETURN_STATE(&CLogicRobot::OnRobot00_2, true);
}

CStatus CLogicRobot::OnRobot00_2()
{
	if (m_mapIndex["当前来料索引"] >= 3) {
		m_mapIndex["当前来料索引"] = 0;
		m_mapIndex["当前吸嘴索引"] = 0;
		m_mapFlag["皮带取料避位标志"] = false;
		RETURN_STATE(&CLogicRobot::OnRobot03, true);
	}

	m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].bExistFlag = false;

	switch (m_mapIndex["当前来料索引"])
	{
	case 0:
		m_mapIndex["取料计数"] = 0;
		RUN_STOP_IF_ERROR(m_pRobot->InBeltAInStatus());
		break;
	case 1:
		RUN_STOP_IF_ERROR(m_pRobot->InBeltBInStatus());
		break;
	case 2:
		if (m_mapIndex["取料计数"] < 2) {
			RUN_STOP_IF_ERROR(m_pRobot->InBeltCInStatus());
		}
		else {
			m_sRet == "Off";
		}
		break;
	default:
		RETURN_STOP();
	}

	if (m_sRet != "On") {
		m_mapIndex["当前来料索引"]++;
		RETURN_SELF("", false);
	}
	
////////////////////////////////////////// 
	int nFixtureCntA = 0, nFixtureCntB = 0, nHeadIndex = 0;

	for (int i=0; i<8; i++)
	{
		if (!m_stMarkInfo[i].bAssembleOk) {
			if (i < 4) {
				nFixtureCntA++;
			}
			else {
				nFixtureCntB++;
			}
		}
	}

	int nFixtureIndex = 0;

	if (nFixtureCntB <= nFixtureCntA) {
		nFixtureIndex = 4;
	}

	bool bFlag = false;

	for (int i=0; i<4; i++)
	{
		if (VAR_ROBOT_B("双主板装配模式") && (i == 1 || i == 3)) {
			continue;
		}

		if (m_stHeadProcResult[i].bHeadPickOk) {
			continue;
		}
		
		if (!m_stMarkInfo[i + nFixtureIndex].bAssembleOk) {
			nHeadIndex = i;
			bFlag = true;
			break;
		}
	}

	static bool bWarnOnce = false;
	if (m_mapIndex["当前来料索引"] == 0 && nHeadIndex == 3) {
		if (!bWarnOnce) {
			bWarnOnce= true;
			REPORT("吸嘴4取皮带1来料,直接跳过拍照", emLogLevelWarn);
		}
		m_mapIndex["当前来料索引"]++;
		RETURN_SELF("", false);
	}
	else {
		bWarnOnce = false;
	}
/////////////////////////////////////////////

	m_mapIndex["取料计数"]++;

	RUN_STOP_IF_ERROR(m_pRobot->UpCameraLightOn());

	RETURN_STATE(&CLogicRobot::OnRobot01, true);
}

CStatus CLogicRobot::OnRobot01()
{
	CString sPosition;

	sPosition.Format("主板进料%d拍照位", m_mapIndex["当前来料索引"] + 1);

	RUN_STOP_IF_ERROR(m_pRobot->Move(sPosition));

	g_pCamera->SetExposureTime("Up", VAR_ROBOT_I("上相机(皮带)曝光时间"));
	g_pCamera->SetGain("Up", VAR_ROBOT_I("上相机(皮带)增益"));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(sPosition));

	if (m_sRet != "Yes") {
		m_sRet.Format("等待机械手运动到主板进料%d拍照位", m_mapIndex["当前来料索引"] + 1);
		RETURN_SELF(m_sRet, true);
	}
	
	RETURN_STATE(&CLogicRobot::OnRobot02, true);
}

CStatus CLogicRobot::OnRobot02()
{
	CString sName;
	sName.Format("皮带%d进料", m_mapIndex["当前来料索引"] + 1);

	RUN_STOP_IF_ERROR(m_pRobot->ProcPrepare(m_mapIndex["当前来料索引"]));

	RUN_STOP_IF_ERROR(m_pRobot->PushImageFlow(g_pImageFlowUp[m_mapIndex["当前来料索引"]], g_pImageFlowScanCodeMainBoard[m_mapIndex["当前来料索引"]], g_pImageWndUp, "皮带来料", sName, "", true, true, -1, false, m_mapIndex["当前来料索引"]));

	RUN_STOP_IF_ERROR(m_pRobot->ProcStart(m_mapIndex["当前来料索引"]));

	RUN_STOP_IF_ERROR(g_pCamera->Trigger("Up"));

	m_mapTick["拍照计时"] = GetTickCount();

	RETURN_STATE(&CLogicRobot::OnRobot02_0, true);
}

CStatus CLogicRobot::OnRobot02_0()
{
	if (!m_pRobot->m_vImageFlow[m_mapIndex["当前来料索引"]][0].bTakePicOk) {
		if (GetTickCount() - m_mapTick["拍照计时"] > 8000) {
			CDat::IncreaseValue("上相机拍照失败数");
			REPORT("上相机拍照超时,请先点击确定后点击开始重试!", emLogLevelError);
			MESSAGEBOX("上相机拍照超时,请先点击确定后点击开始重试!", "", false);
			m_pRobot->RegisterCamera();
			RETURN_STATE(&CLogicRobot::OnRobot00_0, true);
		}
		else {
			RETURN_SELF("等待拍照完成", false);
		}
	}

	RUN_STOP_IF_ERROR(m_pRobot->UpCameraLightOff());

	m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].bExistFlag = true;

	m_mapIndex["当前来料索引"]++;

	RETURN_STATE(&CLogicRobot::OnRobot00_2, true);
}

CStatus CLogicRobot::OnRobot03()
{
	if (m_mapIndex["当前来料索引"] >= 3) {
		m_mapIndex["当前来料索引"] = 0;
		m_mapIndex["当前吸嘴索引"] = 0;

		if (VAR_ROBOT_B("纯收板模式")) {
			RETURN_STATE(&CLogicRobot::OnRobot00, true);
		}

		RETURN_STATE(&CLogicRobot::OnRobot04_1, true);
	}

	if (!m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].bExistFlag) {
		m_mapIndex["当前来料索引"]++;
		RETURN_SELF("", false)
	}

	m_mapTick["图像处理计时"] = GetTickCount();

	RETURN_STATE(&CLogicRobot::OnRobot03_0, true);
}

static bool IsJsonData(std::string strData)
{
	if (strData[0] != '{')
		return false;

	int num = 1;
	for (int i=1; i<strData.length(); ++i)
	{
		if (strData[i] == '{')
		{
			++num;
		}
		else if (strData[i] == '}')
		{
			--num;
		}

		if (num == 0)
		{
			return true;
		}
	}

	return false;
}

CStatus CLogicRobot::OnRobot03_0()
{
	CString sRet;

	bool bProcTimeOut = false;

	if (!m_pRobot->m_vImageFlow[m_mapIndex["当前来料索引"]][0].bProcOk) {
		if (GetTickCount() - m_mapTick["图像处理计时"] > 8000) {
			CDat::IncreaseValue("上相机拍照失败数");
			bProcTimeOut = true;
		}
		else {
			RETURN_SELF("等待皮带来料图像处理完成", false);
		}
	}

	vector<string> vCode;
	g_pImageFlowScanCodeMainBoard[m_mapIndex["当前来料索引"]]->GetCode(vCode);

	CString sCode2D, sTemp;
	for (int i=0; i<vCode.size(); i++)
	{
		sTemp = vCode[i].c_str();
		if (sTemp.GetLength() < 10) {
			continue;
		}

		if (!sCode2D.IsEmpty()) {
			sCode2D += ",";
		}

		sCode2D += sTemp;
	}

	REPORT("扫描到的二维码为: " + sCode2D, emLogLevelNormal);

	if (VAR_MACHINE_B("MES产品自动切换功能启用")) {
		// 重新扫码
		if (sCode2D.IsEmpty()) {
			sRet.Format("皮带来料%d二维码扫描失败!", m_mapIndex["当前来料索引"] + 1);
			REPORT(sRet, emLogLevelWarn);
			m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].nProcTimes++;
			if (m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].nProcTimes >= 3) {
				m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].nProcTimes = 0;
				MESSAGEBOX(sRet, "", false);
			}
			m_mapIndex["当前来料索引"]++;
			RETURN_STATE(&CLogicRobot::OnRobot03, true);
		}

		bool bFlag = false;

		CString sMesRes;
		bFlag = CMes::HttpPostSwitchProduct(VAR_MACHINE_S("MES产品自动切换服务器地址"), 80, sCode2D, VAR_MACHINE_S("MES产品自动切换用户名"), VAR_MACHINE_S("MES产品自动切换密码"), VAR_MACHINE_S("MES产品自动切换工厂"), &sMesRes);

		REPORT("Mes产品自动切换回传数据: " + sMesRes, emLogLevelNormal);

		if (!bFlag) {
			MESSAGEBOX("MES产品自动切换服务器传回数据异常:\n" + sMesRes, "", false);
			RETURN_STOP();
		}

		if (!IsJsonData(sMesRes.GetBuffer())) {
			MESSAGEBOX("MES产品自动切换服务器传回数据格式不正确:\n" + sMesRes, "", false);
			RETURN_STOP();
		}
		
		Json::Value root;
		root.clear();

		char* cJson = sMesRes.GetBuffer(0);
		sMesRes.ReleaseBuffer();

		Json::Reader reader;
		reader.parse(cJson, root);

		if (root["recipeAddress"].isNull()) {
			MESSAGEBOX("MES产品自动切换服务器传回数据不存在字段[recipeAddress]!", "", false);
			RETURN_STOP();
		}

		CString sPath = root["recipeAddress"].asCString();
		CString sName = sPath.Mid(sPath.ReverseFind('/') + 1);
		CString sPro = sName.Left(sName.ReverseFind('.'));
		if (sName.IsEmpty() || sPro.IsEmpty()) {
			MESSAGEBOX("MES传回服务器路径不正确", "", false);
			RETURN_STOP();
		}

		if (!root["lotId"].isNull()) {
			sCode2D = root["lotId"].asCString();
		}

		CString strPath = CString(GetModulePath().c_str());
		CString sDownloadPath = strPath + "\\Download\\";
		CString strProPath = strPath + "\\Pro\\";

		CreatePath(sDownloadPath.GetBuffer());
		sDownloadPath.ReleaseBuffer();

		sDownloadPath += sName;

		CString sCurrentPro;
		sCurrentPro = CSys::m_strPro.c_str();

		if (sPro != sCurrentPro) {
			REPORT(CString("产品发生变化，新产品为 : [") + sPro + "]", emLogLevelNormal);

			for (int i = 0; ; i++)
			{
				vector<string> vStrPro = ListSubDir(strProPath.GetBuffer());
				bool bFlag = false;
				for (unsigned int j=0; j<vStrPro.size(); j++)
				{
					if (sPro == CString(vStrPro[j].c_str())) {
						if (i == 0) {
							REPORT("本地已存在产品记录", emLogLevelNormal);
						}
						bFlag = true;
						break;
					}
				}

				if (!bFlag) {
					if (i == 0) {
						REPORT(CString("产品[") + sPro + "]本地无记录，开始从服务器下载...", emLogLevelWarn);
						if (!FTP_Download((LPSTR)(LPCSTR)VAR_MACHINE_S("MES产品资料存储地址"), (LPSTR)(LPCSTR)VAR_MACHINE_S("MES产品资料访问用户名"), (LPSTR)(LPCSTR)VAR_MACHINE_S("MES产品资料访问密码"), (LPSTR)(LPCSTR)sPath, (LPSTR)(LPCSTR)sDownloadPath)) {
							REPORT("从服务器下载产品失败", emLogLevelError);
							continue;
						}

						CFileFind ff;
						if (ff.FindFile(sDownloadPath)) {
							if (UnCompressDirectory(sDownloadPath, strProPath)) {
								REPORT("解压缩成功", emLogLevelNormal);
							}
							else {
								REPORT("解压缩失败", emLogLevelError);
							}
						}
						else {
							REPORT("未找到下载的数据", emLogLevelError);
						}
						continue;
					}
					else {
						MESSAGEBOX(CString("产品") + sPro + "本地与服务器都无记录，请在本地新建产品并上传服务器", "", emLogLevelWarn);
						CLogicMgr::Stop();
						RETURN_STOP();
					}
				}

				if (AfxMessageBox(CString("是否切换到新产品 : ") + sPro + "?", MB_YESNO) == IDYES) {
					CSys::m_strPro = sPro;
					::SendMessage(g_hMainWnd, UM_PRODUCT_SWITCH, 0, 0);
					CLogicMgr::Stop();
					RETURN_STOP();
				}
				else {
					MESSAGEBOX("请取走产品后点击确认!", "", false);
					m_mapIndex["当前来料索引"]++;
					RETURN_STATE(&CLogicRobot::OnRobot03, true);
				}
			}
		}
	}
	
	if (VAR_MACHINE_B("MES过站功能启用")) {
		if (!sCode2D.IsEmpty()) {		
			CString sMesRes;
			bool bFlag = false;
			bFlag = CMes::HttpPostPassStation(VAR_MACHINE_S("MES过站服务器地址"), 80, sCode2D, VAR_MACHINE_S("MES过站拉线ID"), VAR_MACHINE_S("MES过站工序"), VAR_MACHINE_S("MES过站用户名"), VAR_MACHINE_S("MES过站密码"), VAR_MACHINE_S("MES过站工厂"), &sMesRes);		
			REPORT("Mes过站回传数据: " + sMesRes, emLogLevelNormal);
			/*
			if (!bFlag && sMesRes.Find("工序不匹配") < 0) {
				MESSAGEBOX("Mes过站回传数据: " + sMesRes, "", false);
			}*/
		}
	}

	double nResultPixelX, nResultPixelY, nResultPixelR = 0;
	bool bExistFlag = false;

	if (!bProcTimeOut) {
		m_sRet = g_pImageFlowUp[m_mapIndex["当前来料索引"]]->GetResult(nResultPixelX, nResultPixelY, nResultPixelR, bExistFlag);
	}
	else {
		m_sRet = "图像处理超时";
	}

	if (m_sRet != "OK") {
		if (bProcTimeOut) {
			sRet.Format("皮带来料%d图像处理超时!", m_mapIndex["当前来料索引"] + 1);
		}
		else {
			sRet.Format("皮带来料%d图像处理失败!", m_mapIndex["当前来料索引"] + 1);
		}

		REPORT(sRet, emLogLevelWarn);

		m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].nProcTimes++;
		if (m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].nProcTimes >= 3) {
			m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].nProcTimes = 0;
			MESSAGEBOX(sRet, "", false);
		}

		m_mapIndex["当前来料索引"]++;

		RETURN_STATE(&CLogicRobot::OnRobot03, true);
	}

	m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].nProcTimes = 0;

	if (fabs(nResultPixelR) > 30) {
		sRet.Format("皮带来料%d角度超出范围!", m_mapIndex["当前来料索引"] + 1);
		REPORT(sRet, emLogLevelWarn);
		MESSAGEBOX(sRet, "", false);
		m_mapIndex["当前来料索引"]++;
		RETURN_STATE(&CLogicRobot::OnRobot03, true);
	}

	m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].nPixelX = nResultPixelX;
	m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].nPixelY = nResultPixelY;
	m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].nPixelR = nResultPixelR;
	m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].sCode2D = m_pRobot->m_vImageFlow[m_mapIndex["当前来料索引"]][0].sCode2D;
	
	RETURN_STATE(&CLogicRobot::OnRobot04, true);
}

CStatus CLogicRobot::OnRobot04()
{
	int nFixtureCntA = 0, nFixtureCntB = 0;

	for (int i=0; i<8; i++)
	{
		if (!m_stMarkInfo[i].bAssembleOk) {
			if (i < 4) {
				nFixtureCntA++;
			}
			else {
				nFixtureCntB++;
			}
		}
	}

	int nFixtureIndex = 0;

	if (nFixtureCntB <= nFixtureCntA) {
		nFixtureIndex = 4;
	}

	bool bFlag = false;

	for (int i=0; i<4; i++)
	{
		if (VAR_ROBOT_B("双主板装配模式") && (i == 1 || i == 3)) {
			continue;
		}

		if (m_stHeadProcResult[i].bHeadPickOk) {
			continue;
		}
		
		if (!m_stMarkInfo[i + nFixtureIndex].bAssembleOk) {
			m_mapIndex["当前吸嘴索引"] = i;
			bFlag = true;
			break;
		}
	}

	if (!bFlag) {
		RETURN_STATE(&CLogicRobot::OnRobot04_1, true);
	}

	if ((m_mapIndex["当前来料索引"] == 0) && m_mapIndex["当前吸嘴索引"] == 3) {
		CString sRet;
		sRet.Format("吸嘴%d跳过取皮带1来料", m_mapIndex["当前吸嘴索引"] + 1);
		REPORT(sRet, emLogLevelWarn);

		bool bFlag = false;
		for(int i=1; i<3; i++)
		{
			if (m_stInMainBoardInBelt[i].bExistFlag) {
				bFlag = true;
			}
		}

		if (bFlag) {
			RETURN_STATE(&CLogicRobot::OnRobot11, true);
		}
		else {
			RETURN_STATE(&CLogicRobot::OnRobot12, true);
		}
	}

	CString sPosCapture;
	sPosCapture.Format("主板进料%d拍照位", m_mapIndex["当前来料索引"] + 1);

	m_stDstRobPnt = *m_pRobot->m_mapRobotPoint[sPosCapture];

	double nUpCamOffMachineX = 0, nUpCamOffMachineY = 0;
	nUpCamOffMachineX = VAR_ROBOT_D("上相机中心基于旋转中心坐标X");
	nUpCamOffMachineY = VAR_ROBOT_D("上相机中心基于旋转中心坐标Y");

	CCvImage::Rotate(0, 0, nUpCamOffMachineX, -nUpCamOffMachineY, m_pRobot->m_mapRobotPoint[sPosCapture]->r - m_pRobot->m_mapRobotPoint["空闲等待位"]->r, nUpCamOffMachineX, nUpCamOffMachineY);

	double nUpCamCenterMachineX = 0, nUpCamCenterMachineY = 0;
	g_pImageCalibrateUp->TransToMachine(VAR_ROBOT_D("上相机中心坐标X"), VAR_ROBOT_D("上相机中心坐标Y"), nUpCamCenterMachineX, nUpCamCenterMachineY);

	double nUpCalcMachineX = 0, nUpCalcMachineY = 0;
	g_pImageCalibrateUp->TransToMachine(m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].nPixelX, m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].nPixelY, nUpCalcMachineX, nUpCalcMachineY);

	double nUpCalcOffX = 0, nUpCalcOffY = 0;
	nUpCalcOffX = nUpCalcMachineX - nUpCamCenterMachineX;
	nUpCalcOffY = nUpCalcMachineY - nUpCamCenterMachineY;

 	CCvImage::Rotate(0, 0, nUpCalcOffX, nUpCalcOffY, m_pRobot->m_mapRobotPoint[sPosCapture]->r - m_pRobot->m_mapRobotPoint["空闲等待位"]->r, nUpCalcOffX, nUpCalcOffY);
	
	CString sPosRefer;
	sPosRefer.Format("下相机吸嘴%d取料参考位", m_mapIndex["当前吸嘴索引"] + 1);

	double nRotateAngle = 0;
	nRotateAngle = m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].nPixelR + m_stDstRobPnt.r - m_pRobot->m_mapRobotPoint[sPosRefer]->r;

	CString sPosCenter;
	sPosCenter.Format("吸嘴%d基于旋转中心坐标", m_mapIndex["当前吸嘴索引"] + 1);

	double nCalcHeadMachineX = 0, nCalcHeadMachineY = 0;

	CCvImage::Rotate(0, 0, VAR_ROBOT_D(sPosCenter + "X"), -VAR_ROBOT_D(sPosCenter + "Y"), nRotateAngle, nCalcHeadMachineX, nCalcHeadMachineY);
	
	CString sFeed;
	sFeed.Format("皮带来料吸嘴%d取料补偿", m_mapIndex["当前吸嘴索引"] + 1);

	double nFeedX = 0, nFeedY = 0;

	CCvImage::Rotate(0, 0, VAR_ROBOT_D(sFeed + "X"), -VAR_ROBOT_D(sFeed + "Y"), m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].nPixelR, nFeedX, nFeedY);

	m_stDstRobPnt.x = m_pRobot->m_mapRobotPoint[sPosCapture]->x + nUpCamOffMachineX - nUpCalcOffX - nCalcHeadMachineX + nFeedX;
	m_stDstRobPnt.y = m_pRobot->m_mapRobotPoint[sPosCapture]->y - nUpCamOffMachineY - nUpCalcOffY + nCalcHeadMachineY - nFeedY;
	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;
	m_stDstRobPnt.r = m_pRobot->m_mapRobotPoint[sPosCapture]->r + m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].nPixelR;
	
	m_mapIndex["待料计数"] = 0;

	g_bWaitFlag = false;

	if (m_mapFlag["皮带取料避位标志"] && fabs(m_stDstRobPnt.r - m_stLastDstRobPnt.r) > 90 && m_stLastDstRobPnt.x < m_pRobot->m_mapRobotPoint["空闲等待位"]->x) {
		m_stLastDstRobPnt.x = m_pRobot->m_mapRobotPoint["空闲等待位"]->x;
		RETURN_STATE(&CLogicRobot::OnRobot04_0, true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot05, true);
}

CStatus CLogicRobot::OnRobot04_0()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stLastDstRobPnt));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stLastDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到避位位", true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot05, true);
}

CStatus CLogicRobot::OnRobot04_1()
{
	int nFixtureCntA = 0, nFixtureCntB = 0;

	for (int i=0; i<8; i++)
	{
		if (!m_stMarkInfo[i].bAssembleOk) {
			if (i < 4) {
				nFixtureCntA++;
			}
			else {
				nFixtureCntB++;
			}
		}
	}

	int nFixtureCnt = 0;
	nFixtureCnt = nFixtureCntA < nFixtureCntB ? nFixtureCntA : nFixtureCntB;

	int nCnt = 0;
	for (int i=0; i<4; i++)
	{
		if (m_stHeadProcResult[i].bHeadPickOk) {
			nCnt++;
		}
	}

	if ((nCnt > 0 && nCnt == nFixtureCnt) || (nCnt > 0 && nCnt % 2 == 0)) {
		RUN_STOP_IF_ERROR(m_pRobot->Move("空闲等待位"));
		RUN_STOP_IF_ERROR(m_pRobot->IsInPos("空闲等待位"));
		if (m_sRet != "Yes") {
			RETURN_SELF("等待机械手运动到空闲等待位", true);
		}
	}

	if ((nCnt > 0 && nCnt == nFixtureCnt) || (nCnt > 0 && nCnt % 2 == 0)) {
		RETURN_STATE(&CLogicRobot::OnRobot30, true);
	}
	else {
		RETURN_STATE(&CLogicRobot::OnRobot00_0, true);
	}
}

CStatus CLogicRobot::OnRobot05()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		m_sRet.Format("等待机械手运动到吸嘴%d待取料位", m_mapIndex["当前吸嘴索引"] + 1);
		RETURN_SELF(m_sRet, true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot06, true);
}

CStatus CLogicRobot::OnRobot06()
{
	CString sPosition;

	sPosition = "主板进料正取料位";

	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint[sPosition]->z + VAR_ROBOT_D("皮带取料高度偏移");

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumOn(m_mapIndex["当前吸嘴索引"]));

	RETURN_STATE(&CLogicRobot::OnRobot07, true);
}

CStatus CLogicRobot::OnRobot07()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt, true, false));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderOn(m_mapIndex["当前吸嘴索引"]));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderStatus(m_mapIndex["当前吸嘴索引"]));

	if (m_sRet != "On") {
		if (m_mapFlag["吸嘴气缸伸出标志"]) {
			if (GetTickCount() - m_mapTick["吸嘴气缸伸出计时"] < 3000) {
				m_sRet.Format("等待吸嘴气缸%d伸出", m_mapIndex["当前吸嘴索引"] + 1);
				RETURN_SELF(m_sRet, true);
			}
		}
		else {
			m_mapFlag["吸嘴气缸伸出标志"] = true;
			m_mapTick["吸嘴气缸伸出计时"] = GetTickCount();
			m_sRet.Format("等待吸嘴气缸%d伸出", m_mapIndex["当前吸嘴索引"] + 1);
			RETURN_SELF(m_sRet, true);
		}
	}

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		m_sRet.Format("等待机械手运动到吸嘴%d取料位", m_mapIndex["当前吸嘴索引"] + 1);
		RETURN_SELF(m_sRet, true);
	}

	m_mapFlag["吸嘴气缸伸出标志"] = false;

	m_mapTick["吸嘴吸料计时"] = GetTickCount();

	RETURN_STATE(&CLogicRobot::OnRobot08, true);
}

CStatus CLogicRobot::OnRobot08()
{
	if (GetTickCount() - m_mapTick["吸嘴吸料计时"] < (DWORD)VAR_ROBOT_I("吸嘴吸料延时")) {
		RETURN_SELF("", false);
	}

	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;

	RETURN_STATE(&CLogicRobot::OnRobot09, true);
}

CStatus CLogicRobot::OnRobot09()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt, true, true, NULL, VAR_ROBOT_I("机械手Z轴上升速度百分比")));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderOff(m_mapIndex["当前吸嘴索引"]));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderStatus(m_mapIndex["当前吸嘴索引"]));

	if (m_sRet != "Off") {
		m_sRet.Format("等待吸嘴气缸%d缩回", m_mapIndex["当前吸嘴索引"] + 1);
		RETURN_SELF(m_sRet, true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		m_sRet.Format("等待机械手运动到吸嘴%d待取料位", m_mapIndex["当前吸嘴索引"] + 1);
		RETURN_SELF(m_sRet, true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot10, true);
}

CStatus CLogicRobot::OnRobot10()
{
	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumStatus(m_mapIndex["当前吸嘴索引"]));

	if (m_sRet == "On") {
		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bFromTray = false;
		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadPickOk = true;

		if (m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].bBadCheckNgFlag || m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].bMesNgFlag) {
			m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadThrowFlag = true;
		}
		else {
			m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadThrowFlag = false;
		}

		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bMesNgFlag = m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].bMesNgFlag;
		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].sCode2D = m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].sCode2D;

			// 更新生产开始时间
			CTime startTime = CTime::GetCurrentTime();
			CString sStartTime = startTime.Format("%Y-%m-%d %H:%M:%S");
			CDat::UpdateValue("生产开始时间", sStartTime);

		g_pMachine->m_stInBeltStatus[m_mapIndex["当前来料索引"]].tNowPick = GetTickCount();
		g_pMachine->m_listInBeltStatus.push_back(g_pMachine->m_stInBeltStatus[m_mapIndex["当前来料索引"]]);

		CString strInBeltMonitorMsg;
		strInBeltMonitorMsg.Format("皮带%d来料信息：%d, %d, %d", m_mapIndex["当前来料索引"] + 1, g_pMachine->m_stInBeltStatus[m_mapIndex["当前来料索引"]].tNowIn, g_pMachine->m_stInBeltStatus[m_mapIndex["当前来料索引"]].tNowPick, g_pMachine->m_stInBeltStatus[m_mapIndex["当前来料索引"]].tNowPick - g_pMachine->m_stInBeltStatus[m_mapIndex["当前来料索引"]].tNowIn);

		REPORT(strInBeltMonitorMsg, emLogLevelNormal);

		while (true)
		{
			if (g_pMachine->m_listInBeltStatus.size() > VAR_MACHINE_I("皮带来料监控数量")) {
				g_pMachine->m_listInBeltStatus.pop_front();
			}
			else {
				break;
			}
		}

		if (true) {
			CString strInBeltStatus = "皮带来料监控信息:";

			list<INBELTSTATUS>::iterator it = g_pMachine->m_listInBeltStatus.begin();
			for (; it != g_pMachine->m_listInBeltStatus.end(); it++)
			{
				if (it->tNowIn > 0 && it->tNowPick - it->tNowIn > (DWORD)VAR_MACHINE_I("皮带拥堵超时")) {
					strInBeltStatus += " 1";
				}
				else {
					strInBeltStatus += " 0";
				}
			}

			REPORT(strInBeltStatus, emLogLevelNormal);
		}

		if (g_pMachine->m_listInBeltStatus.size() >= VAR_MACHINE_I("皮带来料监控数量")) {
			g_pMachine->m_listInBeltStatus.pop_front();

			int nBusyCnt = 0;

			if (VAR_MACHINE_B("托盘优先模式")) {				
				list<INBELTSTATUS>::iterator it = g_pMachine->m_listInBeltStatus.begin();
				for (; it != g_pMachine->m_listInBeltStatus.end(); it++)
				{
					if (it->tNowIn > 0 && it->tNowPick - it->tNowIn > (DWORD)VAR_MACHINE_I("皮带拥堵超时")) {
						nBusyCnt++;
					}
				}

				if (nBusyCnt >= VAR_MACHINE_I("皮带来料拥堵数量")) {
					CString str;
					str.Format("在连续%d次皮带来料中，出现%d次皮带拥堵,切换到皮带优先模式", VAR_MACHINE_I("皮带来料监控数量"), VAR_MACHINE_I("皮带来料拥堵数量"));
					REPORT(str, emLogLevelWarn);
					VAR_MACHINE("托盘优先模式") = false;
				}
			}
		}

		g_pMachine->m_stInBeltStatus[m_mapIndex["当前来料索引"]].tNowIn = 0;
		g_pMachine->m_stInBeltStatus[m_mapIndex["当前来料索引"]].tNowPick = 0;
		g_pMachine->m_stInBeltStatus[m_mapIndex["当前来料索引"]].tLastPick = GetTickCount();
		g_pMachine->m_stInBeltStatus[m_mapIndex["当前来料索引"]].bInFlag = false;
	}
	else {
		CString sHead;
		sHead.Format("%d号吸嘴取板失败数", m_mapIndex["当前吸嘴索引"] + 1);
		CDat::IncreaseValue(sHead);
		REPORT("皮带来料取料失败，请确认产品是否放反！", emLogLevelWarn);
		MESSAGEBOX("皮带来料取料失败，请确认产品是否放反！", "", false);
		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadPickOk = false;
	}

	m_stInMainBoardInBelt[m_mapIndex["当前来料索引"]].bExistFlag = false;

	m_mapFlag["皮带取料避位标志"] = true;
	m_mapFlag["主板位置需手动调整报警"] = true;

	m_stLastDstRobPnt = m_stDstRobPnt;

	RETURN_STATE(&CLogicRobot::OnRobot11, true);
}

CStatus CLogicRobot::OnRobot11()
{
	m_mapIndex["当前来料索引"]++;

	RETURN_STATE(&CLogicRobot::OnRobot03, true);
}

CStatus CLogicRobot::OnRobot12()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move("空闲等待位"));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos("空闲等待位"));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到空闲等待位", true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot13, true);
}

CStatus CLogicRobot::OnRobot13()
{
	if (!VAR_TRAY_B("满TRAY盘进料完成标志")) {
		RETURN_STATE(&CLogicRobot::OnRobot00, true);
	}

	if (m_bTrayCameraCaptureFinishFlag) {
		RETURN_STATE(&CLogicRobot::OnRobot16, true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->TrayCameraLightOn());

	RETURN_STATE(&CLogicRobot::OnRobot14, true);
}

CStatus CLogicRobot::OnRobot14()
{
	RUN_STOP_IF_ERROR(m_pRobot->ProcPrepare(0));

	RUN_STOP_IF_ERROR(m_pRobot->PushImageFlow(g_pImageFlowTray, NULL, g_pImageWndTray, "TRAY", "", "", false, false, -1, false, 0));

	RUN_STOP_IF_ERROR(m_pRobot->ProcStart(0));

	RUN_STOP_IF_ERROR(g_pCamera->Trigger("Tray"));
	
	m_mapTick["拍照计时"] = GetTickCount();

	RETURN_STATE(&CLogicRobot::OnRobot15, true);
}

CStatus CLogicRobot::OnRobot15()
{
	if (!m_pRobot->m_vImageFlow[0][0].bProcOk) {
		if (GetTickCount() - m_mapTick["拍照计时"] > 8000) {
			CDat::IncreaseValue("顶部相机拍照失败数");
			REPORT("TRAY相机拍照超时,即将重新拍照!", emLogLevelError);
			MESSAGEBOX("TRAY相机拍照超时,即将重新拍照!", "", false);
			m_pRobot->TrayCameraLightOn();
			m_pRobot->RegisterCamera();
			RETURN_STATE(&CLogicRobot::OnRobot12, true);
		}
		else {
			RETURN_SELF("等待拍照完成", false);
		}
	}

	RUN_STOP_IF_ERROR(m_pRobot->TrayCameraLightOff());

	m_nTrayExistSum = 0;
	m_nPickSum = 0;

	bool bHasMainBoard = false, bExistFlag = false, bAngleOutOfRange = true;

	double nAngle = 0;

	for (int i=0; i<VAR_ROBOT_I("TRAY盘主板行数"); i++)
	{
		for (int j=0; j<VAR_ROBOT_I("TRAY盘主板列数"); j++)
		{
			m_sRet = g_pImageFlowTray->GetResult(m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelX, m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelY, m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelR, bExistFlag, 0, i, j, 0);
			
			if (m_sRet == "OK") {
				nAngle = fabs(m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelR);
// 				if (nAngle > 90) {
// 					nAngle = 180 - nAngle;
// 				}

				if (nAngle >= 0 && nAngle <= 30) {
					bAngleOutOfRange = false;
				}
			}

			if (m_sRet == "OK") {
				m_nTrayExistSum++;
				bHasMainBoard = true;
				m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bExistFlag = true;
			}
			else {
				m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bExistFlag = false;
			}

			if (m_sRet == "OK" || bExistFlag) {
				m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bHasMaterialFlag = true;
			}
		}
	}

	if (bAngleOutOfRange && bHasMainBoard) {
		REPORT("Tray盘中主板角度过大，请手动调整主板角度", emLogLevelWarn);
		MESSAGEBOX("Tray盘中主板角度过大，请手动调整主板角度", "", false);
		RETURN_STATE(&CLogicRobot::OnRobot12, true);
	}

	if (!bHasMainBoard) {
		bool bExistAndNotOKFlag = false;
		for (int i=0; i<VAR_ROBOT_I("TRAY盘主板行数"); i++)
		{
			for (int j=0; j<VAR_ROBOT_I("TRAY盘主板列数"); j++)
			{
				m_sRet = g_pImageFlowTray->GetResult(m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelX, m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelY, m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelR, bExistFlag, 0, i, j, 0);
				if (m_sRet != "OK" && bExistFlag) {
					bExistAndNotOKFlag = true;
					break;
				}
			}
		}

		if (bExistAndNotOKFlag) {
			REPORT("Tray盘中存在未识别的主板，请手动取走", emLogLevelWarn);
			MESSAGEBOX("Tray盘中存在未识别的主板，请手动取走", "", false);
		}

		for (int i=0; i<VAR_ROBOT_I("TRAY盘主板行数"); i++)
		{
			for (int j=0; j<VAR_ROBOT_I("TRAY盘主板列数"); j++)
			{
				m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bHasMaterialFlag = false;
			}
		}

		VAR_TRAY("满TRAY盘进料完成标志") = false;
		VAR_TRAY("空TRAY盘进料完成标志") = false;

		VAR_ROBOT("TRAY盘进料标志") = true;

		m_mapIndex["当前吸嘴索引"] = 0;
		RETURN_STATE(&CLogicRobot::OnRobot12, true);
	}

	m_bTrayCameraCaptureFinishFlag = true;

	m_mapFlag["成功取料标志"] = false;

	RETURN_STATE(&CLogicRobot::OnRobot16, true);
}

CStatus CLogicRobot::OnRobot16()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move("空闲等待位"));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos("空闲等待位"));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到空闲等待位", true);
	}

	m_mapIndex["当前吸嘴索引"] = 0;

	RETURN_STATE(&CLogicRobot::OnRobot16_0, true);
}

CStatus CLogicRobot::OnRobot16_0()
{
	if (m_mapIndex["当前吸嘴索引"] >= 4) {
		m_mapIndex["当前吸嘴索引"] = 0;
		RETURN_STATE(&CLogicRobot::OnRobot25_0, true);
	}

	if (m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadPickOk) {
		m_mapIndex["当前吸嘴索引"]++;
		RETURN_SELF("", false);
	}

	if (VAR_ROBOT_B("双主板装配模式") && (m_mapIndex["当前吸嘴索引"] == 1 || m_mapIndex["当前吸嘴索引"] == 3)) {
		m_mapIndex["当前吸嘴索引"]++;
		RETURN_SELF("", false);
	}

	int nFixtureCntA = 0, nFixtureCntB = 0;

	for (int i=0; i<8; i++)
	{
		if (!m_stMarkInfo[i].bAssembleOk) {
			if (i < 4) {
				nFixtureCntA++;
			}
			else {
				nFixtureCntB++;
			}
		}
	}

	int nFixtureIndex = 0;

	if (nFixtureCntB <= nFixtureCntA) {
		nFixtureIndex = 4;
	}

	if (m_stMarkInfo[m_mapIndex["当前吸嘴索引"] + nFixtureIndex].bAssembleOk) {
		m_mapIndex["当前吸嘴索引"]++;
		RETURN_SELF("", false);
	}
	
	RETURN_STATE(&CLogicRobot::OnRobot17, true);
}

CStatus CLogicRobot::OnRobot17()
{
	m_mapIndex["TRAY盘主板索引"] = -1;

	bool bExistFlag = false;

	double nAngle = 0;

	int nRowIndex = 0, nColIndex = 0;

	switch (m_mapIndex["当前吸嘴索引"])
	{
	case 0:
		for (int i=VAR_ROBOT_I("TRAY盘主板行数") - 1; i>=0; i--)
		{
			for (int j=VAR_ROBOT_I("TRAY盘主板列数") - 1; j>= 0; j--)
			{
				if (m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bExistFlag) {
					bExistFlag = true;
					if (fabs(m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelR) > 30) {
						continue;
					}

					if (i == 0) {
						continue;
					}

					nAngle = fabs(m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelR);
					if (nAngle > 90) {
						nAngle = 180 - nAngle;
					}

					if (nAngle > 30) {
						continue;
					}

					m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["TRAY盘正取放位"];

					nRowIndex = i;
					nColIndex = j;
					m_mapIndex["TRAY盘主板索引"] = i * VAR_ROBOT_I("TRAY盘主板列数") + j;
					break;
				}
			}
			if (m_mapIndex["TRAY盘主板索引"] >= 0) {
				break;
			}
		}
		break;
	case 1:
		for (int i=0; i<VAR_ROBOT_I("TRAY盘主板行数"); i++)
		{
			for (int j=VAR_ROBOT_I("TRAY盘主板列数") - 1; j>= 0; j--)
			{
				if (m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bExistFlag) {
					bExistFlag = true;
					if (fabs(m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelR) > 30) {
						continue;
					}

					if (i == VAR_ROBOT_I("TRAY盘主板行数") - 1) {
						continue;
					}

					nAngle = fabs(m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelR);
					if (nAngle > 90) {
						nAngle = 180 - nAngle;
					}

					if (nAngle > 30) {
						continue;
					}

					m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["TRAY盘正取放位"];

					nRowIndex = i;
					nColIndex = j;
					m_mapIndex["TRAY盘主板索引"] = i * VAR_ROBOT_I("TRAY盘主板列数") + j;
					break;
				}
			}
			if (m_mapIndex["TRAY盘主板索引"] >= 0) {
				break;
			}
		}
		break;
	case 2:
		for (int i=0; i<VAR_ROBOT_I("TRAY盘主板行数"); i++)
		{
			for (int j=0; j<VAR_ROBOT_I("TRAY盘主板列数"); j++)
			{
				if (m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bExistFlag) {
					bExistFlag = true;
					if (fabs(m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelR) > 30) {
						continue;
					}

					if (i == VAR_ROBOT_I("TRAY盘主板行数") - 1) {
						continue;
					}

					nAngle = fabs(m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelR);
					if (nAngle > 90) {
						nAngle = 180 - nAngle;
					}

					if (nAngle > 30) {
						continue;
					}

					m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["TRAY盘正取放位"];

					nRowIndex = i;
					nColIndex = j;
					m_mapIndex["TRAY盘主板索引"] = i * VAR_ROBOT_I("TRAY盘主板列数") + j;
					break;
				}
			}
			if (m_mapIndex["TRAY盘主板索引"] >= 0) {
				break;
			}
		}
		break;
	case 3:
		for (int i=VAR_ROBOT_I("TRAY盘主板行数") - 1; i>=0; i--)
		{
			for (int j=0; j<VAR_ROBOT_I("TRAY盘主板列数"); j++)
			{
				if (m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bExistFlag) {
					bExistFlag = true;
					if (fabs(m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelR) > 30) {
						continue;
					}

					if (i == 0) {
						continue;
					}

					nAngle = fabs(m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelR);
					if (nAngle > 90) {
						nAngle = 180 - nAngle;
					}

					if (nAngle > 30) {
						continue;
					}

					m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["TRAY盘正取放位"];

					nRowIndex = i;
					nColIndex = j;
					m_mapIndex["TRAY盘主板索引"] = i * VAR_ROBOT_I("TRAY盘主板列数") + j;
					break;
				}
			}
			if (m_mapIndex["TRAY盘主板索引"] >= 0) {
				break;
			}
		}
		break;
	default:break;
	}
	
	if (m_mapIndex["TRAY盘主板索引"] < 0) {	// 吸嘴无法取到合适位置的料
		if (bExistFlag && m_mapFlag["主板位置需手动调整报警"]) {
			CString sErrMsg;
			sErrMsg.Format("吸嘴%d无法取到合适的料,请将料放到中间位置", m_mapIndex["当前吸嘴索引"] + 1);
			REPORT(sErrMsg, emLogLevelWarn);
			m_bTrayCameraCaptureFinishFlag = false;
			m_mapFlag["主板位置需手动调整报警"] = false;
		}

		m_mapIndex["当前吸嘴索引"]++;

		RETURN_STATE(&CLogicRobot::OnRobot16_0, true);
	}
	
	m_mapFlag["主板位置需手动调整报警"] = true;

	double nCalcCenterMachineX = 0, nCalcCenterMachineY = 0;

	g_pImageCalibrateTray->TransToMachine(m_pMainBoardInTray[m_mapIndex["TRAY盘主板索引"]].nPixelX, m_pMainBoardInTray[m_mapIndex["TRAY盘主板索引"]].nPixelY, nCalcCenterMachineX, nCalcCenterMachineY);

	double nRotateAngle = 0;

	m_sRet.Format("下相机吸嘴%d取料参考位", m_mapIndex["当前吸嘴索引"] + 1);

	if (fabs(m_pMainBoardInTray[m_mapIndex["TRAY盘主板索引"]].nPixelR) >= 90) {
		if (m_pMainBoardInTray[m_mapIndex["TRAY盘主板索引"]].nPixelR < 0) {
			nRotateAngle = m_pMainBoardInTray[m_mapIndex["TRAY盘主板索引"]].nPixelR + 180 + m_pRobot->m_mapRobotPoint["TRAY盘反取放位"]->r - m_pRobot->m_mapRobotPoint[m_sRet]->r;
		}
		else {
			nRotateAngle = m_pMainBoardInTray[m_mapIndex["TRAY盘主板索引"]].nPixelR - 180 + m_pRobot->m_mapRobotPoint["TRAY盘反取放位"]->r - m_pRobot->m_mapRobotPoint[m_sRet]->r;
		}
	}
	else {
		nRotateAngle = m_pMainBoardInTray[m_mapIndex["TRAY盘主板索引"]].nPixelR + m_pRobot->m_mapRobotPoint["TRAY盘正取放位"]->r - m_pRobot->m_mapRobotPoint[m_sRet]->r;
	}

	double nCalcHeadMachineX = 0, nCalcHeadMachineY = 0, nCalcUpCameraCenterMachineX = 0, nCalcUpCameraCenterMachineY = 0;

	m_sRet.Format("吸嘴%d基于旋转中心坐标", m_mapIndex["当前吸嘴索引"] + 1);

	CCvImage::Rotate(0, 0, VAR_ROBOT_D(m_sRet + "X"), -VAR_ROBOT_D(m_sRet + "Y"), nRotateAngle , nCalcHeadMachineX, nCalcHeadMachineY);

	TRACE("吸嘴%d基于旋转中心坐标旋转%.03f度后： %.03f, %.03f\n", m_mapIndex["当前吸嘴索引"] + 1, -nRotateAngle, nCalcHeadMachineX, nCalcHeadMachineY);

	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;

	CString sFeedX, sFeedY;
	sFeedX.Format("TRAY盘来料吸嘴%d取料补偿X", m_mapIndex["当前吸嘴索引"] + 1);
	sFeedY.Format("TRAY盘来料吸嘴%d取料补偿Y", m_mapIndex["当前吸嘴索引"] + 1);

	double nFeedX = 0, nFeedY = 0;

	CCvImage::Rotate(0, 0, VAR_ROBOT_D(sFeedX), -VAR_ROBOT_D(sFeedY), m_pMainBoardInTray[m_mapIndex["TRAY盘主板索引"]].nPixelR, nFeedX, nFeedY);

	m_sRet.Format("下相机吸嘴%d拍照位", m_mapIndex["当前吸嘴索引"] + 1);

	m_stDstRobPnt.x = nCalcCenterMachineX + VAR_ROBOT_D("上相机中心基于旋转中心坐标X") - nCalcHeadMachineX + nFeedX;
	m_stDstRobPnt.y = nCalcCenterMachineY + VAR_ROBOT_D("上相机中心基于旋转中心坐标Y") + nCalcHeadMachineY - nFeedY;

	if (fabs(m_pMainBoardInTray[m_mapIndex["TRAY盘主板索引"]].nPixelR) >= 90) {
		if (m_pMainBoardInTray[m_mapIndex["TRAY盘主板索引"]].nPixelR < 0) {
			m_stDstRobPnt.r += m_pMainBoardInTray[m_mapIndex["TRAY盘主板索引"]].nPixelR + 180;
		}
		else {
			m_stDstRobPnt.r += m_pMainBoardInTray[m_mapIndex["TRAY盘主板索引"]].nPixelR - 180;
		}
	}
	else {
		m_stDstRobPnt.r += m_pMainBoardInTray[m_mapIndex["TRAY盘主板索引"]].nPixelR;
	}	

	m_bTrayCameraCaptureFinishFlag = false;

	m_mapFlag["成功取料标志"] = true;

	m_mapIndex["待料计数"] = 0;

	g_bWaitFlag = false;

	RETURN_STATE(&CLogicRobot::OnRobot18, true);
}

CStatus CLogicRobot::OnRobot18()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		m_sRet.Format("等待机械手运动到吸嘴%d待取料位", m_mapIndex["当前吸嘴索引"] + 1);
		RETURN_SELF(m_sRet,true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot19, true);
}

CStatus CLogicRobot::OnRobot19()
{
	int nRowIndex = 0, nColIndex = 0;

	nRowIndex = m_mapIndex["TRAY盘主板索引"] / VAR_ROBOT_I("TRAY盘主板列数");
	nColIndex = m_mapIndex["TRAY盘主板索引"] % VAR_ROBOT_I("TRAY盘主板列数");

	if (m_pMainBoardInTray[m_mapIndex["TRAY盘主板索引"]].nPixelR <= 90) {
		m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["TRAY盘正取放位"]->z + VAR_ROBOT_D("Tray取料高度偏移");
	}
	else {
		m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["TRAY盘反取放位"]->z + VAR_ROBOT_D("Tray取料高度偏移");
	}

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumOn(m_mapIndex["当前吸嘴索引"]));

	RETURN_STATE(&CLogicRobot::OnRobot20, true);
}

CStatus CLogicRobot::OnRobot20()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt, true, false));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderOn(m_mapIndex["当前吸嘴索引"]));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderStatus(m_mapIndex["当前吸嘴索引"]));

	if (m_sRet != "On") {
		if (m_mapFlag["吸嘴气缸伸出标志"]) {
			if (GetTickCount() - m_mapTick["吸嘴气缸伸出计时"] < 3000) {
				m_sRet.Format("等待吸嘴气缸%d伸出", m_mapIndex["当前吸嘴索引"] + 1);
				RETURN_SELF(m_sRet, true);
			}
		}
		else {
			m_mapFlag["吸嘴气缸伸出标志"] = true;
			m_mapTick["吸嘴气缸伸出计时"] = GetTickCount();
			m_sRet.Format("等待吸嘴气缸%d伸出", m_mapIndex["当前吸嘴索引"] + 1);
			RETURN_SELF(m_sRet, true);
		}
	}

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		m_sRet.Format("等待机械手运动到吸嘴%d取料位", m_mapIndex["当前吸嘴索引"] + 1);
		RETURN_SELF(m_sRet, true);
	}

	m_mapFlag["吸嘴气缸伸出标志"] = false;

	m_mapTick["吸嘴吸料计时"] = GetTickCount();

	RETURN_STATE(&CLogicRobot::OnRobot21, true);
}

CStatus CLogicRobot::OnRobot21()
{
	if (GetTickCount() - m_mapTick["吸嘴吸料计时"] < (DWORD)VAR_ROBOT_I("吸嘴吸料延时")) {
		RETURN_SELF("", false);
	}

	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;

	RETURN_STATE(&CLogicRobot::OnRobot22, true);
}

CStatus CLogicRobot::OnRobot22()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt, true, true, NULL, VAR_ROBOT_I("机械手Z轴上升速度百分比")));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderOff(m_mapIndex["当前吸嘴索引"]));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderStatus(m_mapIndex["当前吸嘴索引"]));

	if (m_sRet != "Off") {
		m_sRet.Format("等待吸嘴气缸%d缩回", m_mapIndex["当前吸嘴索引"] + 1);
		RETURN_SELF(m_sRet, true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		m_sRet.Format("等待机械手运动到吸嘴%d待取料位", m_mapIndex["当前吸嘴索引"] + 1);
		RETURN_SELF(m_sRet, true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot23, true);
}

CStatus CLogicRobot::OnRobot23()
{
	RETURN_STATE(&CLogicRobot::OnRobot24, true);
}

CStatus CLogicRobot::OnRobot24()
{
	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumStatus(m_mapIndex["当前吸嘴索引"]));

	if (m_sRet != "On") {
		m_sRet.Format("吸嘴%d取料失败，请确认是否掉料！", m_mapIndex["当前吸嘴索引"] + 1);
		REPORT(m_sRet, emLogLevelWarn);

		if (m_mapIndex["上一次取料吸嘴"] == m_mapIndex["当前吸嘴索引"]) {
			m_mapIndex["重复取料计数"]++;
			if (m_mapIndex["重复取料计数"] > 2) {
				CString sHead;
				sHead.Format("%d号吸嘴取板失败数", m_mapIndex["当前吸嘴索引"] + 1);
				CDat::IncreaseValue(sHead);

				m_mapIndex["重复取料计数"] = 0;
				m_sRet.Format("吸嘴%d连续取料失败，请确认吸嘴是否存在问题！", m_mapIndex["当前吸嘴索引"] + 1);
				REPORT(m_sRet, emLogLevelWarn);
				MESSAGEBOX(m_sRet, "", false);
			}
		}
		else {
			m_mapIndex["重复取料计数"] = 0;
			m_mapIndex["上一次取料吸嘴"] = m_mapIndex["当前吸嘴索引"];
		}

		RETURN_STATE(&CLogicRobot::OnRobot12, true);
	}

	m_mapIndex["重复取料计数"] = 0;
	m_mapIndex["上一次取料吸嘴"] = m_mapIndex["当前吸嘴索引"];
	m_pMainBoardInTray[m_mapIndex["TRAY盘主板索引"]].bExistFlag = false;
	m_pMainBoardInTray[m_mapIndex["TRAY盘主板索引"]].bHasMaterialFlag = false;
	m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadPickOk = true;
	m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bFromTray = true;
	m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadThrowFlag = false;
	
	m_nPickSum++;
	if (m_nPickSum == m_nTrayExistSum) {
		m_nPickSum = 0;

		bool bExistAndNotOKFlag = false, bExistFlag = false;
		for (int i=0; i<VAR_ROBOT_I("TRAY盘主板行数"); i++)
		{
			for (int j=0; j<VAR_ROBOT_I("TRAY盘主板列数"); j++)
			{
				m_sRet = g_pImageFlowTray->GetResult(m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelX, m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelY, m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelR, bExistFlag, 0, i, j, 0);
				if (m_sRet != "OK" && bExistFlag) {
					bExistAndNotOKFlag = true;
					break;
				}
			}
		}
		if (bExistAndNotOKFlag) {
			REPORT("Tray盘中存在未识别的主板，请手动取走", emLogLevelWarn);
			MESSAGEBOX("Tray盘中存在未识别的主板，请手动取走", "", false);
		}

		for (int i=0; i<VAR_ROBOT_I("TRAY盘主板行数"); i++)
		{
			for (int j=0; j<VAR_ROBOT_I("TRAY盘主板列数"); j++)
			{
				m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bHasMaterialFlag = false;
			}
		}

		VAR_TRAY("满TRAY盘进料完成标志") = false;
		VAR_TRAY("空TRAY盘进料完成标志") = false;
		VAR_ROBOT("TRAY盘进料标志") = true;
		RETURN_STATE(&CLogicRobot::OnRobot12, true);
	}

	m_mapIndex["当前吸嘴索引"]++;

	RETURN_STATE(&CLogicRobot::OnRobot16_0, true);
}

CStatus CLogicRobot::OnRobot25()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move("空闲等待位"));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos("空闲等待位"));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到空闲等待位", true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot25_0, true);
}

CStatus CLogicRobot::OnRobot25_0()
{
	if (m_mapIndex["当前吸嘴索引"] >= 4) {
		m_mapIndex["当前吸嘴索引"] = 0;
		RETURN_STATE(&CLogicRobot::OnRobot29, true);
	}

	if (!m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].bHeadPickOk) {
		m_mapIndex["当前吸嘴索引"]++;
		RETURN_SELF("", false);
	}

	if (m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].bHeadProcOk) {
		m_mapIndex["当前吸嘴索引"]++;
		RETURN_SELF("", false);
	}

	if (m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].bHeadThrowFlag) {
		m_mapIndex["当前吸嘴索引"]++;
		RETURN_SELF("", false);
	}

	RETURN_STATE(&CLogicRobot::OnRobot26, true);
}

CStatus CLogicRobot::OnRobot26()
{
	CString sPosition;

	sPosition.Format("下相机吸嘴%d拍照位", m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]] + 1);

	RUN_STOP_IF_ERROR(m_pRobot->DnCameraLightOn());

	RUN_STOP_IF_ERROR(m_pRobot->Move(sPosition));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(sPosition));

	if (m_sRet != "Yes") {
		m_sRet.Format("等待机械手运动到下相机吸嘴%d拍照位", m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]] + 1);
		RETURN_SELF(m_sRet, true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot27, true);
}

CStatus CLogicRobot::OnRobot27()
{
	m_pRobot->RegisterCamera();

	RUN_STOP_IF_ERROR(m_pRobot->ProcPrepare(m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]));

	CString sName;
	sName.Format("吸嘴%d", m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]] + 1);
	RUN_STOP_IF_ERROR(m_pRobot->PushImageFlow(g_pImageFlowDn[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]], NULL, g_pImageWndDn, "下相机", sName, m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].sCode2D, false, false, m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]], true, m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]));

	RUN_STOP_IF_ERROR(m_pRobot->ProcStart(m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]));

	Sleep(50);

	RUN_STOP_IF_ERROR(g_pCamera->Trigger("Dn"));

	m_mapTick["拍照计时"] = GetTickCount();

	RETURN_STATE(&CLogicRobot::OnRobot28, true);
}

CStatus CLogicRobot::OnRobot28()
{
	if (!m_pRobot->m_vImageFlow[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]][0].bTakePicOk) {
		if (GetTickCount() - m_mapTick["拍照计时"] > 8000) {
			CDat::IncreaseValue("下相机拍照失败数");
			REPORT("下相机拍照超时,即将重新拍照!", emLogLevelError);
			MESSAGEBOX("下相机拍照超时,即将重新拍照!", "", false);
			RETURN_STATE(&CLogicRobot::OnRobot26, true);
		}
		else {
			RETURN_SELF("等待拍照完成", false);
		}
	}

	RUN_STOP_IF_ERROR(m_pRobot->DnCameraLightOff());

	m_mapIndex["当前吸嘴索引"]++;

	RETURN_STATE(&CLogicRobot::OnRobot25_0, true);
}

CStatus CLogicRobot::OnRobot29()
{
	if (m_mapIndex["当前吸嘴索引"] >= 4) {
		m_mapIndex["当前吸嘴索引"] = 0;

		// 拍照异常，重复拍照
		bool bReProcFlag = false;

		for (int i=0; i<4; i++)
		{
			if (m_stHeadProcResult[i].bHeadPickOk && !m_stHeadProcResult[i].bHeadProcOk) {
				bReProcFlag = true;
			}
		}

		if (bReProcFlag) {
			RETURN_STATE(&CLogicRobot::OnRobot25_0, true);
		}

		// 弹片检测异常，抛料
		bool bThrowFlag = false;

		for (int i=0; i<4; i++)
		{
			if (m_stHeadProcResult[i].bHeadPickOk && m_stHeadProcResult[i].bHeadThrowFlag) {
				bThrowFlag = true;
				break;
			}
		}

		if (bThrowFlag) {
			RETURN_STATE(&CLogicRobot::OnRobot30, true);
		}

		// 是否满足条件去贴装
		int nFixtureCntA = 0, nFixtureCntB = 0;

		for (int i=0; i<8; i++)
		{
			if (!m_stMarkInfo[i].bAssembleOk) {
				if (i < 4) {
					nFixtureCntA++;
				}
				else {
					nFixtureCntB++;
				}
			}
		}

		int nFixtureCnt = 0;
		nFixtureCnt = nFixtureCntA < nFixtureCntB ? nFixtureCntA : nFixtureCntB;

		int nPickCnt = 0;

		for (int i=0; i<4; i++)
		{
			if (m_stHeadProcResult[i].bHeadProcOk) {
				m_stHeadProcResult[i].sCode2D.Empty();
				nPickCnt++;
			}
		}
		// 原逻辑：每取2块板就扫描一次治具二维码
		if ((nPickCnt > 0 && nPickCnt == nFixtureCnt) || (nPickCnt % 2 == 0)) {
			m_mapIndex["当前治具索引"] = 4;
			RETURN_STATE(&CLogicRobot::OnRobot34, true);  // 跳转到治具二维码扫描
		}
		//// 修改治具二维码扫描触发条件：只在装配完整个治具后执行，避免重复拍照
		//// 原逻辑：每取2块板就扫描一次治具二维码 (nPickCnt % 2 == 0)
		//// 新逻辑：只在满足完整治具装配条件时扫描治具二维码
		//if (nPickCnt > 0 && nPickCnt == nFixtureCnt) {
		//	m_mapIndex["当前治具索引"] = 4;
		//	RETURN_STATE(&CLogicRobot::OnRobot34, true);
		//}
		//// 如果还没有满足完整治具装配条件，且有板子需要处理，则继续装配流程
		//else if (nPickCnt > 0) {
		//	m_mapIndex["当前治具索引"] = 4;
		//	// 跳过治具二维码扫描，直接进入装配流程
		//	RETURN_STATE(&CLogicRobot::OnRobot39, true);
		//}
		else {
			RETURN_STATE(&CLogicRobot::OnRobot00, true);
		}
	}

	if (!m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].bHeadPickOk) {
		m_mapIndex["当前吸嘴索引"]++;
		RETURN_SELF("", false);
	}

	if (m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].bHeadProcOk) {
		m_mapIndex["当前吸嘴索引"]++;
		RETURN_SELF("", false);
	}

	if (m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].bHeadThrowFlag) {
		m_mapIndex["当前吸嘴索引"]++;
		RETURN_SELF("", false);
	}

	m_mapTick["图像处理计时"] = GetTickCount();

	RETURN_STATE(&CLogicRobot::OnRobot29_0, true);
}

CStatus CLogicRobot::OnRobot29_0()
{
	bool bProcTimeOut = false;

	if (!m_pRobot->m_vImageFlow[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]][0].bProcOk) {
		if (GetTickCount() - m_mapTick["图像处理计时"] > 3000) {
			bProcTimeOut = true;
			CDat::IncreaseValue("下相机拍照失败数");
			REPORT("下相机图像处理超时", emLogLevelError);
		}
		else {
			RETURN_SELF("等待下相机图像处理完成", false);
		}
	}

	bool bExistFlag = false;
	double nResultPixelX, nResultPixelY, nResultPixelR = 0;

	if (!bProcTimeOut) {
		m_sRet = g_pImageFlowDn[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]]->GetResult(nResultPixelX, nResultPixelY, nResultPixelR, bExistFlag);
	}
	else {
		m_sRet = "图像处理超时";
	}

	if (m_sRet != "OK") {
		m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].bHeadProcOk = false;

		RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumStatus(m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]));
		if (m_sRet == "On") {
			m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].nProcTimes++;
			if (m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].nProcTimes >= 3) {
				m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].nProcTimes = 0;
				if (bProcTimeOut) {
					m_sRet.Format("吸嘴%d图像处理超时!", m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]] + 1);
				}
				else {
					m_sRet.Format("吸嘴%d图像处理失败,请调整模板后重新拍照!", m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]] + 1);
				}
				REPORT(m_sRet, emLogLevelWarn);
				MESSAGEBOX(m_sRet, "", false);
			}
			else {
				m_sRet.Format("吸嘴%d图像处理失败,即将重新拍照!", m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]] + 1);
				REPORT(m_sRet, emLogLevelWarn);
			}
		}
		else {
			m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].bHeadPickOk = false;
			m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].nProcTimes = 0;
			m_sRet.Format("吸嘴%d可能发生掉料,请确认！", m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]] + 1);
			REPORT(m_sRet, emLogLevelWarn);
			MESSAGEBOX(m_sRet, "", emLogLevelWarn);
		}

		m_mapIndex["当前吸嘴索引"]++;
		RETURN_STATE(&CLogicRobot::OnRobot29, true);
	}
		
	m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].nHeadPixelX = nResultPixelX;
	m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].nHeadPixelY = nResultPixelY;
	m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].nHeadPixelR = nResultPixelR;

	CString sPosition;

	sPosition.Format("下相机吸嘴%d拍照位", m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]] + 1);

	m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].nHeadMachineX = m_pRobot->m_mapRobotPoint[sPosition]->x;
	m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].nHeadMachineY = m_pRobot->m_mapRobotPoint[sPosition]->y;
	m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].nHeadMachineR = m_pRobot->m_mapRobotPoint[sPosition]->r;

	m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].bHeadProcOk = true;
	m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].nProcTimes = 0;

	if (m_pRobot->m_vImageFlow[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]][0].bBadCheckResult) {
		CString sMsg;
		sMsg.Format("吸嘴%d弹片检测失败", m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]] + 1);
		REPORT(sMsg, emLogLevelWarn);
		m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].bHeadThrowFlag = true;
	}
	else {
		m_stHeadProcResult[m_nDnCamCaptureSequence[m_mapIndex["当前吸嘴索引"]]].bHeadThrowFlag = false;
	}

	m_mapIndex["当前吸嘴索引"]++;

	RETURN_STATE(&CLogicRobot::OnRobot29, true);
}

CStatus CLogicRobot::OnRobot30()
{
	bool bFlag = false;

	for (int i=0; i<4; i++)
	{
		if (m_stHeadProcResult[i].bHeadThrowFlag) {
			bFlag = true;
			break;
		}
	}

	if (!bFlag) {
		RETURN_STATE(&CLogicRobot::OnRobot30_0, true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->Move("NG物料动作起始位"));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos("NG物料动作起始位"));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到NG预放料位", true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot30_0, true);
}

CStatus CLogicRobot::OnRobot30_0()
{
	if (m_mapIndex["当前吸嘴索引"] >= 4) {
		m_mapIndex["当前吸嘴索引"] = 0;
		m_mapIndex["当前治具索引"] = 4;

		if (VAR_BELTA_B("皮带有料标志") || VAR_BELTB_B("皮带有料标志")) {			
			bool bFlag = true;
			for (int i=0; i<4; i++)
			{
				if (!m_stHeadProcResult[i].bHeadPickOk) {
					bFlag = false;
					break;
				}
			}
			if (bFlag) {
				RETURN_STATE(&CLogicRobot::OnRobot25, true);
			}
		}

		if (!VAR_BELTA_B("允许机械手装配主板标志") && !VAR_BELTB_B("允许机械手装配主板标志")) {
			RETURN_STATE(&CLogicRobot::OnRobot00, true)
		}

		RETURN_STATE(&CLogicRobot::OnRobot25, true);
	}

	if (m_mapIndex["当前吸嘴索引"] < 0) {
		m_mapIndex["当前吸嘴索引"]++;
		RETURN_SELF("", false);
	}

	if (!m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadThrowFlag) {
		m_mapIndex["当前吸嘴索引"]++;
		RETURN_SELF("", false);
	}
	
	RETURN_STATE(&CLogicRobot::OnRobot31, true);
}

CStatus CLogicRobot::OnRobot31()
{
	CString sPostion;

	if (m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bMesNgFlag) {
		sPostion = "吸嘴1MESNG物料放料位";
	}
	else {
		sPostion = "吸嘴1NG物料放料位";
	}
	
	m_stDstRobPnt = *m_pRobot->m_mapRobotPoint[sPostion];

	double nOffX = 0, nOffY = 0;

	switch (m_mapIndex["当前吸嘴索引"])
	{
	case 0:
		break;
	case 1:
		CCvImage::Rotate(0, 0, VAR_ROBOT_D("吸嘴中心间距X"), 0, m_pRobot->m_mapRobotPoint[sPostion]->r - m_pRobot->m_mapRobotPoint["空闲等待位"]->r, nOffX, nOffY);
		m_stDstRobPnt.x -= nOffX;
		m_stDstRobPnt.y += nOffY;
		break;
	case 2:
		CCvImage::Rotate(0, 0, VAR_ROBOT_D("吸嘴中心间距X"), VAR_ROBOT_D("吸嘴中心间距Y"), m_pRobot->m_mapRobotPoint[sPostion]->r - m_pRobot->m_mapRobotPoint["空闲等待位"]->r, nOffX, nOffY);
		m_stDstRobPnt.x -= nOffX;
		m_stDstRobPnt.y += nOffY;
		break;
	case 3:
		CCvImage::Rotate(0, 0, 0, VAR_ROBOT_D("吸嘴中心间距Y"), m_pRobot->m_mapRobotPoint[sPostion]->r - m_pRobot->m_mapRobotPoint["空闲等待位"]->r, nOffX, nOffY);
		m_stDstRobPnt.x -= nOffX;
		m_stDstRobPnt.y += nOffY;
		break;
	default:
		RETURN_STOP();
	}

	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到NG物料放料位", true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot32, true);
}

CStatus CLogicRobot::OnRobot32()
{
	if (!VAR_MACHINE_B("NG皮带允许放料标志")) {
		RETURN_SELF("等待NG皮带允许放料", false);
	}

	RETURN_STATE(&CLogicRobot::OnRobot32_0, true);
}

CStatus CLogicRobot::OnRobot32_0()
{
	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderOn(m_mapIndex["当前吸嘴索引"]));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderStatus(m_mapIndex["当前吸嘴索引"]));

	if (m_sRet != "On") {
		m_sRet.Format("等待吸嘴气缸%d伸出", m_mapIndex["当前吸嘴索引"] + 1);
		RETURN_SELF(m_sRet, true);
	}

	VAR_MACHINE("NG皮带放料完成标志") = false;

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderBrokenVacuumOn(m_mapIndex["当前吸嘴索引"]));

	Sleep(100);

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumOff(m_mapIndex["当前吸嘴索引"]));

	VAR_MACHINE("NG皮带放料完成标志") = true;

	RETURN_STATE(&CLogicRobot::OnRobot33, true);
}

CStatus CLogicRobot::OnRobot33()
{
	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderOff(m_mapIndex["当前吸嘴索引"]));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderStatus(m_mapIndex["当前吸嘴索引"]));

	if (m_sRet != "Off") {
		m_sRet.Format("等待吸嘴气缸%d缩回", m_mapIndex["当前吸嘴索引"] + 1);
		RETURN_SELF(m_sRet, true);
	}

	m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadPickOk = false;
	m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadProcOk = false;
	m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadThrowFlag = false;

	m_mapIndex["当前吸嘴索引"]++;

	RETURN_STATE(&CLogicRobot::OnRobot30_0, true);
}

CStatus CLogicRobot::OnRobot34()
{
	if (!VAR_BELTA_B("允许机械手装配主板标志") && !VAR_BELTB_B("允许机械手装配主板标志")) {
		if (VAR_BELTA_B("皮带有料标志") || VAR_BELTB_B("皮带有料标志")) {
			RETURN_SELF("等待治具到位", false);
		}
		else {
			m_mapIndex["当前吸嘴索引"] = 0;
			RETURN_STATE(&CLogicRobot::OnRobot00, true);
		}
	}

	bool bFlag = true;

	for (int i=0; i<4; i++)
	{
		if (m_stHeadProcResult[i].bHeadProcOk) {
			bFlag = false;
			break;
		}
	}

	if (bFlag) {
		m_mapIndex["当前吸嘴索引"] = 0;
		RETURN_STATE(&CLogicRobot::OnRobot00, true);
	}

	if (!VAR_BELTB_B("允许机械手装配主板标志")) {
		m_mapIndex["当前治具索引"] = 0;
		RETURN_STATE(&CLogicRobot::OnRobot45, true);
	}
	
	if (VAR_BELTA_B("允许机械手装配主板标志")) {
		int nCntA = 0, nCntB = 0;

		for (int i=0; i<8; i++)
		{
			if (!m_stMarkInfo[i].bAssembleOk) {
				if (i < 4) {
					nCntA++;
				}
				else {
					nCntB++;
				}
			}
		}

		if ((nCntA > 0) && (nCntA < nCntB)) {
			m_mapIndex["当前治具索引"] = 0;
			RETURN_STATE(&CLogicRobot::OnRobot45, true);
		}
	}

	if (m_mapIndex["当前治具索引"] >= 8) {
		m_mapIndex["当前治具索引"] = 4;
		m_mapFlag["自动计算补偿标志"] = true;
		if (VAR_ROBOT_B("治具二维码扫描功能启用") || VAR_MACHINE_B("MES治具绑定功能启用")) {
			RETURN_STATE(&CLogicRobot::OnRobot34_0, true);
		}
		else {
			RETURN_STATE(&CLogicRobot::OnRobot39, true);
		}
	}

	if (m_stMarkInfo[m_mapIndex["当前治具索引"]].bAssembleOk) {
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	if (m_stMarkInfo[m_mapIndex["当前治具索引"]].bMarkProcOk) {
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	if (VAR_ROBOT_B("双主板装配模式") && m_mapIndex["当前治具索引"] % 4 == 3) {
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	RETURN_STATE(&CLogicRobot::OnRobot35, true);
}

CStatus CLogicRobot::OnRobot34_0()
{
	if (!VAR_ROBOT_B("治具二维码扫描功能启用") && !VAR_MACHINE_B("MES治具绑定功能启用")) {
		RETURN_STATE(&CLogicRobot::OnRobot39, true);
	}

	double nUpCamOffMachineX = 0, nUpCamOffMachineY = 0;
	nUpCamOffMachineX = VAR_ROBOT_D("上相机中心基于旋转中心坐标X");
	nUpCamOffMachineY = VAR_ROBOT_D("上相机中心基于旋转中心坐标Y");

	int nFixtureIndex = 0;
	nFixtureIndex = m_mapIndex["当前治具索引"] < 4 ? 0 : 4;

	double nUpCamCenterMachineX = 0, nUpCamCenterMachineY = 0;
	g_pImageCalibrateUp->TransToMachine(VAR_ROBOT_D("上相机中心坐标X"), VAR_ROBOT_D("上相机中心坐标Y"), nUpCamCenterMachineX, nUpCamCenterMachineY);

	double nUpCalcMachineX = 0, nUpCalcMachineY = 0;
	g_pImageCalibrateUp->TransToMachine(m_stMarkInfo[nFixtureIndex].nMarkPixelX, m_stMarkInfo[nFixtureIndex].nMarkPixelY, nUpCalcMachineX, nUpCalcMachineY);

	double nUpCalcOffX = 0, nUpCalcOffY = 0;
	nUpCalcOffX = nUpCalcMachineX - nUpCamCenterMachineX;
	nUpCalcOffY = nUpCalcMachineY - nUpCamCenterMachineY;

	TRACE("主板装配位%d上相机计算结果基于图像中心： %.3f, %.3f\n", m_mapIndex["当前治具索引"] + 1, nUpCalcOffX, nUpCalcOffY);

	double nUpOffX = 0, nUpOffY = 0;
	nUpOffX = nUpCamOffMachineX - nUpCalcOffX;
	nUpOffY = nUpCamOffMachineY - nUpCalcOffY;	

	TRACE("主板装配位%d上相机处理结果基于旋转中心： %.3f, %.3f\n", m_mapIndex["当前治具索引"] + 1, nUpOffX, nUpOffY);

	double nMark1MachineX = 0, nMark1MachineY = 0;
	nMark1MachineX = m_stMarkInfo[nFixtureIndex].nMarkMachineX + nUpOffX;
	nMark1MachineY = m_stMarkInfo[nFixtureIndex].nMarkMachineY + nUpOffY;

	ROBOTPOINT rob = *g_pRobot->m_mapRobotPoint["B轨左下角Mark拍照位"];

	rob.x += VAR_ROBOT_D("治具二维码位置偏移X");
	rob.y -= VAR_ROBOT_D("治具二维码位置偏移Y");

	RUN_STOP_IF_ERROR(m_pRobot->Move(rob));

	RUN_STOP_IF_ERROR(m_pRobot->UpCameraLightOn());

	g_pCamera->SetExposureTime("Up", VAR_ROBOT_I("上相机(治具二维码)曝光时间"));
	g_pCamera->SetGain("Up", VAR_ROBOT_I("上相机(治具二维码)增益"));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(rob));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到治具二维码扫描拍照位", true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot34_1, true);
}

CStatus CLogicRobot::OnRobot34_1()
{
	RUN_STOP_IF_ERROR(m_pRobot->ProcPrepare(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->PushImageFlow(NULL, g_pImageFlowScanCodeFixture, g_pImageWndUp, "治具二维码扫描", "", "", true, false, m_mapIndex["当前治具索引"] % 4, true, m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->ProcStart(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(g_pCamera->Trigger("Up"));

	m_mapTick["拍照计时"] = GetTickCount();

	RETURN_STATE(&CLogicRobot::OnRobot34_2, true);
}

CStatus CLogicRobot::OnRobot34_2()
{
	if (!m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].bProcOk) {
		if (GetTickCount() - m_mapTick["拍照计时"] > 8000) {
			CDat::IncreaseValue("上相机拍照失败数");
			REPORT("上相机拍照超时,请先点击确定后点击开始重试!", emLogLevelError);
			MESSAGEBOX("上相机拍照超时,请先点击确定后点击开始重试!", "", false);
			m_pRobot->UpCameraLightOn();
			m_pRobot->RegisterCamera();
			RETURN_STATE(&CLogicRobot::OnRobot34_1, true);
		}
		else {
			RETURN_SELF("等待拍照完成", false);
		}
	}

	RUN_STOP_IF_ERROR(m_pRobot->UpCameraLightOff());

	RETURN_STATE(&CLogicRobot::OnRobot34_3, true);
}

CStatus CLogicRobot::OnRobot34_3()
{
	m_sFixtureCodeB.Format("%s", m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].sCode2D);

	REPORT(CString("B轨道扫描到的治具二维码为： ") + m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].sCode2D, emLogLevelNormal);

	if (m_sFixtureCodeB.GetLength() < 18) {
		MESSAGEBOX("B轨道治具二维码扫描失败", "", false);
		RETURN_STATE(&CLogicRobot::OnRobot34_0, true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot39, true);
}

CStatus CLogicRobot::OnRobot35()
{
	switch (m_mapIndex["当前治具索引"])
	{
	case 0:
		m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["A轨左下角Mark拍照位"];
		break;
	case 1:
		m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["A轨右下角Mark拍照位"];
		break;
	case 2:
		m_mapIndex["当前治具索引"]++;
		RETURN_STATE(&CLogicRobot::OnRobot34, true);
		break;
	case 3:
		m_mapIndex["当前治具索引"]++;
		RETURN_STATE(&CLogicRobot::OnRobot34, true);
		break;
	case 4:
		m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["B轨左下角Mark拍照位"];
		break;
	case 5:
		m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["B轨右下角Mark拍照位"];
		break;
	case 6:
		if (VAR_ROBOT_B("MARK全拍模式")) {
			m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["B轨右上角Mark拍照位"];
		}
		else {
			m_mapIndex["当前治具索引"]++;
			RETURN_STATE(&CLogicRobot::OnRobot34, true);
		}
		break;
	case 7:
		if (VAR_ROBOT_B("MARK全拍模式")) {
			m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["B轨左上角Mark拍照位"];
		}
		else {
			m_mapIndex["当前治具索引"]++;
			RETURN_STATE(&CLogicRobot::OnRobot34, true);
		}
		break;
	default:break;
	}

	RETURN_STATE(&CLogicRobot::OnRobot36, true);
}

CStatus CLogicRobot::OnRobot36()
{
	RUN_STOP_IF_ERROR(m_pRobot->UpCameraLightOn());

	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt));

	g_pCamera->SetExposureTime("Up", VAR_ROBOT_I("上相机(治具)曝光时间"));
	g_pCamera->SetGain("Up", VAR_ROBOT_I("上相机(治具)增益"));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到治具拍照位", true);
	}
	
	m_stMarkInfo[m_mapIndex["当前治具索引"]].nMarkMachineX = m_stDstRobPnt.x;
	m_stMarkInfo[m_mapIndex["当前治具索引"]].nMarkMachineY = m_stDstRobPnt.y;
	m_stMarkInfo[m_mapIndex["当前治具索引"]].nMarkMachineR = m_stDstRobPnt.r;

	m_mapIndex["重复拍照计数"] = 0;

	RETURN_STATE(&CLogicRobot::OnRobot37, true);
}

CStatus CLogicRobot::OnRobot37()
{
	CString sName;

	sName.Format("%s轨-Mark%d", m_mapIndex["当前治具索引"] < 4 ? "A" : "B", (m_mapIndex["当前治具索引"] % 4) + 1);

	RUN_STOP_IF_ERROR(m_pRobot->ProcPrepare(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->PushImageFlow(g_pImageFlowMark[m_mapIndex["当前治具索引"] % 4], NULL, g_pImageWndUp, "Mark", sName, "", false, false, -1, false, m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->ProcStart(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(g_pCamera->Trigger("Up"));

	m_mapTick["拍照计时"] = GetTickCount();

	RETURN_STATE(&CLogicRobot::OnRobot38, true);
}

CStatus CLogicRobot::OnRobot38()
{
	if (!m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].bProcOk) {
		if (GetTickCount() - m_mapTick["拍照计时"] > 8000) {
			CDat::IncreaseValue("上相机拍照失败数");
			REPORT("上相机拍照超时,请先点击确定后点击开始重试!", emLogLevelError);
			MESSAGEBOX("上相机拍照超时,请先点击确定后点击开始重试!", "", false);
			m_pRobot->UpCameraLightOn();
			m_pRobot->RegisterCamera();
			RETURN_STATE(&CLogicRobot::OnRobot37, true);
		}
		else {
			RETURN_SELF("等待拍照完成", false);
		}
	}

	double nPixelX = 0, nPixelY = 0, nPixelR = 0;

	bool bExistFlag = false;

	m_sRet = g_pImageFlowMark[m_mapIndex["当前治具索引"] % 4]->GetResult(nPixelX, nPixelY, nPixelR, bExistFlag, 0);

	if (m_sRet != "OK") {
		m_mapIndex["重复拍照计数"]++;
		if (m_mapIndex["重复拍照计数"] > 3) {
			m_mapIndex["重复拍照计数"] = 0;
			REPORT("Mark图像处理失败,请调整模板后重新拍照!", emLogLevelWarn);
			MESSAGEBOX("Mark图像处理失败,请调整模板后重新拍照!", "", false);
		}
		else {
			REPORT("Mark图像处理失败,重新拍照", emLogLevelWarn);
		}

		m_pRobot->UpCameraLightOn();
		m_pRobot->RegisterCamera();
		RETURN_STATE(&CLogicRobot::OnRobot37, true);
	}

	m_stMarkInfo[m_mapIndex["当前治具索引"]].nMarkPixelX = nPixelX;
	m_stMarkInfo[m_mapIndex["当前治具索引"]].nMarkPixelY = nPixelY;
	m_stMarkInfo[m_mapIndex["当前治具索引"]].nMarkPixelR = nPixelR;
	m_stMarkInfo[m_mapIndex["当前治具索引"]].bMarkProcOk = true;

	RUN_STOP_IF_ERROR(m_pRobot->UpCameraLightOff());

	m_mapIndex["当前治具索引"]++;

	RETURN_STATE(&CLogicRobot::OnRobot34, true);
}

CStatus CLogicRobot::OnRobot39()
{
	if (m_mapIndex["当前治具索引"] >= 8) {
		bool bFlag = true;

		for (int i=4; i<8; i++)
		{
			if (VAR_ROBOT_B("双主板装配模式") && (i % 4 == 1 || i % 4 == 3)) {
				continue;
			}

			if (!m_stMarkInfo[i].bAssembleOk) {
				bFlag = false;
				break;
			}
		}

		if (bFlag) {
			// 自动计算补偿
			if (VAR_ROBOT_B("B轨自动计算补偿") && m_mapFlag["自动计算补偿标志"]) {
				for (int i=4; i<8; i++)
				{
					if (VAR_ROBOT_B("双主板装配模式") && (i % 4 == 1 || i % 4 == 3)) {
						continue;
					}

					if (!m_stMarkInfo[i].bCalcFeedFlag) {
						RETURN_STATE(&CLogicRobot::OnRobot39_0, true);
					}
				}

				for (int i=4; i<8; i++)
				{
					m_stMarkInfo[i].bCalcFeedFlag = false;
				}
				
				VAR_ROBOT("B轨自动计算补偿") = false;
			}

			// 主板回拍
			if (VAR_MACHINE_B("弹片检测功能启用") || VAR_MACHINE_B("弹片检测功能只检测不判断") || VAR_MACHINE_B("MES功能启用") || VAR_MACHINE_B("MES过站功能启用") || VAR_MACHINE_B("MES治具绑定功能启用")) {
				m_mapIndex["当前治具索引"] = 4;
				RETURN_STATE(&CLogicRobot::OnRobot43_0, true);
			}

			// 生产周期监控
			g_pMachine->m_listProductionCycle.push_back(g_pMachine->m_stProductionCircle);

			g_pMachine->m_stProductionCircle.bFree = false;

			while (true)
			{
				if (g_pMachine->m_listProductionCycle.size() > VAR_MACHINE_I("生产周期监控数量")) {
					g_pMachine->m_listProductionCycle.pop_front();
				}
				else {
					break;
				}
			}

			if (true) {
				CString strProductionCycleStatus = "生产周期监控信息:";

				list<PRODUCTIONCYCLE>::iterator it = g_pMachine->m_listProductionCycle.begin();
				for (; it != g_pMachine->m_listProductionCycle.end(); it++)
				{
					if (it->bFree) {
						strProductionCycleStatus += " 1";
					}
					else {
						strProductionCycleStatus += " 0";
					}
				}

				REPORT(strProductionCycleStatus, emLogLevelNormal);
			}

			if (g_pMachine->m_listProductionCycle.size() >= VAR_MACHINE_I("生产周期监控数量")) {
				g_pMachine->m_listProductionCycle.pop_front();

				int nFreeCnt = 0, nContinueCnt = 0;

				if (!VAR_MACHINE_B("托盘优先模式")) {
					list<PRODUCTIONCYCLE>::iterator it = g_pMachine->m_listProductionCycle.begin();
					for (; it != g_pMachine->m_listProductionCycle.end(); it++)
					{
						if (it->bFree) {
							nFreeCnt++;
							nContinueCnt++;
						}
						else {
							nContinueCnt = 0;
						}

						if (nContinueCnt >= 3) {
							REPORT("连续3个生产周期内出现周期待料超时,切换到托盘优先模式", emLogLevelWarn);
							VAR_MACHINE("托盘优先模式") = true;
							break;
						}
					}

					if (!VAR_MACHINE_B("托盘优先模式") && nFreeCnt >= VAR_MACHINE_I("生产周期待料周期数量")) {
						CString str;
						str.Format("在连续%d个生产周期内，出现%d次待料周期,切换到托盘优先模式", VAR_MACHINE_I("生产周期监控数量"), VAR_MACHINE_I("生产周期待料周期数量"));
						REPORT(str, emLogLevelWarn);
						VAR_MACHINE("托盘优先模式") = true;
					}
				}
			}

			CSys::m_nTotalSum++;
			VAR_BELTB("允许机械手装配主板标志") = false;
			VAR_BELTB("机械手装配主板完成标志") = true;
			VAR_BELTB("治具提前取上盖标志") = true;
			for (int i=4; i<8; i++)
			{
				m_stMarkInfo[i].bMarkProcOk = false;
				m_stMarkInfo[i].bAssembleOk = false;
			}

			MANNUAL_MODE();
		}

		m_mapIndex["当前吸嘴索引"] = 0;

		bFlag = false;

		for (int i=0; i<4; i++)
		{
			if (VAR_ROBOT_B("双主板装配模式") && (i % 4 == 1 || i % 4 == 3)) {
				continue;
			}

			if (m_stHeadProcResult[i].bHeadThrowFlag) {
				bFlag = true;
			}
		}

		if (bFlag) {
			RETURN_STATE(&CLogicRobot::OnRobot30, true);
		}

		RETURN_STATE(&CLogicRobot::OnRobot00, true);
	}

	if (VAR_ROBOT_B("双主板装配模式") && (m_mapIndex["当前治具索引"] % 4 == 1 || m_mapIndex["当前治具索引"] % 4 == 3)) {
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	if (m_stMarkInfo[m_mapIndex["当前治具索引"]].bAssembleOk) {
		m_mapFlag["自动计算补偿标志"] = false;
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}
	
	m_mapIndex["当前吸嘴索引"] = -1;

	if (m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadProcOk && !m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadThrowFlag) {
		m_mapIndex["当前吸嘴索引"] = m_mapIndex["当前治具索引"] % 4;
	}

	if (m_mapIndex["当前吸嘴索引"] < 0 || m_mapIndex["当前吸嘴索引"] > 3) {
		m_mapFlag["自动计算补偿标志"] = false;
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	CString sPosition1, sPosition2;
	sPosition1 = "B轨主板装配位";
	sPosition2.Format("下相机吸嘴%d拍照位", m_mapIndex["当前吸嘴索引"] + 1);

	m_stDstRobPnt = *m_pRobot->m_mapRobotPoint[sPosition1];

	double nMark1MachineX = 0, nMark1MachineY = 0, nMark2MachineX = 0, nMark2MachineY = 0;
	double nMark3MachineX = 0, nMark3MachineY = 0, nMark4MachineX = 0, nMark4MachineY = 0;
	
	double nUpCamOffMachineX = 0, nUpCamOffMachineY = 0;
	nUpCamOffMachineX = VAR_ROBOT_D("上相机中心基于旋转中心坐标X");
	nUpCamOffMachineY = VAR_ROBOT_D("上相机中心基于旋转中心坐标Y");

	for (int i=0; i<4; i++)
	{
		int nFixtureIndex = 0;

		switch (i)
		{
		case 0:
			nFixtureIndex = m_mapIndex["当前治具索引"] < 4 ? 0 : 4;
			break;
		case 1:
			nFixtureIndex = m_mapIndex["当前治具索引"] < 4 ? 1 : 5;
			break;
		case 2:
			nFixtureIndex = m_mapIndex["当前治具索引"] < 4 ? 2 : 6;
			break;
		case 3:
			nFixtureIndex = m_mapIndex["当前治具索引"] < 4 ? 3 : 7;
			break;
		default:break;
		}

		double nUpCamCenterMachineX = 0, nUpCamCenterMachineY = 0;
		g_pImageCalibrateUp->TransToMachine(VAR_ROBOT_D("上相机中心坐标X"), VAR_ROBOT_D("上相机中心坐标Y"), nUpCamCenterMachineX, nUpCamCenterMachineY);

		double nUpCalcMachineX = 0, nUpCalcMachineY = 0;
		g_pImageCalibrateUp->TransToMachine(m_stMarkInfo[nFixtureIndex].nMarkPixelX, m_stMarkInfo[nFixtureIndex].nMarkPixelY, nUpCalcMachineX, nUpCalcMachineY);

		double nUpCalcOffX = 0, nUpCalcOffY = 0;
		nUpCalcOffX = nUpCalcMachineX - nUpCamCenterMachineX;
		nUpCalcOffY = nUpCalcMachineY - nUpCamCenterMachineY;

		TRACE("主板装配位%d上相机计算结果基于图像中心： %.3f, %.3f\n", m_mapIndex["当前治具索引"] + 1, nUpCalcOffX, nUpCalcOffY);

		double nUpOffX = 0, nUpOffY = 0;
		nUpOffX = nUpCamOffMachineX - nUpCalcOffX;
		nUpOffY = nUpCamOffMachineY - nUpCalcOffY;	

		TRACE("主板装配位%d上相机处理结果基于旋转中心： %.3f, %.3f\n", m_mapIndex["当前治具索引"] + 1, nUpOffX, nUpOffY);

		switch (i)
		{
		case 0:
			nMark1MachineX = m_stMarkInfo[nFixtureIndex].nMarkMachineX + nUpOffX;
			nMark1MachineY = m_stMarkInfo[nFixtureIndex].nMarkMachineY + nUpOffY;
			break;		
		case 1:
			nMark2MachineX = m_stMarkInfo[nFixtureIndex].nMarkMachineX + nUpOffX;
			nMark2MachineY = m_stMarkInfo[nFixtureIndex].nMarkMachineY + nUpOffY;
			break;
		case 2:
			nMark3MachineX = m_stMarkInfo[nFixtureIndex].nMarkMachineX + nUpOffX;
			nMark3MachineY = m_stMarkInfo[nFixtureIndex].nMarkMachineY + nUpOffY;
			break;		
		case 3:
			nMark4MachineX = m_stMarkInfo[nFixtureIndex].nMarkMachineX + nUpOffX;
			nMark4MachineY = m_stMarkInfo[nFixtureIndex].nMarkMachineY + nUpOffY;
			break;
		default:break;
		}
	}

	if (VAR_ROBOT_B("MARK全拍模式")) {
		switch (m_mapIndex["当前治具索引"])
		{
		case 0:
			VAR_ROBOT("A轨位置1贴装位置偏移X") = 0;
			VAR_ROBOT("A轨位置1贴装位置偏移Y") = 0;
			break;
		case 1:
			VAR_ROBOT("A轨位置2贴装位置偏移X") = nMark2MachineX - nMark1MachineX;
			VAR_ROBOT("A轨位置2贴装位置偏移Y") = nMark1MachineY - nMark2MachineY;
			break;
		case 2:
			VAR_ROBOT("A轨位置3贴装位置偏移X") = nMark3MachineX - nMark2MachineX;
			VAR_ROBOT("A轨位置3贴装位置偏移Y") = nMark2MachineY - nMark3MachineY;
			break;
		case 3:
			VAR_ROBOT("A轨位置4贴装位置偏移X") = nMark4MachineX - nMark1MachineX;
			VAR_ROBOT("A轨位置4贴装位置偏移Y") = nMark1MachineY - nMark4MachineY;
			break;
		case 4:
			VAR_ROBOT("A轨位置1贴装位置偏移X") = 0;
			VAR_ROBOT("A轨位置1贴装位置偏移Y") = 0;
			VAR_ROBOT("B轨位置1贴装位置偏移X") = 0;
			VAR_ROBOT("B轨位置1贴装位置偏移Y") = 0;
			break;
		case 5:
			VAR_ROBOT("A轨位置2贴装位置偏移X") = nMark2MachineX - nMark1MachineX;
			VAR_ROBOT("A轨位置2贴装位置偏移Y") = nMark1MachineY - nMark2MachineY;
			VAR_ROBOT("B轨位置2贴装位置偏移X") = nMark2MachineX - nMark1MachineX;
			VAR_ROBOT("B轨位置2贴装位置偏移Y") = nMark1MachineY - nMark2MachineY;
			break;
		case 6:
			VAR_ROBOT("A轨位置3贴装位置偏移X") = nMark3MachineX - nMark2MachineX;
			VAR_ROBOT("A轨位置3贴装位置偏移Y") = nMark2MachineY - nMark3MachineY;
			VAR_ROBOT("B轨位置3贴装位置偏移X") = nMark3MachineX - nMark2MachineX;
			VAR_ROBOT("B轨位置3贴装位置偏移Y") = nMark2MachineY - nMark3MachineY;
			break;
		case 7:
			VAR_ROBOT("A轨位置4贴装位置偏移X") = nMark4MachineX - nMark1MachineX;
			VAR_ROBOT("A轨位置4贴装位置偏移Y") = nMark1MachineY - nMark4MachineY;
			VAR_ROBOT("B轨位置4贴装位置偏移X") = nMark4MachineX - nMark1MachineX;
			VAR_ROBOT("B轨位置4贴装位置偏移Y") = nMark1MachineY - nMark4MachineY;
			break;
		default:break;
		}
	}

	double nBoardOffX = 0, nBoardOffY = 0;

	CString sOffX, sOffY, sMarkR;
	sOffX.Format("%s轨位置%d贴装位置偏移X", m_mapIndex["当前治具索引"] < 4 ? "A" : "B", (m_mapIndex["当前治具索引"] % 4) + 1);
	sOffY.Format("%s轨位置%d贴装位置偏移Y", m_mapIndex["当前治具索引"] < 4 ? "A" : "B", (m_mapIndex["当前治具索引"] % 4) + 1);
	sMarkR.Format("%s轨治具角度R", m_mapIndex["当前治具索引"] < 4 ? "A" : "B");

	CString sFeedX, sFeedY, sFeedR;
	sFeedX.Format("%s轨位置%d贴装补偿X", m_mapIndex["当前治具索引"] < 4 ? "A" : "B", m_mapIndex["当前治具索引"] % 4 + 1);
	sFeedY.Format("%s轨位置%d贴装补偿Y", m_mapIndex["当前治具索引"] < 4 ? "A" : "B", m_mapIndex["当前治具索引"] % 4 + 1);
	sFeedR.Format("%s轨位置%d贴装补偿R", m_mapIndex["当前治具索引"] < 4 ? "A" : "B", m_mapIndex["当前治具索引"] % 4 + 1);
	
	switch (m_mapIndex["当前治具索引"] % 4)
	{
	case 0:
		nBoardOffX = 0.0 + VAR_ROBOT_D("贴装位置补偿X") + VAR_ROBOT_D(sFeedX);
		nBoardOffY = 0.0 - VAR_ROBOT_D("贴装位置补偿Y") - VAR_ROBOT_D(sFeedY);
		break;
	case 1:
		nBoardOffX = 0.0 + VAR_ROBOT_D("贴装位置补偿X") + VAR_ROBOT_D(sFeedX);
		nBoardOffY = 0.0 - VAR_ROBOT_D("贴装位置补偿Y") - VAR_ROBOT_D(sFeedY);
		break;
	case 2:
		nBoardOffX = VAR_ROBOT_D(sOffX) + VAR_ROBOT_D("贴装位置补偿X") + VAR_ROBOT_D(sFeedX);
		nBoardOffY = VAR_ROBOT_D(sOffY) - VAR_ROBOT_D("贴装位置补偿Y") - VAR_ROBOT_D(sFeedY);
		break;
	case 3:
		nBoardOffX = VAR_ROBOT_D(sOffX) + VAR_ROBOT_D("贴装位置补偿X") + VAR_ROBOT_D(sFeedX);
		nBoardOffY = VAR_ROBOT_D(sOffY) - VAR_ROBOT_D("贴装位置补偿Y") - VAR_ROBOT_D(sFeedY);
		break;
	default:break;
	}

	if (VAR_ROBOT_B("双主板装配模式")) {
		nBoardOffX = 0.0 + VAR_ROBOT_D("贴装位置补偿X") + VAR_ROBOT_D(sFeedX);
		nBoardOffY = 0.0 - VAR_ROBOT_D("贴装位置补偿Y") - VAR_ROBOT_D(sFeedY);
	}

	double nMarkR = 0;

	if (VAR_ROBOT_B("MARK全拍模式")) {
		nMarkR = atan2l(nMark1MachineY - nMark2MachineY, nMark2MachineX - nMark1MachineX);
		nMarkR = nMarkR * 180 / 3.14159265354;
		VAR_ROBOT("A轨治具角度R") = nMarkR;
		VAR_ROBOT("B轨治具角度R") = nMarkR;
		m_pRobot->Save();
	}
	else {
		nMarkR = atan2l(nMark1MachineY - nMark2MachineY, nMark2MachineX - nMark1MachineX);
		nMarkR = nMarkR * 180 / 3.14159265354;
	}

	nMarkR = nMarkR - VAR_ROBOT_D(sMarkR);

	CCvImage::Rotate(0, 0, nBoardOffX, nBoardOffY, nMarkR, nBoardOffX, nBoardOffY);

	nMarkR = nMarkR + VAR_ROBOT_D("贴装位置补偿R") + VAR_ROBOT_D(sFeedR) + VAR_ROBOT_D("B轨贴装角度偏移");

	double nRotateAngle = 0;
	nRotateAngle = m_pRobot->m_mapRobotPoint[sPosition1]->r - m_pRobot->m_mapRobotPoint[sPosition2]->r + nMarkR - m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].nHeadPixelR;

	m_stMarkInfo[m_mapIndex["当前治具索引"]].nRotateR = nRotateAngle;

	double nOffX = 0, nOffY = 0;

	g_pImageCalibrateDn->CalculateOffset(
		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].nHeadPixelX,
		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].nHeadPixelY,
		-nRotateAngle,
		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].nHeadMachineX,
		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].nHeadMachineY,
		nOffX,
		nOffY);
	
	TRACE("吸嘴%d旋转%.3f°下相机基于旋转中心： %.3f, %.3f\n", m_mapIndex["当前吸嘴索引"] + 1, nRotateAngle, nOffX, nOffY);

	CString sBoardOffX, sBoardOffY;
	sBoardOffX.Format("%s轨位置%d贴装位置偏移X", m_mapIndex["当前治具索引"] < 4 ? "A" : "B", (m_mapIndex["当前治具索引"] % 4) + 1);
	sBoardOffY.Format("%s轨位置%d贴装位置偏移Y", m_mapIndex["当前治具索引"] < 4 ? "A" : "B", (m_mapIndex["当前治具索引"] % 4) + 1);

	switch (m_mapIndex["当前治具索引"] % 4)
	{
	case 0:
		m_stDstRobPnt.x = nMark1MachineX - nOffX + nBoardOffX;
		m_stDstRobPnt.y = nMark1MachineY - nOffY - nBoardOffY;
		m_stMarkInfo[m_mapIndex["当前治具索引"]].nLookBackMachineX = nMark1MachineX + VAR_ROBOT_D("主板回拍位置偏移X") - nUpCamOffMachineX;
		m_stMarkInfo[m_mapIndex["当前治具索引"]].nLookBackMachineY = nMark1MachineY - VAR_ROBOT_D("主板回拍位置偏移Y") - nUpCamOffMachineY;
		break;
	case 1:
		m_stDstRobPnt.x = nMark2MachineX - nOffX + nBoardOffX;
		m_stDstRobPnt.y = nMark2MachineY - nOffY - nBoardOffY;
		m_stMarkInfo[m_mapIndex["当前治具索引"]].nLookBackMachineX = nMark2MachineX + VAR_ROBOT_D("主板回拍位置偏移X") - nUpCamOffMachineX;
		m_stMarkInfo[m_mapIndex["当前治具索引"]].nLookBackMachineY = nMark2MachineY - VAR_ROBOT_D("主板回拍位置偏移Y") - nUpCamOffMachineY;
		break;
	case 2:
		m_stDstRobPnt.x = nMark2MachineX - nOffX + nBoardOffX;
		m_stDstRobPnt.y = nMark2MachineY - nOffY - nBoardOffY;
		m_stMarkInfo[m_mapIndex["当前治具索引"]].nLookBackMachineX = nMark2MachineX + VAR_ROBOT_D("主板回拍位置偏移X") + VAR_ROBOT_D(sBoardOffX) - nUpCamOffMachineX;
		m_stMarkInfo[m_mapIndex["当前治具索引"]].nLookBackMachineY = nMark2MachineY - VAR_ROBOT_D("主板回拍位置偏移Y") - VAR_ROBOT_D(sBoardOffY) - nUpCamOffMachineY;
		break;
	case 3:
		m_stDstRobPnt.x = nMark1MachineX - nOffX + nBoardOffX;
		m_stDstRobPnt.y = nMark1MachineY - nOffY - nBoardOffY;
		m_stMarkInfo[m_mapIndex["当前治具索引"]].nLookBackMachineX = nMark1MachineX + VAR_ROBOT_D("主板回拍位置偏移X") + VAR_ROBOT_D(sBoardOffX) - nUpCamOffMachineX;
		m_stMarkInfo[m_mapIndex["当前治具索引"]].nLookBackMachineY = nMark1MachineY - VAR_ROBOT_D("主板回拍位置偏移Y") - VAR_ROBOT_D(sBoardOffY) - nUpCamOffMachineY;
		break;
	default:break;
	}
	
	m_stDstRobPnt.r = m_pRobot->m_mapRobotPoint[sPosition2]->r + nRotateAngle;
	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;
	m_stDstRobPnt.speed = m_pRobot->m_mapRobotPoint["空闲等待位"]->speed;

	m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineX = m_stDstRobPnt.x;
	m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineY = m_stDstRobPnt.y;
	m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineR = m_stDstRobPnt.r;

	RETURN_STATE(&CLogicRobot::OnRobot40, true);
}
					
CStatus CLogicRobot::OnRobot39_0()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move("下相机吸嘴1拍照位"));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos("下相机吸嘴1拍照位"));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到下相机吸嘴1拍照位", true);
	}

	MESSAGEBOX("请确认B轨所有主板已放到位！", "", true);

	m_mapIndex["当前治具索引"] = 4;

	RETURN_STATE(&CLogicRobot::OnRobot39_1, true);
}

CStatus CLogicRobot::OnRobot39_1()
{
	if (m_mapIndex["当前治具索引"] >= 8) {
		m_mapIndex["当前治具索引"] = 4;
		RETURN_STATE(&CLogicRobot::OnRobot39_6, true);
	}

	if (VAR_ROBOT_B("双主板装配模式") && (m_mapIndex["当前治具索引"] % 4 == 1 || m_mapIndex["当前治具索引"] % 4 == 3)) {
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	if (m_stMarkInfo[m_mapIndex["当前治具索引"]].bCalcFeedFlag) {
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["B轨主板装配位"];
	m_stDstRobPnt.x = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineX;
	m_stDstRobPnt.y = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineY;
	m_stDstRobPnt.r = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineR;
	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;

	RETURN_STATE(&CLogicRobot::OnRobot39_2, true);
}

CStatus CLogicRobot::OnRobot39_2()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到主板待装配位", true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumOn(m_mapIndex["当前治具索引"] % 4));

	RETURN_STATE(&CLogicRobot::OnRobot39_3, true);
}

CStatus CLogicRobot::OnRobot39_3()
{
	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderOn(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderStatus(m_mapIndex["当前治具索引"] % 4));

	if (m_sRet != "On") {
		m_sRet.Format("等待吸嘴气缸%d伸出", m_mapIndex["当前治具索引"] % 4 + 1);
		RETURN_SELF(m_sRet, true);
	}

	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["B轨主板装配位"]->z + VAR_ROBOT_D("治具取主板高度偏移");

	RETURN_STATE(&CLogicRobot::OnRobot39_4, true);
}

CStatus CLogicRobot::OnRobot39_4()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt, true, false, NULL, VAR_ROBOT_I("机械手Z轴下降速度百分比")));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到主板装配位", true);
	}

	Sleep(300);

	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;

	RETURN_STATE(&CLogicRobot::OnRobot39_5, true);
}

CStatus CLogicRobot::OnRobot39_5()
{
	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderOff(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderStatus(m_mapIndex["当前治具索引"] % 4));

	if (m_sRet != "Off") {
		m_sRet.Format("等待吸嘴气缸%d缩回", m_mapIndex["当前治具索引"] % 4 + 1);
		RETURN_SELF(m_sRet, true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumStatus(m_mapIndex["当前治具索引"] % 4));

	if (m_sRet != "On") {
		MESSAGEBOX("吸嘴未取到料，请调整取料位置后点击启动按钮！", "", true);
		RETURN_PAUSE(&CLogicRobot::OnRobot39_5_0);
	}

	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt, true, true, NULL, VAR_ROBOT_I("机械手Z轴上升速度百分比")));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到主板待装配位", true);
	}

	m_stMarkInfo[m_mapIndex["当前治具索引"]].bAssembleOk = false;

	m_mapIndex["当前治具索引"]++;

	RETURN_STATE(&CLogicRobot::OnRobot39_1, true);
}

CStatus CLogicRobot::OnRobot39_5_0()
{
	ROBOTPOINT rob;
	if (g_pRobot->GetPos(rob) != "OK") {
		RETURN_SELF("等待获取机械手当前位置", true);
	}

	CString sPos;
	sPos.Format("%s轨位置%d贴装补偿", (m_mapIndex["当前治具索引"] <= 3 ? "A" : "B"), m_mapIndex["当前治具索引"] % 4 + 1);

	VAR_ROBOT(sPos + "X") = VAR_ROBOT_D(sPos + "X") + rob.x - m_stDstRobPnt.x;
	VAR_ROBOT(sPos + "Y") = VAR_ROBOT_D(sPos + "Y") + rob.y - m_stDstRobPnt.y;
	VAR_ROBOT(sPos + "R") = VAR_ROBOT_D(sPos + "R") + rob.r - m_stDstRobPnt.r;

	m_stDstRobPnt.x = rob.x;
	m_stDstRobPnt.y = rob.y;
	m_stDstRobPnt.r = rob.r;

	RETURN_STATE(&CLogicRobot::OnRobot39_3, true);
}

CStatus CLogicRobot::OnRobot39_6()
{
	if (m_mapIndex["当前治具索引"] >= 8) {
		m_pRobot->Save();
		m_mapIndex["当前治具索引"] = 4;
		m_mapIndex["当前吸嘴索引"] = 0;
		RETURN_STATE(&CLogicRobot::OnRobot25_0, true);
	}

	if (VAR_ROBOT_B("双主板装配模式") && (m_mapIndex["当前治具索引"] % 4 == 1 || m_mapIndex["当前治具索引"] % 4 == 3)) {
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	if (m_stMarkInfo[m_mapIndex["当前治具索引"]].bCalcFeedFlag) {
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	RETURN_STATE(&CLogicRobot::OnRobot39_7, true);
}

CStatus CLogicRobot::OnRobot39_7()
{
	CString sPosition;

	sPosition.Format("下相机吸嘴%d拍照位", m_mapIndex["当前治具索引"] % 4 + 1);

	RUN_STOP_IF_ERROR(m_pRobot->DnCameraLightOn());

	RUN_STOP_IF_ERROR(m_pRobot->Move(sPosition));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(sPosition));

	if (m_sRet != "Yes") {
		m_sRet.Format("等待机械手运动到下相机吸嘴%d拍照位", m_mapIndex["当前治具索引"] % 4 + 1);
		RETURN_SELF(m_sRet, true);
	}

	m_mapIndex["重复拍照计数"] = 0;

	RETURN_STATE(&CLogicRobot::OnRobot39_8, true);
}

CStatus CLogicRobot::OnRobot39_8()
{
	CString sName;

	sName.Format("%s轨治具位置%d自动补偿拍照", m_mapIndex["当前治具索引"] <= 3 ? "A" : "B", m_mapIndex["当前治具索引"] % 4 + 1);

	RUN_STOP_IF_ERROR(m_pRobot->ProcPrepare(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->PushImageFlow(g_pImageFlowDn[m_mapIndex["当前治具索引"] % 4], NULL, g_pImageWndDn, "下相机", sName, "", false, false, -1, false, m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->ProcStart(m_mapIndex["当前治具索引"] % 4));

	Sleep(50);

	RUN_STOP_IF_ERROR(g_pCamera->Trigger("Dn"));

	m_mapTick["拍照计时"] = GetTickCount();

	RETURN_STATE(&CLogicRobot::OnRobot39_9, true);
}

CStatus CLogicRobot::OnRobot39_9()
{
	if (!m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].bProcOk) {
		if (GetTickCount() - m_mapTick["拍照计时"] > 8000) {
			CDat::IncreaseValue("下相机拍照失败数");
			REPORT("下相机拍照超时,即将重新拍照!", emLogLevelError);
			MESSAGEBOX("下相机拍照超时,即将重新拍照!", "", false);
			RETURN_STATE(&CLogicRobot::OnRobot39_7, true);
		}
		else {
			RETURN_SELF("等待拍照完成", false);
		}
	}

	double nResultPixelX, nResultPixelY, nResultPixelR = 0;

	bool bExistFlag = false;

	m_sRet = g_pImageFlowDn[m_mapIndex["当前治具索引"] % 4]->GetResult(nResultPixelX, nResultPixelY, nResultPixelR, bExistFlag);

	if (m_sRet != "OK") {
		RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumStatus(m_mapIndex["当前治具索引"] % 4));
		if (m_sRet == "On") {
			m_mapIndex["重复拍照计数"]++;
			if (m_mapIndex["重复拍照计数"] > 3) {
				m_mapIndex["重复拍照计数"] = 0;
				REPORT("吸嘴图像处理失败,请调整模板后重新拍照!", emLogLevelWarn);
				MESSAGEBOX("吸嘴图像处理失败,请调整模板后重新拍照!", "", false);
			}
			else {
				REPORT("吸嘴图像处理失败,即将重新拍照!", emLogLevelWarn);
			}
			m_pRobot->DnCameraLightOn();
			m_pRobot->RegisterCamera();
			RETURN_STATE(&CLogicRobot::OnRobot39_7, true);
		}
		else {
			m_mapIndex["当前治具索引"]++;
			RUN_STOP_IF_ERROR(m_pRobot->DnCameraLightOff());
			RETURN_STATE(&CLogicRobot::OnRobot39_6, true);
		}
	}

	double nOffX = 0, nOffY = 0, nRealOffX = 0, nRealOffY = 0;

	g_pImageCalibrateDn->CalculateOffset(
		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].nHeadPixelX,
		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].nHeadPixelY,
		-m_stMarkInfo[m_mapIndex["当前治具索引"]].nRotateR - m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].nHeadPixelR + nResultPixelR,
		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].nHeadMachineX,
		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].nHeadMachineY,
		nOffX,
		nOffY);

	g_pImageCalibrateDn->CalculateOffset(
		nResultPixelX,
		nResultPixelY,
		-m_stMarkInfo[m_mapIndex["当前治具索引"]].nRotateR - m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].nHeadPixelR + nResultPixelR,
		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].nHeadMachineX,
		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].nHeadMachineY,
		nRealOffX,
		nRealOffY);

	CString sPos;
	sPos.Format("%s轨位置%d贴装补偿", (m_mapIndex["当前治具索引"] <= 3 ? "A" : "B"), m_mapIndex["当前治具索引"] % 4 + 1);

	VAR_ROBOT(sPos + "X") = VAR_ROBOT_D(sPos + "X") + nRealOffX - nOffX;
	VAR_ROBOT(sPos + "Y") = VAR_ROBOT_D(sPos + "Y") + nRealOffY - nOffY;
	VAR_ROBOT(sPos + "R") = VAR_ROBOT_D(sPos + "R") + nResultPixelR - m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].nHeadPixelR;

	m_stMarkInfo[m_mapIndex["当前治具索引"]].bCalcFeedFlag = true;

	m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadPickOk = true;
	m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadProcOk = false;
	m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadThrowFlag = false;

	m_mapIndex["当前治具索引"]++;
	RUN_STOP_IF_ERROR(m_pRobot->DnCameraLightOff());

	RETURN_STATE(&CLogicRobot::OnRobot39_6, true);
}

CStatus CLogicRobot::OnRobot40()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到主板待装配位", true);
	}
	
	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumStatus(m_mapIndex["当前吸嘴索引"]));

	if (m_sRet != "On") {
		m_sRet.Format("吸嘴%d上无料，可能发生掉料,请检查吸嘴真空信号是否有异常，并取走因真空信号异常导致误报的主板！", m_mapIndex["当前吸嘴索引"] + 1);
		REPORT(m_sRet, emLogLevelError);
		MESSAGEBOX(m_sRet, "", false);
		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadProcOk = false;
		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadPickOk = false;
		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadThrowFlag = false;
		RETURN_STATE(&CLogicRobot::OnRobot39, true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot41, true);
}

CStatus CLogicRobot::OnRobot41()
{
	CString sPosition;
	sPosition = "B轨主板装配位";

	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint[sPosition]->z + VAR_ROBOT_D("B轨贴装高度偏移");
	m_stDstRobPnt.speed = m_pRobot->m_mapRobotPoint[sPosition]->speed;

	RETURN_STATE(&CLogicRobot::OnRobot42, true);
}

CStatus CLogicRobot::OnRobot42()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt, true, false, NULL, VAR_ROBOT_I("机械手Z轴下降速度百分比")));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderOn(m_mapIndex["当前吸嘴索引"]));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderStatus(m_mapIndex["当前吸嘴索引"]));

	if (m_sRet != "On") {
		if (m_mapFlag["吸嘴气缸伸出标志"]) {
			if (GetTickCount() - m_mapTick["吸嘴气缸伸出计时"] < 3000) {
				m_sRet.Format("等待吸嘴气缸%d伸出", m_mapIndex["当前吸嘴索引"] + 1);
				RETURN_SELF(m_sRet, true);
			}
		}
		else {
			m_mapFlag["吸嘴气缸伸出标志"] = true;
			m_mapTick["吸嘴气缸伸出计时"] = GetTickCount();
			m_sRet.Format("等待吸嘴气缸%d伸出", m_mapIndex["当前吸嘴索引"] + 1);
			RETURN_SELF(m_sRet, true);
		}
	}

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到主板装配位", true);
	}

	m_mapFlag["吸嘴气缸伸出标志"] = false;

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderBrokenVacuumOn(m_mapIndex["当前吸嘴索引"]));

	Sleep(VAR_ROBOT_I("吸嘴贴装延时"));

	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;

	if (VAR_ROBOT_B("调试模式")) {
		RETURN_PAUSE(&CLogicRobot::OnRobot43);
	}

	RETURN_STATE(&CLogicRobot::OnRobot43, true);
}

CStatus CLogicRobot::OnRobot43()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderOff(m_mapIndex["当前吸嘴索引"]));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderStatus(m_mapIndex["当前吸嘴索引"]));

	if (m_sRet != "Off") {
		m_sRet.Format("等待吸嘴气缸%d缩回", m_mapIndex["当前吸嘴索引"] + 1);
		RETURN_SELF(m_sRet, true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到主板待装配位", true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumOff(m_mapIndex["当前吸嘴索引"]));
	
	RETURN_STATE(&CLogicRobot::OnRobot44, true);
}

CStatus CLogicRobot::OnRobot43_0()
{
	if (m_mapIndex["当前治具索引"] >= 8) {
		m_mapIndex["当前治具索引"] = 4;
		RETURN_STATE(&CLogicRobot::OnRobot43_3_0, true);
	}

	if (VAR_ROBOT_B("双主板装配模式") && (m_mapIndex["当前治具索引"] % 4 == 1 || m_mapIndex["当前治具索引"] % 4 == 3)) {
		m_stMarkInfo[m_mapIndex["当前治具索引"]].bLookBackProcOk = true;
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	if (m_stMarkInfo[m_mapIndex["当前治具索引"]].bLookBackProcOk) {
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	if (VAR_MACHINE_B("弹片检测功能启用") || VAR_MACHINE_B("弹片检测功能只检测不判断") || VAR_MACHINE_B("MES功能启用") || VAR_MACHINE_B("MES过站功能启用") || VAR_MACHINE_B("MES治具绑定功能启用")) {
		RETURN_STATE(&CLogicRobot::OnRobot43_1, true);
	}

	if (!m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bFromTray) {
		m_stMarkInfo[m_mapIndex["当前治具索引"]].bLookBackProcOk = true;
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	RETURN_STATE(&CLogicRobot::OnRobot43_1, true);
}

CStatus CLogicRobot::OnRobot43_1()
{
	ROBOTPOINT rob = *g_pRobot->m_mapRobotPoint["B轨左下角Mark拍照位"];

	rob.x = m_stMarkInfo[m_mapIndex["当前治具索引"]].nLookBackMachineX;
	rob.y = m_stMarkInfo[m_mapIndex["当前治具索引"]].nLookBackMachineY;

	RUN_STOP_IF_ERROR(m_pRobot->Move(rob));

	RUN_STOP_IF_ERROR(m_pRobot->UpCameraLightOn());

	g_pCamera->SetExposureTime("Up", VAR_ROBOT_I("上相机(皮带)曝光时间"));
	g_pCamera->SetGain("Up", VAR_ROBOT_I("上相机(皮带)增益"));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(rob));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到治具拍照位", true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot43_2, true);
}

CStatus CLogicRobot::OnRobot43_2()
{
	RUN_STOP_IF_ERROR(m_pRobot->ProcPrepare(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->PushImageFlow(g_pImageFlowUp[m_mapIndex["当前治具索引"] % 4], g_pImageFlowScanCodeMainBoard[m_mapIndex["当前治具索引"] % 4], g_pImageWndUp, "主板回拍", "", "", true, false, m_mapIndex["当前治具索引"] % 4, true, m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->ProcStart(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(g_pCamera->Trigger("Up"));

	m_mapTick["拍照计时"] = GetTickCount();

	RETURN_STATE(&CLogicRobot::OnRobot43_3, true);
}

CStatus CLogicRobot::OnRobot43_3()
{
	if (!m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].bTakePicOk) {
		if (GetTickCount() - m_mapTick["拍照计时"] > 8000) {
			CDat::IncreaseValue("上相机拍照失败数");
			REPORT("上相机拍照超时,请先点击确定后点击开始重试!", emLogLevelError);
			MESSAGEBOX("上相机拍照超时,请先点击确定后点击开始重试!", "", false);
			m_pRobot->UpCameraLightOn();
			m_pRobot->RegisterCamera();
			RETURN_STATE(&CLogicRobot::OnRobot43_2, true);
		}
		else {
			RETURN_SELF("等待拍照完成", false);
		}
	}

	RUN_STOP_IF_ERROR(m_pRobot->UpCameraLightOff());

	m_mapIndex["当前治具索引"]++;

	RETURN_STATE(&CLogicRobot::OnRobot43_0, true);
}

CStatus CLogicRobot::OnRobot43_3_0()
{
	if (m_mapIndex["当前治具索引"] >= 8) {
		m_mapIndex["当前吸嘴索引"] = 0;
		m_mapIndex["当前治具索引"] = 4;

		bool bFlag = false;

		for (int i=4; i<8; i++)
		{
			if (VAR_ROBOT_B("双主板装配模式") && (i % 4 == 1 || i % 4 == 3)) {
				continue;
			}

			if (!m_stMarkInfo[i].bLookBackProcOk) {
				bFlag = true;
				break;
			}
		}

		if (bFlag) {
			m_mapIndex["当前治具索引"] = 4;
			RETURN_STATE(&CLogicRobot::OnRobot43_0, true);
		}

		bFlag = true;

		for (int i=4; i<8; i++)
		{
			if (VAR_ROBOT_B("双主板装配模式") && (i % 4 == 1 || i % 4 == 3)) {
				continue;
			}

			if (!m_stMarkInfo[i].bAssembleOk) {
				bFlag = false;
				break;
			}
		}

		if (bFlag) {
			// 生产周期监控
			g_pMachine->m_listProductionCycle.push_back(g_pMachine->m_stProductionCircle);

			g_pMachine->m_stProductionCircle.bFree = false;

			while (true)
			{
				if (g_pMachine->m_listProductionCycle.size() > VAR_MACHINE_I("生产周期监控数量")) {
					g_pMachine->m_listProductionCycle.pop_front();
				}
				else {
					break;
				}
			}

			if (true) {
				CString strProductionCycleStatus = "生产周期监控信息:";

				list<PRODUCTIONCYCLE>::iterator it = g_pMachine->m_listProductionCycle.begin();
				for (; it != g_pMachine->m_listProductionCycle.end(); it++)
				{
					if (it->bFree) {
						strProductionCycleStatus += " 1";
					}
					else {
						strProductionCycleStatus += " 0";
					}
				}

				REPORT(strProductionCycleStatus, emLogLevelNormal);
			}

			if (g_pMachine->m_listProductionCycle.size() >= VAR_MACHINE_I("生产周期监控数量")) {
				g_pMachine->m_listProductionCycle.pop_front();

				int nFreeCnt = 0, nContinueCnt = 0;

				if (!VAR_MACHINE_B("托盘优先模式")) {
					list<PRODUCTIONCYCLE>::iterator it = g_pMachine->m_listProductionCycle.begin();
					for (; it != g_pMachine->m_listProductionCycle.end(); it++)
					{
						if (it->bFree) {
							nFreeCnt++;
							nContinueCnt++;
						}
						else {
							nContinueCnt = 0;
						}

						if (nContinueCnt >= 3) {
							REPORT("连续3个生产周期内出现周期待料超时,切换到托盘优先模式", emLogLevelWarn);
							VAR_MACHINE("托盘优先模式") = true;
							break;
						}
					}

					if (!VAR_MACHINE_B("托盘优先模式") && nFreeCnt >= VAR_MACHINE_I("生产周期待料周期数量")) {
						CString str;
						str.Format("在连续%d个生产周期内，出现%d次待料周期,切换到托盘优先模式", VAR_MACHINE_I("生产周期监控数量"), VAR_MACHINE_I("生产周期待料周期数量"));
						REPORT(str, emLogLevelWarn);
						VAR_MACHINE("托盘优先模式") = true;
					}
				}
			}

			CSys::m_nTotalSum++;
			VAR_BELTB("允许机械手装配主板标志") = false;
			VAR_BELTB("机械手装配主板完成标志") = true;
			VAR_BELTB("治具提前取上盖标志") = true;
			for (int i=4; i<8; i++)
			{
				m_stMarkInfo[i].nProcTimes = 0;
				m_stMarkInfo[i].bLookBackProcOk = false;
				m_stMarkInfo[i].bMarkProcOk = false;
				m_stMarkInfo[i].bAssembleOk = false;
			}

			MANNUAL_MODE();

			RETURN_STATE(&CLogicRobot::OnRobot00, true);
		}
		else {
			bFlag = false;

			for (int i=0; i<4; i++)
			{
				if (VAR_ROBOT_B("双主板装配模式") && (i % 4 == 1 || i % 4 == 3)) {
					continue;
				}

				if (m_stHeadProcResult[i].bHeadThrowFlag) {
					bFlag = true;
				}
			}

			if (bFlag) {
				RETURN_STATE(&CLogicRobot::OnRobot30, true);
			}

			RETURN_STATE(&CLogicRobot::OnRobot00, true);
		}
	}

	if (m_stMarkInfo[m_mapIndex["当前治具索引"]].bLookBackProcOk) {
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	m_mapTick["图像处理计时"] = GetTickCount();

	RETURN_STATE(&CLogicRobot::OnRobot43_3_1, true);
}

CStatus CLogicRobot::OnRobot43_3_1()
{
	bool bProcTimeOut = false;

	if (!m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].bProcOk) {
		if (GetTickCount() - m_mapTick["图像处理计时"] > 3000) {
			CDat::IncreaseValue("上相机拍照失败数");
			bProcTimeOut = true;
		}
		else {
			RETURN_SELF("等待图像处理完成", false);
		}
	}

	double nResultPixelX, nResultPixelY, nResultPixelR = 0;
	bool bExistFlag = false;

	if (!bProcTimeOut) {
		m_sRet = g_pImageFlowUp[m_mapIndex["当前治具索引"] % 4]->GetResult(nResultPixelX, nResultPixelY, nResultPixelR, bExistFlag);
	}
	else {
		m_sRet = "图像处理超时";
	}

	if (m_sRet != "OK") {
		if (bProcTimeOut) {
			m_sRet.Format("%s治具位置%d图像处理超时!", m_mapIndex["当前治具索引"] < 4 ? "A轨（2轨）" : "B轨（1轨）", m_mapIndex["当前治具索引"] % 4 + 1);
		}
		else {
			m_sRet.Format("%s治具位置%d图像处理失败!", m_mapIndex["当前治具索引"] < 4 ? "A轨（2轨）" : "B轨（1轨）", m_mapIndex["当前治具索引"] % 4 + 1);
		}

		REPORT(m_sRet, emLogLevelWarn);

		m_stMarkInfo[m_mapIndex["当前治具索引"]].nProcTimes++;

		if (m_stMarkInfo[m_mapIndex["当前治具索引"]].nProcTimes >= 3) {
			m_stMarkInfo[m_mapIndex["当前治具索引"]].nProcTimes = 0;
			MESSAGEBOX(m_sRet, "", false);
		}
		
		m_mapIndex["当前治具索引"]++;

		RETURN_STATE(&CLogicRobot::OnRobot43_3_0, true);
	}

	m_stMarkInfo[m_mapIndex["当前治具索引"]].nProcTimes = 0;
	m_stMarkInfo[m_mapIndex["当前治具索引"]].bLookBackProcOk = true;

// 	if (VAR_MACHINE_B("MES功能启用") && !m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].bMesResult) {
// 		CString sMsg;
// 		sMsg.Format("B轨治具主板%d查询MES结果失败", m_mapIndex["当前治具索引"] % 4 + 1);
// 		REPORT(sMsg, emLogLevelWarn);
// 
// 		m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["B轨主板装配位"];
// 		m_stDstRobPnt.x = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineX;
// 		m_stDstRobPnt.y = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineY;
// 		m_stDstRobPnt.r = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineR;
// 		m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;
// 
// 		m_stMarkInfo[m_mapIndex["当前治具索引"]].bAssembleOk = false;
// 
// 		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadPickOk = true;
// 		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadThrowFlag = true;
// 		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bMesNgFlag = true;
// 
// 		RETURN_STATE(&CLogicRobot::OnRobot43_4, true);
// 	}

	if (VAR_MACHINE_B("弹片检测功能启用") && m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].bBadCheckResult) {
		CString sMsg;
		sMsg.Format("B轨治具主板%d弹片检测失败", m_mapIndex["当前治具索引"] % 4 + 1);
		REPORT(sMsg, emLogLevelWarn);

		m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["B轨主板装配位"];
		m_stDstRobPnt.x = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineX;
		m_stDstRobPnt.y = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineY;
		m_stDstRobPnt.r = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineR;
		m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;

		m_stMarkInfo[m_mapIndex["当前治具索引"]].bAssembleOk = false;

		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadPickOk = true;
		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadThrowFlag = true;

		RETURN_STATE(&CLogicRobot::OnRobot43_4, true);
	}

// 	if (!CSys::m_sFirstCode.IsEmpty() && m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].sCode2D.Find(CSys::m_sFirstCode) < 0) {
// 		CString sMsg;
// 		sMsg.Format("B轨治具主板%d主板二维码和首件二维码不匹配", m_mapIndex["当前治具索引"] % 4 + 1);
// 
// 		m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["B轨主板装配位"];
// 		m_stDstRobPnt.x = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineX;
// 		m_stDstRobPnt.y = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineY;
// 		m_stDstRobPnt.r = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineR;
// 		m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;
// 
// 		m_stMarkInfo[m_mapIndex["当前治具索引"]].bAssembleOk = false;
// 
// 		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadPickOk = true;
// 		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadThrowFlag = true;
// 
// 		RETURN_STATE(&CLogicRobot::OnRobot43_4, true);
// 	}

	CString sCode2D;
	sCode2D = m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].sCode2D;

	CDat::UpdateValue("主板SN号", sCode2D);

		// 更新轨道号 (1=A轨, 2=B轨, 3=C轨)

		// 同步更新设备资产编码（确保与Machine模块参数同步）
		if (g_pMachine != NULL) {
			//CDat::UpdateValue("设备资产编码", VAR_MACHINE_S("设备资产编码"));
			CDat::UpdateValue("程序名", CString(CPro::m_strPro.c_str()));
			CDat::UpdateValue("程序路径", GetModulePath().c_str());
		}
		CDat::UpdateValue("轨道号", m_mapIndex["当前来料索引"] + 1);

	if (VAR_MACHINE_B("MES功能启用")) {
		CString sMesRes;

		bool bFlag = false;
		bFlag = CMes::HttpPost(VAR_MACHINE_S("MES服务器地址"), 80, sCode2D, VAR_MACHINE_S("MES用户名"), VAR_MACHINE_S("MES密码"), VAR_MACHINE_S("MES工厂"), &sMesRes);

		REPORT("Mes回传数据: " + sMesRes, emLogLevelNormal);

		if (!bFlag) {
			CString sMsg;
			sMsg.Format("B轨治具主板%d Mes查询结果失败", m_mapIndex["当前治具索引"] % 4 + 1);

			m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["B轨主板装配位"];
			m_stDstRobPnt.x = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineX;
			m_stDstRobPnt.y = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineY;
			m_stDstRobPnt.r = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineR;
			m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;

			m_stMarkInfo[m_mapIndex["当前治具索引"]].bAssembleOk = false;

			m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadPickOk = true;
			m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadThrowFlag = true;

			RETURN_STATE(&CLogicRobot::OnRobot43_4, true);
		}
	}

	if (VAR_MACHINE_B("MES过站功能启用")) {
		CString sMesRes;

		bool bFlag = false;
		bFlag = CMes::HttpPostPassStation(VAR_MACHINE_S("MES过站服务器地址"), 80, m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].sCode2D, VAR_MACHINE_S("MES过站拉线ID"), VAR_MACHINE_S("MES过站工序"), VAR_MACHINE_S("MES过站用户名"), VAR_MACHINE_S("MES过站密码"), VAR_MACHINE_S("MES过站工厂"), &sMesRes);
		
		REPORT("Mes过站回传数据: " + sMesRes, emLogLevelNormal);

		if (!bFlag && sMesRes.Find("工序不匹配") < 0) {
			MESSAGEBOX("Mes过站回传数据: " + sMesRes, "", false);
		}
	}

	if (VAR_MACHINE_B("MES治具绑定功能启用")) {
		CString sMesRes;

		bool bFlag = false;
		bFlag = CMes::HttpPostFixture(VAR_MACHINE_S("MES治具绑定服务器地址"), 80, m_sFixtureCodeB, m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].sCode2D, VAR_MACHINE_S("MES治具绑定拉线ID"), VAR_MACHINE_S("MES治具绑定站点"), VAR_MACHINE_S("MES治具绑定工序"), VAR_MACHINE_S("MES治具绑定用户名"), VAR_MACHINE_S("MES治具绑定密码"), VAR_MACHINE_S("MES治具绑定工厂"), &sMesRes);
		
		REPORT("Mes治具绑定回传数据: " + sMesRes, emLogLevelNormal);

		if (!bFlag) {
			MESSAGEBOX("Mes治具绑定回传数据: " + sMesRes, "", false);
		}
	}

	m_mapIndex["当前治具索引"]++;

	RETURN_STATE(&CLogicRobot::OnRobot43_3_0, true);
}

CStatus CLogicRobot::OnRobot43_4()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到主板待装配位", true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumOn(m_mapIndex["当前治具索引"] % 4));

	RETURN_STATE(&CLogicRobot::OnRobot43_5, true);
}

CStatus CLogicRobot::OnRobot43_5()
{
	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderOn(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderStatus(m_mapIndex["当前治具索引"] % 4));

	if (m_sRet != "On") {
		m_sRet.Format("等待吸嘴气缸%d伸出", m_mapIndex["当前治具索引"] % 4 + 1);
		RETURN_SELF(m_sRet, true);
	}

	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["B轨主板装配位"]->z + VAR_ROBOT_D("治具取主板高度偏移");

	RETURN_STATE(&CLogicRobot::OnRobot43_6, true);
}

CStatus CLogicRobot::OnRobot43_6()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt, true, false, NULL, VAR_ROBOT_I("机械手Z轴下降速度百分比")));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到主板装配位", true);
	}

	Sleep(300);

	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;

	RETURN_STATE(&CLogicRobot::OnRobot43_7, true);
}

CStatus CLogicRobot::OnRobot43_7()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt, true, true, NULL, VAR_ROBOT_I("机械手Z轴上升速度百分比")));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderOff(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderStatus(m_mapIndex["当前治具索引"] % 4));

	if (m_sRet != "Off") {
		m_sRet.Format("等待吸嘴气缸%d缩回", m_mapIndex["当前治具索引"] % 4 + 1);
		RETURN_SELF(m_sRet, true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到主板待装配位", true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumStatus(m_mapIndex["当前治具索引"] % 4));

	if (m_sRet != "On") {
		REPORT("吸嘴未取到料，请确认！", emLogLevelWarn);
		MESSAGEBOX("吸嘴未取到料，请确认！", "", true);
		RETURN_STATE(&CLogicRobot::OnRobot43_5, true);
	}

	m_mapIndex["当前治具索引"]++;

	RETURN_STATE(&CLogicRobot::OnRobot43_3_0, true);
}

CStatus CLogicRobot::OnRobot44()
{
	m_stMarkInfo[m_mapIndex["当前治具索引"]].bAssembleOk = true;

		// 更新生产结束时间和总时间
		CTime endTime = CTime::GetCurrentTime();
		CString sEndTime = endTime.Format("%Y-%m-%d %H:%M:%S");
		CDat::UpdateValue("生产结束时间", sEndTime);

		// 计算生产总时间(秒)
		// 注意：这里简化处理，实际应该记录开始时间并计算差值
		CDat::UpdateValue("生产总时间", 0.0);
	m_stMarkInfo[m_mapIndex["当前治具索引"]].bFromTray = m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bFromTray;

	m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadProcOk = false;
	m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadPickOk = false;
	m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadThrowFlag = false;

	m_mapIndex["当前治具索引"]++;

	RETURN_STATE(&CLogicRobot::OnRobot39, true);
}

CStatus CLogicRobot::OnRobot45()
{
	bool bFlag = true;

	for (int i=0; i<4; i++)
	{
		if (m_stHeadProcResult[i].bHeadProcOk) {
			bFlag = false;
			break;
		}
	}

	if (bFlag) {
		m_mapIndex["当前吸嘴索引"] = 0;
		RETURN_STATE(&CLogicRobot::OnRobot00, true);
	}

	if (!VAR_BELTA_B("允许机械手装配主板标志")) {
		if (!VAR_BELTB_B("允许机械手装配主板标志")) {
			RETURN_STATE(&CLogicRobot::OnRobot00, true);
		}

		m_mapIndex["当前治具索引"] = 4;
		RETURN_STATE(&CLogicRobot::OnRobot34, true);
	}

	if (m_mapIndex["当前治具索引"] >= 4) {
		m_mapIndex["当前治具索引"] = 0;
		m_mapFlag["自动计算补偿标志"] = true;
		if (VAR_ROBOT_B("治具二维码扫描功能启用") || VAR_MACHINE_B("MES治具绑定功能启用")) {
			RETURN_STATE(&CLogicRobot::OnRobot45_0, true);
		}
		else {
			RETURN_STATE(&CLogicRobot::OnRobot50, true);
		}
	}

	if (m_stMarkInfo[m_mapIndex["当前治具索引"]].bAssembleOk) {
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	if (m_stMarkInfo[m_mapIndex["当前治具索引"]].bMarkProcOk) {
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	if (VAR_ROBOT_B("双主板装配模式") && m_mapIndex["当前治具索引"] % 4 == 3) {
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	RETURN_STATE(&CLogicRobot::OnRobot46, true);
}

CStatus CLogicRobot::OnRobot45_0()
{
	if (!VAR_ROBOT_B("治具二维码扫描功能启用") && !VAR_MACHINE_B("MES治具绑定功能启用")) {
	RETURN_STATE(&CLogicRobot::OnRobot50, true);
	}

	double nUpCamOffMachineX = 0, nUpCamOffMachineY = 0;
	nUpCamOffMachineX = VAR_ROBOT_D("上相机中心基于旋转中心坐标X");
	nUpCamOffMachineY = VAR_ROBOT_D("上相机中心基于旋转中心坐标Y");

	int nFixtureIndex = 0;
	nFixtureIndex = m_mapIndex["当前治具索引"] < 4 ? 0 : 4;

	double nUpCamCenterMachineX = 0, nUpCamCenterMachineY = 0;
	g_pImageCalibrateUp->TransToMachine(VAR_ROBOT_D("上相机中心坐标X"), VAR_ROBOT_D("上相机中心坐标Y"), nUpCamCenterMachineX, nUpCamCenterMachineY);

	double nUpCalcMachineX = 0, nUpCalcMachineY = 0;
	g_pImageCalibrateUp->TransToMachine(m_stMarkInfo[nFixtureIndex].nMarkPixelX, m_stMarkInfo[nFixtureIndex].nMarkPixelY, nUpCalcMachineX, nUpCalcMachineY);

	double nUpCalcOffX = 0, nUpCalcOffY = 0;
	nUpCalcOffX = nUpCalcMachineX - nUpCamCenterMachineX;
	nUpCalcOffY = nUpCalcMachineY - nUpCamCenterMachineY;

	TRACE("主板装配位%d上相机计算结果基于图像中心： %.3f, %.3f\n", m_mapIndex["当前治具索引"] + 1, nUpCalcOffX, nUpCalcOffY);

	double nUpOffX = 0, nUpOffY = 0;
	nUpOffX = nUpCamOffMachineX - nUpCalcOffX;
	nUpOffY = nUpCamOffMachineY - nUpCalcOffY;	

	TRACE("主板装配位%d上相机处理结果基于旋转中心： %.3f, %.3f\n", m_mapIndex["当前治具索引"] + 1, nUpOffX, nUpOffY);

	double nMark1MachineX = 0, nMark1MachineY = 0;
	nMark1MachineX = m_stMarkInfo[nFixtureIndex].nMarkMachineX + nUpOffX;
	nMark1MachineY = m_stMarkInfo[nFixtureIndex].nMarkMachineY + nUpOffY;

	ROBOTPOINT rob = *g_pRobot->m_mapRobotPoint["A轨左下角Mark拍照位"];

	rob.x +=  VAR_ROBOT_D("治具二维码位置偏移X");
	rob.y -=  VAR_ROBOT_D("治具二维码位置偏移Y");

	RUN_STOP_IF_ERROR(m_pRobot->Move(rob));

	RUN_STOP_IF_ERROR(m_pRobot->UpCameraLightOn());

	g_pCamera->SetExposureTime("Up", VAR_ROBOT_I("上相机(治具二维码)曝光时间"));
	g_pCamera->SetGain("Up", VAR_ROBOT_I("上相机(治具二维码)增益"));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(rob));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到治具二维码扫描拍照位", true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot45_1, true);
}

CStatus CLogicRobot::OnRobot45_1()
{
	RUN_STOP_IF_ERROR(m_pRobot->ProcPrepare(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->PushImageFlow(NULL, g_pImageFlowScanCodeFixture, g_pImageWndUp, "治具二维码扫描", "", "", true, false, m_mapIndex["当前治具索引"] % 4, true, m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->ProcStart(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(g_pCamera->Trigger("Up"));

	m_mapTick["拍照计时"] = GetTickCount();

	RETURN_STATE(&CLogicRobot::OnRobot45_2, true);
}

CStatus CLogicRobot::OnRobot45_2()
{
	if (!m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].bProcOk) {
		if (GetTickCount() - m_mapTick["拍照计时"] > 8000) {
			CDat::IncreaseValue("上相机拍照失败数");
			REPORT("上相机拍照超时,请先点击确定后点击开始重试!", emLogLevelError);
			MESSAGEBOX("上相机拍照超时,请先点击确定后点击开始重试!", "", false);
			m_pRobot->UpCameraLightOn();
			m_pRobot->RegisterCamera();
			RETURN_STATE(&CLogicRobot::OnRobot45_1, true);
		}
		else {
			RETURN_SELF("等待拍照完成", false);
		}
	}

	RUN_STOP_IF_ERROR(m_pRobot->UpCameraLightOff());

	RETURN_STATE(&CLogicRobot::OnRobot45_3, true);
}

CStatus CLogicRobot::OnRobot45_3()
{
	m_sFixtureCodeA.Format("%s", m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].sCode2D);

	REPORT(CString("A轨道扫描到的治具二维码为： ") + m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].sCode2D, emLogLevelNormal);
	
	if (m_sFixtureCodeB.IsEmpty()) {
		MESSAGEBOX("A轨道治具二维码扫描失败", "", false);
		RETURN_STATE(&CLogicRobot::OnRobot45_0, true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot50, true);
}

CStatus CLogicRobot::OnRobot46()
{
	switch (m_mapIndex["当前治具索引"])
	{
	case 0:
		m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["A轨左下角Mark拍照位"];
		break;
	case 1:
		m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["A轨右下角Mark拍照位"];
		break;
	case 2:
		m_mapIndex["当前治具索引"]++;
		RETURN_STATE(&CLogicRobot::OnRobot45, true);
		break;
	case 3:
		m_mapIndex["当前治具索引"]++;
		RETURN_STATE(&CLogicRobot::OnRobot45, true);
		break;
	case 4:
		m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["B轨左下角Mark拍照位"];
		break;
	case 5:
		if (VAR_ROBOT_B("MARK全拍模式")) {
			m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["B轨右下角Mark拍照位"];
		}
		else {
			m_mapIndex["当前治具索引"]++;
			RETURN_STATE(&CLogicRobot::OnRobot45, true);
		}
		break;
	case 6:
		m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["B轨右上角Mark拍照位"];
		break;
	case 7:
		if (VAR_ROBOT_B("MARK全拍模式")) {
			m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["B轨左上角Mark拍照位"];
		}
		else {
			m_mapIndex["当前治具索引"]++;
			RETURN_STATE(&CLogicRobot::OnRobot45, true);
		}
		break;
	default:break;
	}

	RETURN_STATE(&CLogicRobot::OnRobot47, true);
}

CStatus CLogicRobot::OnRobot47()
{
	RUN_STOP_IF_ERROR(m_pRobot->UpCameraLightOn());

	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt));

	g_pCamera->SetExposureTime("Up", VAR_ROBOT_I("上相机(治具)曝光时间"));
	g_pCamera->SetGain("Up", VAR_ROBOT_I("上相机(治具)增益"));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到治具拍照位", true);
	}

	m_stMarkInfo[m_mapIndex["当前治具索引"]].nMarkMachineX = m_stDstRobPnt.x;
	m_stMarkInfo[m_mapIndex["当前治具索引"]].nMarkMachineY = m_stDstRobPnt.y;
	m_stMarkInfo[m_mapIndex["当前治具索引"]].nMarkMachineR = m_stDstRobPnt.r;

	m_mapIndex["重复拍照计数"] = 0;

	RETURN_STATE(&CLogicRobot::OnRobot48, true);
}

CStatus CLogicRobot::OnRobot48()
{
	CString sName;

	sName.Format("%s轨-Mark%d", m_mapIndex["当前治具索引"] < 4 ? "A" : "B", (m_mapIndex["当前治具索引"] % 4) + 1);

	RUN_STOP_IF_ERROR(m_pRobot->ProcPrepare(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->PushImageFlow(g_pImageFlowMark[m_mapIndex["当前治具索引"] % 4], NULL, g_pImageWndUp, "Mark", sName, "", false, false, -1, false, m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->ProcStart(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(g_pCamera->Trigger("Up"));

	m_mapTick["拍照计时"] = GetTickCount();

	RETURN_STATE(&CLogicRobot::OnRobot49, true);
}

CStatus CLogicRobot::OnRobot49()
{
	if (!m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].bProcOk) {
		if (GetTickCount() - m_mapTick["拍照计时"] > 8000) {
			CDat::IncreaseValue("上相机拍照失败数");
			REPORT("上相机拍照超时,请先点击确定后点击开始重试!", emLogLevelError);
			MESSAGEBOX("上相机拍照超时,请先点击确定后点击开始重试!", "", false);
			m_pRobot->UpCameraLightOn();
			m_pRobot->RegisterCamera();
			RETURN_STATE(&CLogicRobot::OnRobot48, true);
		}
		else {
			RETURN_SELF("等待拍照完成", true);
		}
	}

	double nPixelX = 0, nPixelY = 0, nPixelR = 0;

	bool bExistFlag = false;

	m_sRet = g_pImageFlowMark[m_mapIndex["当前治具索引"] % 4]->GetResult(nPixelX, nPixelY, nPixelR, bExistFlag, 0);

	if (m_sRet != "OK") {
		m_mapIndex["重复拍照计数"]++;
		if (m_mapIndex["重复拍照计数"] > 3) {
			m_mapIndex["重复拍照计数"] = 0;
			REPORT("Mark图像处理失败,请调整模板后重新拍照!", emLogLevelWarn);
			MESSAGEBOX("Mark图像处理失败,请调整模板后重新拍照!", "", false);
		}
		else {
			REPORT("Mark图像处理失败,重新拍照", emLogLevelWarn);
		}

		m_pRobot->UpCameraLightOn();
		m_pRobot->RegisterCamera();
		RETURN_STATE(&CLogicRobot::OnRobot48, true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->UpCameraLightOff());

	m_stMarkInfo[m_mapIndex["当前治具索引"]].nMarkPixelX = nPixelX;
	m_stMarkInfo[m_mapIndex["当前治具索引"]].nMarkPixelY = nPixelY;
	m_stMarkInfo[m_mapIndex["当前治具索引"]].nMarkPixelR = nPixelR;
	m_stMarkInfo[m_mapIndex["当前治具索引"]].bMarkProcOk = true;

	m_mapIndex["当前治具索引"]++;

	RETURN_STATE(&CLogicRobot::OnRobot45, true);
}

CStatus CLogicRobot::OnRobot50()
{
	if (m_mapIndex["当前治具索引"] >= 4) {
		bool bFlag = true;

		for (int i=0; i<4; i++)
		{
			if (VAR_ROBOT_B("双主板装配模式") && (i % 4 == 1 || i % 4 == 3)) {
				continue;
			}

			if (!m_stMarkInfo[i].bAssembleOk) {
				bFlag = false;
				break;
			}
		}

		if (bFlag) {
			// 自动计算补偿
			if (VAR_ROBOT_B("A轨自动计算补偿") && m_mapFlag["自动计算补偿标志"]) {
				for (int i=0; i<4; i++)
				{
					if (VAR_ROBOT_B("双主板装配模式") && (i % 4 == 1 || i % 4 == 3)) {
						continue;
					}

					if (!m_stMarkInfo[i].bCalcFeedFlag) {
						RETURN_STATE(&CLogicRobot::OnRobot50_0, true);
					}
				}

				for (int i=0; i<4; i++)
				{
					m_stMarkInfo[i].bCalcFeedFlag = false;
				}

				VAR_ROBOT("A轨自动计算补偿") = false;
			}

			// 主板回拍
			if (VAR_MACHINE_B("弹片检测功能启用") || VAR_MACHINE_B("弹片检测功能只检测不判断") || VAR_MACHINE_B("MES功能启用") || VAR_MACHINE_B("MES过站功能启用") || VAR_MACHINE_B("MES治具绑定功能启用")) {
				m_mapIndex["当前治具索引"] = 0;
				RETURN_STATE(&CLogicRobot::OnRobot54_0, true);
			}

			// 生产周期监控
			g_pMachine->m_listProductionCycle.push_back(g_pMachine->m_stProductionCircle);

			g_pMachine->m_stProductionCircle.bFree = false;

			while (true)
			{
				if (g_pMachine->m_listProductionCycle.size() > VAR_MACHINE_I("生产周期监控数量")) {
					g_pMachine->m_listProductionCycle.pop_front();
				}
				else {
					break;
				}
			}

			if (true) {
				CString strProductionCycleStatus = "生产周期监控信息:";

				list<PRODUCTIONCYCLE>::iterator it = g_pMachine->m_listProductionCycle.begin();
				for (; it != g_pMachine->m_listProductionCycle.end(); it++)
				{
					if (it->bFree) {
						strProductionCycleStatus += " 1";
					}
					else {
						strProductionCycleStatus += " 0";
					}
				}

				REPORT(strProductionCycleStatus, emLogLevelNormal);
			}

			if (g_pMachine->m_listProductionCycle.size() >= VAR_MACHINE_I("生产周期监控数量")) {
				g_pMachine->m_listProductionCycle.pop_front();

				int nFreeCnt = 0, nContinueCnt = 0;

				if (!VAR_MACHINE_B("托盘优先模式")) {
					list<PRODUCTIONCYCLE>::iterator it = g_pMachine->m_listProductionCycle.begin();
					for (; it != g_pMachine->m_listProductionCycle.end(); it++)
					{
						if (it->bFree) {
							nFreeCnt++;
							nContinueCnt++;
						}
						else {
							nContinueCnt = 0;
						}

						if (nContinueCnt >= 3) {
							REPORT("连续3个生产周期内出现周期待料超时,切换到托盘优先模式", emLogLevelWarn);
							VAR_MACHINE("托盘优先模式") = true;
							break;
						}
					}

					if (!VAR_MACHINE_B("托盘优先模式") && nFreeCnt >= VAR_MACHINE_I("生产周期待料周期数量")) {
						CString str;
						str.Format("在连续参数%d个生产周期内，出现参数%d次待料周期,切换到托盘优先模式", VAR_MACHINE_I("生产周期监控数量"), VAR_MACHINE_I("生产周期待料周期数量"));
						REPORT(str, emLogLevelWarn);
						VAR_MACHINE("托盘优先模式") = true;
					}
				}
			}

			CSys::m_nTotalSum++;
			VAR_BELTA("允许机械手装配主板标志") = false;
			VAR_BELTA("机械手装配主板完成标志") = true;
			VAR_BELTA("治具提前取上盖标志") = true;
			for (int i=0; i<4; i++)
			{
				m_stMarkInfo[i].bMarkProcOk = false;
				m_stMarkInfo[i].bAssembleOk = false;
			}

			MANNUAL_MODE();
		}

		m_mapIndex["当前吸嘴索引"] = 0;

		bFlag = false;

		for (int i=0; i<4; i++)
		{
			if (VAR_ROBOT_B("双主板装配模式") && (i % 4 == 1 || i % 4 == 3)) {
				continue;
			}

			if (m_stHeadProcResult[i].bHeadThrowFlag) {
				bFlag = true;
			}
		}

		if (bFlag) {
			RETURN_STATE(&CLogicRobot::OnRobot30, true);
		}

		RETURN_STATE(&CLogicRobot::OnRobot00, true);
	}

	if (VAR_ROBOT_B("双主板装配模式") && (m_mapIndex["当前治具索引"] % 4 == 1 || m_mapIndex["当前治具索引"] % 4 == 3)) {
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	if (m_stMarkInfo[m_mapIndex["当前治具索引"]].bAssembleOk) {
		m_mapFlag["自动计算补偿标志"] = false;
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	m_mapIndex["当前吸嘴索引"] = -1;

	if (m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadProcOk && !m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadThrowFlag) {
		m_mapIndex["当前吸嘴索引"] = m_mapIndex["当前治具索引"] % 4;
	}

	if (m_mapIndex["当前吸嘴索引"] < 0 || m_mapIndex["当前吸嘴索引"] > 3) {
		m_mapFlag["自动计算补偿标志"] = false;
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	CString sPosition1, sPosition2;
	sPosition1 = "A轨主板装配位";
	sPosition2.Format("下相机吸嘴%d拍照位", m_mapIndex["当前吸嘴索引"] + 1);

	m_stDstRobPnt = *m_pRobot->m_mapRobotPoint[sPosition1];

	double nMark1MachineX = 0, nMark1MachineY = 0, nMark1R = 0, nMark2MachineX = 0, nMark2MachineY = 0, nMark2R = 0;
	double nMark3MachineX = 0, nMark3MachineY = 0, nMark3R = 0, nMark4MachineX = 0, nMark4MachineY = 0, nMark4R = 0;

	double nUpCamOffMachineX = 0, nUpCamOffMachineY = 0;
	nUpCamOffMachineX = VAR_ROBOT_D("上相机中心基于旋转中心坐标X");
	nUpCamOffMachineY = VAR_ROBOT_D("上相机中心基于旋转中心坐标Y");

	for (int i=0; i<4; i++)
	{
		int nFixtureIndex = 0;

		switch (i)
		{
		case 0:
			nFixtureIndex = m_mapIndex["当前治具索引"] < 4 ? 0 : 4;
			break;
		case 1:
			nFixtureIndex = m_mapIndex["当前治具索引"] < 4 ? 1 : 5;
			break;
		case 2:
			nFixtureIndex = m_mapIndex["当前治具索引"] < 4 ? 2 : 6;
			break;
		case 3:
			nFixtureIndex = m_mapIndex["当前治具索引"] < 4 ? 3 : 7;
			break;
		default:break;
		}

		double nUpCamCenterMachineX = 0, nUpCamCenterMachineY = 0;
		g_pImageCalibrateUp->TransToMachine(VAR_ROBOT_D("上相机中心坐标X"), VAR_ROBOT_D("上相机中心坐标Y"), nUpCamCenterMachineX, nUpCamCenterMachineY);

		double nUpCalcMachineX = 0, nUpCalcMachineY = 0;
		g_pImageCalibrateUp->TransToMachine(m_stMarkInfo[nFixtureIndex].nMarkPixelX, m_stMarkInfo[nFixtureIndex].nMarkPixelY, nUpCalcMachineX, nUpCalcMachineY);

		double nUpCalcOffX = 0, nUpCalcOffY = 0;
		nUpCalcOffX = nUpCalcMachineX - nUpCamCenterMachineX;
		nUpCalcOffY = nUpCalcMachineY - nUpCamCenterMachineY;

		TRACE("主板装配位%d上相机计算结果基于图像中心： %.3f, %.3f\n", m_mapIndex["当前治具索引"] + 1, nUpCalcOffX, nUpCalcOffY);

		double nUpOffX = 0, nUpOffY = 0;
		nUpOffX = nUpCamOffMachineX - nUpCalcOffX;
		nUpOffY = nUpCamOffMachineY - nUpCalcOffY;	

		TRACE("主板装配位%d上相机处理结果基于旋转中心： %.3f, %.3f\n", m_mapIndex["当前治具索引"] + 1, nUpOffX, nUpOffY);

		switch (i)
		{
		case 0:
			nMark1MachineX = m_stMarkInfo[nFixtureIndex].nMarkMachineX + nUpOffX;
			nMark1MachineY = m_stMarkInfo[nFixtureIndex].nMarkMachineY + nUpOffY;
			nMark1R = m_stMarkInfo[nFixtureIndex].nMarkPixelR;
			break;		
		case 1:
			nMark2MachineX = m_stMarkInfo[nFixtureIndex].nMarkMachineX + nUpOffX;
			nMark2MachineY = m_stMarkInfo[nFixtureIndex].nMarkMachineY + nUpOffY;
			nMark2R = m_stMarkInfo[nFixtureIndex].nMarkPixelR;
			break;
		case 2:
			nMark3MachineX = m_stMarkInfo[nFixtureIndex].nMarkMachineX + nUpOffX;
			nMark3MachineY = m_stMarkInfo[nFixtureIndex].nMarkMachineY + nUpOffY;
			nMark3R = m_stMarkInfo[nFixtureIndex].nMarkPixelR;
			break;		
		case 3:
			nMark4MachineX = m_stMarkInfo[nFixtureIndex].nMarkMachineX + nUpOffX;
			nMark4MachineY = m_stMarkInfo[nFixtureIndex].nMarkMachineY + nUpOffY;
			nMark4R = m_stMarkInfo[nFixtureIndex].nMarkPixelR;
			break;
		default:break;
		}
	}

	double nBoardOffX = 0, nBoardOffY = 0;

	CString sOffX, sOffY, sMarkR;
	sOffX.Format("%s轨位置%d贴装位置偏移X", m_mapIndex["当前治具索引"] < 4 ? "A" : "B", (m_mapIndex["当前治具索引"] % 4) + 1);
	sOffY.Format("%s轨位置%d贴装位置偏移Y", m_mapIndex["当前治具索引"] < 4 ? "A" : "B", (m_mapIndex["当前治具索引"] % 4) + 1);
	sMarkR.Format("%s轨治具角度R", m_mapIndex["当前治具索引"] < 4 ? "A" : "B");

	CString sFeedX, sFeedY, sFeedR;
	sFeedX.Format("%s轨位置%d贴装补偿X", m_mapIndex["当前治具索引"] < 4 ? "A" : "B", m_mapIndex["当前治具索引"] % 4 + 1);
	sFeedY.Format("%s轨位置%d贴装补偿Y", m_mapIndex["当前治具索引"] < 4 ? "A" : "B", m_mapIndex["当前治具索引"] % 4 + 1);
	sFeedR.Format("%s轨位置%d贴装补偿R", m_mapIndex["当前治具索引"] < 4 ? "A" : "B", m_mapIndex["当前治具索引"] % 4 + 1);

	switch (m_mapIndex["当前治具索引"] % 4)
	{
	case 0:
		nBoardOffX = 0.0 + VAR_ROBOT_D("贴装位置补偿X") + VAR_ROBOT_D(sFeedX);
		nBoardOffY = 0.0 - VAR_ROBOT_D("贴装位置补偿Y") - VAR_ROBOT_D(sFeedY);
		break;
	case 1:
		nBoardOffX = 0.0 + VAR_ROBOT_D("贴装位置补偿X") + VAR_ROBOT_D(sFeedX);
		nBoardOffY = 0.0 - VAR_ROBOT_D("贴装位置补偿Y") - VAR_ROBOT_D(sFeedY);
		break;
	case 2:
		nBoardOffX = VAR_ROBOT_D(sOffX) + VAR_ROBOT_D("贴装位置补偿X") + VAR_ROBOT_D(sFeedX);
		nBoardOffY = VAR_ROBOT_D(sOffY) - VAR_ROBOT_D("贴装位置补偿Y") - VAR_ROBOT_D(sFeedY);
		break;
	case 3:
		nBoardOffX = VAR_ROBOT_D(sOffX) + VAR_ROBOT_D("贴装位置补偿X") + VAR_ROBOT_D(sFeedX);
		nBoardOffY = VAR_ROBOT_D(sOffY) - VAR_ROBOT_D("贴装位置补偿Y") - VAR_ROBOT_D(sFeedY);
		break;
	default:break;
	}

	if (VAR_ROBOT_B("双主板装配模式")) {
		nBoardOffX = 0.0 + VAR_ROBOT_D("贴装位置补偿X") + VAR_ROBOT_D(sFeedX);
		nBoardOffY = 0.0 - VAR_ROBOT_D("贴装位置补偿Y") - VAR_ROBOT_D(sFeedY);
	}

	double nMarkR = 0;

	nMarkR = atan2l(nMark1MachineY - nMark2MachineY, nMark2MachineX - nMark1MachineX);
	nMarkR = nMarkR * 180 / 3.14159265354;

	nMarkR = nMarkR - VAR_ROBOT_D(sMarkR);

 	CCvImage::Rotate(0, 0, nBoardOffX, nBoardOffY, nMarkR, nBoardOffX, nBoardOffY);

	nMarkR = nMarkR + VAR_ROBOT_D("贴装位置补偿R") + VAR_ROBOT_D(sFeedR) + VAR_ROBOT_D("A轨贴装角度偏移");

	double nRotateAngle = 0;
	nRotateAngle = m_pRobot->m_mapRobotPoint[sPosition1]->r - m_pRobot->m_mapRobotPoint[sPosition2]->r + nMarkR - m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].nHeadPixelR;

	m_stMarkInfo[m_mapIndex["当前治具索引"]].nRotateR = nRotateAngle;

	double nOffX = 0, nOffY = 0;

	g_pImageCalibrateDn->CalculateOffset(
		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].nHeadPixelX,
		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].nHeadPixelY, 
		-nRotateAngle,
		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].nHeadMachineX,
		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].nHeadMachineY,
		nOffX,
		nOffY);

	TRACE("吸嘴%d旋转%.3f°下相机基于旋转中心： %.3f, %.3f\n", m_mapIndex["当前吸嘴索引"] + 1, nRotateAngle, nOffX, nOffY);

	CString sBoardOffX, sBoardOffY;
	sBoardOffX.Format("%s轨位置%d贴装位置偏移X", m_mapIndex["当前治具索引"] < 4 ? "A" : "B", (m_mapIndex["当前治具索引"] % 4) + 1);
	sBoardOffY.Format("%s轨位置%d贴装位置偏移Y", m_mapIndex["当前治具索引"] < 4 ? "A" : "B", (m_mapIndex["当前治具索引"] % 4) + 1);

	switch (m_mapIndex["当前治具索引"] % 4)
	{
	case 0:
		m_stDstRobPnt.x = nMark1MachineX - nOffX + nBoardOffX;
		m_stDstRobPnt.y = nMark1MachineY - nOffY - nBoardOffY;
		m_stMarkInfo[m_mapIndex["当前治具索引"]].nLookBackMachineX = nMark1MachineX + VAR_ROBOT_D("主板回拍位置偏移X") - nUpCamOffMachineX;
		m_stMarkInfo[m_mapIndex["当前治具索引"]].nLookBackMachineY = nMark1MachineY - VAR_ROBOT_D("主板回拍位置偏移Y") - nUpCamOffMachineY;
		break;
	case 1:
		m_stDstRobPnt.x = nMark2MachineX - nOffX + nBoardOffX;
		m_stDstRobPnt.y = nMark2MachineY - nOffY - nBoardOffY;
		m_stMarkInfo[m_mapIndex["当前治具索引"]].nLookBackMachineX = nMark2MachineX + VAR_ROBOT_D("主板回拍位置偏移X") - nUpCamOffMachineX;
		m_stMarkInfo[m_mapIndex["当前治具索引"]].nLookBackMachineY = nMark2MachineY - VAR_ROBOT_D("主板回拍位置偏移Y") - nUpCamOffMachineY;
		break;
	case 2:
		m_stDstRobPnt.x = nMark2MachineX - nOffX + nBoardOffX;
		m_stDstRobPnt.y = nMark2MachineY - nOffY - nBoardOffY;
		m_stMarkInfo[m_mapIndex["当前治具索引"]].nLookBackMachineX = nMark2MachineX + VAR_ROBOT_D("主板回拍位置偏移X") + VAR_ROBOT_D(sBoardOffX) - nUpCamOffMachineX;
		m_stMarkInfo[m_mapIndex["当前治具索引"]].nLookBackMachineY = nMark2MachineY - VAR_ROBOT_D("主板回拍位置偏移Y") - VAR_ROBOT_D(sBoardOffY) - nUpCamOffMachineY;
		break;
	case 3:
		m_stDstRobPnt.x = nMark1MachineX - nOffX + nBoardOffX;
		m_stDstRobPnt.y = nMark1MachineY - nOffY - nBoardOffY;
		m_stMarkInfo[m_mapIndex["当前治具索引"]].nLookBackMachineX = nMark1MachineX + VAR_ROBOT_D("主板回拍位置偏移X") + VAR_ROBOT_D(sBoardOffX) - nUpCamOffMachineX;
		m_stMarkInfo[m_mapIndex["当前治具索引"]].nLookBackMachineY = nMark1MachineY - VAR_ROBOT_D("主板回拍位置偏移Y") - VAR_ROBOT_D(sBoardOffY) - nUpCamOffMachineY;
		break;
	default:break;
	}

	m_stDstRobPnt.r = m_pRobot->m_mapRobotPoint[sPosition2]->r + nRotateAngle;
	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;
	m_stDstRobPnt.speed = m_pRobot->m_mapRobotPoint["空闲等待位"]->speed;

	m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineX = m_stDstRobPnt.x;
	m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineY = m_stDstRobPnt.y;
	m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineR = m_stDstRobPnt.r;
	
	RETURN_STATE(&CLogicRobot::OnRobot51, true);
}

CStatus CLogicRobot::OnRobot50_0()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move("空闲等待位"));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos("空闲等待位"));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到空闲等待位", true);
	}

	MESSAGEBOX("请确认A轨所有主板已放到位！", "", true);

	m_mapIndex["当前治具索引"] = 0;

	RETURN_STATE(&CLogicRobot::OnRobot50_1, true);
}

CStatus CLogicRobot::OnRobot50_1()
{
	if (m_mapIndex["当前治具索引"] >= 4) {
		m_mapIndex["当前治具索引"] = 0;
		RETURN_STATE(&CLogicRobot::OnRobot50_6, true);
	}

	if (VAR_ROBOT_B("双主板装配模式") && (m_mapIndex["当前治具索引"] % 4 == 1 || m_mapIndex["当前治具索引"] % 4 == 3)) {
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	if (m_stMarkInfo[m_mapIndex["当前治具索引"]].bCalcFeedFlag) {
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["A轨主板装配位"];
	m_stDstRobPnt.x = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineX;
	m_stDstRobPnt.y = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineY;
	m_stDstRobPnt.r = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineR;
	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;

	RETURN_STATE(&CLogicRobot::OnRobot50_2, true);
}

CStatus CLogicRobot::OnRobot50_2()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到主板待装配位", true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumOn(m_mapIndex["当前治具索引"] % 4));

	RETURN_STATE(&CLogicRobot::OnRobot50_3, true);
}

CStatus CLogicRobot::OnRobot50_3()
{
	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderOn(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderStatus(m_mapIndex["当前治具索引"] % 4));

	if (m_sRet != "On") {
		m_sRet.Format("等待吸嘴气缸%d伸出", m_mapIndex["当前治具索引"] % 4 + 1);
		RETURN_SELF(m_sRet, true);
	}

	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["A轨主板装配位"]->z + VAR_ROBOT_D("治具取主板高度偏移");

	RETURN_STATE(&CLogicRobot::OnRobot50_4, true);
}

CStatus CLogicRobot::OnRobot50_4()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt, true, false, NULL, VAR_ROBOT_I("机械手Z轴下降速度百分比")));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到主板装配位", true);
	}

	Sleep(300);

	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;

	RETURN_STATE(&CLogicRobot::OnRobot50_5, true);
}

CStatus CLogicRobot::OnRobot50_5()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt, true, true, NULL, VAR_ROBOT_I("机械手Z轴上升速度百分比")));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderOff(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderStatus(m_mapIndex["当前治具索引"] % 4));

	if (m_sRet != "Off") {
		m_sRet.Format("等待吸嘴气缸%d缩回", m_mapIndex["当前治具索引"] % 4 + 1);
		RETURN_SELF(m_sRet, true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到主板待装配位", true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumStatus(m_mapIndex["当前治具索引"] % 4));

	if (m_sRet != "On") {
		MESSAGEBOX("吸嘴未取到料，请调整取料位置后点击启动按钮！", "", true);
		RETURN_PAUSE(&CLogicRobot::OnRobot50_5_0);
	}

	m_stMarkInfo[m_mapIndex["当前治具索引"]].bAssembleOk = false;

	m_mapIndex["当前治具索引"]++;

	RETURN_STATE(&CLogicRobot::OnRobot50_1, true);
}

CStatus CLogicRobot::OnRobot50_5_0()
{
	ROBOTPOINT rob;
	if (g_pRobot->GetPos(rob) != "OK") {
		RETURN_SELF("等待获取机械手当前位置", true);
	}

	CString sPos;
	sPos.Format("%s轨位置%d贴装补偿", (m_mapIndex["当前治具索引"] <= 3 ? "A" : "B"), m_mapIndex["当前治具索引"] % 4 + 1);

	VAR_ROBOT(sPos + "X") = VAR_ROBOT_D(sPos + "X") + rob.x - m_stDstRobPnt.x;
	VAR_ROBOT(sPos + "Y") = VAR_ROBOT_D(sPos + "Y") + rob.y - m_stDstRobPnt.y;
	VAR_ROBOT(sPos + "R") = VAR_ROBOT_D(sPos + "R") + rob.r - m_stDstRobPnt.r;

	m_stDstRobPnt.x = rob.x;
	m_stDstRobPnt.y = rob.y;
	m_stDstRobPnt.r = rob.r;

	RETURN_STATE(&CLogicRobot::OnRobot50_3, true);
}

CStatus CLogicRobot::OnRobot50_6()
{
	if (m_mapIndex["当前治具索引"] >= 4) {
		m_pRobot->Save();
		m_mapIndex["当前治具索引"] = 4;
		m_mapIndex["当前吸嘴索引"] = 0;
		RETURN_STATE(&CLogicRobot::OnRobot25_0, true);
	}

	if (VAR_ROBOT_B("双主板装配模式") && (m_mapIndex["当前治具索引"] % 4 == 1 || m_mapIndex["当前治具索引"] % 4 == 3)) {
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	if (m_stMarkInfo[m_mapIndex["当前治具索引"]].bCalcFeedFlag) {
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	RETURN_STATE(&CLogicRobot::OnRobot50_7, true);
}

CStatus CLogicRobot::OnRobot50_7()
{
	CString sPosition;

	sPosition.Format("下相机吸嘴%d拍照位", m_mapIndex["当前治具索引"] % 4 + 1);

	RUN_STOP_IF_ERROR(m_pRobot->DnCameraLightOn());

	RUN_STOP_IF_ERROR(m_pRobot->Move(sPosition));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(sPosition));

	if (m_sRet != "Yes") {
		m_sRet.Format("等待机械手运动到下相机吸嘴%d拍照位", m_mapIndex["当前治具索引"] % 4 + 1);
		RETURN_SELF(m_sRet, true);
	}

	m_mapIndex["重复拍照计数"] = 0;

	RETURN_STATE(&CLogicRobot::OnRobot50_8, true);
}

CStatus CLogicRobot::OnRobot50_8()
{
	CString sName;

	sName.Format("%s轨治具位置%d自动补偿拍照", m_mapIndex["当前治具索引"] <= 3 ? "A" : "B", m_mapIndex["当前治具索引"] % 4 + 1);

	RUN_STOP_IF_ERROR(m_pRobot->ProcPrepare(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->PushImageFlow(g_pImageFlowDn[m_mapIndex["当前治具索引"] % 4], NULL, g_pImageWndDn, "下相机", sName, "", false, false, -1, false, m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->ProcStart(m_mapIndex["当前治具索引"] % 4));

	Sleep(50);

	RUN_STOP_IF_ERROR(g_pCamera->Trigger("Dn"));

	m_mapTick["拍照计时"] = GetTickCount();

	RETURN_STATE(&CLogicRobot::OnRobot50_9, true);
}

CStatus CLogicRobot::OnRobot50_9()
{
	if (!m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].bProcOk) {
		if (GetTickCount() - m_mapTick["拍照计时"] > 8000) {
			CDat::IncreaseValue("下相机拍照失败数");
			REPORT("下相机拍照超时,即将重新拍照!", emLogLevelError);
			MESSAGEBOX("下相机拍照超时,即将重新拍照!", "", false);
			RETURN_STATE(&CLogicRobot::OnRobot50_7, true);
		}
		else {
			RETURN_SELF("等待拍照完成", false);
		}
	}

	double nResultPixelX, nResultPixelY, nResultPixelR = 0;

	bool bExistFlag = false;

	m_sRet = g_pImageFlowDn[m_mapIndex["当前治具索引"] % 4]->GetResult(nResultPixelX, nResultPixelY, nResultPixelR, bExistFlag);

	if (m_sRet != "OK") {
		RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumStatus(m_mapIndex["当前治具索引"] % 4));
		if (m_sRet == "On") {
			m_mapIndex["重复拍照计数"]++;
			if (m_mapIndex["重复拍照计数"] > 3) {
				m_mapIndex["重复拍照计数"] = 0;
				REPORT("吸嘴图像处理失败,请调整模板后重新拍照!", emLogLevelWarn);
				MESSAGEBOX("吸嘴图像处理失败,请调整模板后重新拍照!", "", false);
			}
			else {
				REPORT("吸嘴图像处理失败,即将重新拍照!", emLogLevelWarn);
			}
			m_pRobot->DnCameraLightOn();
			m_pRobot->RegisterCamera();
			RETURN_STATE(&CLogicRobot::OnRobot50_7, true);
		}
		else {
			m_mapIndex["当前治具索引"]++;
			RUN_STOP_IF_ERROR(m_pRobot->DnCameraLightOff());
			RETURN_STATE(&CLogicRobot::OnRobot50_6, true);
		}
	}

	double nOffX = 0, nOffY = 0, nRealOffX = 0, nRealOffY = 0;

	g_pImageCalibrateDn->CalculateOffset(
		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].nHeadPixelX,
		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].nHeadPixelY,
		-m_stMarkInfo[m_mapIndex["当前治具索引"]].nRotateR - m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].nHeadPixelR + nResultPixelR,
		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].nHeadMachineX,
		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].nHeadMachineY,
		nOffX,
		nOffY);

	g_pImageCalibrateDn->CalculateOffset(
		nResultPixelX,
		nResultPixelY,
		-m_stMarkInfo[m_mapIndex["当前治具索引"]].nRotateR - m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].nHeadPixelR + nResultPixelR,
		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].nHeadMachineX,
		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].nHeadMachineY,
		nRealOffX,
		nRealOffY);

	CString sPos;
	sPos.Format("%s轨位置%d贴装补偿", (m_mapIndex["当前治具索引"] <= 3 ? "A" : "B"), m_mapIndex["当前治具索引"] % 4 + 1);

	VAR_ROBOT(sPos + "X") = VAR_ROBOT_D(sPos + "X") + nRealOffX - nOffX;
	VAR_ROBOT(sPos + "Y") = VAR_ROBOT_D(sPos + "Y") + nRealOffY - nOffY;
	VAR_ROBOT(sPos + "R") = VAR_ROBOT_D(sPos + "R") + nResultPixelR - m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].nHeadPixelR;

	m_stMarkInfo[m_mapIndex["当前治具索引"]].bCalcFeedFlag = true;

	m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadPickOk = true;
	m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadProcOk = false;
	m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadThrowFlag = false;

	m_mapIndex["当前治具索引"]++;
	RUN_STOP_IF_ERROR(m_pRobot->DnCameraLightOff());

	RETURN_STATE(&CLogicRobot::OnRobot50_6, true);
}

CStatus CLogicRobot::OnRobot51()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到主板待装配位", true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumStatus(m_mapIndex["当前吸嘴索引"]));

	if (m_sRet != "On") {
		m_sRet.Format("吸嘴%d上无料，可能发生掉料,请检查吸嘴真空信号是否有异常，并取走因真空信号异常导致误报的主板！", m_mapIndex["当前吸嘴索引"] + 1);
		REPORT(m_sRet, emLogLevelError);
		MESSAGEBOX(m_sRet, "", false);
		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadProcOk = false;
		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadPickOk = false;
		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadThrowFlag = false;
		RETURN_STATE(&CLogicRobot::OnRobot50, true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot52, true);
}

CStatus CLogicRobot::OnRobot52()
{
	CString sPosition;
	sPosition = "A轨主板装配位";

	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint[sPosition]->z + VAR_ROBOT_D("A轨贴装高度偏移");
	m_stDstRobPnt.speed = m_pRobot->m_mapRobotPoint[sPosition]->speed;

	RETURN_STATE(&CLogicRobot::OnRobot53, true);
}

CStatus CLogicRobot::OnRobot53()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt, true, false, NULL, VAR_ROBOT_I("机械手Z轴下降速度百分比")));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderOn(m_mapIndex["当前吸嘴索引"]));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderStatus(m_mapIndex["当前吸嘴索引"]));

	if (m_sRet != "On") {
		if (m_mapFlag["吸嘴气缸伸出标志"]) {
			if (GetTickCount() - m_mapTick["吸嘴气缸伸出计时"] < 3000) {
				m_sRet.Format("等待吸嘴气缸%d伸出", m_mapIndex["当前吸嘴索引"] + 1);
				RETURN_SELF(m_sRet, true);
			}
		}
		else {
			m_mapFlag["吸嘴气缸伸出标志"] = true;
			m_mapTick["吸嘴气缸伸出计时"] = GetTickCount();
			m_sRet.Format("等待吸嘴气缸%d伸出", m_mapIndex["当前吸嘴索引"] + 1);
			RETURN_SELF(m_sRet, true);
		}
	}

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到主板装配位", true);
	}

	m_mapFlag["吸嘴气缸伸出标志"] = false;

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderBrokenVacuumOn(m_mapIndex["当前吸嘴索引"]));

	Sleep(VAR_ROBOT_I("吸嘴贴装延时"));

	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;

	if (VAR_ROBOT_B("调试模式")) {
		RETURN_PAUSE(&CLogicRobot::OnRobot54);
	}

	RETURN_STATE(&CLogicRobot::OnRobot54, true);
}

CStatus CLogicRobot::OnRobot54()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderOff(m_mapIndex["当前吸嘴索引"]));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderStatus(m_mapIndex["当前吸嘴索引"]));

	if (m_sRet != "Off") {
		m_sRet.Format("等待吸嘴气缸%d缩回", m_mapIndex["当前吸嘴索引"] + 1);
		RETURN_SELF(m_sRet, true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到主板待装配位", true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumOff(m_mapIndex["当前吸嘴索引"]));
	
	RETURN_STATE(&CLogicRobot::OnRobot55, true);
}

CStatus CLogicRobot::OnRobot54_0()
{
	if (m_mapIndex["当前治具索引"] >= 4) {
		m_mapIndex["当前治具索引"] = 0;
		RETURN_STATE(&CLogicRobot::OnRobot54_3_0, true);
	}

	if (VAR_ROBOT_B("双主板装配模式") && (m_mapIndex["当前治具索引"] % 4 == 1 || m_mapIndex["当前治具索引"] % 4 == 3)) {
		m_stMarkInfo[m_mapIndex["当前治具索引"]].bLookBackProcOk = true;
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	if (m_stMarkInfo[m_mapIndex["当前治具索引"]].bLookBackProcOk) {
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	if (VAR_MACHINE_B("弹片检测功能启用") || VAR_MACHINE_B("弹片检测功能只检测不判断") || VAR_MACHINE_B("MES功能启用") || VAR_MACHINE_B("MES过站功能启用") || VAR_MACHINE_B("MES治具绑定功能启用")) {
		RETURN_STATE(&CLogicRobot::OnRobot54_1, true);
	}

	if (!m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bFromTray) {
		m_stMarkInfo[m_mapIndex["当前治具索引"]].bLookBackProcOk = true;
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	RETURN_STATE(&CLogicRobot::OnRobot54_1, true);
}

CStatus CLogicRobot::OnRobot54_1()
{
	ROBOTPOINT rob = *g_pRobot->m_mapRobotPoint["A轨左下角Mark拍照位"];

	rob.x = m_stMarkInfo[m_mapIndex["当前治具索引"]].nLookBackMachineX;
	rob.y = m_stMarkInfo[m_mapIndex["当前治具索引"]].nLookBackMachineY;

	RUN_STOP_IF_ERROR(m_pRobot->Move(rob));

	RUN_STOP_IF_ERROR(m_pRobot->UpCameraLightOn());

	g_pCamera->SetExposureTime("Up", VAR_ROBOT_I("上相机(皮带)曝光时间"));
	g_pCamera->SetGain("Up", VAR_ROBOT_I("上相机(皮带)增益"));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(rob));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到治具拍照位", true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot54_2, true);
}

CStatus CLogicRobot::OnRobot54_2()
{
	RUN_STOP_IF_ERROR(m_pRobot->ProcPrepare(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->PushImageFlow(g_pImageFlowUp[m_mapIndex["当前治具索引"] % 4], g_pImageFlowScanCodeMainBoard[m_mapIndex["当前治具索引"] % 4], g_pImageWndUp, "主板回拍", "", "", true, false, m_mapIndex["当前治具索引"] % 4, true, m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->ProcStart(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(g_pCamera->Trigger("Up"));

	m_mapTick["拍照计时"] = GetTickCount();

	RETURN_STATE(&CLogicRobot::OnRobot54_3, true);
}

CStatus CLogicRobot::OnRobot54_3()
{
	if (!m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].bTakePicOk) {
		if (GetTickCount() - m_mapTick["拍照计时"] > 8000) {
			CDat::IncreaseValue("上相机拍照失败数");
			REPORT("上相机拍照超时,请先点击确定后点击开始重试!", emLogLevelWarn);
			MESSAGEBOX("上相机拍照超时,请先点击确定后点击开始重试!", "", false);
			m_pRobot->UpCameraLightOn();
			m_pRobot->RegisterCamera();
			RETURN_STATE(&CLogicRobot::OnRobot54_2, true);
		}
		else {
			RETURN_SELF("等待拍照完成", false);
		}
	}

	RUN_STOP_IF_ERROR(m_pRobot->UpCameraLightOff());

	m_mapIndex["当前治具索引"]++;

	RETURN_STATE(&CLogicRobot::OnRobot54_0, true);
}

CStatus CLogicRobot::OnRobot54_3_0()
{
	if (m_mapIndex["当前治具索引"] >= 4) {
		m_mapIndex["当前吸嘴索引"] = 0;
		m_mapIndex["当前治具索引"] = 0;

		bool bFlag = false;

		for (int i=0; i<4; i++)
		{
			if (VAR_ROBOT_B("双主板装配模式") && (i % 4 == 1 || i % 4 == 3)) {
				continue;
			}

			if (!m_stMarkInfo[i].bLookBackProcOk) {
				bFlag = true;
				break;
			}
		}

		if (bFlag) {
			m_mapIndex["当前治具索引"] = 0;
			RETURN_STATE(&CLogicRobot::OnRobot54_0, true);
		}

		bFlag = true;

		for (int i=0; i<4; i++)
		{
			if (VAR_ROBOT_B("双主板装配模式") && (i % 4 == 1 || i % 4 == 3)) {
				continue;
			}

			if (!m_stMarkInfo[i].bAssembleOk) {
				bFlag = false;
				break;
			}
		}

		if (bFlag) {
			// 生产周期监控
			g_pMachine->m_listProductionCycle.push_back(g_pMachine->m_stProductionCircle);

			g_pMachine->m_stProductionCircle.bFree = false;

			while (true)
			{
				if (g_pMachine->m_listProductionCycle.size() > VAR_MACHINE_I("生产周期监控数量")) {
					g_pMachine->m_listProductionCycle.pop_front();
				}
				else {
					break;
				}
			}

			if (true) {
				CString strProductionCycleStatus = "生产周期监控信息:";

				list<PRODUCTIONCYCLE>::iterator it = g_pMachine->m_listProductionCycle.begin();
				for (; it != g_pMachine->m_listProductionCycle.end(); it++)
				{
					if (it->bFree) {
						strProductionCycleStatus += " 1";
					}
					else {
						strProductionCycleStatus += " 0";
					}
				}

				REPORT(strProductionCycleStatus, emLogLevelNormal);
			}

			if (g_pMachine->m_listProductionCycle.size() >= VAR_MACHINE_I("生产周期监控数量")) {
				g_pMachine->m_listProductionCycle.pop_front();

				int nFreeCnt = 0, nContinueCnt = 0;

				if (!VAR_MACHINE_B("托盘优先模式")) {
					list<PRODUCTIONCYCLE>::iterator it = g_pMachine->m_listProductionCycle.begin();
					for (; it != g_pMachine->m_listProductionCycle.end(); it++)
					{
						if (it->bFree) {
							nFreeCnt++;
							nContinueCnt++;
						}
						else {
							nContinueCnt = 0;
						}

						if (nContinueCnt >= 3) {
							REPORT("连续3个生产周期内出现周期待料超时,切换到托盘优先模式", emLogLevelWarn);
							VAR_MACHINE("托盘优先模式") = true;
							break;
						}
					}

					if (!VAR_MACHINE_B("托盘优先模式") && nFreeCnt >= VAR_MACHINE_I("生产周期待料周期数量")) {
						CString str;
						str.Format("在连续%d个生产周期内，出现%d次待料周期,切换到托盘优先模式", VAR_MACHINE_I("生产周期监控数量"), VAR_MACHINE_I("生产周期待料周期数量"));
						REPORT(str, emLogLevelWarn);
						VAR_MACHINE("托盘优先模式") = true;
					}
				}
			}

			CSys::m_nTotalSum++;
			VAR_BELTA("允许机械手装配主板标志") = false;
			VAR_BELTA("机械手装配主板完成标志") = true;
			VAR_BELTA("治具提前取上盖标志") = true;
			for (int i=0; i<4; i++)
			{
				m_stMarkInfo[i].nProcTimes = 0;
				m_stMarkInfo[i].bLookBackProcOk = false;
				m_stMarkInfo[i].bMarkProcOk = false;
				m_stMarkInfo[i].bAssembleOk = false;
			}

			MANNUAL_MODE();

			RETURN_STATE(&CLogicRobot::OnRobot00, true);
		}
		else {		
			bFlag = false;

			for (int i=0; i<4; i++)
			{
				if (VAR_ROBOT_B("双主板装配模式") && (i % 4 == 1 || i % 4 == 3)) {
					continue;
				}

				if (m_stHeadProcResult[i].bHeadThrowFlag) {
					bFlag = true;
				}
			}

			if (bFlag) {
				RETURN_STATE(&CLogicRobot::OnRobot30, true);
			}

			RETURN_STATE(&CLogicRobot::OnRobot00, true);
		}
	}

	if (m_stMarkInfo[m_mapIndex["当前治具索引"]].bLookBackProcOk) {
		m_mapIndex["当前治具索引"]++;
		RETURN_SELF("", false);
	}

	m_mapTick["图像处理计时"] = GetTickCount();

	RETURN_STATE(&CLogicRobot::OnRobot54_3_1, true);
}

CStatus CLogicRobot::OnRobot54_3_1()
{
	bool bProcTimeOut = false;

	if (!m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].bProcOk) {
		if (GetTickCount() - m_mapTick["图像处理计时"] > 3000) {
			CDat::IncreaseValue("上相机拍照失败数");
			bProcTimeOut = true;
		}
		else {
			RETURN_SELF("等待图像处理完成", false);
		}
	}

	double nResultPixelX, nResultPixelY, nResultPixelR = 0;
	bool bExistFlag = false;

	if (!bProcTimeOut) {
		m_sRet = g_pImageFlowUp[m_mapIndex["当前治具索引"] % 4]->GetResult(nResultPixelX, nResultPixelY, nResultPixelR, bExistFlag);
	}
	else {
		m_sRet = "图像处理超时";
	}

	if (m_sRet != "OK") {
		if (bProcTimeOut) {
			m_sRet.Format("%s治具位置%d图像处理超时!", m_mapIndex["当前治具索引"] < 4 ? "A轨（2轨）" : "B轨（1轨）", m_mapIndex["当前治具索引"] % 4 + 1);
		}
		else {
			m_sRet.Format("%s治具位置%d图像处理失败!", m_mapIndex["当前治具索引"] < 4 ? "A轨（2轨）" : "B轨（1轨）", m_mapIndex["当前治具索引"] % 4 + 1);
		}

		REPORT(m_sRet, emLogLevelWarn);

		m_stMarkInfo[m_mapIndex["当前治具索引"]].nProcTimes++;

		if (m_stMarkInfo[m_mapIndex["当前治具索引"]].nProcTimes >= 3) {
			m_stMarkInfo[m_mapIndex["当前治具索引"]].nProcTimes = 0;
			MESSAGEBOX(m_sRet, "", false);
		}

		m_mapIndex["当前治具索引"]++;

		RETURN_STATE(&CLogicRobot::OnRobot54_3_0, true);
	}

	m_stMarkInfo[m_mapIndex["当前治具索引"]].nProcTimes = 0;
	m_stMarkInfo[m_mapIndex["当前治具索引"]].bLookBackProcOk = true;

// 	if (VAR_MACHINE_B("MES功能启用") && !m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].bMesResult) {
// 		CString sMsg;
// 		sMsg.Format("A轨治具主板%d查询MES结果失败", m_mapIndex["当前治具索引"] % 4 + 1);
// 		REPORT(sMsg, emLogLevelWarn);
// 
// 		m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["A轨主板装配位"];
// 		m_stDstRobPnt.x = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineX;
// 		m_stDstRobPnt.y = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineY;
// 		m_stDstRobPnt.r = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineR;
// 		m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;
// 
// 		m_stMarkInfo[m_mapIndex["当前治具索引"]].bAssembleOk = false;
// 
// 		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadPickOk = true;
// 		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadThrowFlag = true;
// 		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bMesNgFlag = true;
// 
// 		RETURN_STATE(&CLogicRobot::OnRobot54_4, true);
// 	}

	if (VAR_MACHINE_B("弹片检测功能启用") && m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].bBadCheckResult) {
		CString sMsg;
		sMsg.Format("A轨治具主板%d弹片检测失败", m_mapIndex["当前治具索引"] % 4 + 1);
		REPORT(sMsg, emLogLevelWarn);

		m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["A轨主板装配位"];
		m_stDstRobPnt.x = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineX;
		m_stDstRobPnt.y = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineY;
		m_stDstRobPnt.r = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineR;
		m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;

		m_stMarkInfo[m_mapIndex["当前治具索引"]].bAssembleOk = false;

		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadPickOk = true;
		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadThrowFlag = true;

		RETURN_STATE(&CLogicRobot::OnRobot54_4, true);
	}

// 	if (!CSys::m_sFirstCode.IsEmpty() && m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].sCode2D.Find(CSys::m_sFirstCode) < 0) {
// 		CString sMsg;
// 		sMsg.Format("A轨治具主板%d主板二维码和首件二维码不匹配", m_mapIndex["当前治具索引"] % 4 + 1);
// 
// 		m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["A轨主板装配位"];
// 		m_stDstRobPnt.x = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineX;
// 		m_stDstRobPnt.y = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineY;
// 		m_stDstRobPnt.r = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineR;
// 		m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;
// 
// 		m_stMarkInfo[m_mapIndex["当前治具索引"]].bAssembleOk = false;
// 
// 		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadPickOk = true;
// 		m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadThrowFlag = true;
// 
// 		RETURN_STATE(&CLogicRobot::OnRobot54_4, true);
// 	}

	CString sCode2D;
	sCode2D = m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].sCode2D;

	CDat::UpdateValue("主板SN号", sCode2D);
	if (VAR_MACHINE_B("MES功能启用")) {
		CString sMesRes;

		bool bFlag = false;
		bFlag = CMes::HttpPost(VAR_MACHINE_S("MES服务器地址"), 80, sCode2D, VAR_MACHINE_S("MES用户名"), VAR_MACHINE_S("MES密码"), VAR_MACHINE_S("MES工厂"), &sMesRes);

		REPORT("Mes回传数据: " + sMesRes, emLogLevelNormal);
		
		if (!bFlag) {
			CString sMsg;
			sMsg.Format("A轨治具主板%d Mes查询结果失败", m_mapIndex["当前治具索引"] % 4 + 1);

			m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["A轨主板装配位"];
			m_stDstRobPnt.x = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineX;
			m_stDstRobPnt.y = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineY;
			m_stDstRobPnt.r = m_stMarkInfo[m_mapIndex["当前治具索引"]].nRealMachineR;
			m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;

			m_stMarkInfo[m_mapIndex["当前治具索引"]].bAssembleOk = false;

			m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadPickOk = true;
			m_stHeadProcResult[m_mapIndex["当前治具索引"] % 4].bHeadThrowFlag = true;

			RETURN_STATE(&CLogicRobot::OnRobot54_4, true);
		}
	}

	if (VAR_MACHINE_B("MES过站功能启用")) {
		CString sMesRes;

		bool bFlag = false;
		bFlag = CMes::HttpPostPassStation(VAR_MACHINE_S("MES过站服务器地址"), 80, m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].sCode2D, VAR_MACHINE_S("MES过站拉线ID"), VAR_MACHINE_S("MES过站工序"), VAR_MACHINE_S("MES过站用户名"), VAR_MACHINE_S("MES过站密码"), VAR_MACHINE_S("MES过站工厂"), &sMesRes);
		
		REPORT("Mes过站回传数据: " + sMesRes, emLogLevelNormal);
		
		if (!bFlag && sMesRes.Find("工序不匹配") < 0) {
			MESSAGEBOX("Mes过站回传数据: " + sMesRes, "", false);
		}
	}

	if (VAR_MACHINE_B("MES治具绑定功能启用")) {
		CString sMesRes;

		bool bFlag = false;
		bFlag = CMes::HttpPostFixture(VAR_MACHINE_S("MES治具绑定服务器地址"), 80, m_sFixtureCodeA, m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].sCode2D, VAR_MACHINE_S("MES治具绑定拉线ID"), VAR_MACHINE_S("MES治具绑定站点"), VAR_MACHINE_S("MES治具绑定工序"), VAR_MACHINE_S("MES治具绑定用户名"), VAR_MACHINE_S("MES治具绑定密码"), VAR_MACHINE_S("MES治具绑定工厂"), &sMesRes);
		
		REPORT("Mes治具绑定回传数据: " + sMesRes, emLogLevelNormal);

		if (!bFlag) {
			MESSAGEBOX("Mes治具绑定回传数据: " + sMesRes, "", false);
		}
	}

	m_mapIndex["当前治具索引"]++;

	RETURN_STATE(&CLogicRobot::OnRobot54_3_0, true);
}

CStatus CLogicRobot::OnRobot54_4()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到主板待装配位", true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumOn(m_mapIndex["当前治具索引"] % 4));

	RETURN_STATE(&CLogicRobot::OnRobot54_5, true);
}

CStatus CLogicRobot::OnRobot54_5()
{
	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderOn(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderStatus(m_mapIndex["当前治具索引"] % 4));

	if (m_sRet != "On") {
		m_sRet.Format("等待吸嘴气缸%d伸出", m_mapIndex["当前治具索引"] % 4 + 1);
		RETURN_SELF(m_sRet, true);
	}

	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["A轨主板装配位"]->z + VAR_ROBOT_D("治具取主板高度偏移");

	RETURN_STATE(&CLogicRobot::OnRobot54_6, true);
}

CStatus CLogicRobot::OnRobot54_6()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt, true, false, NULL, VAR_ROBOT_I("机械手Z轴下降速度百分比")));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到主板装配位", true);
	}

	Sleep(300);

	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;

	RETURN_STATE(&CLogicRobot::OnRobot54_7, true);
}

CStatus CLogicRobot::OnRobot54_7()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt, true, true, NULL, VAR_ROBOT_I("机械手Z轴上升速度百分比")));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderOff(m_mapIndex["当前治具索引"] % 4));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderStatus(m_mapIndex["当前治具索引"] % 4));

	if (m_sRet != "Off") {
		m_sRet.Format("等待吸嘴气缸%d缩回", m_mapIndex["当前治具索引"] % 4 + 1);
		RETURN_SELF(m_sRet, true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到主板待装配位", true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumStatus(m_mapIndex["当前治具索引"] % 4));

	if (m_sRet != "On") {
		REPORT("吸嘴未取到料，请确认！", emLogLevelWarn);
		MESSAGEBOX("吸嘴未取到料，请确认！", "", true);
		RETURN_STATE(&CLogicRobot::OnRobot54_5, true);
	}

	m_mapIndex["当前治具索引"]++;

	RETURN_STATE(&CLogicRobot::OnRobot54_3_0, true);
}

CStatus CLogicRobot::OnRobot55()
{
	m_stMarkInfo[m_mapIndex["当前治具索引"]].bAssembleOk = true;

	m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadProcOk = false;
	m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadPickOk = false;
	m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadThrowFlag = false;

	m_mapIndex["当前治具索引"]++;

	RETURN_STATE(&CLogicRobot::OnRobot50, true);
}

CStatus CLogicRobot::OnRobot56()
{
	if (!VAR_TRAY_B("空TRAY盘进料完成标志")) {
		RETURN_STATE(&CLogicRobot::OnRobot00, true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->TrayCameraLightOn());

	RUN_STOP_IF_ERROR(m_pRobot->Move("空闲等待位"));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos("空闲等待位"));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到空闲等待位", true);
	}

	RUN_STOP_IF_ERROR(g_pTray->FullTrayMaterialExistStatus());

	if (m_sRet != "On") {
		REPORT("空TRAY盘送料异常，请手动摆好TRAY盘？", emLogLevelWarn);
		MESSAGEBOX("空TRAY盘送料异常，请手动摆好TRAY盘？", "", false);
		RETURN_SELF("", false);
	}

	m_mapIndex["已抛料数量"] = 0;

	RETURN_STATE(&CLogicRobot::OnRobot57, true);
}

CStatus CLogicRobot::OnRobot57()
{
	RUN_STOP_IF_ERROR(m_pRobot->ProcPrepare(0));

	RUN_STOP_IF_ERROR(m_pRobot->PushImageFlow(g_pImageFlowTray, NULL, g_pImageWndTray, "TRAY", "", "", false, false, -1, false, 0));

	RUN_STOP_IF_ERROR(m_pRobot->ProcStart(0));

	RUN_STOP_IF_ERROR(g_pCamera->Trigger("Tray"));

	m_mapTick["拍照计时"] = GetTickCount();

	RETURN_STATE(&CLogicRobot::OnRobot58, true);
}

CStatus CLogicRobot::OnRobot58()
{
	if (!m_pRobot->m_vImageFlow[0][0].bProcOk) {
		if (GetTickCount() - m_mapTick["拍照计时"] > 8000) {
			CDat::IncreaseValue("顶部相机拍照失败数");
			REPORT("TRAY相机拍照超时,即将重新拍照!", emLogLevelError);
			MESSAGEBOX("TRAY相机拍照超时,即将重新拍照!", "", false);
			m_pRobot->TrayCameraLightOn();
			m_pRobot->RegisterCamera();
			RETURN_STATE(&CLogicRobot::OnRobot57, true);
		}
		else {
			RETURN_SELF("等待拍照完成", true);
		}
	}

	RUN_STOP_IF_ERROR(m_pRobot->TrayCameraLightOff());

	bool bHasEmptyPosition = false;

	bool bExistFlag = false;

	for (int i=0; i<VAR_ROBOT_I("TRAY盘主板行数"); i++)
	{
		for (int j=0; j<VAR_ROBOT_I("TRAY盘主板列数"); j++)
		{
			m_sRet = g_pImageFlowTray->GetResult(m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelX, m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelY, m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].nPixelR, bExistFlag, 0, i, j, 0);

			if (m_sRet != "OK" && !bExistFlag) {
				bHasEmptyPosition = true;
				m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bExistFlag = false;
			}
			else {
				m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bExistFlag = true;
			}
		}
	}

	if (!bHasEmptyPosition) {
		for (int i=0; i<VAR_ROBOT_I("TRAY盘主板行数"); i++)
		{
			for (int j=0; j<VAR_ROBOT_I("TRAY盘主板列数"); j++)
			{
				m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bHasMaterialFlag = false;
			}
		}

		VAR_TRAY("空TRAY盘进料完成标志") = false;
		VAR_TRAY("满TRAY盘进料完成标志") = false;
		VAR_ROBOT("TRAY盘退料标志") = true;
		RETURN_STATE(&CLogicRobot::OnRobot59, true);
	}
	else {
		m_mapIndex["当前吸嘴索引"] = 0;
		RETURN_STATE(&CLogicRobot::OnRobot60, true);
	}
}

CStatus CLogicRobot::OnRobot59()
{
	if (!VAR_TRAY_B("空TRAY盘进料完成标志")) {
		RETURN_SELF("等待空TRAY盘到位", true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot56, true);
}

CStatus CLogicRobot::OnRobot60()
{
	if (m_mapIndex["已抛料数量"] >= m_nHeadBackCnt || m_mapIndex["当前吸嘴索引"] > 3) {
		m_nHeadBackCnt = 0;
		m_mapIndex["当前吸嘴索引"] = 0;
		RETURN_STATE(&CLogicRobot::OnRobot60_0, true);
	}

	if (!m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadPickOk) {
		m_mapIndex["当前吸嘴索引"]++;
		RETURN_SELF("", false);
	}

	RETURN_STATE(&CLogicRobot::OnRobot61, true);
}

CStatus CLogicRobot::OnRobot60_0()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move("空闲等待位"));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos("空闲等待位"));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到空闲等待位", true);
	}

	VAR_ROBOT("主板回放标志") = false;

	RETURN_STATE(&CLogicRobot::OnRobot00, true);
}

CStatus CLogicRobot::OnRobot61()
{
	m_mapIndex["TRAY盘主板索引"] = -1;

	int nRowIndex = 0, nColIndex = 0;
	do 
	{
		// 正找
		if (m_mapIndex["当前吸嘴索引"] == 1 || m_mapIndex["当前吸嘴索引"] == 2) {
			for (int i=0; i<VAR_ROBOT_I("TRAY盘主板行数"); i++)
			{
				if (m_mapIndex["当前吸嘴索引"] == 1) {
					for (int j=VAR_ROBOT_I("TRAY盘主板列数") - 1; j>= 0; j--)
					{
						if (!m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bExistFlag && !m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bHasMaterialFlag) {
							if (i == VAR_ROBOT_I("TRAY盘主板行数") - 1) {
								continue;
							}

							if (!m_pRobot->m_vecMainBoardBackPos[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bExistFlag) {
								continue;
							}

							m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["TRAY盘正取放位"];

							nRowIndex = i;
							nColIndex = j;
							m_mapIndex["TRAY盘主板索引"] = i * VAR_ROBOT_I("TRAY盘主板列数") + j;

							break;
						}
					}
				}
				else {
					for (int j=0; j<VAR_ROBOT_I("TRAY盘主板列数"); j++)
					{
						if (!m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bExistFlag && !m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bHasMaterialFlag) {
							if (i == VAR_ROBOT_I("TRAY盘主板行数") - 1) {
								continue;
							}

							if (!m_pRobot->m_vecMainBoardBackPos[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bExistFlag) {
								continue;
							}

							m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["TRAY盘正取放位"];

							nRowIndex = i;
							nColIndex = j;
							m_mapIndex["TRAY盘主板索引"] = i * VAR_ROBOT_I("TRAY盘主板列数") + j;

							break;
						}
					}
				}

				if (m_mapIndex["TRAY盘主板索引"] >= 0) {
					break;
				}
			}
		}
		else {
			for (int i=VAR_ROBOT_I("TRAY盘主板行数") - 1; i>=0; i--)
			{
				if (m_mapIndex["当前吸嘴索引"] == 0) {
					for (int j=VAR_ROBOT_I("TRAY盘主板列数") - 1; j>= 0; j--)
					{
						if (!m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bExistFlag && !m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bHasMaterialFlag) {
							if (!m_pRobot->m_vecMainBoardBackPos[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bExistFlag) {
								continue;
							}

							m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["TRAY盘正取放位"];

							nRowIndex = i;
							nColIndex = j;
							m_mapIndex["TRAY盘主板索引"] = i * VAR_ROBOT_I("TRAY盘主板列数") + j;

							break;
						}
					}
				}
				else {
					for (int j=0; j<VAR_ROBOT_I("TRAY盘主板列数"); j++)
					{
						if (!m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bExistFlag && !m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bHasMaterialFlag) {
							if (!m_pRobot->m_vecMainBoardBackPos[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bExistFlag) {
								continue;
							}

							m_stDstRobPnt = *m_pRobot->m_mapRobotPoint["TRAY盘正取放位"];

							nRowIndex = i;
							nColIndex = j;
							m_mapIndex["TRAY盘主板索引"] = i * VAR_ROBOT_I("TRAY盘主板列数") + j;

							break;
						}
					}
				}

				if (m_mapIndex["TRAY盘主板索引"] >= 0) {
					break;
				}
			}
		}

		if (m_mapIndex["TRAY盘主板索引"] >= 0) {
			break;
		}

	} while (false);

	if (m_mapIndex["TRAY盘主板索引"] < 0) {
		for (int i=0; i<VAR_ROBOT_I("TRAY盘主板行数"); i++)
		{
			for (int j=0; j<VAR_ROBOT_I("TRAY盘主板列数"); j++)
			{
				m_pMainBoardInTray[i * VAR_ROBOT_I("TRAY盘主板列数") + j].bHasMaterialFlag = false;
			}
		}

		VAR_TRAY("空TRAY盘进料完成标志") = false;
		VAR_TRAY("满TRAY盘进料完成标志") = false;
		VAR_ROBOT("TRAY盘退料标志") = true;
		RETURN_STATE(&CLogicRobot::OnRobot59, true);
	}

	double nCalcCenterMachineX = 0, nCalcCenterMachineY = 0;

	g_pImageCalibrateTray->TransToMachine(m_pRobot->m_vecMainBoardBackPos[m_mapIndex["TRAY盘主板索引"]].nPixelX, m_pRobot->m_vecMainBoardBackPos[m_mapIndex["TRAY盘主板索引"]].nPixelY, nCalcCenterMachineX, nCalcCenterMachineY);

	double nRotateAngle = 0;

	m_sRet.Format("下相机吸嘴%d拍照位", m_mapIndex["当前吸嘴索引"] + 1);

	nRotateAngle = m_pRobot->m_mapRobotPoint["TRAY盘正取放位"]->r - m_pRobot->m_mapRobotPoint[m_sRet]->r;

	double nCalcHeadMachineX = 0, nCalcHeadMachineY = 0, nCalcUpCameraCenterMachineX = 0, nCalcUpCameraCenterMachineY = 0;

	m_sRet.Format("吸嘴%d基于旋转中心坐标", m_mapIndex["当前吸嘴索引"] + 1);

	CCvImage::Rotate(0, 0, VAR_ROBOT_D(m_sRet + "X"), VAR_ROBOT_D(m_sRet + "Y"), -nRotateAngle , nCalcHeadMachineX, nCalcHeadMachineY);

	TRACE("吸嘴%d基于旋转中心坐标旋转%.03f度后： %.03f, %.03f\n", m_mapIndex["当前吸嘴索引"] + 1, -nRotateAngle, nCalcHeadMachineX, nCalcHeadMachineY);

	m_stDstRobPnt.x = nCalcCenterMachineX + VAR_ROBOT_D("上相机中心基于旋转中心坐标X") - nCalcHeadMachineX;
	m_stDstRobPnt.y = nCalcCenterMachineY + VAR_ROBOT_D("上相机中心基于旋转中心坐标Y") - nCalcHeadMachineY;
	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;
	m_sRet.Format("下相机吸嘴%d拍照位", m_mapIndex["当前吸嘴索引"] + 1);

	m_pMainBoardInTray[m_mapIndex["TRAY盘主板索引"]].bExistFlag = true;
	m_pMainBoardInTray[m_mapIndex["TRAY盘主板索引"]].bHasMaterialFlag = true;

	RETURN_STATE(&CLogicRobot::OnRobot62, true);
}

CStatus CLogicRobot::OnRobot62()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt));

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到TRAY盘放料位", true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumStatus(m_mapIndex["当前吸嘴索引"]));

	if (m_sRet != "On") {
		CString sRet;
		sRet.Format("回放时检测到吸嘴%d真空检测异常，请确认是否掉料？", m_mapIndex["当前吸嘴索引"] + 1);
		MESSAGEBOX(sRet, "", false);

		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadPickOk = false;
		m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadProcOk = false;

		m_mapIndex["已抛料数量"]++;
		RETURN_STATE(&CLogicRobot::OnRobot60, true);
	}

	m_stDstRobPnt.z += VAR_ROBOT_D("Tray放料高度偏移");

	RETURN_STATE(&CLogicRobot::OnRobot63, true);
}

CStatus CLogicRobot::OnRobot63()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt, true, false));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderOn(m_mapIndex["当前吸嘴索引"]));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderStatus(m_mapIndex["当前吸嘴索引"]));

	if (m_sRet != "On") {
		m_sRet.Format("等待吸嘴气缸%d伸出", m_mapIndex["当前吸嘴索引"] + 1);
		RETURN_SELF(m_sRet, true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到TRAY盘放料位", true);
	}

	RETURN_STATE(&CLogicRobot::OnRobot64, true);
}

CStatus CLogicRobot::OnRobot64()
{
	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderBrokenVacuumOn(m_mapIndex["当前吸嘴索引"]));

	Sleep(200);

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderVacuumOff(m_mapIndex["当前吸嘴索引"]));

	m_stDstRobPnt.z = m_pRobot->m_mapRobotPoint["空闲等待位"]->z;

	RETURN_STATE(&CLogicRobot::OnRobot65, true);
}

CStatus CLogicRobot::OnRobot65()
{
	RUN_STOP_IF_ERROR(m_pRobot->Move(m_stDstRobPnt));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderOff(m_mapIndex["当前吸嘴索引"]));

	RUN_STOP_IF_ERROR(m_pRobot->PickCylinderStatus(m_mapIndex["当前吸嘴索引"]));

	if (m_sRet != "Off") {
		m_sRet.Format("等待吸嘴气缸%d缩回", m_mapIndex["当前吸嘴索引"] + 1);
		RETURN_SELF(m_sRet, true);
	}

	RUN_STOP_IF_ERROR(m_pRobot->IsInPos(m_stDstRobPnt));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待机械手运动到TRAY盘放料位", true);
	}

	m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadPickOk = false;
	m_stHeadProcResult[m_mapIndex["当前吸嘴索引"]].bHeadProcOk = false;

	m_mapIndex["已抛料数量"]++;

	RETURN_STATE(&CLogicRobot::OnRobot60, true);
}
