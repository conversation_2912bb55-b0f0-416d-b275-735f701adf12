{"version": "1.0", "description": "MQTT发布主题配置 - 基于SCADA v1.13协议标准", "mqtt_publish_topics": [{"name": "property_report", "topic_template": "$oc/devices/{deviceId}/sys/properties/report", "qos": 1, "retain": false, "description": "设备属性周期上报", "message_type": "properties"}, {"name": "property_get_response", "topic_template": "$oc/devices/{deviceId}/sys/properties/get/response/request_id={requestId}", "qos": 1, "retain": false, "description": "设备响应属性查询", "message_type": "properties"}, {"name": "property_set_response", "topic_template": "$oc/devices/{deviceId}/sys/properties/set/response/request_id={requestId}", "qos": 1, "retain": false, "description": "设备响应属性设置", "message_type": "response"}, {"name": "command_response", "topic_template": "$oc/devices/{deviceId}/sys/commands/response/request_id={requestId}", "qos": 1, "retain": false, "description": "设备响应平台命令", "message_type": "response"}, {"name": "event_report", "topic_template": "$oc/devices/{deviceId}/sys/events/up/request_id={requestId}", "qos": 2, "retain": false, "description": "设备主动上报事件", "message_type": "events"}]}