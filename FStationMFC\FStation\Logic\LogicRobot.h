﻿#pragma once

#include "ThreadBase.h"
using namespace yzBase;

#include "Robot.h"

typedef struct _HEAD_PROC_RESULT_
{
	bool	bHeadPickOk;

	bool	bHeadThrowFlag;

	bool	bMesNgFlag;

	bool	bHeadProcOk;

	int		nProcTimes;

	bool	bFromTray;

	double	nHeadPixelX;
	double	nHeadPixelY;
	double	nHeadPixelR;

	double	nHeadMachineX;
	double	nHeadMachineY;
	double	nHeadMachineR;

	CString sCode2D;
} HEADPROCRESULT, *PHEADPROCRESULT;

typedef struct _TRAY_MAIN_BOARD_INFO_
{
	double	nPixelX;
	double	nPixelY;
	double	nPixelR;

	bool	bExistFlag;

	bool    bProcOk;

	int		nProcTimes;

	bool	bMesNgFlag;

	bool	bBadCheckNgFlag;

	bool	bHasMaterialFlag;

	CString sCode2D;
}TRAYMAINBOARDINFO, *PTRAYMAINBOARDINFO, INMAINBOARDINFO, *PINMAINBOARDINFO;

typedef struct _MARK_INFO_
{
	bool	bAssembleOk;

	bool	bMarkProcOk;

	bool    bLookBackProcOk;

	int		nProcTimes;

	double	nMarkPixelX;
	double	nMarkPixelY;
	double	nMarkPixelR;

	double	nMarkMachineX;
	double	nMarkMachineY;
	double	nMarkMachineR;

	double  nRotateR;

	double  nRealMachineX;
	double  nRealMachineY;
	double  nRealMachineR;

	double  nLookBackMachineX;
	double  nLookBackMachineY;
	double  nLookBackMachineR;

	bool	bCalcFeedFlag;

	bool	bFromTray;
}MARKINFO, *PMARKINFO;

class CLogicRobot : public CThreadBase
{
public:
	CLogicRobot();
	virtual ~CLogicRobot();

public:
	// 基础控制函数
	EnumStatus OnSafeCheck();       // 安全检查：检查设备安全状态和初始条件
	EnumStatus OnStart();           // 启动控制：初始化机器人系统，准备开始生产
	EnumStatus OnPause();           // 暂停控制：暂停当前操作，保持状态
	EnumStatus OnResume();          // 恢复控制：从暂停状态恢复运行
	EnumStatus OnStop();            // 停止控制：停止所有操作，复位系统

public:
	// 主运行函数
	CStatus OnRun();                // 主状态机运行函数：协调整个机器人工作流程
	
	// ========== 待料和取料流程 (OnRobot00-OnRobot11) ==========
	CStatus OnRobot00();            // 空闲等待：机器人处于空闲状态，等待来料或治具
	CStatus OnRobot00_0();          // 来料检测：检测皮带和托盘来料状态
	CStatus OnRobot00_1();          // 等待来料：等待皮带来料超时处理
	CStatus OnRobot00_2();          // 来料分配：分配当前来料索引，选择取料位置
	CStatus OnRobot01();            // 预留状态1：备用流程
	CStatus OnRobot02();            // 预留状态2：备用流程  
	CStatus OnRobot02_0();          // 预留状态2_0：备用流程
	CStatus OnRobot03();            // 皮带来料处理：处理皮带上的主板来料
	CStatus OnRobot03_0();          // 皮带图像处理：处理皮带来料的图像识别和二维码扫描
	CStatus OnRobot04();            // 取料准备：计算取料位置和补偿参数
	CStatus OnRobot04_0();          // 避位动作：机械手避位移动
	CStatus OnRobot04_1();          // 取料判断：判断是否满足取料条件，决定下一步动作
	CStatus OnRobot05();            // 移动到取料位：机械手移动到计算好的取料位置
	CStatus OnRobot06();            // 取料准备：打开真空，准备取料
	CStatus OnRobot07();            // 执行取料：下降取料，检查吸嘴状态
	CStatus OnRobot08();            // 取料完成：上升离开，检查取料是否成功
	CStatus OnRobot09();            // 取料验证：验证取料结果，处理取料失败
	CStatus OnRobot10();            // 取料后处理：更新取料状态，准备下一步
	CStatus OnRobot11();            // 取料流程结束：完成取料，跳转到下一流程

	// ========== 托盘取料流程 (OnRobot12-OnRobot24) ==========
	CStatus OnRobot12();            // 托盘检测：检测托盘是否到位和有料
	CStatus OnRobot13();            // 托盘拍照准备：移动到托盘拍照位置
	CStatus OnRobot14();            // 托盘拍照：执行托盘相机拍照
	CStatus OnRobot15();            // 托盘图像处理：处理托盘图像，识别主板位置
	CStatus OnRobot16();            // 托盘取料准备：准备从托盘取料
	CStatus OnRobot16_0();          // 托盘取料选择：选择托盘中的主板进行取料
	CStatus OnRobot17();            // 托盘取料计算：计算托盘取料的位置补偿
	CStatus OnRobot18();            // 移动到托盘取料位：机械手移动到托盘取料位置
	CStatus OnRobot19();            // 托盘取料准备：打开真空，准备从托盘取料
	CStatus OnRobot20();            // 执行托盘取料：下降取料，检查吸嘴状态
	CStatus OnRobot21();            // 托盘取料完成：上升离开，检查取料是否成功
	CStatus OnRobot22();            // 托盘取料验证：验证托盘取料结果
	CStatus OnRobot23();            // 托盘取料后处理：更新托盘取料状态
	CStatus OnRobot24();            // 托盘取料流程结束：完成托盘取料，跳转到下一流程

	// ========== 下相机检测流程 (OnRobot25-OnRobot29) ==========
	CStatus OnRobot25();            // 下相机检测准备：准备进行下相机检测
	CStatus OnRobot25_0();          // 下相机检测选择：选择需要检测的吸嘴
	CStatus OnRobot26();            // 移动到下相机位：机械手移动到下相机拍照位置
	CStatus OnRobot27();            // 下相机拍照：执行下相机拍照
	CStatus OnRobot28();            // 下相机图像处理：处理下相机图像，检测主板质量
	CStatus OnRobot29();            // 下相机检测完成：完成所有吸嘴检测，决定下一步流程
	CStatus OnRobot29_0();          // 下相机检测后处理：处理检测结果，准备装配或抛料

	// ========== 抛料流程 (OnRobot30-OnRobot33) ==========
	CStatus OnRobot30();            // 抛料准备：检查是否有需要抛料的主板
	CStatus OnRobot30_0();          // 抛料选择：选择需要抛料的吸嘴
	CStatus OnRobot31();            // 移动到抛料位：机械手移动到NG物料抛料位置
	CStatus OnRobot32();            // 抛料等待：等待NG皮带允许放料信号
	CStatus OnRobot32_0();          // 执行抛料：执行抛料动作，关闭真空
	CStatus OnRobot33();            // 抛料完成：完成抛料，更新吸嘴状态

	// ========== 治具识别和定位流程 (OnRobot34-OnRobot38) ==========
	CStatus OnRobot34();            // 治具流程控制：控制治具识别和装配流程
	CStatus OnRobot34_0();          // 治具二维码扫描准备：移动到治具二维码扫描位置
	CStatus OnRobot34_1();          // 治具二维码拍照：执行治具二维码拍照
	CStatus OnRobot34_2();          // 治具二维码处理：处理治具二维码图像
	CStatus OnRobot34_3();          // 治具二维码完成：完成治具二维码识别，跳转装配流程
	CStatus OnRobot35();            // Mark点拍照准备：选择Mark点拍照位置
	CStatus OnRobot36();            // 移动到Mark拍照位：机械手移动到治具Mark拍照位置
	CStatus OnRobot37();            // Mark点拍照：执行治具Mark点拍照
	CStatus OnRobot38();            // Mark点处理：处理Mark点图像，计算治具位置偏移

	// ========== 装配流程 (OnRobot39-OnRobot42) ==========
	CStatus OnRobot39();            // 装配流程控制：控制主板装配流程，处理装配完成逻辑
	CStatus OnRobot39_0();          // 装配补偿计算：计算装配位置补偿
	CStatus OnRobot39_1();          // 装配位置计算：计算精确的装配位置
	CStatus OnRobot39_2();          // 移动到装配位：机械手移动到主板装配位置
	CStatus OnRobot39_3();          // 装配准备：准备装配，检查装配条件
	CStatus OnRobot39_4();          // 执行装配：执行主板装配动作
	CStatus OnRobot39_5();          // 装配完成检查：检查装配是否成功
	CStatus OnRobot39_5_0();        // 装配状态处理：处理装配状态，更新装配标志
	CStatus OnRobot39_6();          // 装配后处理：装配完成后的处理
	CStatus OnRobot39_7();          // 装配验证：验证装配质量
	CStatus OnRobot39_8();          // 装配记录：记录装配数据
	CStatus OnRobot39_9();          // 装配流程结束：完成装配流程
	CStatus OnRobot40();            // 预留装配状态1：备用装配流程
	CStatus OnRobot41();            // 预留装配状态2：备用装配流程
	CStatus OnRobot42();            // 预留装配状态3：备用装配流程

	// ========== 回拍检测流程 (OnRobot43-OnRobot44) ==========
	CStatus OnRobot43();            // 回拍检测控制：控制装配后的回拍检测流程
	CStatus OnRobot43_0();          // 回拍检测准备：准备回拍检测，选择检测位置
	CStatus OnRobot43_1();          // 移动到回拍位：机械手移动到主板回拍位置
	CStatus OnRobot43_2();          // 回拍拍照：执行主板回拍拍照
	CStatus OnRobot43_3();          // 回拍图像处理：处理回拍图像，检测装配质量
	CStatus OnRobot43_3_0();        // 回拍结果处理：处理回拍检测结果
	CStatus OnRobot43_3_1();        // 回拍数据上传：上传回拍数据到MES系统
	CStatus OnRobot43_4();          // 回拍验证：验证回拍检测结果
	CStatus OnRobot43_5();          // 回拍记录：记录回拍检测数据
	CStatus OnRobot43_6();          // 回拍完成：完成回拍检测
	CStatus OnRobot43_7();          // 回拍流程结束：完成回拍流程
	CStatus OnRobot44();            // 回拍后处理：回拍完成后的后续处理

	// ========== A轨装配流程 (OnRobot45-OnRobot55) ==========
	CStatus OnRobot45();            // A轨装配控制：控制A轨装配流程
	CStatus OnRobot45_0();          // A轨装配准备：准备A轨装配
	CStatus OnRobot45_1();          // A轨装配位置计算：计算A轨装配位置
	CStatus OnRobot45_2();          // 移动到A轨装配位：机械手移动到A轨装配位置
	CStatus OnRobot45_3();          // A轨装配执行：执行A轨装配动作
	CStatus OnRobot46();            // A轨装配检查：检查A轨装配状态
	CStatus OnRobot47();            // A轨装配完成：完成A轨装配
	CStatus OnRobot48();            // A轨装配后处理：A轨装配完成后处理
	CStatus OnRobot49();            // A轨装配验证：验证A轨装配质量
	CStatus OnRobot50();            // A轨回拍控制：控制A轨回拍检测流程
	CStatus OnRobot50_0();          // A轨回拍准备：准备A轨回拍检测
	CStatus OnRobot50_1();          // 移动到A轨回拍位：机械手移动到A轨回拍位置
	CStatus OnRobot50_2();          // A轨回拍拍照：执行A轨回拍拍照
	CStatus OnRobot50_3();          // A轨回拍处理：处理A轨回拍图像
	CStatus OnRobot50_4();          // A轨回拍验证：验证A轨回拍结果
	CStatus OnRobot50_5();          // A轨回拍完成：完成A轨回拍
	CStatus OnRobot50_5_0();        // A轨回拍数据处理：处理A轨回拍数据
	CStatus OnRobot50_6();          // A轨回拍记录：记录A轨回拍数据
	CStatus OnRobot50_7();          // A轨回拍上传：上传A轨回拍数据
	CStatus OnRobot50_8();          // A轨回拍结束检查：检查A轨回拍是否全部完成
	CStatus OnRobot50_9();          // A轨回拍流程结束：完成A轨回拍流程
	CStatus OnRobot51();            // A轨流程完成：完成A轨所有流程
	CStatus OnRobot52();            // A轨后处理1：A轨流程完成后处理
	CStatus OnRobot53();            // A轨后处理2：A轨流程后续处理
	CStatus OnRobot54();            // A轨最终处理：A轨流程最终处理
	CStatus OnRobot54_0();          // A轨最终处理准备：准备A轨最终处理
	CStatus OnRobot54_1();          // A轨最终处理执行：执行A轨最终处理
	CStatus OnRobot54_2();          // A轨最终处理验证：验证A轨最终处理
	CStatus OnRobot54_3();          // A轨最终处理完成：完成A轨最终处理
	CStatus OnRobot54_3_0();        // A轨完成状态处理：处理A轨完成状态
	CStatus OnRobot54_3_1();        // A轨完成数据上传：上传A轨完成数据
	CStatus OnRobot54_4();          // A轨完成验证：验证A轨完成状态
	CStatus OnRobot54_5();          // A轨完成记录：记录A轨完成数据
	CStatus OnRobot54_6();          // A轨完成后处理：A轨完成后续处理
	CStatus OnRobot54_7();          // A轨完成流程结束：结束A轨完成流程
	CStatus OnRobot55();            // A轨流程结束：A轨所有流程结束

	// ========== 主板回放和特殊处理流程 (OnRobot56-OnRobot66) ==========
	CStatus OnRobot56();            // 主板回放控制：控制主板回放流程
	CStatus OnRobot57();            // 主板回放准备：准备主板回放
	CStatus OnRobot58();            // 主板回放执行：执行主板回放动作
	CStatus OnRobot59();            // 主板回放完成：完成主板回放
	CStatus OnRobot60();            // 特殊处理控制：控制特殊情况处理
	CStatus OnRobot60_0();          // 特殊处理准备：准备特殊情况处理
	CStatus OnRobot61();            // 特殊处理执行：执行特殊情况处理
	CStatus OnRobot62();            // 特殊处理验证：验证特殊处理结果
	CStatus OnRobot63();            // 特殊处理完成：完成特殊处理
	CStatus OnRobot64();            // 系统复位准备：准备系统复位
	CStatus OnRobot65();            // 系统复位执行：执行系统复位
	CStatus OnRobot66();            // 系统复位完成：完成系统复位，返回初始状态

private:
	CRobot*				m_pRobot;           // 机器人设备对象指针

	CString				m_sRet;             // 函数返回字符串结果

	map<CString, DWORD> m_mapTick;          // 时间计时器映射表，用于超时控制
	map<CString, int>	m_mapIndex;         // 索引映射表，记录各种当前索引状态
	map<CString, bool>	m_mapFlag;          // 标志映射表，记录各种状态标志

	ROBOTPOINT			m_stDstRobPnt;      // 目标机器人位置点
	ROBOTPOINT			m_stLastDstRobPnt;  // 上一次目标机器人位置点

	HEADPROCRESULT		m_stHeadProcResult[4];  // 4个吸嘴的处理结果
	MARKINFO			m_stMarkInfo[8];        // 8个Mark点信息（A轨4个+B轨4个）

	int					m_nDnCamCaptureSequence[4];  // 下相机拍照序列

	PTRAYMAINBOARDINFO	m_pMainBoardInTray;           // 托盘中主板信息指针
	INMAINBOARDINFO		m_stInMainBoardInBelt[3];     // 皮带上3个主板信息
	
	bool				m_bTrayCameraCaptureFinishFlag;  // 托盘相机拍照完成标志
	bool				m_bExcuteOnce;                   // 执行一次标志

	int					m_nHeadBackCnt;     // 主板回放数量计数
	int					m_nTrayExistSum;    // 托盘存在主板总数
	int					m_nPickSum;         // 取料总数计数

	CString				m_sFixtureCodeA;    // A轨治具二维码
	CString				m_sFixtureCodeB;    // B轨治具二维码
};
