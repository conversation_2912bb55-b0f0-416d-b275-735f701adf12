# 线程间关系与协作机制

## 系统架构关系图

```
                    CLogicMgr (线程管理器)
                         │
           ┌─────────────┼─────────────┐
           │             │             │
    设备控制层        物料处理层      流程协调层
           │             │             │
    ┌─────────┐    ┌─────────┐    ┌─────────┐
    │LogicMachine│ │LogicTray│    │LogicContinueRun│
    │LogicRobot  │ │├Empty   │    │LogicProc │
    │LogicBelt   │ │└Full    │    │LogicReset│
    │            │ │LogicFixture│  │          │
    │            │ │LogicFixture│  │          │
    │            │ │Transport│     │          │
    └─────────┘    └─────────┘    └─────────┘
```

## 核心协作关系

### 1. 管理层关系

#### CLogicMgr → 所有线程
- **控制方式**: 统一的生命周期管理
- **接口**: Start(), Stop(), Pause(), Resume()
- **状态查询**: GetStatus(), isRunning(), isPause()

#### LogicContinueRun → 各子系统
- **协调角色**: 系统总指挥
- **控制范围**: 托盘、治具、传送带系统
- **工作模式**: 五个子线程并发协调

### 2. 物料流转关系

#### Robot ↔ Tray 系统
```cpp
// 机器人请求托盘
g_pRobot->m_mapParam["TRAY盘进料标志"] = true;
g_pRobot->m_mapParam["TRAY盘退料标志"] = true;

// 托盘系统响应
PARAM("空TRAY盘退料标志") = true;   // 提供空托盘
PARAM("满TRAY盘退料标志") = true;   // 回收满托盘
```

#### Fixture ↔ FixtureTransport
- **功能分工**: Fixture负责装卸，Transport负责传输
- **协调机制**: 通过治具状态参数同步
- **安全联锁**: 相互检查对方状态确保安全

#### Tray ↔ TrayEmpty/TrayFull
```cpp
// 主控制器协调子系统
LogicTray::OnTray01() → 运行满托盘子线程
LogicTray::OnTray02() → 运行空托盘子线程
```

### 3. 安全监控关系

#### LogicMachine → 全系统
```cpp
// 安全事件触发系统级暂停
CLogicMgr::Pause();  // 安全门开启时
CLogicMgr::Run();    // 安全条件恢复时
```

#### 分级安全响应
1. **紧急停止**: 立即停止所有运动
2. **安全暂停**: 暂停相关线程，保持状态
3. **警告继续**: 记录警告但继续运行

## 通信机制详解

### 1. 参数映射表通信
```cpp
// 布尔标志通信
g_pRobot->m_mapParam["标志名"]->B() = true/false;

// 数值参数通信  
g_pTray->m_mapParam["位置参数"]->D() = 100.5;

// 字符串参数通信
g_pFixture->m_mapParam["状态信息"]->S() = "完成";
```

### 2. 线程状态通信
```cpp
// 检查其他线程状态
EnumStatus status = CLogicMgr::m_mapThread["Robot"].pThread->GetStatus();

// 控制其他线程
CLogicMgr::m_mapThread["TrayFull"].pThread->Start();
CLogicMgr::m_mapThread["TrayEmpty"].pThread->Stop();
```

### 3. 子线程通信
```cpp
// 主线程内部子线程协调
EnumStatus status = m_pFuncButton->Run(this);
switch (status) {
    case emRun: break;
    case emStop: RETURN_STOP();
    case emPause: RETURN_PAUSE(NULL);
}
```

## 典型协作场景

### 1. 产品生产流程
```
1. Robot请求空托盘 → Tray系统提供
2. Robot装配产品 → Machine监控安全
3. Robot完成装配 → 归还满托盘
4. Tray系统回收 → Fixture处理成品
5. Belt传输产品 → 流程循环
```

### 2. 系统启动流程
```
1. ContinueRun启动 → 激活子系统协调
2. 启动TrayFull/Empty → 准备物料系统
3. 各线程自检 → 确保设备就绪
4. Machine安全检查 → 确认安全条件
5. 进入生产循环 → 开始正常生产
```

### 3. 异常处理流程
```
1. 检测到异常 → 相关线程报告
2. Machine评估严重程度 → 决定响应级别
3. 执行相应动作 → 暂停/停止/警告
4. 等待条件恢复 → 监控异常状态
5. 自动或手动恢复 → 继续生产
```

## 数据流向分析

### 1. 命令流 (自上而下)
```
CLogicMgr → 各线程 → 子线程 → 设备控制
```

### 2. 状态流 (自下而上)
```
设备状态 → 线程状态 → 系统状态 → 用户界面
```

### 3. 数据流 (横向交换)
```
Robot ↔ Tray ↔ Fixture ↔ Belt
     ↕       ↕       ↕      ↕
  视觉系统  物料状态  治具状态  传输状态
```

## 设计优势

### 1. 松耦合设计
- 通过参数映射表实现间接通信
- 减少线程间的直接依赖
- 提高系统的可维护性

### 2. 分层管理
- 清晰的管理层次结构
- 职责明确的功能分工
- 统一的接口规范

### 3. 容错机制
- 单线程故障不影响全系统
- 分级的异常处理策略
- 自动恢复能力

### 4. 扩展性
- 标准化的线程接口
- 模块化的功能设计
- 易于添加新的功能模块

这种协作机制确保了FStation系统的高效、稳定和可靠运行。 