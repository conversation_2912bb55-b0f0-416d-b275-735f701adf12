#pragma once

#include "SimpleConfigManager.h"
#include <string>
#include <vector>
#include <map>
#include <regex>

// Topic管理器 - 负责MQTT主题的构建和匹配
class TopicManager {
public:
    TopicManager();
    ~TopicManager() = default;
    
    // 初始化
    bool Initialize(const SimpleConfigManager& configManager);
    void SetDeviceId(const std::string& deviceId);
    
    // 订阅管理
    std::vector<std::string> GetSubscriptionTopics() const;
    int GetSubscriptionQoS(const std::string& topicPattern) const;
    std::string GetMessageHandler(const std::string& topic) const;
    bool IsSubscriptionEnabled(const std::string& name) const;
    
    // 发布管理
    std::string BuildPublishTopic(const std::string& topicName, 
                                 const std::map<std::string, std::string>& params = {}) const;
    int GetPublishQoS(const std::string& topicName) const;
    bool GetRetainFlag(const std::string& topicName) const;
    std::string GetMessageType(const std::string& topicName) const;
    
    // Topic匹配和解析
    bool IsTopicMatch(const std::string& topic, const std::string& pattern) const;
    std::string ExtractRequestId(const std::string& topic) const;
    std::string DetermineMessageHandler(const std::string& topic) const;
    
    // 工具方法
    std::string ReplaceTopicPlaceholders(const std::string& topicTemplate, 
                                        const std::map<std::string, std::string>& params) const;
    bool ValidateTopicFormat(const std::string& topic) const;
    
private:
    std::string m_deviceId;
    std::vector<SubscriptionConfig> m_subscriptions;
    std::vector<PublishTopicConfig> m_publishTopics;
    
    // Topic模式缓存
    std::map<std::string, std::regex> m_topicPatterns;
    
    // 初始化Topic模式
    void InitializeTopicPatterns();
    std::string ConvertTopicPatternToRegex(const std::string& mqttPattern) const;
};
