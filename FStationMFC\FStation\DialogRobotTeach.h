﻿#pragma once

#include "Resource.h"
// CDialog 对话框

#include "ColorList.h"

class CDialogRobotTeach : public CDialogEx, public CParallel
{
	DECLARE_DYNAMIC(CDialogRobotTeach)

public:
	CDialogRobotTeach(CPoint pt, CWnd* pParent = NULL);   // 标准构造函数
	virtual ~CDialogRobotTeach();

// 对话框数据
	enum { IDD = IDD_DIALOG_ROBOT_TEACH };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持
	virtual BOOL PreTranslateMessage(MSG* pMsg);
	virtual void OnOK() {};
	virtual BOOL OnInitDialog();
	virtual bool Excute();

	DECLARE_MESSAGE_MAP()
	afx_msg void OnDestroy();
	afx_msg void OnNMDblclkList1(NMHDR *pNMHDR, LRESULT *pResult);
	afx_msg void OnNMClickList1(NMHDR *pNMHDR, LRESULT *pResult);
	afx_msg void OnEnKillfocusEdit5();
	afx_msg void OnBnClickedButton1();
	afx_msg void OnBnClickedButton2();
	afx_msg void OnBnClickedButton3();
	afx_msg void OnBnClickedButton4();
	afx_msg void OnBnClickedButton5();
	afx_msg void OnBnClickedButton6();
	afx_msg void OnBnClickedButton7();
	afx_msg void OnBnClickedButton8();
	afx_msg void OnBnClickedButton9();
	afx_msg void OnBnClickedButton10();
	afx_msg void OnTimer(UINT_PTR nIDEvent);

protected:
	CImageList	m_imageList;

	CColorList	m_list;

	CEdit		m_edit;

private:
	CPoint		m_pt;

	ROBOTPOINT	m_curRobPnt;

	int			m_nRowForEdit;
	int			m_nColForEdit;
};
