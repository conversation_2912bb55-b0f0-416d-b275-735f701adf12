# 治具绑定接口增强功能说明

## 概述

本次更新对FStation的治具绑定接口进行了增强，在原有的`lotId`和`toolId`基础上，增加了三个新的参数：`lineId`（拉线ID）、`station`（站点）、`oper`（工序），以便更好地进行生产追溯和返查。

## 修改内容

### 1. 接口参数增强

原有JSON格式：
```json
{
  "lotId": "001730721728032300000014",
  "toolId": "J***2CC1090****1065201711030006"
}
```

增强后JSON格式：
```json
{
  "lotId": "001730721728032300000014",
  "toolId": "J***2CC1090****1065201711030006",
  "lineId": "STB10102",
  "station": "AD01_01",
  "oper": "T3800"
}
```

### 2. 配置参数

新增了以下三个配置项，用户可以在系统参数中进行设置：

| 配置项名称 | 默认值 | 说明 |
|------------|--------|------|
| MES治具绑定拉线ID | STB10102 | 生产拉线标识 |
| MES治具绑定站点 | AD01_01 | 工作站点标识 |
| MES治具绑定工序 | T3800 | 工序代码 |

### 3. 代码修改

#### 3.1 函数声明修改 (Mes.h)
```cpp
// 原函数声明
static bool HttpPostFixture(CString sIp, int nPort, CString sFixtureCode, CString sPcbaCode, CString sUserName, CString sPassword, CString sFactory, CString *pStrRecv);

// 新函数声明
static bool HttpPostFixture(CString sIp, int nPort, CString sFixtureCode, CString sPcbaCode, CString sLineId, CString sStation, CString sOper, CString sUserName, CString sPassword, CString sFactory, CString *pStrRecv);
```

#### 3.2 函数实现修改 (Mes.cpp)
在JSON构建部分添加了新的字段：
```cpp
Json::Value root;
root["lotId"] = Json::Value(sPcbaCode.GetBuffer());
root["toolId"] = Json::Value(sFixtureCode.GetBuffer());
root["lineId"] = Json::Value(sLineId.GetBuffer());
root["station"] = Json::Value(sStation.GetBuffer());
root["oper"] = Json::Value(sOper.GetBuffer());
```

#### 3.3 配置项添加 (Machine.cpp)
```cpp
MakePair("MES治具绑定拉线ID", new CData(CString("STB10102"), true, 4));
MakePair("MES治具绑定站点", new CData(CString("AD01_01"), true, 4));
MakePair("MES治具绑定工序", new CData(CString("T3800"), true, 4));
```

#### 3.4 函数调用更新 (LogicRobot.cpp)
```cpp
// 原调用方式
bFlag = CMes::HttpPostFixture(VAR_MACHINE_S("MES治具绑定服务器地址"), 80, m_sFixtureCodeB, m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].sCode2D, VAR_MACHINE_S("MES治具绑定用户名"), VAR_MACHINE_S("MES治具绑定密码"), VAR_MACHINE_S("MES治具绑定工厂"), &sMesRes);

// 新调用方式
bFlag = CMes::HttpPostFixture(VAR_MACHINE_S("MES治具绑定服务器地址"), 80, m_sFixtureCodeB, m_pRobot->m_vImageFlow[m_mapIndex["当前治具索引"] % 4][0].sCode2D, VAR_MACHINE_S("MES治具绑定拉线ID"), VAR_MACHINE_S("MES治具绑定站点"), VAR_MACHINE_S("MES治具绑定工序"), VAR_MACHINE_S("MES治具绑定用户名"), VAR_MACHINE_S("MES治具绑定密码"), VAR_MACHINE_S("MES治具绑定工厂"), &sMesRes);
```

## 使用说明

### 1. 参数配置
用户可以通过系统参数配置界面设置以下参数：
- **MES治具绑定拉线ID**：设置生产拉线标识
- **MES治具绑定站点**：设置工作站点标识  
- **MES治具绑定工序**：设置工序代码

### 2. 生产追溯
通过新增的三个参数，可以实现更精确的生产追溯：
- **lineId**：可以追溯到具体的生产线
- **station**：可以追溯到具体的工作站
- **oper**：可以追溯到具体的工序

### 3. 兼容性
- 本次修改保持了与现有MES系统的兼容性
- 新增参数为可配置项，不影响现有功能
- 接口URL和认证方式保持不变

## 注意事项

1. 需要确保MES服务器端已支持新增的三个字段
2. 配置参数需要根据实际生产环境进行调整
3. 建议在测试环境中先验证接口功能后再部署到生产环境

## 版本信息

- 修改日期：2024-12-15
- 修改版本：基于当前FStation版本
- 影响范围：治具绑定功能模块 