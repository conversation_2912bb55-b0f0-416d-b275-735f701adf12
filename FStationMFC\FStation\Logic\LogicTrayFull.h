﻿#pragma once

#include "ThreadBase.h"
using namespace yzBase;

#include "Tray.h"

class CLogicTrayFull : public CThreadBase
{
public:
	CLogicTrayFull();
	virtual ~CLogicTrayFull();

public:
	// 基础控制函数
	EnumStatus OnSafeCheck();       // 安全检查：检查满托盘系统安全状态
	EnumStatus OnStart();           // 启动控制：初始化满托盘系统
	EnumStatus OnPause();           // 暂停控制：暂停满托盘操作
	EnumStatus OnResume();          // 恢复控制：从暂停状态恢复满托盘操作
	EnumStatus OnStop();            // 停止控制：停止满托盘系统

public:
	// 主运行函数
	CStatus OnRun();                // 主状态机运行函数：协调满托盘系统工作流程

	// ========== 自检流程 (OnSelfCheck00-OnSelfCheck01) ==========
	CStatus OnSelfCheck00();        // 自检准备：准备满托盘系统自检
	CStatus OnSelfCheck01();        // 自检执行：执行满托盘系统自检

	// ========== 满托盘主流程 (OnFullTray00-OnFullTray02) ==========
	CStatus OnFullTray00();         // 满托盘流程控制：控制满托盘主流程
	CStatus OnFullTray01();         // 满托盘状态检查：检查满托盘状态
	CStatus OnFullTray02();         // 满托盘流程分配：分配满托盘流程

	// ========== 满托盘进料流程 (OnFullTrayFeed00-OnFullTrayFeed13) ==========
	CStatus OnFullTrayFeed00();     // 满托盘进料准备：准备满托盘进料
	CStatus OnFullTrayFeed01();     // 满托盘进料检测：检测满托盘进料状态
	CStatus OnFullTrayFeed01_0();   // 满托盘进料检测子流程：检测满托盘进料详细状态
	CStatus OnFullTrayFeed01_1();   // 满托盘进料检测验证：验证满托盘进料检测结果
	CStatus OnFullTrayFeed01_2();   // 满托盘进料检测完成：完成满托盘进料检测
	CStatus OnFullTrayFeed01_3();   // 满托盘进料检测后处理：满托盘进料检测后处理
	CStatus OnFullTrayFeed02();     // 满托盘进料定位：定位满托盘进料位置
	CStatus OnFullTrayFeed03();     // 满托盘进料夹紧：夹紧满托盘
	CStatus OnFullTrayFeed03_0();   // 满托盘进料夹紧子流程：执行满托盘夹紧详细操作
	CStatus OnFullTrayFeed03_1();   // 满托盘进料夹紧验证：验证满托盘夹紧状态
	CStatus OnFullTrayFeed03_2();   // 满托盘进料夹紧完成：完成满托盘夹紧
	CStatus OnFullTrayFeed03_3();   // 满托盘进料夹紧后处理：满托盘夹紧后处理
	CStatus OnFullTrayFeed04();     // 满托盘进料抬升：抬升满托盘到工作位置
	CStatus OnFullTrayFeed04_0();   // 满托盘进料抬升子流程：执行满托盘抬升详细操作
	CStatus OnFullTrayFeed04_1();   // 满托盘进料抬升验证：验证满托盘抬升状态
	CStatus OnFullTrayFeed05();     // 满托盘进料验证：验证满托盘进料状态
	CStatus OnFullTrayFeed06();     // 满托盘进料完成：完成满托盘进料
	CStatus OnFullTrayFeed07();     // 满托盘进料后处理：满托盘进料后处理
	CStatus OnFullTrayFeed07_0();   // 满托盘进料后处理子流程：执行满托盘进料后处理详细操作
	CStatus OnFullTrayFeed07_1();   // 满托盘进料后处理验证：验证满托盘进料后处理状态
	CStatus OnFullTrayFeed08();     // 满托盘进料清理：清理满托盘进料区域
	CStatus OnFullTrayFeed09();     // 满托盘进料记录：记录满托盘进料数据
	CStatus OnFullTrayFeed10();     // 满托盘进料状态更新：更新满托盘进料状态
	CStatus OnFullTrayFeed11();     // 满托盘进料流程验证：验证满托盘进料流程
	CStatus OnFullTrayFeed12();     // 满托盘进料流程完成：完成满托盘进料流程
	CStatus OnFullTrayFeed13();     // 满托盘进料流程结束：结束满托盘进料流程

	// ========== 满托盘回收流程 (OnFullTrayBack00-OnFullTrayBack10) ==========
	CStatus OnFullTrayBack00();     // 满托盘回收准备：准备满托盘回收
	CStatus OnFullTrayBack01();     // 满托盘回收检测：检测满托盘回收条件
	CStatus OnFullTrayBack02();     // 满托盘回收定位：定位满托盘回收位置
	CStatus OnFullTrayBack03();     // 满托盘回收下降：下降满托盘到回收位置
	CStatus OnFullTrayBack03_0();   // 满托盘回收下降子流程：执行满托盘下降详细操作
	CStatus OnFullTrayBack03_1();   // 满托盘回收下降验证：验证满托盘下降状态
	CStatus OnFullTrayBack03_2();   // 满托盘回收下降完成：完成满托盘下降
	CStatus OnFullTrayBack04();     // 满托盘回收松开：松开满托盘夹紧
	CStatus OnFullTrayBack04_0();   // 满托盘回收松开子流程：执行满托盘松开详细操作
	CStatus OnFullTrayBack04_1();   // 满托盘回收松开验证：验证满托盘松开状态
	CStatus OnFullTrayBack05();     // 满托盘回收推出：推出满托盘
	CStatus OnFullTrayBack06();     // 满托盘回收验证：验证满托盘回收状态
	CStatus OnFullTrayBack07();     // 满托盘回收完成：完成满托盘回收
	CStatus OnFullTrayBack08();     // 满托盘回收后处理：满托盘回收后处理
	CStatus OnFullTrayBack09();     // 满托盘回收清理：清理满托盘回收区域
	CStatus OnFullTrayBack10();     // 满托盘回收流程结束：结束满托盘回收流程

private:
	CTray*					m_pTray;            // 托盘设备对象指针

	CString					m_sRet;             // 函数返回字符串结果

	map<CString, bool>		m_mapFlag;          // 标志映射表，记录各种状态标志
	map<CString, DWORD>		m_mapTick;          // 时间计时器映射表，用于超时控制
	map<CString, double>	m_mapPos;           // 位置映射表，记录各种位置信息
};
