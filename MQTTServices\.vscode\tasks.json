{"version": "2.0.0", "tasks": [{"label": "CMake: Configure", "type": "shell", "command": "cmake", "args": ["-B", "build", "-S", ".", "-DCMAKE_TOOLCHAIN_FILE=D:/vcpkg/scripts/buildsystems/vcpkg.cmake", "-G", "Visual Studio 17 2022", "-A", "x64"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "CMake: Build Debug", "type": "shell", "command": "cmake", "args": ["--build", "build", "--config", "Debug", "--parallel", "8"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile", "dependsOn": "CMake: Configure"}, {"label": "CMake: Build Release", "type": "shell", "command": "cmake", "args": ["--build", "build", "--config", "Release", "--parallel", "8"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile", "dependsOn": "CMake: Configure"}, {"label": "CMake: Clean", "type": "shell", "command": "cmake", "args": ["--build", "build", "--target", "clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "CMake: Install", "type": "shell", "command": "cmake", "args": ["--install", "build", "--config", "Debug"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "dependsOn": "CMake: Build Debug"}, {"label": "vcpkg: Install Dependencies", "type": "shell", "command": "vcpkg", "args": ["install", "--triplet", "x64-windows"], "options": {"cwd": "${workspaceFolder}/MQTTGateway"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}