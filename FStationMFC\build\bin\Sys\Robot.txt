MODULE MainModule
    VAR string sIp := "*************";
    VAR num nPort := 8000;
    
    PERS loaddata pieces := [2.5,[0,0,100],[1,0,0,0],0,0,0];
    
    VAR socketdev sSever;
    VAR socketdev sClient;
    
    VAR string sRecv; ! ?cmd:1,2,3,4,5!
    
    PROC ServerInit()
        SocketCreate sSever;        
        
        SocketBind sSever, sIp, nPort;    
        
        SocketListen sSever;   
        
        SocketAccept sSever, sClient;
        
        TPWrite "Client connect.";        
        
    ERROR
        IF ERRNO = ERR_SOCK_TIMEOUT THEN
            RETRY;
        ELSEIF ERRNO = ERR_SOCK_CLOSED THEN
            TPWrite "Client disconnect.";
            SeverRecover;
            RETRY;
        ELSE
            TPWrite "ERRNO = "\Num := ERRNO;
            Stop;
        ENDIF
    ENDPROC
    
    PROC SeverClose()       
        SocketClose sSever;
        SocketClose sClient;
    ENDPROC
    
    PROC SeverRecover()
        SocketClose sSever;
        SocketClose sClient;
        SocketCreate sSever;
        SocketBind sSever, sIp, nPort;
        SocketListen sSever;
        SocketAccept sSever, sClient;        
        TPWrite "Client connect.";        
    ERROR
        IF ERRNO = ERR_SOCK_TIMEOUT THEN
            RETRY;
        ELSEIF ERRNO = ERR_SOCK_CLOSED THEN
            RETURN;
        ELSE
            TPWrite "ERRNO = "\Num:=ERRNO;
            Stop;
        ENDIF
    ENDPROC

    PROC RequestCmd()    
        VAR num nLen := 0;
        SocketReceive sClient\Str:=sRecv;
        nLen := StrLen(sRecv);
        IF nLen < 64 THEN
           TPWrite "Receive data : " + sRecv; 
        ENDIF      
    ERROR
        IF ERRNO = ERR_SOCK_TIMEOUT THEN
            RETRY;
        ELSEIF ERRNO = ERR_SOCK_CLOSED THEN
            TPWrite "Client disconnect.";
            SeverRecover;
            RETRY;
        ELSE
            TPWrite "ERRNO = "\Num:=ERRNO;
            Stop;
        ENDIF
    ENDPROC
    
    PROC RobotMove(string sCmd)
		VAR num x;
		VAR num y;
		VAR num z;
		VAR num r;
        
		VAR num c1;
		VAR num c2;
		VAR num c3;
		VAR num c4;
        
		VAR num conf;
        
		VAR bool ok;
        
		VAR num speed;
        
        VAR num anglex;
		VAR num angley;
		VAR num anglez;
        
		VAR jointtarget jointpos;
		VAR errnum nErrNum;

        
		VAR robtarget p;
        
		VAR speeddata localSpeed := [100, 500, 1000, 500];

		VAR confdata confDst := [-1,-2,0,1];

        VAR num nStartIndex := 0;
        VAR num nEndIndex := 0;
        
		nStartIndex := StrMatch(sCmd, 1, ":");
		nEndIndex := StrMatch(sCmd, nStartIndex + 1, ",");
		ok:=StrToVal(strpart(sCmd, nStartIndex + 1, nEndIndex - nStartIndex - 1), y);
      
        nStartIndex := nEndIndex;
		nEndIndex := StrMatch(sCmd, nStartIndex + 1, ",");
		ok:=StrToVal(strpart(sCmd, nStartIndex + 1, nEndIndex - nStartIndex - 1), x);
        
        nStartIndex := nEndIndex;
		nEndIndex := StrMatch(sCmd, nStartIndex + 1, ",");
		ok:=StrToVal(strpart(sCmd, nStartIndex + 1, nEndIndex - nStartIndex - 1), z);
        
        nStartIndex := nEndIndex;
		nEndIndex := StrMatch(sCmd, nStartIndex + 1, ",");
		ok:=StrToVal(strpart(sCmd, nStartIndex + 1, nEndIndex - nStartIndex - 1), r);
        
        nStartIndex := nEndIndex;
		nEndIndex := StrMatch(sCmd, nStartIndex + 1, ",");
		ok:=StrToVal(strpart(sCmd, nStartIndex + 1, nEndIndex - nStartIndex - 1), c1);
        
        nStartIndex := nEndIndex;
		nEndIndex := StrMatch(sCmd, nStartIndex + 1, ",");
		ok:=StrToVal(strpart(sCmd, nStartIndex + 1, nEndIndex - nStartIndex - 1), c2);
        
        nStartIndex := nEndIndex;
		nEndIndex := StrMatch(sCmd, nStartIndex + 1, ",");
		ok:=StrToVal(strpart(sCmd, nStartIndex + 1, nEndIndex - nStartIndex - 1), c3);
        
        nStartIndex := nEndIndex;
		nEndIndex := StrMatch(sCmd, nStartIndex + 1, ",");
		ok:=StrToVal(strpart(sCmd, nStartIndex + 1, nEndIndex - nStartIndex - 1), c4);
        
        nStartIndex := nEndIndex;
		nEndIndex := StrMatch(sCmd, nStartIndex + 1, ",");
		ok:=StrToVal(strpart(sCmd, nStartIndex + 1, nEndIndex - nStartIndex - 1), speed);
        
        nStartIndex := nEndIndex;
		nEndIndex := StrMatch(sCmd, nStartIndex + 1, ",");
		ok:=StrToVal(strpart(sCmd, nStartIndex + 1, nEndIndex - nStartIndex - 1), conf);
        
		localSpeed.v_tcp := speed;

		IF conf = 1 THEN
			ConfJ\On;
		ELSE
			ConfJ\Off;
		ENDIF
		
        IF y<-502 THEN
            
        ENDIF
        
		p := CRobT(\Tool := tool0 \WObj := wobj0);
        
		anglex := EulerZYX(\X, p.rot);
		angley := EulerZYX(\Y, p.rot);
		anglez := EulerZYX(\Z, p.rot);
		anglez := r;
        
		p.trans.x := x;
		p.trans.y := y;
		p.trans.z := z;

		p.rot :=OrientZYX(anglez, angley, anglex);
        
		confDst.cf1 := c1;
		confDst.cf4 := c2;
		confDst.cf6 := c3;
		confDst.cfx := c4;
        
		p.robconf := confDst;

		jointpos := CalcJointT(p, tool0 \WObj := wobj0 \ErrorNumber := nErrNum);
        
        AccSet 30, 50;
        
		IF nErrNum = ERR_ROBLIMIT THEN
			TPWrite "Joint jointpos can not be reached.";
			TPWrite "jointpos.robax.rax_1: " + ValToStr(jointpos.robax.rax_1);
			TPWrite "jointpos.extax.eax_f" + ValToStr(jointpos.extax.eax_f);
			SocketSend sClient \Str := "Position can not be reached!";
		ELSEIF nErrNum = ERR_OUTSIDE_REACH THEN
			TPWrite "Joint jointpos is outside reach.";
			TPWrite "jointpos.robax.rax_1: " + ValToStr(jointpos.robax.rax_1);
			SocketSend sClient \Str:="Position is outside reach!";
		ELSE
			MoveJ p, localSpeed, fine, tool0 \WObj := wobj0;
			SocketSend sClient\Str := "OK";
		ENDIF
    ENDPROC
    
    PROC RobotWhere()
		VAR num nPosR;
        
		VAR string sWhere;
        
		VAR robtarget p;
        
		p := CRobT(\Tool := tool0 \WObj := wobj0);
        
        sWhere := "?";
        
		sWhere := sWhere + numtostr(p.trans.y, 3) + ",";
		sWhere := sWhere + numtostr(p.trans.x, 3) + ",";
		sWhere := sWhere + numtostr(p.trans.z, 3) + ",";

		nPosR := EulerZYX(\Z, p.rot);
        
		sWhere := sWhere + numtostr(nPosR,3) + ",";
		sWhere := sWhere + numtostr(p.robconf.cf1, 3) + ",";
		sWhere := sWhere + numtostr(p.robconf.cf4, 3) + ",";
		sWhere := sWhere + numtostr(p.robconf.cf6, 3) + ",";
		sWhere := sWhere + numtostr(p.robconf.cfx, 3) + ",";
        
        sWhere := sWhere + "!";
        
        TPWrite "Where : " + sWhere;
        
		SocketSend sClient\Str := sWhere;
	ERROR
		IF ERRNO=ERR_SOCK_TIMEOUT THEN
			RETRY;
		ELSEIF ERRNO=ERR_SOCK_CLOSED THEN
			SeverRecover;
			RETRY;
		ELSE
            TPWrite "ERRNO = "\Num:=ERRNO;
            Stop;
		ENDIF
    ENDPROC
    
	PROC main()
        VAR string sRecvCmd;
		VAR string sCmd;
        
		VAR string sStart;
		VAR string sEnd;

        VAR num nCmdLen := 0;
        
		VAR num nCmd := 0;
        
		ServerInit;
        
        GripLoad pieces;

		WHILE TRUE DO
            RequestCmd;
            
            nCmdLen := StrLen(sRecv);
           
            IF nCmdLen >= 7 THEN
                sRecvCmd := sRecv;
                sRecv := "";
                
                sStart := StrPart(sRecvCmd, 1, 1);
                sEnd := StrPart(sRecvCmd, nCmdLen, 1);
                
                IF sStart = "?" THEN
                    IF sEnd = "!" THEN
                        nCmd := StrFind(sRecvCmd, 1, ":");
                        sCmd := StrPart(sRecvCmd, 2, nCmd - 2);
                        TEST sCmd
                            CASE "Move":
                                RobotMove(sRecvCmd);  
                            CASE "Stop":
                                StopMove;
                            CASE "Where":
                                RobotWhere;
                            DEFAULT:
                        ENDTEST
                    ENDIF
                ENDIF                
            ENDIF
            
		ENDWHILE
        
        SeverClose;
        
	ENDPROC
    
ENDMODULE