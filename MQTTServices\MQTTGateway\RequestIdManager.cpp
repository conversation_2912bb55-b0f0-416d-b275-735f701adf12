#include "RequestIdManager.h"
#include <QLoggingCategory>
#include <algorithm>

Q_LOGGING_CATEGORY(requestIdManager, "mqtt.gateway.requestid")

RequestIdManager::RequestIdManager(QObject* parent)
    : QObject(parent)
    , m_cleanupTimer(nullptr)
    , m_cacheExpirationMinutes(10)  // 默认10分钟过期
    , m_cleanupIntervalMinutes(5)   // 默认5分钟清理一次
    , m_maxCacheSize(1000)          // 默认最大1000个缓存
    , m_duplicateRequestCount(0)
{
    // 创建定期清理定时器
    m_cleanupTimer = new QTimer(this);
    connect(m_cleanupTimer, &QTimer::timeout, this, &RequestIdManager::OnCleanupTimer);
    
    // 启动定时器（转换为毫秒）
    m_cleanupTimer->start(m_cleanupIntervalMinutes * 60 * 1000);
    
    qCInfo(requestIdManager) << "RequestIdManager初始化完成";
    qCInfo(requestIdManager) << "- 缓存过期时间:" << m_cacheExpirationMinutes << "分钟";
    qCInfo(requestIdManager) << "- 清理间隔:" << m_cleanupIntervalMinutes << "分钟";
    qCInfo(requestIdManager) << "- 最大缓存数量:" << m_maxCacheSize;
}

RequestIdManager::~RequestIdManager() {
    if (m_cleanupTimer) {
        m_cleanupTimer->stop();
    }
    
    std::lock_guard<std::mutex> lock(m_mutex);
    qCInfo(requestIdManager) << "RequestIdManager销毁";
    qCInfo(requestIdManager) << "- 最终处理请求数:" << m_processedRequestIds.size();
    qCInfo(requestIdManager) << "- 最终缓存响应数:" << m_cachedResponses.size();
    qCInfo(requestIdManager) << "- 总重复请求数:" << m_duplicateRequestCount;
}

bool RequestIdManager::IsRequestProcessed(const std::string& requestId) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    bool isProcessed = m_processedRequestIds.find(requestId) != m_processedRequestIds.end();
    
    if (isProcessed) {
        // 增加重复请求计数
        m_duplicateRequestCount++;
        qCDebug(requestIdManager) << "🔄 检测到重复请求 - RequestId:" << QString::fromStdString(requestId)
                                 << "总重复数:" << m_duplicateRequestCount;
    }
    
    return isProcessed;
}

void RequestIdManager::MarkRequestProcessed(const std::string& requestId, 
                                          const std::string& requestType,
                                          const QJsonObject& responseData,
                                          bool success) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // 检查是否需要强制清理（防止内存无限增长）
    if (m_processedRequestIds.size() >= m_maxCacheSize) {
        ForceCleanupOldest();
    }
    
    // 标记请求为已处理
    m_processedRequestIds.insert(requestId);
    
    // 缓存响应数据
    m_cachedResponses[requestId] = CachedResponse(responseData, requestType, success);
    
    qCDebug(requestIdManager) << "✅ 标记请求已处理 - RequestId:" << QString::fromStdString(requestId)
                             << "类型:" << QString::fromStdString(requestType)
                             << "成功:" << success
                             << "缓存数量:" << m_processedRequestIds.size();
}

bool RequestIdManager::GetCachedResponse(const std::string& requestId, CachedResponse& cachedResponse) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_cachedResponses.find(requestId);
    if (it != m_cachedResponses.end()) {
        // 检查缓存是否过期
        if (!IsCacheExpired(it->second.timestamp)) {
            cachedResponse = it->second;
            qCDebug(requestIdManager) << "📋 返回缓存响应 - RequestId:" << QString::fromStdString(requestId)
                                     << "类型:" << QString::fromStdString(it->second.requestType);
            return true;
        } else {
            qCDebug(requestIdManager) << "⏰ 缓存已过期 - RequestId:" << QString::fromStdString(requestId);
        }
    }
    
    return false;
}

void RequestIdManager::CleanupExpiredData() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto now = std::chrono::system_clock::now();
    size_t beforeCount = m_processedRequestIds.size();
    size_t beforeResponseCount = m_cachedResponses.size();
    
    // 清理过期的响应缓存
    auto responseIt = m_cachedResponses.begin();
    while (responseIt != m_cachedResponses.end()) {
        if (IsCacheExpired(responseIt->second.timestamp)) {
            // 同时从请求ID集合中移除
            m_processedRequestIds.erase(responseIt->first);
            responseIt = m_cachedResponses.erase(responseIt);
        } else {
            ++responseIt;
        }
    }
    
    size_t afterCount = m_processedRequestIds.size();
    size_t afterResponseCount = m_cachedResponses.size();
    
    if (beforeCount > afterCount || beforeResponseCount > afterResponseCount) {
        qCInfo(requestIdManager) << "🧹 清理过期数据完成";
        qCInfo(requestIdManager) << "- 请求ID: " << beforeCount << " -> " << afterCount 
                                << " (清理" << (beforeCount - afterCount) << "个)";
        qCInfo(requestIdManager) << "- 响应缓存: " << beforeResponseCount << " -> " << afterResponseCount
                                << " (清理" << (beforeResponseCount - afterResponseCount) << "个)";
    }
}

RequestIdManager::Statistics RequestIdManager::GetStatistics() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    Statistics stats;
    stats.processedCount = m_processedRequestIds.size();
    stats.cachedResponseCount = m_cachedResponses.size();
    stats.duplicateCount = m_duplicateRequestCount;
    
    return stats;
}

void RequestIdManager::SetCleanupIntervalMinutes(int minutes) {
    m_cleanupIntervalMinutes = minutes;
    
    if (m_cleanupTimer) {
        m_cleanupTimer->stop();
        m_cleanupTimer->start(m_cleanupIntervalMinutes * 60 * 1000);
        qCInfo(requestIdManager) << "更新清理间隔为:" << minutes << "分钟";
    }
}

void RequestIdManager::OnCleanupTimer() {
    qCDebug(requestIdManager) << "🕐 执行定期清理";
    CleanupExpiredData();
}

bool RequestIdManager::IsCacheExpired(const std::chrono::system_clock::time_point& timestamp) const {
    auto now = std::chrono::system_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::minutes>(now - timestamp);
    return elapsed.count() >= m_cacheExpirationMinutes;
}

void RequestIdManager::ForceCleanupOldest() {
    if (m_cachedResponses.empty()) {
        return;
    }
    
    // 找到最旧的缓存项
    auto oldestIt = std::min_element(m_cachedResponses.begin(), m_cachedResponses.end(),
        [](const auto& a, const auto& b) {
            return a.second.timestamp < b.second.timestamp;
        });
    
    if (oldestIt != m_cachedResponses.end()) {
        qCDebug(requestIdManager) << "🗑️ 强制清理最旧缓存 - RequestId:" << QString::fromStdString(oldestIt->first);
        
        // 同时从请求ID集合中移除
        m_processedRequestIds.erase(oldestIt->first);
        m_cachedResponses.erase(oldestIt);
    }
}
