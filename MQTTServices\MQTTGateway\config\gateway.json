{"version": "1.0", "config_version": "2024.12.28.001", "description": "MQTT网关基础配置", "device": {"deviceId": "A320021760", "secret": "123456", "userName": "A320021760"}, "mqtt": {"broker": "*************", "port": 1883, "version": "3.1.1", "keepAliveInterval": 60, "cleanSession": true, "automaticReconnect": true, "maxReconnectDelay": 30000, "minReconnectDelay": 1000, "connectionTimeout": 30}, "socket": {"listenPort": 8888, "bindAddress": "0.0.0.0", "maxConnections": 1, "keepAlive": true, "receiveBufferSize": 8192, "sendBufferSize": 8192}, "features": {"defaultReportCycleS": 300, "requestDataEnabled": true, "propertyGetEnabled": true, "commandsEnabled": true, "eventsEnabled": true, "freshnessThresholdMs": 10000}, "logging": {"level": "info", "filePath": "logs/gateway.log", "maxSizeMB": 20, "maxFiles": 5, "consoleOutput": true}, "performance": {"maxQueueSize": 1000, "publishTimeoutS": 5, "responseTimeoutS": 10, "maxRetries": 3}}