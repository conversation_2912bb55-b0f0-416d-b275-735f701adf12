# FStation机器运行机制总览

## 系统概述

FStation是一个复杂的工业自动化贴装系统，专门用于主板装配和品质检测。该系统采用多线程架构，通过11个专业化线程控制各个子系统，实现高精度、高效率的自动化生产。

### 核心组件架构

```
FStation 工业自动化系统
├── 主控制系统 (FStationDlg)
├── 线程管理系统 (LogicMgr)
├── 机器人系统 (Robot + LogicRobot)
├── 视觉检测系统 (Up/Dn/Tray Camera)
├── 托盘管理系统 (Tray + LogicTray系列)
├── 治具控制系统 (Fixture + LogicFixture系列)
├── 传送带系统 (Belt + LogicBelt)
├── 安全监控系统 (LogicMachine)
└── 数据管理系统 (MES/MQTT)
```

## 硬件系统组成

### 1. 机器人系统（核心执行单元）
- **运动控制**：4轴机器人（X、Y、Z、R轴）
- **吸嘴系统**：4个独立控制的吸嘴头
- **真空系统**：吸取/破真空控制
- **速度控制**：可调节速度百分比
- **位置精度**：高精度伺服控制

### 2. 视觉系统（精确定位）
- **上相机（Up）**：治具定位、二维码扫描、Mark识别
- **下相机（Dn）**：元器件检测、位置识别、质量判断
- **TRAY相机**：托盘定位、主板识别

### 3. 输送系统
- **A轨传送带**：主要生产线路
- **B轨传送带**：辅助生产线路
- **NG皮带**：不良品处理
- **托盘传送**：满/空托盘循环

### 4. 托盘系统
- **满托盘处理**：带主板的托盘进料
- **空托盘处理**：托盘回收和供给
- **升降机构**：Z轴高度调节
- **分盘机构**：托盘分离装置

### 5. 治具系统
- **治具开合**：上盖/下盖控制
- **分板机构**：主板分离
- **搬运系统**：治具传输控制
- **高度检测**：装配质量检测

### 6. 安全监控系统
- **三色灯**：状态指示（红/黄/绿）
- **安全光栅**：人员保护
- **急停按钮**：紧急停止
- **安全门**：多门联锁保护

## 软件架构层次

### 1. 应用层
```cpp
FStationDlg (主对话框)
├── 用户界面管理
├── 系统初始化
├── 消息处理
├── 状态监控
└── 日志管理
```

### 2. 业务逻辑层（11个专业线程）
```cpp
线程管理器 (LogicMgr)
├── LogicMachine    // 机器状态监控
├── LogicRobot      // 机器人控制核心（5914行）
├── LogicTray       // 托盘总控
├── LogicTrayFull   // 满托盘处理
├── LogicTrayEmpty  // 空托盘处理
├── LogicFixture    // 治具管理
├── LogicFixtureTransport // 治具传输
├── LogicBelt (A/B) // 传送带控制
├── LogicContinueRun // 连续运行协调
├── LogicProc (1-4) // 工艺处理
└── LogicReset      // 系统重置
```

### 3. 硬件抽象层
```cpp
模块层 (Module)
├── Machine  // 机器基础控制
├── Robot    // 机器人硬件接口
├── Tray     // 托盘硬件控制
├── Fixture  // 治具硬件控制
├── Belt     // 传送带控制
└── HeightSensor // 高度检测
```

### 4. 底层驱动层
```cpp
BaseApi (基础API)
├── Control  // IO控制
├── Motion   // 运动控制
├── Camera   // 相机控制
└── Vision   // 视觉处理
```

## 系统运行流程

### 1. 系统启动流程
```
系统启动 → 硬件初始化 → 参数加载 → 线程创建 → 安全检查 → 待机状态
```

**详细步骤**：
1. **硬件初始化**：检测所有硬件设备连接状态
2. **参数加载**：读取配置文件和工艺参数
3. **线程创建**：初始化11个业务线程
4. **视觉标定**：相机标定和视觉系统初始化
5. **安全检查**：安全门、光栅、急停等检测
6. **回原点**：所有轴回到安全位置

### 2. 生产循环流程

#### A. 主板供给流程
```
托盘进料 → 视觉识别 → 位置标定 → 机器人取料 → 质量检测
```

#### B. 装配流程
```
治具进料 → Mark拍照 → 位置补偿 → 主板贴装 → 精度检测 → 治具出料
```

#### C. 质量控制流程
```
视觉检测 → 尺寸测量 → 位置偏差 → 质量判断 → NG分流
```

### 3. 线程协调机制

```
主线程 (FStationDlg)
├── 监控所有子线程状态
├── 处理用户界面事件
├── 系统状态显示
└── 异常处理

线程管理器 (LogicMgr)
├── 创建和销毁线程
├── 线程状态控制
├── 线程间通信
└── 资源管理

业务线程组
├── 并发执行
├── 状态同步
├── 数据共享
└── 异常传递
```

## 关键运行模式

### 1. 正常生产模式
- **双轨并行**：A轨和B轨同时生产
- **多吸嘴协作**：4个吸嘴并行工作
- **智能调度**：根据来料情况优化节拍

### 2. 老化模式
- **连续运行**：LogicContinueRun协调器启用
- **5子线程**：专门的老化测试流程
- **长时间运行**：稳定性测试

### 3. 直通模式
- **简化流程**：只启用传送带线程
- **快速通过**：用于调试和测试
- **减少干预**：最小化处理步骤

### 4. 调试模式
- **单步执行**：可逐步调试各个环节
- **参数调整**：实时修改运行参数
- **日志详细**：记录详细运行信息

## 安全保护机制

### 1. 硬件安全
- **多重联锁**：安全门、光栅、急停三重保护
- **区域保护**：不同工作区域独立监控
- **动作检测**：实时监控设备动作状态

### 2. 软件安全
- **状态机保护**：每个状态都有安全检查
- **超时保护**：防止死锁和异常等待
- **异常恢复**：自动重试和故障恢复

### 3. 数据安全
- **参数备份**：关键参数多重备份
- **日志记录**：完整的操作日志
- **异常记录**：详细的错误信息

## 数据流通信

### 1. 线程间通信
```cpp
// 参数映射表机制
map<CString, bool>   m_mapFlag;   // 标志位映射
map<CString, DWORD>  m_mapTick;   // 时间戳映射
map<CString, double> m_mapPos;    // 位置参数映射
map<CString, int>    m_mapIndex;  // 索引映射
```

### 2. MES集成
- **过站功能**：生产数据上报
- **治具绑定**：治具状态管理
- **参数上传**：工艺参数同步
- **产品切换**：自动产品管理

### 3. MQTT通信
- **实时数据**：设备状态上报
- **远程控制**：支持远程操作
- **数据采集**：生产数据收集

## 性能优化特点

### 1. 并发处理
- **多线程架构**：11个并发线程
- **异步执行**：非阻塞操作
- **资源共享**：高效资源利用

### 2. 智能调度
- **优先级管理**：关键任务优先
- **负载均衡**：多吸嘴协作
- **动态调整**：实时优化节拍

### 3. 缓存机制
- **图像缓存**：视觉处理加速
- **参数缓存**：快速参数访问
- **状态缓存**：减少IO操作

## 系统监控机制

### 1. 状态监控
- **实时状态**：设备运行状态
- **性能指标**：生产效率统计
- **异常报警**：故障自动检测

### 2. 三色灯状态指示
```cpp
// 三色灯状态逻辑
绿灯：正常生产运行
黄灯：待机或预警状态  
红灯：故障或紧急停止
```

### 3. 生产统计
- **产量统计**：实时产量监控
- **良品率**：质量统计分析
- **效率分析**：设备利用率

## 维护和调试机制

### 1. 参数管理
- **分层参数**：按功能模块分类
- **实时调整**：运行时参数修改
- **备份恢复**：参数安全保护

### 2. 日志系统
- **分级日志**：不同级别日志记录
- **历史存储**：长期数据保存
- **快速检索**：高效日志查询

### 3. 调试工具
- **单步模式**：逐步调试功能
- **状态查看**：实时状态监控
- **参数调试**：在线参数调整

## 系统扩展性

### 1. 模块化设计
- **独立模块**：各功能模块独立
- **标准接口**：统一的模块接口
- **易于扩展**：支持功能扩展

### 2. 配置化管理
- **参数配置**：灵活参数设置
- **模式配置**：多种运行模式
- **设备配置**：支持设备变更

### 3. 集成能力
- **MES集成**：企业系统集成
- **数据接口**：标准数据接口
- **通信协议**：多种通信支持

## 总结

FStation系统体现了现代工业自动化的核心特征：

- **高度集成**：硬件和软件深度融合
- **智能控制**：多线程状态机驱动
- **安全可靠**：多层次安全保护机制
- **高效生产**：并发处理和智能调度
- **易于维护**：完善的监控和调试机制
- **灵活扩展**：模块化和配置化设计

该系统为工业4.0智能制造提供了完整的解决方案，展现了复杂工业设备控制系统的设计精髓。 