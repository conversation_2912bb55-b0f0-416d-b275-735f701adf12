﻿// DialogTray.cpp : 实现文件
//

#include "stdafx.h"
#include "FStation.h"
#include "DialogTray.h"
#include "afxdialogex.h"

#include "LogicMgr.h"

// CDialogTray 对话框

IMPLEMENT_DYNAMIC(CDialogTray, CDialogEx)

CDialogTray::CDialogTray(CPoint pt, CWnd* pParent /*=NULL*/)
	: CDialogEx(CDialogTray::IDD, pParent)
{
	m_pt = pt;
}

CDialogTray::~CDialogTray()
{
}

void CDialogTray::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_BUTTON1, m_btnReset);
	DDX_Control(pDX, IDC_BUTTON2, m_btnStart);
	DDX_Control(pDX, IDC_BUTTON3, m_btnPause);
	DDX_Control(pDX, IDC_BUTTON4, m_btnStop);
}

BEGIN_MESSAGE_MAP(CDialogTray, CDialogEx)
	ON_WM_TIMER()
	ON_BN_CLICKED(IDC_BUTTON1, &CDialogTray::OnBnClickedButton1)
	ON_BN_CLICKED(IDC_BUTTON2, &CDialogTray::OnBnClickedButton2)
	ON_BN_CLICKED(IDC_BUTTON3, &CDialogTray::OnBnClickedButton3)
	ON_BN_CLICKED(IDC_BUTTON4, &CDialogTray::OnBnClickedButton4)
	ON_BN_CLICKED(IDC_BUTTON5, &CDialogTray::OnBnClickedButton5)
	ON_BN_CLICKED(IDC_BUTTON6, &CDialogTray::OnBnClickedButton6)
	ON_BN_CLICKED(IDC_BUTTON7, &CDialogTray::OnBnClickedButton7)
	ON_BN_CLICKED(IDC_BUTTON8, &CDialogTray::OnBnClickedButton8)
	ON_BN_CLICKED(IDC_BUTTON9, &CDialogTray::OnBnClickedButton9)
	ON_BN_CLICKED(IDC_BUTTON10, &CDialogTray::OnBnClickedButton10)
	ON_BN_CLICKED(IDC_BUTTON11, &CDialogTray::OnBnClickedButton11)
	ON_BN_CLICKED(IDC_BUTTON12, &CDialogTray::OnBnClickedButton12)
	ON_BN_CLICKED(IDC_BUTTON13, &CDialogTray::OnBnClickedButton13)
	ON_BN_CLICKED(IDC_BUTTON14, &CDialogTray::OnBnClickedButton14)
	ON_BN_CLICKED(IDC_BUTTON15, &CDialogTray::OnBnClickedButton15)
	ON_BN_CLICKED(IDC_BUTTON16, &CDialogTray::OnBnClickedButton16)
	ON_BN_CLICKED(IDC_BUTTON17, &CDialogTray::OnBnClickedButton17)
	ON_BN_CLICKED(IDC_BUTTON18, &CDialogTray::OnBnClickedButton18)
	ON_BN_CLICKED(IDC_BUTTON19, &CDialogTray::OnBnClickedButton19)
	ON_BN_CLICKED(IDC_BUTTON20, &CDialogTray::OnBnClickedButton20)
	ON_BN_CLICKED(IDC_BUTTON21, &CDialogTray::OnBnClickedButton21)
	ON_BN_CLICKED(IDC_BUTTON22, &CDialogTray::OnBnClickedButton22)
END_MESSAGE_MAP()

BOOL CDialogTray::PreTranslateMessage( MSG* pMsg )
{
	if ((WM_SYSKEYDOWN == pMsg->message && VK_F4 == pMsg->wParam) || (pMsg->message == WM_KEYDOWN && pMsg->wParam == VK_ESCAPE)) { //屏蔽Alt + F4
		return TRUE;
	}

	return CDialogEx::PreTranslateMessage(pMsg);
}

BOOL CDialogTray::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	CRect rc;

	GetWindowRect(&rc);

	MoveWindow(m_pt.x, m_pt.y, rc.Width(), rc.Height());

	CString strPath = CString(GetModulePath().c_str());
	strPath += "\\Sys\\images\\";

	CString strName[] = { 
		"resetbk.bmp", 
		"start.bmp", 
		"pause.bmp", 
		"stop.bmp"
	};

	CColorButton *pBtn[] = { 
		&m_btnReset, 
		&m_btnStart, 
		&m_btnPause, 
		&m_btnStop
	};

	for (int i=0; i<4; i++)
	{
		pBtn[i]->SetColor(RGB(240, 240, 240), RGB(0, 0, 0));

		pBtn[i]->SetImagePos(5, 0);
		pBtn[i]->SetIcon(strPath + strName[i], strPath + strName[i], false);

		pBtn[i]->SetFontSize(20, true);
		pBtn[i]->SetTextPos(68, 0);

	}

	SetTimer(99, 100, NULL);

	return TRUE;
}

bool CDialogTray::Excute()
{
	CString sRet, sRet1;

	DWORD nTick = 0;

	CLogicMgr::m_mapThread["TrayFull"].pThread->Start();
	CLogicMgr::m_mapThread["TrayEmpty"].pThread->Start();

	while (!m_bExit) 
	{
		while (!m_bExit)
		{
			VAR_ROBOT("TRAY盘进料标志") = true;

			while (!m_bExit)
			{
				if (VAR_TRAY_B("满TRAY盘进料完成标志")) {
					VAR_TRAY("满TRAY盘进料完成标志") = false;
					break;
				}

				Sleep(10);
			}

			double nCurPos = 0;

			sRet = g_pTray->FullTrayUpDnZCurrentPos(nCurPos);

			if (sRet != "OK") {
				return false;
			}

			sRet = g_pTray->FullTrayUpInPosStatus();

			sRet1 = g_pTray->FullTrayBeltInPosStatus();

			if (fabs(nCurPos - VAR_TRAY_D("满TRAY盘升降Z轴放料位")) < 1 && sRet != "On" && sRet1 != "On") {
				break;
			}

			Sleep(1000);
		}

		while (!m_bExit)
		{
			VAR_ROBOT("TRAY盘退料标志") = true;

			while (!m_bExit)
			{
				if (VAR_TRAY_B("空TRAY盘进料完成标志")) {
					VAR_TRAY("空TRAY盘进料完成标志") = false;
					VAR_TRAY("满TRAY盘进料完成标志") = false;
					break;
				}

				Sleep(10);
			}

			nTick = GetTickCount();

			while (!m_bExit)
			{
				sRet = g_pTray->EmptyTraySeparateZMove(VAR_TRAY_D("空TRAY盘分盘Z轴支撑位"));
				if (sRet != "OK") {
					AfxMessageBox("空TRAY盘分盘Z轴移动失败");
					m_bExit = true;
				}

				sRet = g_pTray->IsEmptyTraySeparateZInPos(VAR_TRAY_D("空TRAY盘分盘Z轴支撑位"));
				if (sRet == "Yes") {
					break;
				}

				if (GetTickCount() - nTick > 30000) {
					AfxMessageBox("空TRAY盘分盘Z轴移动超时");
					m_bExit = true;
				}
			}

			sRet1 = g_pTray->EmptyTrayMaterialExistStatus();

			nTick = GetTickCount();

			while (!m_bExit)
			{
				sRet = g_pTray->EmptyTraySeparateZMove(VAR_TRAY_D("空TRAY盘分盘Z轴放料位"));
				if (sRet != "OK") {
					AfxMessageBox("空TRAY盘分盘Z轴移动失败");
					m_bExit = true;
				}

				sRet = g_pTray->IsEmptyTraySeparateZInPos(VAR_TRAY_D("空TRAY盘分盘Z轴放料位"));
				if (sRet == "Yes") {
					break;
				}

				if (GetTickCount() - nTick > 30000) {
					AfxMessageBox("空TRAY盘分盘Z轴移动超时");
					m_bExit = true;
				}
			}

			if (sRet1 != "On") {
				Sleep(1000);
				break;
			}

			Sleep(1000);
		}
	}

	CLogicMgr::m_mapThread["TrayFull"].pThread->Stop();
	CLogicMgr::m_mapThread["TrayEmpty"].pThread->Stop();

	return true;
}

// CDialogTray 消息处理程序

void CDialogTray::OnTimer(UINT_PTR nIDEvent)
{
	CString sRet;

	if (nIDEvent == 99) {
		UpdateButtonState();
	}

	if (nIDEvent == 1) {
		do 
		{
			sRet = g_pTray->IsFullTrayUpDnZHomeOK();
			if (sRet != "Yes") {
				break;
			}

			sRet = g_pTray->IsEmptyTraySeparateZHomeOK();
			if (sRet != "Yes") {
				break;
			}

			sRet = g_pTray->IsEmptyTrayTransportYHomeOK();
			if (sRet != "Yes") {
				break;
			}

			KillTimer(1);

			if (GetTickCount() - m_nResetTimer > 30000) {
				AfxMessageBox("复位超时");
				break;
			}

			AfxMessageBox("复位完成");
		} while (false);
	}

	CDialogEx::OnTimer(nIDEvent);
}

void CDialogTray::OnBnClickedButton1()
{
	CLogicMgr::m_mapThread["TrayFull"].pThread->Stop();
	CLogicMgr::m_mapThread["TrayEmpty"].pThread->Stop();

	g_pTray->EmptyTrayPullMaterialCylinderOff();
	g_pTray->EmptyTrayEndStopCylinderOff();

	g_pTray->FullTraySeparateCylinderOff();
	g_pTray->FullTrayEndStopCylinderOff();

	g_pTray->FullTrayBeltOff();
	g_pTray->EmptyTrayBeltOff();

	g_pTray->FullTrayUpDnZHome();

	g_pTray->EmptyTraySeparateZHome();
	g_pTray->EmptyTrayTransportYHome();

	m_nResetTimer = GetTickCount();

	SetTimer(1, 100, NULL);
}

void CDialogTray::OnBnClickedButton2()
{
	CLogicMgr::m_mapThread["TrayFull"].pThread->Start();
	CLogicMgr::m_mapThread["TrayEmpty"].pThread->Start();
}

void CDialogTray::OnBnClickedButton3()
{
	CLogicMgr::m_mapThread["TrayFull"].pThread->Pause();
	CLogicMgr::m_mapThread["TrayEmpty"].pThread->Pause();
}

void CDialogTray::OnBnClickedButton4()
{
	CLogicMgr::m_mapThread["TrayFull"].pThread->Stop();
	CLogicMgr::m_mapThread["TrayEmpty"].pThread->Stop();
}

void CDialogTray::OnBnClickedButton5()
{
	PREMISSION_CTRL();

	*g_pTray->m_mapParam["空TRAY盘提前进料标志"] = true;
	*g_pTray->m_mapParam["空TRAY盘进料标志"] = true;
}

void CDialogTray::OnBnClickedButton6()
{
	PREMISSION_CTRL();

	*g_pTray->m_mapParam["空TRAY盘退料标志"] = true;
}

void CDialogTray::OnBnClickedButton7()
{
	PREMISSION_CTRL();

	*g_pTray->m_mapParam["满TRAY盘进料标志"] = true;
}

void CDialogTray::OnBnClickedButton8()
{
	PREMISSION_CTRL();

	*g_pTray->m_mapParam["满TRAY盘退料标志"] = true;
}

void CDialogTray::OnBnClickedButton9()
{
}

void CDialogTray::OnBnClickedButton10()
{
	PREMISSION_CTRL();

	VAR_TRAY("满TRAY盘进料完成标志") = true;
	VAR_TRAY("空TRAY盘进料完成标志") = true;
}

void CDialogTray::OnBnClickedButton11()
{
// 	CString sRet;
// 
// 	GetDlgItemText(IDC_BUTTON11, sRet);
// 
// 	if (sRet.Find("开") >= 0) {
// 		SetDlgItemText(IDC_BUTTON11, "老化程序 - 关");
// 		m_bExit = false;
// 		Start();
// 	}
// 	else {
// 		SetDlgItemText(IDC_BUTTON11, "老化程序 - 开");
// 		Stop();
// 	}
}

void CDialogTray::OnBnClickedButton12()
{
	PREMISSION_CTRL();

	int nID = IDC_BUTTON12;

	CString str;
	GetDlgItemText(nID, str);

	if (str.Find("伸出") >= 0) {
		g_pTray->FullTraySupportCylinderOn();
		SetDlgItemText(nID, "满Tray盘\n支撑气缸-缩回");
	}
	else {
		g_pTray->FullTraySupportCylinderOff();
		SetDlgItemText(nID, "满Tray盘\n支撑气缸-伸出");
	}
}

void CDialogTray::OnBnClickedButton13()
{
	PREMISSION_CTRL();

	int nID = IDC_BUTTON13;

	CString str;
	GetDlgItemText(nID, str);

	if (str.Find("伸出") >= 0) {
		g_pTray->FullTraySeparateCylinderOn();
		SetDlgItemText(nID, "满Tray盘\n分盘气缸-缩回");
	}
	else {
		g_pTray->FullTraySeparateCylinderOff();
		SetDlgItemText(nID, "满Tray盘\n分盘气缸-伸出");
	}
}

void CDialogTray::OnBnClickedButton14()
{
	PREMISSION_CTRL();

	int nID = IDC_BUTTON14;

	CString str;
	GetDlgItemText(nID, str);

	if (str.Find("伸出") >= 0) {
		g_pTray->EmptyTraySupportCylinderOn();
		SetDlgItemText(nID, "空Tray盘\n支撑气缸-缩回");
	}
	else {
		g_pTray->EmptyTraySupportCylinderOff();
		SetDlgItemText(nID, "空Tray盘\n支撑气缸-伸出");
	}
}

void CDialogTray::OnBnClickedButton15()
{
	PREMISSION_CTRL();

	int nID = IDC_BUTTON15;

	CString str;
	GetDlgItemText(nID, str);

	if (str.Find("伸出") >= 0) {
		g_pTray->EmptyTrayPullMaterialCylinderOn();
		SetDlgItemText(nID, "空Tray盘\n拨料气缸-缩回");
	}
	else {
		g_pTray->EmptyTrayPullMaterialCylinderOff();
		SetDlgItemText(nID, "空Tray盘\n拨料气缸-伸出");
	}
}

void CDialogTray::OnBnClickedButton16()
{
	PREMISSION_CTRL();

	int nID = IDC_BUTTON16;

	CString str;
	GetDlgItemText(nID, str);

	if (str.Find("开") >= 0) {
		g_pTray->EmptyTrayVacuumOn();
		SetDlgItemText(nID, "空Tray盘\n真空-关");
	}
	else {
		g_pTray->EmptyTrayBrokenVacuumOn();
		Sleep(100);
		g_pTray->EmptyTrayVacuumOff();
		SetDlgItemText(nID, "空Tray盘\n真空-开");
	}
}

void CDialogTray::OnBnClickedButton17()
{
	PREMISSION_CTRL();

	g_pTray->FullTrayUpDnZMove(VAR_TRAY_D("满TRAY盘升降Z轴放料位"));
}

void CDialogTray::OnBnClickedButton18()
{
	PREMISSION_CTRL();

	// TODO: 在此添加控件通知处理程序代码
}

void CDialogTray::OnBnClickedButton19()
{
	PREMISSION_CTRL();

	g_pTray->EmptyTraySeparateZMove(VAR_TRAY_D("空TRAY盘分盘Z轴放料位"));
}

void CDialogTray::OnBnClickedButton20()
{
	PREMISSION_CTRL();

	g_pTray->EmptyTraySeparateZMove(VAR_TRAY_D("空TRAY盘分盘Z轴分盘位"));
}

void CDialogTray::OnBnClickedButton21()
{
	PREMISSION_CTRL();

	g_pTray->EmptyTraySeparateZMove(VAR_TRAY_D("空TRAY盘分盘Z轴支撑位"));
}

void CDialogTray::OnBnClickedButton22()
{
	PREMISSION_CTRL();

	// TODO: 在此添加控件通知处理程序代码
}

void CDialogTray::UpdateButtonState()
{
	CString strPath = CString(GetModulePath().c_str());
	strPath += "\\Sys\\images\\";

	EnumStatus eStat1 = CLogicMgr::m_mapThread["TrayFull"].pThread->GetStatus();
	EnumStatus eStat2 = CLogicMgr::m_mapThread["TrayEmpty"].pThread->GetStatus();

	if (eStat1 == emStop && eStat2 == emStop) {
		m_btnReset.SetIcon(strPath + "resetbk.bmp", strPath + "resetbk.bmp");
		m_btnReset.EnableWindow(TRUE);
	}
	else {
		m_btnReset.SetIcon(strPath + "resetbk_d.bmp", strPath + "resetbk_d.bmp");
		m_btnReset.EnableWindow(TRUE);
	}

	if (eStat1 == emPause || eStat2 == emPause || eStat1 == emStop || eStat2 == emStop) {
		m_btnStart.SetIcon(strPath + "start.bmp", strPath + "start.bmp");
		m_btnStart.EnableWindow(TRUE);
	}
	else {
		m_btnStart.SetIcon(strPath + "start_d.bmp", strPath + "start_d.bmp");
		m_btnStart.EnableWindow(FALSE);
	}

	if (eStat1 == emRun || eStat2 == emRun) {
		m_btnPause.SetIcon(strPath + "pause.bmp", strPath + "pause.bmp");
		m_btnPause.EnableWindow(TRUE);
	}
	else {
		m_btnPause.SetIcon(strPath + "pause_d.bmp", strPath + "pause_d.bmp");
		m_btnPause.EnableWindow(FALSE);
	}

	if (eStat1 == emRun || eStat2 == emRun || eStat1 == emPause || eStat2 == emPause) {
		m_btnStop.SetIcon(strPath + "stop.bmp", strPath + "stop.bmp");
		m_btnStop.EnableWindow(TRUE);
	}
	else {
		m_btnStop.SetIcon(strPath + "stop_d.bmp", strPath + "stop_d.bmp");
		m_btnStop.EnableWindow(FALSE);
	}
}
