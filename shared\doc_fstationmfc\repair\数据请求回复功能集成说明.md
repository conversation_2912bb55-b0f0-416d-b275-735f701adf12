# FStation数据请求回复功能集成说明

## 📋 功能概述

本文档说明如何在FStation生产设备程序中集成数据请求回复功能，用于响应MQTT网关的周期性数据请求。

## 🔧 已实现功能

### 1. **Socket通信接口**
- ✅ **双向通信** - 支持发送数据到MQTT网关，同时接收来自网关的数据请求
- ✅ **数据请求处理** - 自动解析JSON格式的数据请求消息
- ✅ **数据收集** - 从CDat数据集合中收集各种设备数据
- ✅ **JSON响应构建** - 自动构建符合SCADA协议的JSON响应数据
- ✅ **线程安全** - 使用临界区保护数据访问
- ✅ **自动重连** - 支持连接断开后自动重连

### 2. **数据类型支持**
- ✅ **设备状态数据** (DEVICE_STATUS) - 包括运行状态、生产计数等
- ✅ **生产数据** (PRODUCTION_DATA) - 包括产品信息、质量结果等
- ✅ **报警数据** (ALARM_DATA) - 包括故障信息、异常计数等
- ✅ **系统数据** (SYSTEM_DATA) - 包括设备连接状态、系统资源等
- ✅ **特定数据点** - 支持按数据点名称请求特定数据

### 3. **数据映射**
根据Dat.cpp中定义的数据集合，实现了以下数据点的映射：

| 数据代码 | 数据名称 | 数据类型 | 说明 |
|---------|---------|---------|------|
| A00002 | 正常生产 | 布尔值 | 设备是否处于正常生产状态 |
| A00003 | 运行暂停 | 布尔值 | 设备是否处于暂停状态 |
| A00004 | 设备故障 | 布尔值 | 设备是否存在故障 |
| A00006 | 待机状态 | 布尔值 | 设备是否处于待机状态 |
| A00008 | 成功数 | 整数 | 成功生产的产品数量 |
| A00009 | 总测试数 | 整数 | 总测试产品数量 |
| A00010 | 机型程序名 | 字符串 | 当前使用的程序名称 |
| A00011 | 主板SN号 | 字符串 | 当前产品的SN号 |
| A00012 | 当前测试结果 | 整数 | 当前测试的结果 |
| A00014 | 运行周期 | 浮点数 | 设备运行周期时间 |
| A00015 | 累计产能 | 整数 | 累计生产数量 |
| B40008-B40015 | 轨道测高值 | 浮点数 | 各轨道的测高数值 |
| B40016 | 测高阈值 | 浮点数 | 测高的阈值设定 |

## 🛠️ 集成步骤

### 步骤1：包含头文件
在需要使用Socket接口的文件中包含头文件：
```cpp
#include "SocketInterface.h"
```

### 步骤2：初始化Socket接口
在程序启动时调用初始化函数：
```cpp
void CFStationDlg::InitializeSocketInterface()
{
    try {
        // 获取Socket接口实例
        CSocketInterface* pSocket = CSocketInterface::GetInstance();
        
        // 配置Socket参数（可选）
        SocketConfig config;
        config.serverIP = "127.0.0.1";
        config.serverPort = 8888;
        config.autoReconnect = true;
        pSocket->SetConfig(config);
        
        // 启动连接和接收线程
        if (pSocket->Connect()) {
            REPORT(_T("Socket接口初始化成功"), emLogLevelNormal);
        } else {
            REPORT(_T("Socket接口连接失败，将在后台自动重试"), emLogLevelWarning);
        }
        
    } catch (...) {
        REPORT(_T("Socket接口初始化异常"), emLogLevelError);
    }
}
```

### 步骤3：清理资源
在程序退出时调用清理函数：
```cpp
void CFStationDlg::CleanupSocketInterface()
{
    try {
        CSocketInterface::DestroyInstance();
        REPORT(_T("Socket接口已清理"), emLogLevelNormal);
    } catch (...) {
        REPORT(_T("Socket接口清理异常"), emLogLevelError);
    }
}
```

### 步骤4：主动发送数据（可选）
除了响应数据请求外，还可以主动发送数据：
```cpp
// 发送设备状态
CSocketInterface::SendDeviceStatus(_T("RUNNING"), prodCount, errCount);

// 发送生产数据
CSocketInterface::SendProductionData(_T("PROD_001"), _T("BATCH_001"), _T("PASS"));

// 发送报警事件
CSocketInterface::SendAlarmEvent(_T("SENSOR_ERROR"), _T("温度传感器异常"), 2);
```

## 📊 数据请求格式

### 请求消息格式
```json
{
    "type": "DATA_REQUEST",
    "request_id": "REQ_001",
    "data": "DEVICE_STATUS"
}
```

### 支持的数据类型
- `DEVICE_STATUS` - 设备状态数据
- `PRODUCTION_DATA` - 生产数据
- `ALARM_DATA` - 报警数据
- `SYSTEM_DATA` - 系统数据
- `ALL` - 所有数据（默认为设备状态数据）
- `数据点名称` - 特定数据点（如"正常生产"）

### 响应消息格式
```json
{
    "version": "1.0",
    "type": "RESPONSE",
    "messageId": "FSTATION_12345678_ABCD",
    "timestamp": "2024-12-15T16:02:21Z",
    "source": "FStation",
    "target": "MQTTGateway",
    "requestId": "REQ_001",
    "data": {
        "service_id": "DefaultService",
        "properties": {
            "A00002": true,
            "A00003": false,
            "A00004": false,
            "A00006": false,
            "A00008": 100,
            "A00009": 102,
            "A00010": "2AC112-0",
            "A00011": "SN123456789",
            "A00012": 1,
            "A00014": 25.300,
            "A00015": 3500,
            "B40008": 1.250,
            "B40009": 1.280
        },
        "event_time": "2024-12-15T16:02:21Z"
    }
}
```

## 🧪 测试方法

### 1. 编译测试程序
```bash
cd Tests/build
cmake ..
cmake --build . --config Release
```

### 2. 运行测试程序
```bash
.\bin\Release\SocketTest.exe
```

### 3. 测试选项
1. **自动测试** - 自动发送各种类型的数据请求
2. **手动测试** - 手动输入JSON格式的请求消息
3. **周期性测试** - 模拟周期性数据请求（每5秒一次）

### 4. 测试流程
1. 确保FStation程序正在运行并已初始化Socket接口
2. 运行SocketTest.exe测试程序
3. 选择测试模式并观察响应结果
4. 检查FStation程序的日志输出

## 🔍 故障排除

### 常见问题及解决方案

1. **连接失败**
   - 检查FStation程序是否正在运行
   - 检查Socket接口是否已正确初始化
   - 检查端口8888是否被其他程序占用

2. **数据响应为null**
   - 检查CDat数据集合是否已正确初始化
   - 检查数据点名称是否正确
   - 检查CData对象是否有效

3. **JSON格式错误**
   - 检查特殊字符是否已正确转义
   - 检查数据类型判断逻辑是否正确

4. **内存访问异常**
   - 检查临界区的使用是否正确
   - 检查CData指针是否有效

## 📝 配置文件

### Socket配置文件 (config/socket_config.json)
```json
{
    "serverIP": "127.0.0.1",
    "serverPort": 8888,
    "connectTimeout": 5000,
    "sendTimeout": 3000,
    "receiveTimeout": 3000,
    "retryInterval": 10000,
    "maxRetryCount": 3,
    "autoReconnect": true
}
```

## 🚀 性能优化建议

1. **数据缓存** - 对于频繁请求的数据，可以考虑添加缓存机制
2. **批量处理** - 对于大量数据点的请求，可以优化为批量处理
3. **异步处理** - 数据收集过程可以考虑异步处理以提高响应速度
4. **内存管理** - 注意字符串对象的生命周期管理

## 📚 相关文档

- [SCADA平台MQTT协议设备接入指南v1.13.md](../../doc/SCADA平台MQTT协议设备接入指南v1.13.md)
- [Socket协议文档.md](../../doc/Socket协议文档.md)
- [周期性上报测试说明.md](../../doc/周期性上报测试说明.md)
- [SocketManager集成说明.md](../../doc/SocketManager集成说明.md)

## 📞 技术支持

如有问题，请参考相关文档或联系技术支持团队。 