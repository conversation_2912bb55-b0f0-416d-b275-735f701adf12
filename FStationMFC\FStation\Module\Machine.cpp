﻿#include "stdafx.h"
#include "Machine.h"

CMachine::CMachine() : CModule(6)
{
	for (int i=0; i<3; i++)
	{
		m_stInBeltStatus[i].nIndex = i;
		m_stInBeltStatus[i].bInFlag = false;
		m_stInBeltStatus[i].tLastPick = 0;
		m_stInBeltStatus[i].tNowIn = 0;
		m_stInBeltStatus[i].tNowPick = 0;
	}

	m_stProductionCircle.bFree = false;
	
	MakePair("老化模式", new CData(false, true, 4));

	MakePair("直通模式", new CData(false, false, 3));

	MakePair("托盘优先模式", new CData(false, false, 2));

	MakePair("皮带待料超时", new CData(30000, 1, 999000, true, 2));

	MakePair("皮带拥堵超时", new CData(30000, 1, 999000, true, 2));

	MakePair("生产周期监控数量", new CData(5, 1, 100, true, 2));

	MakePair("生产周期待料周期数量", new CData(3, 1, 100, true, 2));

	MakePair("皮带来料监控数量", new CData(5, 1, 100, true, 2));

	MakePair("皮带来料拥堵数量", new CData(3, 1, 100, true, 2));

	MakePair("AGV通讯端口", new CData(502, 0, 65535, true, 4));

	MakePair("NG皮带允许放料标志", new CData(true, false));

	MakePair("NG皮带放料完成标志", new CData(true, false));

	MakePair("NG皮带正转方向", new CData(true, true, 4));

	MakePair("NG皮带转动延时", new CData(500));

	MakePair("弹片检测功能启用", new CData(false, true, 4));

	MakePair("弹片检测功能只检测不判断", new CData(false, true, 4));

	MakePair("图片存储大小", new CData(0.35, 0.1, 10.0, true, 4));

	MakePair("图片存储格式", new CData(CString("jpg"), true, 4));

	MakePair("历史记录天数", new CData(7, 0, 100, true, 4));

	MakePair("硬盘剩余空间", new CData(50, 0, 100, true, 4));

	MakePair("MES功能启用", new CData(false, true, 4));

	MakePair("MES服务器地址", new CData(CString("172.16.255.226"), true, 4));

	MakePair("MES工厂", new CData(CString("CASMT01"), true, 4));

	MakePair("MES用户名", new CData(CString("PT11608"), true, 4));

	MakePair("MES密码", new CData(CString("SMTPT11608"), true, 4));

	MakePair("MES过站功能启用", new CData(false, true, 4));

	MakePair("MES过站服务器地址", new CData(CString("172.16.255.114"), true, 4));

	MakePair("MES过站工厂", new CData(CString("CASMT01"), true, 4));

	MakePair("MES过站用户名", new CData(CString("test002"), true, 4));

	MakePair("MES过站密码", new CData(CString("test002"), true, 4));

	MakePair("MES过站拉线ID", new CData(CString("PT10108"), true, 4));

	MakePair("MES过站工序", new CData(CString("R9330"), true, 4));

	MakePair("MES治具绑定功能启用", new CData(false, true, 4));

	MakePair("MES治具绑定服务器地址", new CData(CString("172.16.255.114"), true, 4));

	MakePair("MES治具绑定工厂", new CData(CString("CASMT01"), true, 4));

	MakePair("MES治具绑定用户名", new CData(CString("PT11608"), true, 4));

	MakePair("MES治具绑定密码", new CData(CString("SMTPT11608"), true, 4));

	MakePair("MES治具绑定拉线ID", new CData(CString("STB10102"), true, 4));

	MakePair("MES治具绑定站点", new CData(CString("AD01_01"), true, 4));

	MakePair("MES治具绑定工序", new CData(CString("T3800"), true, 4));

	MakePair("MES响应超时时间", new CData(15000, 5000, 60000, true, 4));

	MakePair("MES产品自动切换功能启用", new CData(false, true, 4));

	MakePair("MES产品自动切换服务器地址", new CData(CString("172.16.255.114"), true, 4));

	MakePair("MES产品自动切换工厂", new CData(CString("CASMT01"), true, 4));

	MakePair("MES产品自动切换用户名", new CData(CString("PT11608"), true, 4));

	MakePair("MES产品自动切换密码", new CData(CString("SMTPT11608"), true, 4));

	MakePair("MES产品资料存储地址", new CData(CString("127.0.0.1"), true, 4));

	MakePair("MES产品资料访问用户名", new CData(CString("8020123"), true, 4));

	MakePair("MES产品资料访问密码", new CData(CString("123"), true, 4));
	
	MakePair("MES参数上传功能启用", new CData(false, true, 4));

	MakePair("MES参数上传服务器地址", new CData(CString("172.16.255.114"), true, 4));

	MakePair("MES参数上传工厂", new CData(CString("CASMT01"), true, 4));

	MakePair("MES参数上传用户名", new CData(CString("PT11608"), true, 4));

	MakePair("MES参数上传密码", new CData(CString("SMTPT11608"), true, 4));

	MakePair("MES参数上传拉线", new CData(CString("1608"), true, 4));

	MakePair("MES参数上传专案号", new CData(CString(""), true, 4));

	MakePair("MES参数上传产品代码", new CData(CString(""), true, 4));
	
	MakePair("强制自动模式", new CData(false, true, 4));

	MakePair("机台待机变灯时间", new CData(90000, 30000, 300000, true, 4));

	MakePair("MQTT功能启用", new CData(false, true, 4));

	
	
	
	Load();
}

CMachine::~CMachine()
{
}

CString CMachine::BootLightOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("启动按钮灯", true));

	return "OK";
}

CString CMachine::BootLightOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("启动按钮灯", false));

	return "OK";
}

CString CMachine::NGBeltLightOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("NG皮带线按钮灯", true));

	return "OK";
}

CString CMachine::NGBeltLightOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("NG皮带线按钮灯", false));

	return "OK";
}

CString CMachine::RedLightOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("三色灯红", true));

	EXCUTE_RETURN(g_pControl->WriteOutput("三色灯黄", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("三色灯绿", false));

	return "OK";
}

CString CMachine::RedLightOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("三色灯红", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("三色灯黄", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("三色灯绿", false));

	return "OK";
}

CString CMachine::GreenLightOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("三色灯红", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("三色灯黄", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("三色灯绿", true));

	return "OK";
}

CString CMachine::GreenLightOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("三色灯红", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("三色灯黄", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("三色灯绿", false));

	return "OK";
}

CString CMachine::YellowLightOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("三色灯红", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("三色灯黄", true));

	EXCUTE_RETURN(g_pControl->WriteOutput("三色灯绿", false));

	return "OK";
}

CString CMachine::YellowLightOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("三色灯红", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("三色灯黄", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("三色灯绿", false));

	return "OK";
}

CString CMachine::BuzzerOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("蜂鸣器", true));

	return "OK";
}

CString CMachine::BuzzerOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("蜂鸣器", false));

	return "OK";
}

CString CMachine::NGBeltOn(bool bDir)
{
	EXCUTE_RETURN(g_pControl->WriteOutput("NG皮带方向", bDir));

	EXCUTE_RETURN(g_pControl->WriteOutput("NG皮带启动", true));

	return "OK";
}

CString CMachine::NGBeltOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("NG皮带启动", false));

	return "OK";
}

CString CMachine::EmergencyStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("急停信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CMachine::BootStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("启动信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CMachine::NGBeltBootStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("NG皮带线启动信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CMachine::LeftDoorStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("左安全门信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CMachine::RightDoorStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("右安全门信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CMachine::FrontDoorStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("前安全门信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CMachine::BackDoorStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("后安全门信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CMachine::SafetyGratingStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("安全光栅信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CMachine::NGBeltSignal1Status()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("NG皮带线信号1", bStatus));

	return bStatus ? "On" : "Off";
}

CString CMachine::NGBeltSignal2Status()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("NG皮带线信号2", bStatus));

	return bStatus ? "On" : "Off";
}

CString CMachine::NGBeltSignal3Status()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("NG皮带线信号3", bStatus));

	return bStatus ? "On" : "Off";
}
