{"version": "0.2.0", "configurations": [{"name": "Debug MQTTGateway", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/build/MQTTGateway/Debug/MQTTGateway.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "PATH", "value": "D:/Qt6/6.9.1/msvc2022_64/bin;D:/vcpkg/installed/x64-windows/bin;${env:PATH}"}], "console": "externalTerminal", "preLaunchTask": "CMake: Build Debug"}, {"name": "Debug MQTTGatewayUI", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/build/MQTTGatewayUI/Debug/MQTTGatewayUI.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "PATH", "value": "D:/Qt6/6.9.1/msvc2022_64/bin;D:/vcpkg/installed/x64-windows/bin;${env:PATH}"}], "console": "externalTerminal", "preLaunchTask": "CMake: Build Debug"}, {"name": "Release MQTTGateway", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/build/MQTTGateway/Release/MQTTGateway.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "PATH", "value": "D:/Qt6/6.9.1/msvc2022_64/bin;D:/vcpkg/installed/x64-windows/bin;${env:PATH}"}], "console": "externalTerminal", "preLaunchTask": "CMake: Build Release"}, {"name": "Release MQTTGatewayUI", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/build/MQTTGatewayUI/Release/MQTTGatewayUI.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "PATH", "value": "D:/Qt6/6.9.1/msvc2022_64/bin;D:/vcpkg/installed/x64-windows/bin;${env:PATH}"}], "console": "externalTerminal", "preLaunchTask": "CMake: Build Release"}, {"name": "Attach to Process", "type": "cppvsdbg", "request": "attach", "processId": "${command:pickProcess}"}]}