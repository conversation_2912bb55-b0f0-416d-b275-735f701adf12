# MES响应超时时间配置功能说明

## 概述

本次更新实现了MES接口响应超时时间的可配置功能。原来所有MES接口都使用固定的10秒超时时间，现在可以通过系统参数进行统一配置，以适应不同网络环境和MES服务器响应特性。

## 问题背景

在实际生产环境中，由于网络延迟、MES服务器负载等因素，个别MES校验接口可能会出现超时报警，主要涉及：

- 参数校验接口
- 治具绑定接口  
- 自动转产接口
- MES过站接口

固定的10秒超时时间无法满足所有场景需求，需要提供可配置的超时时间设置。

## 解决方案

### 1. 新增配置参数

在系统参数中新增"MES响应超时时间"配置项：

| 配置项名称 | 默认值 | 取值范围 | 单位 | 说明 |
|------------|--------|----------|------|------|
| MES响应超时时间 | 15000 | 5000-60000 | 毫秒 | MES接口响应超时时间 |

**配置说明：**
- 默认值：15000毫秒（15秒），比原来的10秒增加了5秒缓冲
- 最小值：5000毫秒（5秒），保证基本的响应时间
- 最大值：60000毫秒（60秒），避免过长的等待时间

### 2. 影响的MES接口

以下5个MES接口函数都已更新为使用配置的超时时间：

#### 2.1 参数监控接口
- **函数名：** `CMes::HttpPost`
- **接口地址：** `/mesplus/service/CwipPcbaGlueCheck.json`
- **用途：** PCBA胶水检查/参数监控

#### 2.2 MES过站接口  
- **函数名：** `CMes::HttpPostPassStation`
- **接口地址：** `/mesplus/service/CwipBakeAutoStation.json`
- **用途：** 产品过站记录

#### 2.3 治具绑定接口
- **函数名：** `CMes::HttpPostFixture`
- **接口地址：** `/mesplus/service/CwipBindLotIdAndJigInterface.json`
- **用途：** 治具与产品绑定

#### 2.4 自动转产接口
- **函数名：** `CMes::HttpPostSwitchProduct`
- **接口地址：** `/mesplus/service/CinvLineTransferInteractiveApi.json`
- **用途：** 产线自动转产

#### 2.5 参数上传接口
- **函数名：** `CMes::HttpPostUploadParam`
- **接口地址：** `/mesplus/service/CateCheckMchineOperateParameters.json`
- **用途：** 设备参数上传

### 3. 代码修改详情

#### 3.1 配置项添加 (Machine.cpp)
```cpp
MakePair("MES响应超时时间", new CData(15000, 5000, 60000, true, 4));
```

#### 3.2 超时时间应用 (Mes.cpp)
原代码：
```cpp
pSock->GetRecv(buffRecvUtf, nRecvLen, 10000);
```

修改后：
```cpp
pSock->GetRecv(buffRecvUtf, nRecvLen, VAR_MACHINE_I("MES响应超时时间"));
```

## 使用方法

### 1. 参数配置
1. 打开FStation系统参数设置界面
2. 找到"MES响应超时时间"配置项
3. 根据实际网络环境和MES服务器响应情况设置合适的超时时间
4. 保存参数配置

### 2. 推荐设置值

| 网络环境 | 推荐超时时间 | 说明 |
|----------|--------------|------|
| 局域网环境 | 10000-15000ms | 网络延迟较低 |
| 跨网段环境 | 15000-25000ms | 可能存在网络延迟 |
| 广域网环境 | 20000-30000ms | 网络延迟较高 |
| 高负载环境 | 25000-40000ms | MES服务器负载较高 |

### 3. 故障排查

如果仍然出现超时报警：

1. **检查网络连接**
   - 确认网络连通性
   - 检查网络延迟和丢包率

2. **检查MES服务器状态**
   - 确认MES服务器运行正常
   - 检查服务器负载情况

3. **调整超时时间**
   - 适当增加超时时间设置
   - 观察是否还有超时现象

4. **联系MES管理员**
   - 如果问题持续存在，联系MES系统管理员检查服务器端问题

## 注意事项

1. **合理设置超时时间**
   - 过短：可能导致正常响应也被判定为超时
   - 过长：会延长异常情况下的等待时间，影响生产效率

2. **生产环境调整**
   - 建议在非生产时间进行参数调整
   - 调整后观察一段时间确认效果

3. **记录和监控**
   - 记录参数调整的时间和原因
   - 监控调整后的超时报警情况

## 技术实现

### 配置管理
- 使用现有的系统参数框架
- 支持运行时动态修改
- 参数验证确保取值范围合理

### 兼容性
- 保持所有MES接口功能不变
- 仅修改超时时间获取方式
- 向后兼容现有配置

### 错误处理
- 超时时仍然返回相应的错误信息
- 保持原有的错误处理逻辑
- 不影响其他功能模块

## 版本信息

- **修改日期：** 2024年12月
- **影响模块：** MES接口模块
- **配置文件：** 系统参数配置
- **重启要求：** 参数修改后立即生效，无需重启 