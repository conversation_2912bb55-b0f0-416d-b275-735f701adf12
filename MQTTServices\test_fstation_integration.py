#!/usr/bin/env python3
"""
FStationMFC集成测试脚本
测试完整的MQTT网关 → FStationMFC → 响应流程
"""

import paho.mqtt.client as mqtt
import json
import time
import threading

# MQTT 配置
MQTT_BROKER = "192.168.88.44"
MQTT_PORT = 1883
DEVICE_ID = "A320021760"
CLIENT_ID = f"integration_test_{int(time.time())}"

class IntegrationTester:
    def __init__(self):
        self.client = None
        self.responses = {}
        self.lock = threading.Lock()
        
    def on_connect(self, client, userdata, flags, rc):
        if rc == 0:
            print(f"✅ 连接成功到 EMQX")
            # 订阅命令响应
            response_topic = f"$oc/devices/{DEVICE_ID}/sys/commands/response/+"
            client.subscribe(response_topic, qos=1)
            print(f"📥 订阅命令响应主题")
        else:
            print(f"❌ 连接失败: {rc}")
    
    def on_message(self, client, userdata, msg):
        print(f"\n📨 收到FStationMFC响应:")
        print(f"   主题: {msg.topic}")
        print(f"   大小: {len(msg.payload)} 字节")
        
        # 提取request_id
        topic_parts = msg.topic.split('/')
        request_id = None
        for part in topic_parts:
            if part.startswith('request_id='):
                request_id = part.split('=', 1)[1]
                break
        
        try:
            response = json.loads(msg.payload.decode())
            print(f"   RequestId: {request_id}")
            print(f"   结果码: {response.get('result_code', 'N/A')}")
            print(f"   结果消息: {response.get('result_message', 'N/A')}")
            
            # 显示详细响应数据
            if 'data' in response and response['data']:
                print(f"   响应数据: {json.dumps(response['data'], indent=4, ensure_ascii=False)}")
            
            with self.lock:
                self.responses[request_id] = response
                
        except Exception as e:
            print(f"   JSON解析失败: {e}")
            print(f"   原始数据: {msg.payload.decode()}")
    
    def send_command(self, command_data, request_id, description):
        """发送命令到FStationMFC"""
        command_topic = f"$oc/devices/{DEVICE_ID}/sys/commands/request_id={request_id}"
        
        print(f"\n📤 {description}")
        print(f"   主题: {command_topic}")
        print(f"   RequestId: {request_id}")
        print(f"   命令类型: {command_data.get('command_name', 'N/A')}")
        
        result = self.client.publish(command_topic, json.dumps(command_data), qos=2)
        
        if result.rc == mqtt.MQTT_ERR_SUCCESS:
            print(f"✅ 命令发送成功")
            return True
        else:
            print(f"❌ 命令发送失败: {result.rc}")
            return False
    
    def test_sn_deliver(self):
        """测试SN下发命令"""
        request_id = f"integration_sn_{int(time.time())}"
        command = {
            "service_id": "CommandService",
            "command_name": "COMMAND_SN_DELIVER",
            "properties": {
                "sn": "INTEGRATION_TEST_SN_001",
                "production_model": "A121000185"
            }
        }
        
        return self.send_command(command, request_id, "发送SN下发命令到FStationMFC"), request_id
    
    def test_pause_production(self):
        """测试暂停生产命令"""
        request_id = f"integration_pause_{int(time.time())}"
        command = {
            "service_id": "CommandService",
            "command_name": "COMMAND_PAUSE",
            "properties": {
                "reason": "集成测试暂停"
            }
        }
        
        return self.send_command(command, request_id, "发送暂停生产命令到FStationMFC"), request_id
    
    def test_resume_production(self):
        """测试恢复生产命令"""
        request_id = f"integration_resume_{int(time.time())}"
        command = {
            "service_id": "CommandService",
            "command_name": "COMMAND_PRODUCTION",
            "properties": {}
        }
        
        return self.send_command(command, request_id, "发送恢复生产命令到FStationMFC"), request_id
    
    def wait_for_response(self, request_id, timeout=20):
        """等待FStationMFC响应"""
        print(f"⏳ 等待FStationMFC处理和响应...")
        start_time = time.time()
        while time.time() - start_time < timeout:
            with self.lock:
                if request_id in self.responses:
                    return self.responses[request_id]
            time.sleep(0.1)
        return None
    
    def analyze_response(self, response, test_name):
        """分析响应结果"""
        if not response:
            return f"{test_name}: 超时无响应", "TIMEOUT"
        
        result_code = response.get('result_code', -999)
        result_message = response.get('result_message', 'N/A')
        
        if result_code == 0:
            return f"{test_name}: 成功 - {result_message}", "SUCCESS"
        else:
            return f"{test_name}: 失败 - 错误码{result_code} - {result_message}", "FAILED"
    
    def run_integration_test(self):
        """运行完整的集成测试"""
        print("=" * 80)
        print("FStationMFC集成测试")
        print("=" * 80)
        print("测试目标:")
        print("1. 验证MQTT网关正确转发命令到FStationMFC")
        print("2. 验证FStationMFC正确处理SCADA命令")
        print("3. 验证FStationMFC正确返回响应到MQTT网关")
        print("4. 验证MQTT网关正确发布响应到MQTT平台")
        print()
        
        # 创建MQTT客户端
        self.client = mqtt.Client(CLIENT_ID)
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        
        try:
            # 连接到MQTT Broker
            self.client.connect(MQTT_BROKER, MQTT_PORT, 60)
            self.client.loop_start()
            
            time.sleep(3)  # 等待连接建立
            
            print("🚀 开始集成测试...")
            
            # 测试序列
            tests = [
                ("SN下发测试", self.test_sn_deliver),
                ("暂停生产测试", self.test_pause_production),
                ("恢复生产测试", self.test_resume_production),
            ]
            
            results = []
            
            for test_name, test_func in tests:
                print(f"\n{'='*60}")
                print(f"执行: {test_name}")
                print(f"{'='*60}")
                
                try:
                    success, request_id = test_func()
                    if success:
                        response = self.wait_for_response(request_id, 25)
                        result_msg, status = self.analyze_response(response, test_name)
                        print(f"📊 {result_msg}")
                        results.append((test_name, status, result_msg))
                    else:
                        results.append((test_name, "SEND_FAILED", "MQTT发送失败"))
                        
                    time.sleep(3)  # 测试间隔
                    
                except Exception as e:
                    error_msg = f"{test_name}: 异常 - {str(e)}"
                    print(f"❌ {error_msg}")
                    results.append((test_name, "EXCEPTION", error_msg))
            
            # 打印最终结果
            self.print_final_results(results)
            
        except Exception as e:
            print(f"❌ 集成测试异常: {e}")
        finally:
            if self.client:
                self.client.loop_stop()
                self.client.disconnect()
    
    def print_final_results(self, results):
        """打印最终测试结果"""
        print("\n" + "=" * 80)
        print("FStationMFC集成测试结果汇总")
        print("=" * 80)
        
        success_count = 0
        total_count = len(results)
        
        print(f"{'测试项':<20} {'状态':<12} {'详细信息':<40}")
        print("-" * 80)
        
        for test_name, status, message in results:
            status_icon = "✅" if status == "SUCCESS" else "❌" if status == "FAILED" else "⏰" if status == "TIMEOUT" else "🚫"
            print(f"{test_name:<20} {status_icon} {status:<10} {message[:40]:<40}")
            
            if status == "SUCCESS":
                success_count += 1
        
        print("-" * 80)
        print(f"📊 测试统计: {success_count}/{total_count} 成功 ({success_count/total_count*100:.1f}%)")
        
        if success_count == total_count:
            print("\n🎉 所有集成测试通过！")
            print("✅ MQTT网关 ↔ FStationMFC 通信完全正常")
            print("✅ SCADA命令处理功能完全正常")
            print("✅ 系统已准备好接收平台命令")
        elif success_count > 0:
            print(f"\n⚠️ 部分测试通过 ({success_count}/{total_count})")
            print("🔧 请检查失败的测试项并修复相关问题")
        else:
            print("\n❌ 所有测试失败")
            print("🔧 请检查以下组件:")
            print("   1. MQTT网关是否正在运行")
            print("   2. FStationMFC是否正在运行")
            print("   3. Socket连接是否正常")
            print("   4. SCADA命令处理器是否已初始化")

def main():
    print("FStationMFC集成测试")
    print("=" * 50)
    print("请确保以下组件正在运行:")
    print("1. ✅ EMQX MQTT Broker")
    print("2. ✅ MQTT网关 (MQTTGateway)")
    print("3. ✅ FStationMFC应用程序")
    print("4. ✅ Socket连接已建立")
    print()
    
    input("按回车键开始集成测试...")
    
    tester = IntegrationTester()
    tester.run_integration_test()

if __name__ == "__main__":
    main()
