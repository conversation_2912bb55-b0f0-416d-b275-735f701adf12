# FStationMFC SCADA命令手动测试文档

## 测试环境配置

### 必需组件
1. ✅ **EMQX MQTT Broker** (*************:1883)
2. ✅ **MQTT网关** (MQTTGateway.exe)
3. ✅ **FStationMFC应用程序** (FStation.exe)
4. ✅ **MQTT客户端工具** (如MQTTX、Mosquitto客户端等)

### 设备信息
- **设备ID**: `A320021760`
- **Socket端口**: `8888`

---

## 测试用MQTT主题和JSON

### 1. SN下发命令测试

#### 📤 **发布主题**
```
$oc/devices/A320021760/sys/commands/request_id=test_sn_001
```

#### 📝 **JSON消息体**
```json
{
  "service_id": "CommandService",
  "command_name": "COMMAND_SN_DELIVER",
  "properties": {
    "sn": "TEST_SN_20250731001",
    "production_model": "A121000185"
  }
}
```

#### 📥 **预期响应主题**
```
$oc/devices/A320021760/sys/commands/response/request_id=test_sn_001
```

#### 📋 **预期响应JSON**
```json
{
  "result_code": 0,
  "result_message": "SN下发成功",
  "data": {
    "sn": "TEST_SN_20250731001",
    "production_model": "A121000185"
  },
  "timestamp": 1753907800
}
```

---

### 2. 转产/生产参数下发测试

#### 📤 **发布主题**
```
$oc/devices/A320021760/sys/commands/request_id=test_bop_001
```

#### 📝 **JSON消息体**
```json
{
  "service_id": "CommandService",
  "command_name": "COMMAND_BOP_DELIVER",
  "properties": {
    "production_model": "A121000185",
    "profiles": {
      "工艺参数1": "值1",
      "工艺参数2": "值2",
      "质量标准": "A级"
    }
  }
}
```

#### 📥 **预期响应主题**
```
$oc/devices/A320021760/sys/commands/response/request_id=test_bop_001
```

#### 📋 **预期响应JSON**
```json
{
  "result_code": 0,
  "result_message": "转产参数设置成功",
  "data": {
    "production_model": "A121000185",
    "parameters_count": 3
  },
  "timestamp": 1753907800
}
```

---

### 3. 暂停生产命令测试

#### 📤 **发布主题**
```
$oc/devices/A320021760/sys/commands/request_id=test_pause_001
```

#### 📝 **JSON消息体**
```json
{
  "service_id": "CommandService",
  "command_name": "COMMAND_PAUSE",
  "properties": {
    "reason": "手动测试暂停"
  }
}
```

#### 📥 **预期响应主题**
```
$oc/devices/A320021760/sys/commands/response/request_id=test_pause_001
```

#### 📋 **预期响应JSON**
```json
{
  "result_code": 0,
  "result_message": "生产已暂停",
  "data": {
    "reason": "手动测试暂停",
    "paused_at": "2025-07-31 04:30:00"
  },
  "timestamp": 1753907800
}
```

---

### 4. 恢复生产命令测试

#### 📤 **发布主题**
```
$oc/devices/A320021760/sys/commands/request_id=test_resume_001
```

#### 📝 **JSON消息体**
```json
{
  "service_id": "CommandService",
  "command_name": "COMMAND_PRODUCTION",
  "properties": {}
}
```

#### 📥 **预期响应主题**
```
$oc/devices/A320021760/sys/commands/response/request_id=test_resume_001
```

#### 📋 **预期响应JSON**
```json
{
  "result_code": 0,
  "result_message": "生产已恢复",
  "data": {
    "resumed_at": "2025-07-31 04:35:00"
  },
  "timestamp": 1753907800
}
```

---

### 5. MQTT配置下发测试

#### 📤 **发布主题**
```
$oc/devices/A320021760/sys/commands/request_id=test_mqtt_config_001
```

#### 📝 **JSON消息体**
```json
{
  "service_id": "CommandService",
  "command_name": "COMMAND_MQTT_CONFIG_DELIVER",
  "properties": {
    "report_interval": 180000,
    "enable_mqtt": true
  }
}
```

#### 📥 **预期响应主题**
```
$oc/devices/A320021760/sys/commands/response/request_id=test_mqtt_config_001
```

#### 📋 **预期响应JSON**
```json
{
  "result_code": 0,
  "result_message": "MQTT配置更新成功",
  "data": {
    "report_interval": 180000,
    "enable_mqtt": true
  },
  "timestamp": 1753907800
}
```

---

## 错误响应示例

### 命令验证失败
```json
{
  "result_code": -4,
  "result_message": "命令验证失败: 不支持的命令类型: INVALID_COMMAND",
  "timestamp": 1753907800
}
```

### 参数缺失
```json
{
  "result_code": -3,
  "result_message": "参数验证失败: 缺少必需参数: sn",
  "timestamp": 1753907800
}
```

### 系统错误
```json
{
  "result_code": -1,
  "result_message": "系统内部错误: 处理命令时发生异常",
  "timestamp": 1753907800
}
```

---

## 手动测试步骤

### 使用MQTTX客户端测试

1. **连接MQTT Broker**
   ```
   Host: *************
   Port: 1883
   Client ID: manual_test_client
   ```

2. **订阅响应主题**
   ```
   $oc/devices/A320021760/sys/commands/response/+
   ```

3. **发送测试命令**
   - 复制上述JSON消息体
   - 发布到对应的命令主题
   - QoS设置为2

4. **观察响应**
   - 检查响应主题是否正确
   - 验证JSON格式和内容
   - 确认result_code和result_message

### 使用Mosquitto命令行测试

#### 订阅响应
```bash
mosquitto_sub -h ************* -p 1883 -t '$oc/devices/A320021760/sys/commands/response/+' -v
```

#### 发送SN下发命令
```bash
mosquitto_pub -h ************* -p 1883 -q 2 \
  -t '$oc/devices/A320021760/sys/commands/request_id=manual_test_001' \
  -m '{"service_id":"CommandService","command_name":"COMMAND_SN_DELIVER","properties":{"sn":"MANUAL_TEST_SN_001","production_model":"A121000185"}}'
```

#### 发送暂停命令
```bash
mosquitto_pub -h ************* -p 1883 -q 2 \
  -t '$oc/devices/A320021760/sys/commands/request_id=manual_test_002' \
  -m '{"service_id":"CommandService","command_name":"COMMAND_PAUSE","properties":{"reason":"手动测试暂停"}}'
```

---

## 测试验证清单

### ✅ 基础连通性测试
- [ ] MQTT网关启动成功
- [ ] FStationMFC启动成功
- [ ] Socket连接建立成功
- [ ] MQTT客户端连接成功

### ✅ 命令处理测试
- [ ] SN下发命令响应正确
- [ ] 转产命令响应正确
- [ ] 暂停命令响应正确
- [ ] 恢复命令响应正确
- [ ] MQTT配置命令响应正确

### ✅ 错误处理测试
- [ ] 无效命令类型返回错误
- [ ] 缺少参数返回错误
- [ ] JSON格式错误返回错误
- [ ] 超时处理正确

### ✅ 系统集成测试
- [ ] FStationMFC系统变量更新正确
- [ ] 生产逻辑控制正确
- [ ] 日志记录完整
- [ ] 状态同步正确

---

## 故障排除

### 无响应问题
1. 检查MQTT网关是否运行
2. 检查FStationMFC是否运行
3. 检查Socket连接状态
4. 检查MQTT主题格式

### 响应错误问题
1. 检查JSON格式是否正确
2. 检查必需参数是否完整
3. 检查命令类型是否支持
4. 查看FStationMFC日志

### 系统状态问题
1. 检查FStationMFC系统变量
2. 检查生产逻辑状态
3. 检查设备连接状态
4. 查看错误日志

---

## 注意事项

⚠️ **重要提醒**
- 测试前确保所有组件正常运行
- 使用唯一的request_id避免冲突
- 生产环境测试需谨慎操作
- 及时清理测试数据和状态

🔧 **调试建议**
- 开启详细日志记录
- 使用网络抓包工具分析
- 监控系统资源使用
- 记录测试结果和异常

---

## 测试结果记录模板

### 测试信息
- **测试日期**: ___________
- **测试人员**: ___________
- **软件版本**: ___________
- **测试环境**: ___________

### 测试结果
| 测试项目 | 预期结果 | 实际结果 | 状态 | 备注 |
|---------|---------|---------|------|------|
| SN下发命令 | 成功响应 |  | ⭕ |  |
| 转产命令 | 成功响应 |  | ⭕ |  |
| 暂停命令 | 成功响应 |  | ⭕ |  |
| 恢复命令 | 成功响应 |  | ⭕ |  |
| MQTT配置 | 成功响应 |  | ⭕ |  |

### 问题记录
1. **问题描述**: ___________
   **解决方案**: ___________
   **状态**: ___________

2. **问题描述**: ___________
   **解决方案**: ___________
   **状态**: ___________

### 总结
- **测试通过率**: ____%
- **主要问题**: ___________
- **改进建议**: ___________
