﻿#pragma once

//包含Excel接口头文件
#include "CApplication.h"
#include "CWorkbook.h"
#include "CWorkbooks.h"
#include "CWorksheet.h"
#include "CWorksheets.h"
#include "CRange.h"
#include "CFont0.h"

typedef struct _ITEM_INFO_
{
	int		nRow;
	int		nCol;
	CString sValue;
} ITEMINFO, *PITEMINFO;

class CExcelEx
{
public:
	CExcelEx(void);
	~CExcelEx();

public:
	bool CreateInstance();
	bool IsVersionOk();
	void SetVisible(bool bVisible);
	bool OpenExcel(CString sExcelName, bool bFlagCreate);
	bool OpenSheet(CString sSheetName, bool bFlagCreate);
	void SaveExcel(CString sExcelName, bool bSaveAs);
	int	 GetRowCount();
	int  GetColumnCount();
	void ResetContent();
	bool GetByTitle(CString sTitle, vector<ITEMINFO> &vItem);
	bool GetValue(int nRowIndex, int nColumnIndex, CString &str);
	bool GetValue(int nRowIndex, int nColumnIndex, int &nValue);
	bool GetValue(int nRowIndex, int nColumnIndex, double &dbValue);
	void SetValue(int nRowIndex, int nColumnIndex, CString str);
	void SetValue(int nRowIndex, int nColumnIndex, int nVal);
	void SetValue(int nRowIndex, int nColumnIndex, double dbVal);
	void SetColumnStyle(int nIndex, int nType);//居中显示
	bool ReadExcel(CString sExcel, CString sSheet, bool bAutoCreate);

protected:
	CString			m_sExcelName;
	int				m_nRowCount;
	int				m_nColumnCount;
	CApplication*	m_pApplication;
	CWorkbook*		m_pWorkBook;
	CWorkbooks*		m_pWorkBooks;
	CWorksheet*		m_pWorkSheet;
	CWorksheets*	m_pWorkSheets;
};
