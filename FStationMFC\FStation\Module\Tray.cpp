﻿#include "stdafx.h"
#include "Tray.h"

#include "Sys.h"

CTray::CTray() : CModule(2)
{
	MakePair("空TRAY盘皮带正转方向", new CData(false, true, 4)); // 往前为正方向
	MakePair("满TRAY盘皮带正转方向", new CData(false, true, 4)); // 往前为正方向

	MakePair("空TRAY盘提前进料标志", new CData(false, false));
	MakePair("空TRAY盘进料标志", new CData(false, false));
	MakePair("空TRAY盘进料完成标志", new CData(false, false));
	MakePair("空TRAY盘退料标志", new CData(false, false));

	MakePair("满TRAY盘进料标志", new CData(false, false));
	MakePair("满TRAY盘进料完成标志", new CData(false, false));
	MakePair("满TRAY盘退料标志", new CData(false, false));

	MakePair("单TRAY盘高度", new CData(30.0, 0.0,  50.0));
	MakePair("满TRAY盘回放安全距离", new CData(350.0, 200.0,  500.0));

	MakePair("空TRAY盘搬运Y轴进料起始位", new CData(0.0, -9999.999, 9999.999, true, 4));
	MakePair("空TRAY盘搬运Y轴进料结束位", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("空TRAY盘搬运Y轴退料起始位", new CData(0.0, -9999.999, 9999.999, true, 4));
	MakePair("空TRAY盘搬运Y轴退料结束位", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("空TRAY盘分盘Z轴支撑位", new CData(0.0, -9999.999, 9999.999, true, 4));
	MakePair("空TRAY盘分盘Z轴分盘位", new CData(0.0, -9999.999, 9999.999, true, 4));
	MakePair("空TRAY盘分盘Z轴破真空位", new CData(0.0, -9999.999, 9999.999, true, 4));
	MakePair("空TRAY盘分盘Z轴放料位", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("满TRAY盘升降Z轴送料偏移量", new CData(0.0));
	MakePair("满TRAY盘升降Z轴TRAY盘检测起始位", new CData(500.0, -9999.999, 9999.999, true, 4));

	MakePair("满TRAY盘升降Z轴分盘偏移量", new CData(0.0));

	MakePair("满TRAY盘升降Z轴最大待料位", new CData(0.0, -9999.999, 9999.999, true, 4));
	MakePair("满TRAY盘升降Z轴接料偏移量", new CData(0.0));

	MakePair("满TRAY盘升降Z轴放料位", new CData(0.0, -9999.999, 9999.999, true, 4));

	MakePair("空TRAY盘真空检测超时", new CData(3000));
	MakePair("空TRAY盘退料超时", new CData(10000));	

	MakePair("空TRAY盘进料到位延时", new CData(3000));

	MakePair("皮带传输到位延时", new CData(1000));

	Load();
}

CTray::~CTray()
{
}

CString CTray::FullTrayEndStopCylinderOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("满TRAY盘尾端阻挡气缸", true));

	return "OK";
}

CString CTray::FullTrayEndStopCylinderOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("满TRAY盘尾端阻挡气缸", false));

	return "OK";
}

CString CTray::FullTrayEndStopCylinderStatus()
{
	bool bStatus1 = false, bStatus2 = false;

	EXCUTE_RETURN(g_pControl->ReadInput("满TRAY盘尾端阻挡气缸缩回信号", bStatus1));

	EXCUTE_RETURN(g_pControl->ReadInput("满TRAY盘尾端阻挡气缸伸出信号", bStatus2));

	if (bStatus1 && !bStatus2) {
		return "Off";
	}
	else if (!bStatus1 && bStatus2) {
		return "On";
	}
	else {
		return "UnKnow";
	}
}

CString CTray::FullTraySupportCylinderOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("满TRAY盘支撑气缸缩回", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("满TRAY盘支撑气缸伸出", true));

	return "OK";
}

CString CTray::FullTraySupportCylinderOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("满TRAY盘支撑气缸伸出", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("满TRAY盘支撑气缸缩回", true));

	return "OK";
}

CString CTray::FullTraySupportCylinderStatus()
{
	bool bStatus1 = false, bStatus2 = false, bStatus3 = false, bStatus4 = false;

	EXCUTE_RETURN(g_pControl->ReadInput("满TRAY盘支撑气缸1缩回信号", bStatus1));

	EXCUTE_RETURN(g_pControl->ReadInput("满TRAY盘支撑气缸1伸出信号", bStatus2));

	EXCUTE_RETURN(g_pControl->ReadInput("满TRAY盘支撑气缸2缩回信号", bStatus3));

	EXCUTE_RETURN(g_pControl->ReadInput("满TRAY盘支撑气缸2伸出信号", bStatus4));

	if (bStatus1 && !bStatus2 && bStatus3 && !bStatus4) {
		return "Off";
	}
	else if (!bStatus1 && bStatus2 && !bStatus3 && bStatus4) {
		return "On";
	}
	else {
		return "UnKnow";
	}
}

CString CTray::FullTraySeparateCylinderOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("满TRAY盘分盘气缸", true));

	return "OK";
}

CString CTray::FullTraySeparateCylinderOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("满TRAY盘分盘气缸", false));

	return "OK";
}

CString CTray::FullTraySeparateCylinderStatus()
{
	bool bStatus1 = false, bStatus2 = false, bStatus3 = false, bStatus4 = false;

	EXCUTE_RETURN(g_pControl->ReadInput("满TRAY盘分盘气缸1缩回信号", bStatus1));

	EXCUTE_RETURN(g_pControl->ReadInput("满TRAY盘分盘气缸1伸出信号", bStatus2));

	EXCUTE_RETURN(g_pControl->ReadInput("满TRAY盘分盘气缸2缩回信号", bStatus3));

	EXCUTE_RETURN(g_pControl->ReadInput("满TRAY盘分盘气缸2伸出信号", bStatus4));

	if (bStatus1 && !bStatus2 && bStatus3 && !bStatus4) {
		return "Off";
	}
	else if (!bStatus1 && bStatus2 && !bStatus3 && bStatus4) {
		return "On";
	}
	else {
		return "UnKnow";
	}
}

CString CTray::FullTrayBeltOn(bool bDir)
{
	EXCUTE_RETURN(g_pControl->WriteOutput("满TRAY盘皮带方向", bDir));

	EXCUTE_RETURN(g_pControl->WriteOutput("满TRAY盘皮带启动", true));

	return "OK";
}

CString CTray::FullTrayBeltOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("满TRAY盘皮带启动", false));

	return "OK";
}

CString CTray::FullTrayBeltInMaterialStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("满TRAY盘皮带进料信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CTray::FullTrayBeltWaitMaterialStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("满TRAY盘皮带待料信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CTray::FullTrayBeltInPosStatus()
{
	bool bStatus = false;
	
	EXCUTE_RETURN(g_pControl->ReadInput("满TRAY盘皮带到位信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CTray::FullTrayUpInPosStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("满TRAY盘上升到位信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CTray::FullTrayMaterialExistStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("满TRAY盘有无检测信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CTray::EmptyTrayPullMaterialCylinderOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("空TRAY盘拨料气缸", true));

	return "OK";
}

CString CTray::EmptyTrayPullMaterialCylinderOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("空TRAY盘拨料气缸", false));

	return "OK";
}

CString CTray::EmptyTrayPullMaterialCylinderStatus()
{
	bool bStatus1 = false, bStatus2 = false;

	EXCUTE_RETURN(g_pControl->ReadInput("空TRAY盘拨料气缸缩回信号", bStatus1));

	EXCUTE_RETURN(g_pControl->ReadInput("空TRAY盘拨料气缸伸出信号", bStatus2));

	if (bStatus1 && !bStatus2) {
		return "Off";
	}
	else if (!bStatus1 && bStatus2) {
		return "On";
	}
	else {
		return "UnKnow";
	}
}

CString CTray::EmptyTrayVacuumOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("空TRAY盘分盘破真空", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("空TRAY盘分盘真空", true));

	return "OK";
}

CString CTray::EmptyTrayBrokenVacuumOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("空TRAY盘分盘真空", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("空TRAY盘分盘破真空", true));

	return "OK";
}

CString CTray::EmptyTrayVacuumOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("空TRAY盘分盘真空", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("空TRAY盘分盘破真空", false));

	return "OK";
}

CString CTray::EmptyTrayVacuumStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("空TRAY盘分盘真空信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CTray::EmptyTrayEndStopCylinderOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("空TRAY盘尾端阻挡气缸", true));

	return "OK";
}

CString CTray::EmptyTrayEndStopCylinderOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("空TRAY盘尾端阻挡气缸", false));

	return "OK";
}

CString CTray::EmptyTrayEndStopCylinderStatus()
{
	bool bStatus1 = false, bStatus2 = false;

	EXCUTE_RETURN(g_pControl->ReadInput("空TRAY盘尾端阻挡气缸缩回信号", bStatus1));

	EXCUTE_RETURN(g_pControl->ReadInput("空TRAY盘尾端阻挡气缸伸出信号", bStatus2));

	if (bStatus1 && !bStatus2) {
		return "Off";
	}
	else if (!bStatus1 && bStatus2) {
		return "On";
	}
	else {
		return "UnKnow";
	}
}

CString CTray::EmptyTraySupportCylinderOn()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("空TRAY盘支撑气缸缩回", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("空TRAY盘支撑气缸伸出", true));

	return "OK";
}

CString CTray::EmptyTraySupportCylinderOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("空TRAY盘支撑气缸伸出", false));

	EXCUTE_RETURN(g_pControl->WriteOutput("空TRAY盘支撑气缸缩回", true));

	return "OK";
}

CString CTray::EmptyTraySupportCylinderStatus()
{
	bool bStatus1 = false, bStatus2 = false, bStatus3 = false, bStatus4 = false;

	EXCUTE_RETURN(g_pControl->ReadInput("空TRAY盘支撑气缸1缩回信号", bStatus1));

	EXCUTE_RETURN(g_pControl->ReadInput("空TRAY盘支撑气缸1伸出信号", bStatus2));

	EXCUTE_RETURN(g_pControl->ReadInput("空TRAY盘支撑气缸2缩回信号", bStatus3));

	EXCUTE_RETURN(g_pControl->ReadInput("空TRAY盘支撑气缸2伸出信号", bStatus4));

	if (bStatus1 && !bStatus2 && bStatus3 && !bStatus4) {
		return "Off";
	}
	else if (!bStatus1 && bStatus2 && !bStatus3 && bStatus4) {
		return "On";
	}
	else {
		return "UnKnow";
	}
}

CString CTray::EmptyTrayBeltOn(bool bDir)
{
	EXCUTE_RETURN(g_pControl->WriteOutput("空TRAY盘皮带方向", bDir));

	EXCUTE_RETURN(g_pControl->WriteOutput("空TRAY盘皮带启动", true));

	return "OK";
}

CString CTray::EmptyTrayBeltOff()
{
	EXCUTE_RETURN(g_pControl->WriteOutput("空TRAY盘皮带启动", false));

	return "OK";
}

CString CTray::EmptyTrayBeltInMaterialStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("空TRAY盘进料信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CTray::EmptyTrayBeltInPosStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("空TRAY盘到位信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CTray::EmptyTrayMaterialExistStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("空TRAY盘有无检测信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CTray::EmptyTrayOutOfRangePreWarnStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("空TRAY盘数量预警信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CTray::EmptyTrayOutOfRangeWarnStatus()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->ReadInput("空TRAY盘数量上限报警信号", bStatus));

	return bStatus ? "On" : "Off";
}

CString CTray::FullTrayUpDnZHome()
{
	EXCUTE_RETURN(g_pControl->Home("满TRAY盘升降Z轴"));

	return "OK";
}

CString CTray::IsFullTrayUpDnZHomeOK()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->IsHomeOk("满TRAY盘升降Z轴", bStatus));

	return bStatus ? "Yes" : "No";
}

CString CTray::EmptyTrayTransportYHome()
{
	EXCUTE_RETURN(g_pControl->Home("空TRAY盘搬运Y轴"));

	return "OK";
}

CString CTray::IsEmptyTrayTransportYHomeOK()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->IsHomeOk("空TRAY盘搬运Y轴", bStatus));

	return bStatus ? "Yes" : "No";
}

CString CTray::EmptyTraySeparateZHome()
{
	EXCUTE_RETURN(g_pControl->Home("空TRAY盘分盘Z轴"));

	return "OK";
}

CString CTray::IsEmptyTraySeparateZHomeOK()
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->IsHomeOk("空TRAY盘分盘Z轴", bStatus));

	return bStatus ? "Yes" : "No";
}

CString CTray::FullTrayUpDnZMove(double nPos, int nSpeedRate/* = 100*/)
{
	if (!CSys::m_bInit) {
		return "未复位";
	}

	if (!OnSafeCheck("满TRAY盘升降Z轴", NULL)) {
		return "OK";
	}

	EXCUTE_RETURN(g_pControl->MoveTo("满TRAY盘升降Z轴", nPos, nSpeedRate));

	return "OK";
}

CString CTray::FullTrayUpDnZCurrentPos(double &nCurPos)
{
	if (!CSys::m_bInit) {
		return "未复位";
	}

	EXCUTE_RETURN(g_pControl->GetMtPos("满TRAY盘升降Z轴", nCurPos));

	return "OK";
}

CString CTray::IsFullTrayUpDnZInPos(double nPos, double nTolerance/* = 0.005*/)
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->IsInPos("满TRAY盘升降Z轴", nPos, nTolerance, bStatus));

	return bStatus ? "Yes" : "No";
}

CString CTray::FullTrayUpDnZStop()
{
	EXCUTE_RETURN(g_pControl->Stop("满TRAY盘升降Z轴"));

	return "OK";
}

CString CTray::EmptyTrayTransportYMove(double nPos, int nSpeedRate/* = 100*/)
{
	if (!CSys::m_bInit) {
		return "未复位";
	}

	if (IsEmptyTrayTransportYHomeOK() != "Yes") {
		return "OK";
	}

	EXCUTE_RETURN(g_pControl->MoveTo("空TRAY盘搬运Y轴", nPos, nSpeedRate));

	return "OK";
}

CString CTray::IsEmptyTrayTransportYInPos(double nPos, double nTolerance/* = 0.05*/)
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->IsInPos("空TRAY盘搬运Y轴", nPos, nTolerance, bStatus));

	return bStatus ? "Yes" : "No";
}


CString CTray::EmptyTrayTransportYStop()
{
	EXCUTE_RETURN(g_pControl->Stop("空TRAY盘搬运Y轴"));

	return "OK";
}

CString CTray::EmptyTraySeparateZMove(double nPos, int nSpeedRate/* = 100*/)
{
	if (!CSys::m_bInit) {
		return "未复位";
	}

	if (IsEmptyTraySeparateZHomeOK() != "Yes") {
		return "OK";
	}

	g_pControl->MoveTo("空TRAY盘分盘Z轴", nPos, nSpeedRate);

	return "OK";
}

CString CTray::IsEmptyTraySeparateZInPos(double nPos, double nTolerance/* = 0.05*/)
{
	bool bStatus = false;

	EXCUTE_RETURN(g_pControl->IsInPos("空TRAY盘分盘Z轴", nPos, nTolerance, bStatus));

	return bStatus ? "Yes" : "No";
}

CString CTray::EmptyTraySeparateZStop()
{
	EXCUTE_RETURN(g_pControl->Stop("空TRAY盘分盘Z轴"));

	return "OK";
}
