﻿#include "stdafx.h"
#include "LogicFixtureTransport.h"

#include "Sys.h"
#include "Mes.h"
#include "LogicMgr.h"

#define PARAM(NAME)			(*m_pFixture->m_mapParam[NAME])

#define PARAM_BOOL(NAME)	(*m_pFixture->m_mapParam[NAME]).B()
#define PARAM_INT(NAME)		(*m_pFixture->m_mapParam[NAME]).I()
#define PARAM_DOUBLE(NAME)	(*m_pFixture->m_mapParam[NAME]).D()
#define PARAM_STRING(NAME)	(*m_pFixture->m_mapParam[NAME]).S()

CLogicFixtureTransport::CLogicFixtureTransport()
{
	m_pFixture = g_pFixture;
}

CLogicFixtureTransport::~CLogicFixtureTransport()
{
}

EnumStatus CLogicFixtureTransport::OnSafeCheck()
{
	if (!CSys::m_bInit) {
		return emStop;
	}

	return emRun;
}

EnumStatus CLogicFixtureTransport::OnStart()
{
	return emRun;
}

EnumStatus CLogicFixtureTransport::OnPause()
{
	m_pFixture->TransportYStop();

	m_pFixture->TransportZStop();

	return emRun;
}

EnumStatus CLogicFixtureTransport::OnResume()
{
	map<CString, DWORD>::iterator it = m_mapTick.begin();
	for (; it!=m_mapTick.end(); it++)
	{
		it->second = GetTickCount();
	}

	return emRun;
}

EnumStatus CLogicFixtureTransport::OnStop()
{
	m_pFixture->TransportYStop();

	m_pFixture->TransportZStop();
	
	return emRun;
}

CStatus CLogicFixtureTransport::OnRun()
{
	PARAM("搬运取上盖完成标志") = true;
	
	PARAM("搬运取下盖完成标志") = true;

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport00, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport00()
{
	RUN_STOP_IF_ERROR(m_pFixture->DnLidInPosStatus());

	if (g_pBeltB->m_mapParam["允许治具下盖上料标志"]->B() && PARAM_BOOL("允许搬运取下盖标志") && m_sRet == "On") {
		*g_pBeltB->m_mapParam["允许治具下盖上料标志"] = false;
		PARAM("允许搬运取下盖标志") = false;
		PARAM("搬运取下盖完成标志") = false;
		m_mapPos["治具搬运Y轴下盖放料位"] = PARAM_DOUBLE("治具搬运Y轴B轨放下盖位置");
		RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport01, true);
	}

	if (VAR_BELTA_B("允许治具下盖上料标志") && PARAM_BOOL("允许搬运取下盖标志") && m_sRet == "On") {
		*g_pBeltA->m_mapParam["允许治具下盖上料标志"] = false;
		PARAM("允许搬运取下盖标志") = false;
		PARAM("搬运取下盖完成标志") = false;
		m_mapPos["治具搬运Y轴下盖放料位"] = PARAM_DOUBLE("治具搬运Y轴A轨放下盖位置");
		RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport01, true);
	}

	CString sRet1, sRet2;

	RUN_STOP_IF_ERROR(m_pFixture->UpLidUpLayerExistStatus());

	sRet1 = m_sRet;

	RUN_STOP_IF_ERROR(m_pFixture->UpLidDnLayerExistStatus());

	sRet2 = m_sRet;

	if (g_pBeltB->m_mapParam["治具提前取上盖标志"]->B() && PARAM_BOOL("允许搬运取上盖标志") && (sRet1 == "On" || sRet2 == "On")) {
		*g_pBeltB->m_mapParam["治具提前取上盖标志"] = false;
		PARAM("搬运取上盖完成标志") = false;
		m_mapFlag["当前合盖轨道为A轨"] = false;
		m_mapPos["治具搬运Y轴上盖放料位"] = PARAM_DOUBLE("治具搬运Y轴B轨放上盖位置");
		RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02, true);
	}

	if (g_pBeltA->m_mapParam["治具提前取上盖标志"]->B() && PARAM_BOOL("允许搬运取上盖标志") && (sRet1 == "On" || sRet2 == "On")) {
		*g_pBeltA->m_mapParam["治具提前取上盖标志"] = false;
		PARAM("搬运取上盖完成标志") = false;
		m_mapFlag["当前合盖轨道为A轨"] = true;
		m_mapPos["治具搬运Y轴上盖放料位"] = PARAM_DOUBLE("治具搬运Y轴A轨放上盖位置");
		RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02, true);
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport00, false);
}

CStatus CLogicFixtureTransport::OnFixtureTransport01()
{
	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport01_0, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport01_0()
{
	m_mapPos["治具搬运Z轴当前目标位置"] = PARAM_DOUBLE("治具搬运Z轴安全位置");

	RUN_STOP_IF_ERROR(m_pFixture->TransportZMove(m_mapPos["治具搬运Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(m_pFixture->IsTransportZInPos(m_mapPos["治具搬运Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴到位", true);
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport01_1, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport01_1()
{
	RUN_STOP_IF_ERROR(m_pFixture->RollCylinderOff());

	m_mapPos["治具搬运Y轴当前目标位置"] = PARAM_DOUBLE("治具搬运Y轴取下盖位置");

	RUN_STOP_IF_ERROR(m_pFixture->TransportYMove(m_mapPos["治具搬运Y轴当前目标位置"]));

	RUN_STOP_IF_ERROR(m_pFixture->IsTransportYInPos(m_mapPos["治具搬运Y轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Y轴到位", true);
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport01_2, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport01_2()
{
	RUN_STOP_IF_ERROR(m_pFixture->RollCylinderOff());

	RUN_STOP_IF_ERROR(m_pFixture->RollCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待旋转气缸缩回", true);
	}

	Sleep(300);

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport01_3, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport01_3()
{
	RUN_STOP_IF_ERROR(m_pFixture->VacuumOn());

	m_mapPos["治具搬运Z轴当前目标位置"] = PARAM_DOUBLE("治具搬运Z轴取下盖位置");

	RUN_STOP_IF_ERROR(m_pFixture->TransportZMove(m_mapPos["治具搬运Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(m_pFixture->IsTransportZInPos(m_mapPos["治具搬运Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴到位", true);
	}

	m_mapTick["治具吸真空计时"] = GetTickCount();

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport01_4, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport01_4()
{
	RUN_STOP_IF_ERROR(m_pFixture->VacuumOn());

	RUN_STOP_IF_ERROR(m_pFixture->VacuumStatus());

	if (m_sRet != "On") {
		if (GetTickCount() - m_mapTick["治具吸真空计时"] < (DWORD)PARAM_INT("治具吸真空超时")) {
			RETURN_SELF("等待真空信号", false);
		}
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport01_5, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport01_5()
{
	m_mapPos["治具搬运Z轴当前目标位置"] = PARAM_DOUBLE("治具搬运Z轴安全位置");

	RUN_STOP_IF_ERROR(m_pFixture->TransportZMove(m_mapPos["治具搬运Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(m_pFixture->IsTransportZInPos(m_mapPos["治具搬运Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴到位", true);
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport01_6, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport01_6()
{
	RUN_STOP_IF_ERROR(m_pFixture->VacuumStatus());

	if (m_sRet != "On") {
		REPORT("治具吸真空异常!", emLogLevelError);
		MESSAGEBOX("治具吸真空异常!", "", false);
		RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport01_3, true);
	}

	PARAM("搬运取下盖完成标志") = true;

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport01_7, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport01_7()
{
	RUN_STOP_IF_ERROR(m_pFixture->RollCylinderOn());

	m_mapPos["治具搬运Y轴当前目标位置"] = m_mapPos["治具搬运Y轴下盖放料位"];

	RUN_STOP_IF_ERROR(m_pFixture->TransportYMove(m_mapPos["治具搬运Y轴当前目标位置"]));

	RUN_STOP_IF_ERROR(m_pFixture->IsTransportYInPos(m_mapPos["治具搬运Y轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Y轴到位", true);
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport01_8, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport01_8()
{
	RUN_STOP_IF_ERROR(m_pFixture->RollCylinderOn());

	RUN_STOP_IF_ERROR(m_pFixture->RollCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待旋转气缸伸出", true);
	}

	Sleep(300);

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport01_9, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport01_9()
{
	m_mapPos["治具搬运Z轴当前目标位置"] = PARAM_DOUBLE("治具搬运Z轴放下盖位置");

	RUN_STOP_IF_ERROR(m_pFixture->TransportZMove(m_mapPos["治具搬运Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(m_pFixture->IsTransportZInPos(m_mapPos["治具搬运Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴到位", true);
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport01_10, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport01_10()
{
	RUN_STOP_IF_ERROR(m_pFixture->BrokenVacuumOn());

	Sleep(200);

	RUN_STOP_IF_ERROR(m_pFixture->VacuumOff());

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport01_11, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport01_11()
{
	m_mapPos["治具搬运Z轴当前目标位置"] = PARAM_DOUBLE("治具搬运Z轴安全位置");

	RUN_STOP_IF_ERROR(m_pFixture->TransportZMove(m_mapPos["治具搬运Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(m_pFixture->IsTransportZInPos(m_mapPos["治具搬运Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴到位", true);
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport01_12, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport01_12()
{
	if (fabs(m_mapPos["治具搬运Y轴下盖放料位"] - PARAM_DOUBLE("治具搬运Y轴A轨放下盖位置")) < 0.1) {
		*g_pBeltA->m_mapParam["治具下盖上料完成标志"] = true;
	}
	else {
		*g_pBeltB->m_mapParam["治具下盖上料完成标志"] = true;
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport00, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport02()
{
	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_0, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport02_0()
{
	m_mapPos["治具搬运Z轴当前目标位置"] = PARAM_DOUBLE("治具搬运Z轴安全位置");

	RUN_STOP_IF_ERROR(m_pFixture->TransportZMove(m_mapPos["治具搬运Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(m_pFixture->IsTransportZInPos(m_mapPos["治具搬运Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴到位", true);
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_1, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport02_1()
{
	RUN_STOP_IF_ERROR(m_pFixture->RollCylinderOff());

	m_mapPos["治具搬运Y轴当前目标位置"] = PARAM_DOUBLE("治具搬运Y轴取上盖位置");

	RUN_STOP_IF_ERROR(m_pFixture->TransportYMove(m_mapPos["治具搬运Y轴当前目标位置"]));

	RUN_STOP_IF_ERROR(m_pFixture->IsTransportYInPos(m_mapPos["治具搬运Y轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Y轴到位", true);
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_2, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport02_2()
{
	RUN_STOP_IF_ERROR(m_pFixture->RollCylinderOff());

	RUN_STOP_IF_ERROR(m_pFixture->RollCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待旋转气缸缩回", true);
	}

	Sleep(300);

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_3, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport02_3()
{
	RUN_STOP_IF_ERROR(m_pFixture->VacuumOn());

	m_mapPos["治具搬运Z轴当前目标位置"] = PARAM_DOUBLE("治具搬运Z轴取上盖位置");

	RUN_STOP_IF_ERROR(m_pFixture->TransportZMove(m_mapPos["治具搬运Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(m_pFixture->IsTransportZInPos(m_mapPos["治具搬运Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴到位", true);
	}

	m_mapTick["治具吸真空计时"] = GetTickCount();

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_4, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport02_4()
{
	RUN_STOP_IF_ERROR(m_pFixture->VacuumOn());

	RUN_STOP_IF_ERROR(m_pFixture->VacuumStatus());

	if (m_sRet != "On") {
		if (GetTickCount() - m_mapTick["治具吸真空计时"] < (DWORD)PARAM_INT("治具吸真空超时")) {
			RETURN_SELF("等待真空信号", false);
		}
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_5, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport02_5()
{
	m_mapPos["治具搬运Z轴当前目标位置"] = PARAM_DOUBLE("治具搬运Z轴安全位置");

	RUN_STOP_IF_ERROR(m_pFixture->TransportZMove(m_mapPos["治具搬运Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(m_pFixture->IsTransportZInPos(m_mapPos["治具搬运Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴到位", true);
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_6, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport02_6()
{
	RUN_STOP_IF_ERROR(m_pFixture->VacuumStatus());

	if (m_sRet != "On") {
		REPORT("治具吸真空异常!", emLogLevelError);
		MESSAGEBOX("治具吸真空异常!", "", false);
		RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_3, true);
	}

	PARAM("搬运取上盖完成标志") = true;

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_7, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport02_7()
{
	RUN_STOP_IF_ERROR(m_pFixture->RollCylinderOn());

	m_mapPos["治具搬运Y轴当前目标位置"] = m_mapPos["治具搬运Y轴上盖放料位"];

	RUN_STOP_IF_ERROR(m_pFixture->TransportYMove(m_mapPos["治具搬运Y轴当前目标位置"]));

	RUN_STOP_IF_ERROR(m_pFixture->IsTransportYInPos(m_mapPos["治具搬运Y轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Y轴到位", true);
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_8, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport02_8()
{
	RUN_STOP_IF_ERROR(m_pFixture->RollCylinderOn());

	RUN_STOP_IF_ERROR(m_pFixture->RollCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待旋转气缸伸出", true);
	}

	Sleep(300);

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_8_0, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport02_8_0()
{
	if (m_mapFlag["当前合盖轨道为A轨"]) {
		if (!VAR_BELTA_B("允许治具合盖标志")) {
			RETURN_SELF("等待治具下盖到位", true);
		}

		VAR_BELTA("允许治具合盖标志") = false;
	}
	else {
		if (!VAR_BELTB_B("允许治具合盖标志")) {
			RETURN_SELF("等待治具下盖到位", true);
		}

		VAR_BELTB("允许治具合盖标志") = false;
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_9, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport02_9()
{
	m_mapPos["治具搬运Z轴当前目标位置"] = PARAM_DOUBLE("治具搬运Z轴放上盖位置");

	RUN_STOP_IF_ERROR(m_pFixture->TransportZMove(m_mapPos["治具搬运Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(m_pFixture->IsTransportZInPos(m_mapPos["治具搬运Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴到位", true);
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_10, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport02_10()
{
	RUN_STOP_IF_ERROR(m_pFixture->BrokenVacuumOn());

	Sleep(200);

	RUN_STOP_IF_ERROR(m_pFixture->VacuumOff());

	if (m_mapFlag["当前合盖轨道为A轨"]) {
		RUN_STOP_IF_ERROR(g_pBeltA->FixtureAssembleSetPosCylinderOn());
		Sleep(100);
		RUN_STOP_IF_ERROR(g_pBeltA->FixtureAssembleSetPosCylinderOff());
	}
	else {
		RUN_STOP_IF_ERROR(g_pBeltB->FixtureAssembleSetPosCylinderOn());
		Sleep(100);
		RUN_STOP_IF_ERROR(g_pBeltB->FixtureAssembleSetPosCylinderOff());
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_10_0, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport02_10_0()
{
	m_mapPos["治具搬运Z轴当前目标位置"] = PARAM_DOUBLE("治具搬运Z轴测高位置");

	RUN_STOP_IF_ERROR(m_pFixture->TransportZMove(m_mapPos["治具搬运Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(m_pFixture->IsTransportZInPos(m_mapPos["治具搬运Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴到位", true);
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_10_1, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport02_10_1()
{
	if (m_mapFlag["当前合盖轨道为A轨"]) {
		m_mapPos["治具搬运Y轴当前目标位置"] = VAR_FIXTURE_D("治具搬运Y轴A轨测高位置1");
	}
	else {
		m_mapPos["治具搬运Y轴当前目标位置"] = VAR_FIXTURE_D("治具搬运Y轴B轨测高位置1");
	}

	RUN_STOP_IF_ERROR(m_pFixture->TransportYMove(m_mapPos["治具搬运Y轴当前目标位置"], false));

	RUN_STOP_IF_ERROR(m_pFixture->IsTransportYInPos(m_mapPos["治具搬运Y轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Y轴到位", true);
	}

	m_mapTick["延迟测高计时"] = GetTickCount();

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_10_2, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport02_10_2()
{
	if (GetTickCount() - m_mapTick["延迟测高计时"] < VAR_FIXTURE_I("延迟测高时间")) {
		RETURN_SELF("", false);
	}

	m_mapPos["左测高位1"] = 0;

	for (int i=0; i<3; i++)
	{
		m_sRet = m_pFixture->GetHeightL(m_mapPos["左测高位1"]);

		if (m_mapPos["左测高位1"] > 80) {
			break;
		}
	}

	if (m_sRet != "OK") {
		REPORT(m_sRet, emLogLevelError);
	}

	m_mapPos["右测高位1"] = 0;

	for (int i=0; i<3; i++)
	{
		m_sRet = m_pFixture->GetHeightR(m_mapPos["右测高位1"]);

		if (m_mapPos["右测高位1"] > 80) {
			break;
		}
	}

	if (m_sRet != "OK") {
		REPORT(m_sRet, emLogLevelError);
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_10_3, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport02_10_3()
{
	if (m_mapFlag["当前合盖轨道为A轨"]) {
		m_mapPos["治具搬运Y轴当前目标位置"] = VAR_FIXTURE_D("治具搬运Y轴A轨测高位置2");
	}
	else {
		m_mapPos["治具搬运Y轴当前目标位置"] = VAR_FIXTURE_D("治具搬运Y轴B轨测高位置2");
	}

	RUN_STOP_IF_ERROR(m_pFixture->TransportYMove(m_mapPos["治具搬运Y轴当前目标位置"], false));

	RUN_STOP_IF_ERROR(m_pFixture->IsTransportYInPos(m_mapPos["治具搬运Y轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Y轴到位", true);
	}

	m_mapTick["延迟测高计时"] = GetTickCount();

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_10_4, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport02_10_4()
{
	if (GetTickCount() - m_mapTick["延迟测高计时"] < VAR_FIXTURE_I("延迟测高时间")) {
		RETURN_SELF("", false);
	}

	m_mapPos["左测高位2"] = 0;

	for (int i=0; i<3; i++)
	{
		m_sRet = m_pFixture->GetHeightL(m_mapPos["左测高位2"]);

		if (m_mapPos["左测高位2"] > 80) {
			break;
		}
	}

	if (m_sRet != "OK") {
		REPORT(m_sRet, emLogLevelError);
	}

	m_mapPos["右测高位2"] = 0;

	for (int i=0; i<3; i++)
	{
		m_sRet = m_pFixture->GetHeightR(m_mapPos["右测高位2"]);

		if (m_mapPos["右测高位2"] > 80) {
			break;
		}
	}

	if (m_sRet != "OK") {
		REPORT(m_sRet, emLogLevelError);
	}

	CDat::UpdateValue("测高阈值", VAR_FIXTURE_D("合盖高度差正常范围"));

	CString sTemp;
	if (m_mapFlag["当前合盖轨道为A轨"]) {	
		CDat::UpdateValue("1轨测高值1", m_mapPos["左测高位1"]);
		sTemp.Format("左测高位1测高值： %.3f; 标准值: %.03f, 差值: %.03f", m_mapPos["左测高位1"], VAR_FIXTURE_D("治具搬运Y轴A轨测高位置1高度标准值1"), fabs(m_mapPos["左测高位1"] - VAR_FIXTURE_D("治具搬运Y轴A轨测高位置1高度标准值1")));	
		if (fabs(m_mapPos["左测高位1"] - VAR_FIXTURE_D("治具搬运Y轴A轨测高位置1高度标准值1")) > VAR_FIXTURE_D("合盖高度差正常范围")) {
			VAR_BELTA("治具合盖异常标志") = true;
			REPORT(sTemp, emLogLevelError);
		}
		else {
			REPORT(sTemp, emLogLevelNormal);
		}

		CDat::UpdateValue("1轨测高值2", m_mapPos["右测高位1"]);
		sTemp.Format("右测高位1测高值： %.3f; 标准值: %.03f, 差值: %.03f", m_mapPos["右测高位1"], VAR_FIXTURE_D("治具搬运Y轴A轨测高位置1高度标准值2"), fabs(m_mapPos["右测高位1"] - VAR_FIXTURE_D("治具搬运Y轴A轨测高位置1高度标准值2")));
		if (fabs(m_mapPos["右测高位1"] - VAR_FIXTURE_D("治具搬运Y轴A轨测高位置1高度标准值2")) > VAR_FIXTURE_D("合盖高度差正常范围")) {
			VAR_BELTA("治具合盖异常标志") = true;
			REPORT(sTemp, emLogLevelError);
		}
		else {
			REPORT(sTemp, emLogLevelNormal);
		}

		CDat::UpdateValue("1轨测高值3", m_mapPos["左测高位2"]);
		sTemp.Format("左测高位2测高值： %.3f; 标准值: %.03f, 差值: %.03f", m_mapPos["左测高位2"], VAR_FIXTURE_D("治具搬运Y轴A轨测高位置2高度标准值1"), fabs(m_mapPos["左测高位2"] - VAR_FIXTURE_D("治具搬运Y轴A轨测高位置2高度标准值1")));
		if (fabs(m_mapPos["左测高位2"] - VAR_FIXTURE_D("治具搬运Y轴A轨测高位置2高度标准值1")) > VAR_FIXTURE_D("合盖高度差正常范围")) {
			VAR_BELTA("治具合盖异常标志") = true;
			REPORT(sTemp, emLogLevelError);
		}
		else {
			REPORT(sTemp, emLogLevelNormal);
		}

		CDat::UpdateValue("1轨测高值4", m_mapPos["右测高位2"]);
		sTemp.Format("右测高位2测高值： %.3f; 标准值: %.03f, 差值: %.03f", m_mapPos["右测高位2"], VAR_FIXTURE_D("治具搬运Y轴A轨测高位置2高度标准值2"), fabs(m_mapPos["右测高位2"] - VAR_FIXTURE_D("治具搬运Y轴A轨测高位置2高度标准值2")));
		if (fabs(m_mapPos["右测高位2"] - VAR_FIXTURE_D("治具搬运Y轴A轨测高位置2高度标准值2")) > VAR_FIXTURE_D("合盖高度差正常范围")) {
			VAR_BELTA("治具合盖异常标志") = true;
			REPORT(sTemp, emLogLevelError);
		}
		else {
			REPORT(sTemp, emLogLevelNormal);
		}
	}
	else {
		CDat::UpdateValue("2轨测高值1", m_mapPos["左测高位1"]);
		sTemp.Format("左测高位1测高值： %.3f; 标准值: %.03f, 差值: %.03f", m_mapPos["左测高位1"], VAR_FIXTURE_D("治具搬运Y轴B轨测高位置1高度标准值1"), fabs(m_mapPos["左测高位1"] - VAR_FIXTURE_D("治具搬运Y轴B轨测高位置1高度标准值1")));
		if (fabs(m_mapPos["左测高位1"] - VAR_FIXTURE_D("治具搬运Y轴B轨测高位置1高度标准值1")) > VAR_FIXTURE_D("合盖高度差正常范围")) {
			VAR_BELTB("治具合盖异常标志") = true;
			REPORT(sTemp, emLogLevelError);
		}
		else {
			REPORT(sTemp, emLogLevelNormal);
		}

		CDat::UpdateValue("2轨测高值2", m_mapPos["右测高位1"]);
		sTemp.Format("右测高位1测高值： %.3f; 标准值: %.03f, 差值: %.03f", m_mapPos["右测高位1"], VAR_FIXTURE_D("治具搬运Y轴B轨测高位置1高度标准值2"), fabs(m_mapPos["右测高位1"] - VAR_FIXTURE_D("治具搬运Y轴B轨测高位置1高度标准值2")));
		if (fabs(m_mapPos["右测高位1"] - VAR_FIXTURE_D("治具搬运Y轴B轨测高位置1高度标准值2")) > VAR_FIXTURE_D("合盖高度差正常范围")) {
			VAR_BELTB("治具合盖异常标志") = true;
			REPORT(sTemp, emLogLevelError);
		}
		else {
			REPORT(sTemp, emLogLevelNormal);
		}

		CDat::UpdateValue("2轨测高值3", m_mapPos["左测高位2"]);
		sTemp.Format("左测高位2测高值： %.3f; 标准值: %.03f, 差值: %.03f", m_mapPos["左测高位2"], VAR_FIXTURE_D("治具搬运Y轴B轨测高位置2高度标准值1"), fabs(m_mapPos["左测高位2"] - VAR_FIXTURE_D("治具搬运Y轴B轨测高位置2高度标准值1")));
		if (fabs(m_mapPos["左测高位2"] - VAR_FIXTURE_D("治具搬运Y轴B轨测高位置2高度标准值1")) > VAR_FIXTURE_D("合盖高度差正常范围")) {
			VAR_BELTB("治具合盖异常标志") = true;
			REPORT(sTemp, emLogLevelError);
		}
		else {
			REPORT(sTemp, emLogLevelNormal);
		}

		CDat::UpdateValue("2轨测高值4", m_mapPos["右测高位2"]);
		sTemp.Format("右测高位2测高值： %.3f; 标准值: %.03f, 差值: %.03f", m_mapPos["右测高位2"], VAR_FIXTURE_D("治具搬运Y轴B轨测高位置2高度标准值2"), fabs(m_mapPos["右测高位2"] - VAR_FIXTURE_D("治具搬运Y轴B轨测高位置2高度标准值2")));
		if (fabs(m_mapPos["右测高位2"] - VAR_FIXTURE_D("治具搬运Y轴B轨测高位置2高度标准值2")) > VAR_FIXTURE_D("合盖高度差正常范围")) {
			VAR_BELTB("治具合盖异常标志") = true;
			REPORT(sTemp, emLogLevelError);
		}
		else {
			REPORT(sTemp, emLogLevelNormal);
		}
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_11, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport02_11()
{
	m_mapPos["治具搬运Z轴当前目标位置"] = PARAM_DOUBLE("治具搬运Z轴安全位置");

	RUN_STOP_IF_ERROR(m_pFixture->TransportZMove(m_mapPos["治具搬运Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(m_pFixture->IsTransportZInPos(m_mapPos["治具搬运Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴到位", true);
	}
	
	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_12, true);
}

CStatus CLogicFixtureTransport::OnFixtureTransport02_12()
{
	if (VAR_FIXTURE_B("测高失败暂停模式") && (VAR_BELTA_B("治具合盖异常标志") || VAR_BELTB_B("治具合盖异常标志"))) {
		MESSAGEBOX("测高失败暂停模式已开启，测高失败,设备暂停", "", false);
	}

	if (fabs(m_mapPos["治具搬运Y轴上盖放料位"] - PARAM_DOUBLE("治具搬运Y轴A轨放上盖位置")) < 0.1) {
		*g_pBeltA->m_mapParam["治具合盖完成标志"] = true;
	}
	else {
		*g_pBeltB->m_mapParam["治具合盖完成标志"] = true;
	}

	/*static */int nCnt = 0;

	if (nCnt > 10) {
		nCnt = 0;
		RUN_STOP_IF_ERROR(m_pFixture->TransportZHome());
		RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport02_13, true);
	}
	else {
		nCnt++;
		RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport00, true);
	}
}

CStatus CLogicFixtureTransport::OnFixtureTransport02_13()
{
	RUN_STOP_IF_ERROR(m_pFixture->IsTransportZHomeOK());

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴回零", false);
	}

	RETURN_STATE(&CLogicFixtureTransport::OnFixtureTransport00, true);
}
