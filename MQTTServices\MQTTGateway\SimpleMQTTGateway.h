#pragma once

#include <QObject>
#include <QTimer>
#include <QLoggingCategory>
#include <memory>
#include <atomic>
#include <map>
#include <mutex>
#include <thread>

#include "mqtt/async_client.h"
#include "SocketServer.h"
#include "SimpleConfigManager.h"
#include "TopicManager.h"
#include "RequestIdManager.h"
#include "CommandManager.h"
#include <openssl/hmac.h>
#include <openssl/sha.h>

Q_DECLARE_LOGGING_CATEGORY(simpleMqttGateway)

// 简化的MQTT网关 - 配置驱动的极简架构
class SimpleMQTTGateway : public QObject, public virtual mqtt::callback {
    Q_OBJECT

public:
    explicit SimpleMQTTGateway(QObject* parent = nullptr);
    virtual ~SimpleMQTTGateway();
    
    // 核心接口
    bool Initialize(const std::string& configDir = "config");
    void Run();
    void Stop();
    
    // 状态查询
    bool IsMQTTConnected() const;
    bool IsSocketConnected() const;
    
    // MQTT回调接口
    void connection_lost(const std::string& cause) override;
    void message_arrived(mqtt::const_message_ptr msg) override;
    void delivery_complete(mqtt::delivery_token_ptr token) override;

signals:
    void connectionStatusChanged(bool mqttConnected, bool socketConnected);
    void errorOccurred(const QString& error);

private slots:
    void OnPeriodicReport();

private:
    // 基础组件
    std::unique_ptr<mqtt::async_client> m_mqttClient;
    std::unique_ptr<SocketServer> m_socketServer;
    SimpleConfigManager m_configManager;
    TopicManager m_topicManager;
    
    // 运行状态
    std::atomic<bool> m_running{false};
    std::atomic<bool> m_mqttConnected{false};
    
    // 定时器
    QTimer* m_reportTimer;
    
    // 数据缓存
    std::map<std::string, std::string> m_dataCache;
    std::mutex m_dataMutex;
    
    // 事件答复管理
    struct PendingEvent {
        std::string eventType;
        std::chrono::system_clock::time_point timestamp;
        bool responseReceived;
    };
    std::map<std::string, PendingEvent> m_pendingEvents;
    std::mutex m_eventMutex;

    // 幂等性管理器
    RequestIdManager m_requestIdManager;

    // 命令管理器
    std::unique_ptr<CommandManager> m_commandManager;
    
    // 初始化方法
    bool InitializeMQTT();
    bool InitializeSocket();
    bool SubscribeToTopics();
    void StartPeriodicReporting();
    
    // Socket消息处理
    void ProcessSocketMessages();
    void HandleDeviceStatusMessage(const QJsonObject& data);
    void HandleEventMessage(const std::string& eventType, const QJsonObject& data);
    
    // MQTT消息处理
    void HandlePropertyGetRequest(mqtt::const_message_ptr msg);
    void HandlePropertySetRequest(mqtt::const_message_ptr msg);
    void HandleCommandRequest(mqtt::const_message_ptr msg);
    void HandleEventResponse(mqtt::const_message_ptr msg);

    // 命令处理增强
    void HandleCommandResponse(const std::string& requestId, const QJsonObject& response);
    void HandleCommandTimeout(const QString& requestId, const QString& commandName);
    
    // 数据处理
    QJsonObject BuildServicesMessage(const QJsonObject& sourceData);
    QJsonObject BuildDefaultService();
    QJsonObject BuildSnService();
    QJsonObject BuildStatusService();
    QJsonObject BuildEventService(const std::string& eventType, const QJsonObject& eventData);
    
    // MQTT发布
    bool PublishMessage(const std::string& topicName, const QJsonObject& payload,
                       const std::map<std::string, std::string>& params = {});
    
    // 工具方法
    std::string GenerateRequestId();
    std::string GetCurrentTimestamp();
    std::string GetSCADATimestamp();
    void UpdateDataCache(const QJsonObject& data);

    // SCADA协议认证方法
    std::string GenerateHMACPassword(const std::string& secret, const std::string& timestamp);
    std::string ExtractTimestampFromClientId(const std::string& clientId);
    std::string GetMQTTReturnCodeDescription(int returnCode);
    
    // 事件答复管理
    void RegisterPendingEvent(const std::string& requestId, const std::string& eventType);
    bool ProcessEventResponse(const std::string& requestId, bool success, const std::string& message);
    void CleanupExpiredEvents();
};
