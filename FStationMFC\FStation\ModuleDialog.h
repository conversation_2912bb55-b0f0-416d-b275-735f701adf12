﻿#pragma once

#include "ColorList.h"
#include "ColorButton.h"
#include "afxcmn.h"
#include "afxwin.h"

// CModuleDialog 对话框

class CModuleDialog : public CDialogEx
{
	DECLARE_DYNAMIC(CModuleDialog)

public:
	CModuleDialog(CPoint pt, CWnd* pParent = NULL);   // 标准构造函数
	virtual ~CModuleDialog();

// 对话框数据
	enum { IDD = IDD_DIALOG_MODULE };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持
	virtual BOOL PreTranslateMessage(MSG* pMsg);
	virtual void OnOK() {};
	virtual BOOL OnInitDialog();

	DECLARE_MESSAGE_MAP()
	afx_msg void OnPaint();
	afx_msg void OnTimer(UINT_PTR nIDEvent);	
	afx_msg HBRUSH OnCtlColor(CDC* pDC, CWnd* pWnd, UINT nCtlColor);
	afx_msg void OnNMRClickList1(NMHDR *pNMHDR, LRESULT *pResult);
	afx_msg void OnNMDblclkList1(NMHDR *pNMHDR, LRESULT *pResult);
	afx_msg void OnNMClickList3(NMHDR *pNMHDR, LRESULT *pResult);
	afx_msg void OnNMDblclkList3(NMHDR *pNMHDR, LRESULT *pResult);
	afx_msg void OnTcnSelchangeTab1(NMHDR *pNMHDR, LRESULT *pResult);
	afx_msg void OnEnKillfocusEdit1();
	afx_msg void OnBnClickedButton1();
	afx_msg void OnBnClickedButton2();
	afx_msg void OnBnClickedButton3();
	afx_msg void OnBnClickedButton4();
	afx_msg void OnBnClickedButton5();
	afx_msg void OnBnClickedButton6();
	afx_msg void OnBnClickedButton7();

public:
	void SwitchModule(CModule *pModule, int nBtnIndex);
	void RefreshModule();
	void UpdateList();
	void ShowDialog(int nIndex, int nSubIndex);

public:
	CImageList					m_imageList;
	CImageList					m_imageList1;

	CColorList					m_list1;
	CColorList					m_list2;
	CColorList					m_list3;

	CColorButton				m_btn[7];

	CTabCtrl					m_tab;
	CEdit						m_edit;

	CImageWindow*				m_pImageWindow;
private:
	CPoint						m_pt;

	CModule*					m_pModule;

	int							m_nCurIndex;
	int							m_nCurSubIndex;

	int							m_nRowForEdit;
	int							m_nColForEdit;

	vector<vector<CDialogEx*>>	m_vecDialogList;

	bool						m_bFlag;
};
