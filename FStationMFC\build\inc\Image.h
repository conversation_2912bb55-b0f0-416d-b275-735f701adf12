#pragma once

#ifdef MODULE_IMAGE_PROCESS_DLL
#define MODULE_IMAGE_PROCESS_API __declspec(dllexport)
#else
#define MODULE_IMAGE_PROCESS_API __declspec(dllimport)
#endif

#include <vector>
using namespace std;

#include "Device.h"

#define UM_DRAW_FINISH WM_USER + 101
#define UM_RIGHT_MOVE  WM_USER + 102

namespace yzBase
{
	typedef struct _POINT_2D_
	{
		double x;
		double y;
	}POINT2D, *PPOINT2D;

	class MODULE_IMAGE_PROCESS_API CCvImage
	{
	public:
		CCvImage() {}
		virtual ~CCvImage() {}

	public:
		static CCvImage* CreateInstance();
		static void Rotate(double nCenterX, double nCenterY, double nX, double nY, double nR, double &nRotatedX, double &nRotatedY);
		
	public:
		virtual CString LoadImage() = 0;
		virtual CString LoadImage(CString strImgFile) = 0;
		virtual CString SaveImage(CString strImgFile, double nZoomRate = 1.0, bool bInit = false) = 0;

		virtual CString SetImage(CCvImage *pImage) = 0;
		virtual CString SetImage(BYTE *pBuffer, int nWidth, int nHeight, bool bTranspose, bool bFlipX, bool bFlipY) = 0;

		virtual CString GetWidth(int &nWidth) = 0;
		virtual CString GetHeight(int &nHeight) = 0;

		virtual CString ClearText(CString strName) = 0;
		virtual CString PutText(CString strName, CString strText, int nX, int nY, double nScale, int nLineWidth, COLORREF clr) = 0;
		virtual CString ClearLine(CString strName) = 0;
		virtual CString PutLine(CString strName, int nStartX, int nStartY, int nEndX, int nEndY, int nLineWidth, COLORREF clr) = 0;
		virtual CString ClearRect(CString strName) = 0;
		virtual CString PutRect(CString strName, int nLeft, int nTop, int nRight, int nBottom, int nLineWidth, COLORREF clr) = 0;
		virtual CString ClearCircle(CString strName) = 0;
		virtual CString PutCircle(CString strName, int nCenterX, int nCenterY, double nRadius, int nLineWidth, COLORREF clr) = 0;
		virtual CString ClearRotatedRect(CString strName) = 0;
		virtual CString PutRotatedRect(CString strName, int nCenterX, int nCenterY, int nWidth, int nHeight, double nAngle, int nLineWidth, COLORREF clr) = 0;
	};

	class MODULE_IMAGE_PROCESS_API CImageWindow
	{
	public:
		CImageWindow() {}
		virtual ~CImageWindow() {}

	public:
		static CImageWindow* CreateInstance(CString strName);

	public:
		virtual CString AttatchWindow(CRect rcWnd) = 0;
		virtual CString Show(CCvImage *pImage) = 0;
		virtual CString DrawPoint() = 0;
		virtual CString DrawRect() = 0;
		virtual CString DrawCircle() = 0;
		virtual CString SetDrawWnd(HWND hDrawWnd) = 0;
		virtual CString Show(bool bFlag) = 0;
	};

	class MODULE_IMAGE_PROCESS_API CImageFlow
	{
	public:
		CImageFlow() {}
		virtual ~CImageFlow() {}

	public:
		static CImageFlow *CreateInstance(CString strName, CString strDirectory, CCameraBase *pCamera = NULL, CString strCamName = "");

	public:
		virtual CString Load(CString sDirectory) = 0;
		virtual CString Save(CString sDirectory) = 0;
		virtual CString Save() = 0;
		virtual CString Reset() = 0;
		virtual CString Excute(CCvImage *pImage, CString sModel, CString sCode2D, int nIndex, bool bTop, bool bCheckFlag = false) = 0;
		virtual CString GetFinishFlag(bool &bFinishFlag) = 0;
		virtual CString GetResult(double &nResultX, double &nResultY, double &nResultR, bool &bExistFlag, int nOutIndex = 0, int nRowIndex = 0, int nColIndex = 0, int nIndex = 0) = 0;
		virtual CString GetCode2D(int nOutIndex = 0, int nRowIndex = 0, int nColIndex = 0, int nIndex = 0) = 0;
		virtual CString GetNccResult(int nOutIndex = 0, int nRowIndex = 0, int nColIndex = 0, int nIndex = 0) = 0;
		virtual CString ScanCode(CCvImage *pImage, bool bScanAll) = 0;
		virtual CString GetCode(vector<string> &vCode) = 0;
	};

	class MODULE_IMAGE_PROCESS_API CImageProcessWindow
	{
	public:
		CImageProcessWindow() {}
		virtual ~CImageProcessWindow() {}

	public:
		static CImageProcessWindow* CreateInstance(vector<CImageFlow*> *pvImageFlow = NULL);
		static CImageProcessWindow* CreateInstance(CImageFlow* pImageFlow = NULL);

	public:
		virtual CString Show(bool bOnlyEdit) = 0;
		virtual CString Show() = 0;
	};

	class MODULE_IMAGE_PROCESS_API CImageCalibrate
	{
	public:
		CImageCalibrate() {}
		virtual ~CImageCalibrate() {}

	public:
		static CImageCalibrate *CreateInstance(CString strName, CString strDirectory);

	public:
		virtual CString CalibrateCoordinate(const vector<double> vecPixelX, const vector<double> vecPixelY, const vector<double> vecMachineX, const vector<double> vecMachineY) = 0;

		virtual CString CalibrateRotate(const vector<double> vecX, const vector<double> vecY, double &nOffX, double &nOffY, double nCameraCenterPixelX = 1023.5, double nCameraCenterPixelY = 1223.5) = 0;

		virtual CString CalibrateUDMatch(double nUpPixelX, double nUpPixelY, double nDnPixelX, double nDnPixelY, double nMachineX, double nMachineY) = 0;

		virtual CString CalculatePickPos(CImageCalibrate *pImageCalibrateUp, double nUpPixelX, double nUpPixelY, double nUpAngle, double nMachineX, double nMachineY, double &nOutX, double &nOutY) = 0;

		virtual CString CalculateMntPos(CImageCalibrate *pImageCalibrateUp, double nUpPixelX, double nUpPixelY, double nUpAngle, double nUpMachineX, double nUpMachineY, double nDnPixelX, double nDnPixelY, double nDnAngle, double nDnMachineX, double nDnMachineY, double &nOutX, double &nOutY) =  0;
		
		virtual CString GetCalibrateCoordinate(double &nPosX, double &nPosY) = 0;
		virtual CString GetCalibrateCenter(double &nPixelX, double &nPixelY) = 0;

		virtual CString GetHeadOff(double &nOffX, double &nOffY) = 0;

		virtual void TransToMachine(double nPixelX, double nPixelY, double &nMachineX, double &nMachineY) = 0;
		virtual void TransToPixel(double nMachineX, double nMachineY, double &nPixelX, double &nPixelY) = 0;
		virtual void CalculateOffset(double nPixelX, double nPixelY, double nAngle, double nMachineX, double nMachineY, double &nOutX, double &nOutY) = 0;
		virtual void CalculateOffset(CImageCalibrate *pImageCalibrateUp, double nUpPixelX, double nUpPixelY, double nAngle, double nMachineX, double nMachineY, double &nOutX, double &nOutY) = 0;

		virtual CString SetPickOffset(double nOffX, double nOffY) = 0;
		virtual CString GetPickOffset(double& nOffX, double& nOffY) = 0;

		virtual CString SetMntOffset(double nOffX, double nOffY) = 0;
		virtual CString GetMntOffset(double& nOffX, double& nOffY) = 0;
	};

}