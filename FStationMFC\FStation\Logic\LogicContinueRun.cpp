﻿#include "stdafx.h"
#include "LogicContinueRun.h"

#include "Sys.h"

#include "LogicMgr.h"

CLogicContinueRun::CLogicContinueRun()
{
	m_pFuncTray = CLogicMgr::m_ThreadFactory.CreateThreadFunc("TRAY老化");
	m_pFuncFixture = CLogicMgr::m_ThreadFactory.CreateThreadFunc("治具老化");
	m_pFuncFixtureThransport = CLogicMgr::m_ThreadFactory.CreateThreadFunc("治具搬运老化");
	m_pFuncBeltA = CLogicMgr::m_ThreadFactory.CreateThreadFunc("A轨老化");
	m_pFuncBeltB = CLogicMgr::m_ThreadFactory.CreateThreadFunc("B轨老化");
}

CLogicContinueRun::~CLogicContinueRun()
{
	delete m_pFuncTray;
	delete m_pFuncFixture;
	delete m_pFuncFixtureThransport;
	delete m_pFuncBeltA;
	delete m_pFuncBeltB;
}

EnumStatus CLogicContinueRun::OnSafeCheck()
{
	if (!CSys::m_bInit) {
		return emStop;
	}

	return emRun;
}

EnumStatus CLogicContinueRun::OnStart()
{
	return emRun;
}

EnumStatus CLogicContinueRun::OnPause()
{
	g_pBeltA->BeltOff();

	g_pBeltB->BeltOff();

	g_pFixture->UploadBeltOff();

	g_pFixture->SeparateZStop();

	g_pFixture->LidZStop();

	g_pFixture->TransportYStop();

	g_pFixture->TransportZStop();

	g_pTray->EmptyTrayBeltOff();

	g_pTray->EmptyTrayTransportYStop();

	g_pTray->EmptyTraySeparateZStop();

	g_pTray->FullTrayBeltOff();

	g_pTray->FullTrayUpDnZStop();

	m_pFuncTray->SetStatus(emPause);
	m_pFuncFixture->SetStatus(emPause);
	m_pFuncFixtureThransport->SetStatus(emPause);
	m_pFuncBeltA->SetStatus(emPause);
	m_pFuncBeltB->SetStatus(emPause);


	return emRun;
}

EnumStatus CLogicContinueRun::OnResume()
{
	map<CString, DWORD>::iterator it = m_mapTick.begin();
	for (; it!=m_mapTick.end(); it++)
	{
		it->second = GetTickCount();
	}

	return emRun;
}

EnumStatus CLogicContinueRun::OnStop()
{
	g_pBeltA->BeltOff();

	g_pBeltB->BeltOff();

	g_pFixture->UploadBeltOff();

	g_pFixture->SeparateZStop();

	g_pFixture->LidZStop();

	g_pFixture->TransportYStop();

	g_pFixture->TransportZStop();

	g_pTray->EmptyTrayBeltOff();

	g_pTray->EmptyTrayTransportYStop();

	g_pTray->EmptyTraySeparateZStop();

	g_pTray->FullTrayBeltOff();

	g_pTray->FullTrayUpDnZStop();

	m_pFuncTray->SetStatus(emStop);
	m_pFuncFixture->SetStatus(emStop);
	m_pFuncFixtureThransport->SetStatus(emStop);
	m_pFuncBeltA->SetStatus(emStop);
	m_pFuncBeltB->SetStatus(emStop);

	CLogicMgr::m_mapThread["TrayFull"].pThread->Stop();
	CLogicMgr::m_mapThread["TrayEmpty"].pThread->Stop();

	return emRun;
}

CStatus CLogicContinueRun::OnRun()
{
	m_pFuncTray->SetAction(static_cast<THREAD_FUNC>(&CLogicContinueRun::OnTray00));
	m_pFuncFixture->SetAction(static_cast<THREAD_FUNC>(&CLogicContinueRun::OnFixture00));
	m_pFuncFixtureThransport->SetAction(static_cast<THREAD_FUNC>(&CLogicContinueRun::OnFixtureTransport00));
	m_pFuncBeltA->SetAction(static_cast<THREAD_FUNC>(&CLogicContinueRun::OnBeltA00));
	m_pFuncBeltB->SetAction(static_cast<THREAD_FUNC>(&CLogicContinueRun::OnBeltB00));

	CLogicMgr::m_mapThread["TrayFull"].pThread->Start();
	CLogicMgr::m_mapThread["TrayEmpty"].pThread->Start();

	RETURN_STATE(&CLogicContinueRun::OnContinue00, true);
}

CStatus CLogicContinueRun::OnContinue00()
{
	EnumStatus  status = m_pFuncTray->Run(this);

	switch (status)
	{
	case emRun:
		break;
	case emStop:
		RETURN_STOP();
	case emPause:
		RETURN_PAUSE(NULL);
	default:
		break;
	}

	RETURN_STATE(&CLogicContinueRun::OnContinue01, true);
}

CStatus CLogicContinueRun::OnContinue01()
{
	EnumStatus  status = m_pFuncFixture->Run(this);

	switch (status)
	{
	case emRun:
		break;
	case emStop:
		RETURN_STOP();
	case emPause:
		RETURN_PAUSE(NULL);
	default:
		break;
	}

	RETURN_STATE(&CLogicContinueRun::OnContinue02, true);
}

CStatus CLogicContinueRun::OnContinue02()
{
	EnumStatus  status = m_pFuncFixtureThransport->Run(this);

	switch (status)
	{
	case emRun:
		break;
	case emStop:
		RETURN_STOP();
	case emPause:
		RETURN_PAUSE(NULL);
	default:
		break;
	}

	RETURN_STATE(&CLogicContinueRun::OnContinue03, true);
}

CStatus CLogicContinueRun::OnContinue03()
{
	EnumStatus  status = m_pFuncBeltA->Run(this);

	switch (status)
	{
	case emRun:
		break;
	case emStop:
		RETURN_STOP();
	case emPause:
		RETURN_PAUSE(NULL);
	default:
		break;
	}

	RETURN_STATE(&CLogicContinueRun::OnContinue04, true);
}

CStatus CLogicContinueRun::OnContinue04()
{
	EnumStatus  status = m_pFuncBeltB->Run(this);

	switch (status)
	{
	case emRun:
		break;
	case emStop:
		RETURN_STOP();
	case emPause:
		RETURN_PAUSE(NULL);
	default:
		break;
	}

	RETURN_STATE(&CLogicContinueRun::OnContinue00, true);
}

CStatus CLogicContinueRun::OnTray00()
{
	VAR_ROBOT("TRAY盘进料标志") = true;

	RETURN_STATE(&CLogicContinueRun::OnTray01, true);
}

CStatus CLogicContinueRun::OnTray01()
{
	if (!VAR_TRAY_B("满TRAY盘进料完成标志")) {
		RETURN_SELF("等待满TRAY盘进料完成", false);
	}

	VAR_TRAY("满TRAY盘进料完成标志") = false;

	RETURN_STATE(&CLogicContinueRun::OnTray02, true);
}

CStatus CLogicContinueRun::OnTray02()
{
	RUN_STOP_IF_ERROR(g_pTray->FullTrayUpInPosStatus());

	if (m_sRet == "On") {
		RETURN_STATE(&CLogicContinueRun::OnTray00, true);
	}

	RUN_STOP_IF_ERROR(g_pTray->FullTrayBeltInPosStatus());

	if (m_sRet == "On") {
		RETURN_STATE(&CLogicContinueRun::OnTray00, true);
	}

	double nCurPos = 0;

	RUN_STOP_IF_ERROR(g_pTray->FullTrayUpDnZCurrentPos(nCurPos));

	if (nCurPos - VAR_TRAY_D("满TRAY盘升降Z轴放料位") > 1) {
		RETURN_STATE(&CLogicContinueRun::OnTray00, true);
	}

	RETURN_STATE(&CLogicContinueRun::OnTray03, true);
}

CStatus CLogicContinueRun::OnTray03()
{
	VAR_ROBOT("TRAY盘退料标志") = true;

	RETURN_STATE(&CLogicContinueRun::OnTray04, true);
}

CStatus CLogicContinueRun::OnTray04()
{
	if (!VAR_TRAY_B("空TRAY盘进料完成标志")) {
		RETURN_SELF("等待空TRAY盘进料完成", false);
	}

	VAR_TRAY("空TRAY盘进料完成标志") = false;
	VAR_TRAY("满TRAY盘进料完成标志") = false;

	RETURN_STATE(&CLogicContinueRun::OnTray05, true);
}

CStatus CLogicContinueRun::OnTray05()
{
	RUN_STOP_IF_ERROR(g_pTray->EmptyTraySeparateZMove(VAR_TRAY_D("空TRAY盘分盘Z轴支撑位")));

	RUN_STOP_IF_ERROR(g_pTray->IsEmptyTraySeparateZInPos(VAR_TRAY_D("空TRAY盘分盘Z轴支撑位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘分盘Z轴运动到支撑位", true);
	}

	RUN_STOP_IF_ERROR(g_pTray->EmptyTrayMaterialExistStatus());

	if (m_sRet == "On") {
		m_mapFlag["存在空TRAY盘"] = true;
	}
	else {
		m_mapFlag["存在空TRAY盘"] = false;
	}

	RETURN_STATE(&CLogicContinueRun::OnTray06, true);
}

CStatus CLogicContinueRun::OnTray06()
{
	RUN_STOP_IF_ERROR(g_pTray->EmptyTraySeparateZMove(VAR_TRAY_D("空TRAY盘分盘Z轴放料位")));

	RUN_STOP_IF_ERROR(g_pTray->IsEmptyTraySeparateZInPos(VAR_TRAY_D("空TRAY盘分盘Z轴放料位")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待空TRAY盘分盘Z轴运动到放料位", true);
	}

	if (m_mapFlag["存在空TRAY盘"]) {
		RETURN_STATE(&CLogicContinueRun::OnTray03, true);
	}
	else {
		RETURN_STATE(&CLogicContinueRun::OnTray00, true);
	}
}

CStatus CLogicContinueRun::OnFixture00()
{
	RUN_STOP_IF_ERROR(g_pFixture->InStopCylinderOff());
	
	RUN_STOP_IF_ERROR(g_pFixture->InStopCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具入料阻挡气缸缩回", true);
	}

	m_mapTick["治具皮带计时"] = GetTickCount();

	RETURN_STATE(&CLogicContinueRun::OnFixture01, true);
}

CStatus CLogicContinueRun::OnFixture01()
{
	RUN_STOP_IF_ERROR(g_pFixture->UploadBeltOn(VAR_FIXTURE_B("皮带正转方向")));

	if (GetTickCount() - m_mapTick["治具皮带计时"] < 2000) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(g_pFixture->UploadBeltOff());

	RUN_STOP_IF_ERROR(g_pFixture->UpLidSetPosCylinderOn());

	Sleep(200);

	RUN_STOP_IF_ERROR(g_pFixture->UpLidSetPosCylinderOff());

	RUN_STOP_IF_ERROR(g_pFixture->UpLidLiftCylinderOn());

	RETURN_STATE(&CLogicContinueRun::OnFixture02, true);
}

CStatus CLogicContinueRun::OnFixture02()
{
	RUN_STOP_IF_ERROR(g_pFixture->LidZMove(VAR_FIXTURE_D("治具开盖Z轴上层位置"), 100));

	RUN_STOP_IF_ERROR(g_pFixture->IsLidZInPos(VAR_FIXTURE_D("治具开盖Z轴上层位置")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具开盖Z轴到位", true);
	}

	RETURN_STATE(&CLogicContinueRun::OnFixture03, true);
}

CStatus CLogicContinueRun::OnFixture03()
{
	RUN_STOP_IF_ERROR(g_pFixture->UpLidLiftCylinderOff());

	RUN_STOP_IF_ERROR(g_pFixture->UpLidLiftCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具上盖位顶升气缸缩回", true);
	}

	RETURN_STATE(&CLogicContinueRun::OnFixture04, true);
}

CStatus CLogicContinueRun::OnFixture04()
{
	RUN_STOP_IF_ERROR(g_pFixture->LidZMove(VAR_FIXTURE_D("治具开盖Z轴放料位置"), 100));

	RUN_STOP_IF_ERROR(g_pFixture->IsLidZInPos(VAR_FIXTURE_D("治具开盖Z轴放料位置")));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具开盖Z轴到位", true);
	}

	RETURN_STATE(&CLogicContinueRun::OnFixture05, true);
}

CStatus CLogicContinueRun::OnFixture05()
{
	RUN_STOP_IF_ERROR(g_pFixture->UpLidStopCylinderOff());

	RUN_STOP_IF_ERROR(g_pFixture->UpLidStopCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具上盖位阻挡气缸缩回", true);
	}

	m_mapTick["治具皮带计时"] = GetTickCount();

	RETURN_STATE(&CLogicContinueRun::OnFixture06, true);
}

CStatus CLogicContinueRun::OnFixture06()
{
	RUN_STOP_IF_ERROR(g_pFixture->UploadBeltOn(VAR_FIXTURE_B("皮带正转方向")));

	if (GetTickCount() - m_mapTick["治具皮带计时"] < 2000) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(g_pFixture->UploadBeltOff());

	RUN_STOP_IF_ERROR(g_pFixture->DnLidSetPosCylinderOn());

	Sleep(500);

	RUN_STOP_IF_ERROR(g_pFixture->DnLidSetPosCylinderOff());

	RETURN_STATE(&CLogicContinueRun::OnFixture07, true);
}

CStatus CLogicContinueRun::OnFixture07()
{
	RUN_STOP_IF_ERROR(g_pFixture->UpLidStopCylinderOn());

	RUN_STOP_IF_ERROR(g_pFixture->InStopCylinderOn());

	RUN_STOP_IF_ERROR(g_pFixture->UpLidStopCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待治具上盖位阻挡气缸伸出", true);
	}

	RUN_STOP_IF_ERROR(g_pFixture->InStopCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待治具入料阻挡气缸伸出", true);
	}

	RETURN_STATE(&CLogicContinueRun::OnFixture08, true);
}

CStatus CLogicContinueRun::OnFixture08()
{
	m_mapPos["治具分板Z轴当前目标位置"] = VAR_FIXTURE_D("治具分板Z轴支撑位置");

	RUN_STOP_IF_ERROR(g_pFixture->SeparateZMove(m_mapPos["治具分板Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(g_pFixture->IsSeparateZInPos(m_mapPos["治具分板Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具分板Z轴到位", true);
	}

	RETURN_STATE(&CLogicContinueRun::OnFixture09, true);
}

CStatus CLogicContinueRun::OnFixture09()
{
	RUN_STOP_IF_ERROR(g_pFixture->SupportCylinderOff());

	RUN_STOP_IF_ERROR(g_pFixture->SeparateCylinderOff());

	RUN_STOP_IF_ERROR(g_pFixture->SupportCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具支撑气缸缩回", true);
	}

	RUN_STOP_IF_ERROR(g_pFixture->SeparateCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具分板气缸缩回", true);
	}

	RETURN_STATE(&CLogicContinueRun::OnFixture10, true);
}

CStatus CLogicContinueRun::OnFixture10()
{
	m_mapPos["治具分板Z轴当前目标位置"] = VAR_FIXTURE_D("治具分板Z轴分板位置");

	RUN_STOP_IF_ERROR(g_pFixture->SeparateZMove(m_mapPos["治具分板Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(g_pFixture->IsSeparateZInPos(m_mapPos["治具分板Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具分板Z轴到位", true);
	}

	RETURN_STATE(&CLogicContinueRun::OnFixture11, true);
}

CStatus CLogicContinueRun::OnFixture11()
{
	RUN_STOP_IF_ERROR(g_pFixture->SupportCylinderOn());

	RUN_STOP_IF_ERROR(g_pFixture->SupportCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待治具支撑气缸伸出", true);
	}

	RUN_STOP_IF_ERROR(g_pFixture->SeparateCylinderOn());

	RETURN_STATE(&CLogicContinueRun::OnFixture12, true);
}

CStatus CLogicContinueRun::OnFixture12()
{
	m_mapPos["治具分板Z轴当前目标位置"] = VAR_FIXTURE_D("治具分板Z轴放料位置");

	RUN_STOP_IF_ERROR(g_pFixture->SeparateZMove(m_mapPos["治具分板Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(g_pFixture->IsSeparateZInPos(m_mapPos["治具分板Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具分板Z轴到位", true);
	}

	RETURN_STATE(&CLogicContinueRun::OnFixture13, true);
}

CStatus CLogicContinueRun::OnFixture13()
{
	RUN_STOP_IF_ERROR(g_pFixture->SeparateCylinderOff());

	RUN_STOP_IF_ERROR(g_pFixture->SeparateCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具分板气缸缩回", true);
	}

	RETURN_STATE(&CLogicContinueRun::OnFixture00, true);
}

CStatus CLogicContinueRun::OnFixtureTransport00()
{
	m_mapPos["治具搬运Z轴当前目标位置"] = VAR_FIXTURE_D("治具搬运Z轴安全位置");

	RUN_STOP_IF_ERROR(g_pFixture->TransportZMove(m_mapPos["治具搬运Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(g_pFixture->IsTransportZInPos(m_mapPos["治具搬运Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴到位", true);
	}

	RETURN_STATE(&CLogicContinueRun::OnFixtureTransport01, true);
}

CStatus CLogicContinueRun::OnFixtureTransport01()
{
	RUN_STOP_IF_ERROR(g_pFixture->RollCylinderOff());

	m_mapPos["治具搬运Y轴当前目标位置"] = VAR_FIXTURE_D("治具搬运Y轴取下盖位置");

	RUN_STOP_IF_ERROR(g_pFixture->TransportYMove(m_mapPos["治具搬运Y轴当前目标位置"]));

	RUN_STOP_IF_ERROR(g_pFixture->IsTransportYInPos(m_mapPos["治具搬运Y轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Y轴到位", true);
	}

	RETURN_STATE(&CLogicContinueRun::OnFixtureTransport02, true);
}

CStatus CLogicContinueRun::OnFixtureTransport02()
{
	RUN_STOP_IF_ERROR(g_pFixture->RollCylinderOff());

	RUN_STOP_IF_ERROR(g_pFixture->RollCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待旋转气缸缩回", true);
	}

	Sleep(500);

	RETURN_STATE(&CLogicContinueRun::OnFixtureTransport03, true);
}

CStatus CLogicContinueRun::OnFixtureTransport03()
{
	RUN_STOP_IF_ERROR(g_pFixture->VacuumOn());

	m_mapPos["治具搬运Z轴当前目标位置"] = VAR_FIXTURE_D("治具搬运Z轴取下盖位置");

	RUN_STOP_IF_ERROR(g_pFixture->TransportZMove(m_mapPos["治具搬运Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(g_pFixture->IsTransportZInPos(m_mapPos["治具搬运Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴到位", true);
	}

	m_mapTick["治具吸真空计时"] = GetTickCount();

	RETURN_STATE(&CLogicContinueRun::OnFixtureTransport04, true);
}

CStatus CLogicContinueRun::OnFixtureTransport04()
{
	RUN_STOP_IF_ERROR(g_pFixture->VacuumOn());

	RUN_STOP_IF_ERROR(g_pFixture->VacuumStatus());

	if (m_sRet != "On") {
		if (GetTickCount() - m_mapTick["治具吸真空计时"] < (DWORD)VAR_FIXTURE_I("治具吸真空超时")) {
			RETURN_SELF("等待真空信号", false);
		}
	}

	RETURN_STATE(&CLogicContinueRun::OnFixtureTransport05, true);
}

CStatus CLogicContinueRun::OnFixtureTransport05()
{
	m_mapPos["治具搬运Z轴当前目标位置"] = VAR_FIXTURE_D("治具搬运Z轴安全位置");

	RUN_STOP_IF_ERROR(g_pFixture->TransportZMove(m_mapPos["治具搬运Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(g_pFixture->IsTransportZInPos(m_mapPos["治具搬运Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴到位", true);
	}

	RETURN_STATE(&CLogicContinueRun::OnFixtureTransport06, true);
}

CStatus CLogicContinueRun::OnFixtureTransport06()
{
	RUN_STOP_IF_ERROR(g_pFixture->RollCylinderOn());

	m_mapPos["治具搬运Y轴当前目标位置"] = VAR_FIXTURE_D("治具搬运Y轴A轨放下盖位置");

	RUN_STOP_IF_ERROR(g_pFixture->TransportYMove(m_mapPos["治具搬运Y轴当前目标位置"]));

	RUN_STOP_IF_ERROR(g_pFixture->IsTransportYInPos(m_mapPos["治具搬运Y轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Y轴到位", true);
	}

	RETURN_STATE(&CLogicContinueRun::OnFixtureTransport07, true);
}

CStatus CLogicContinueRun::OnFixtureTransport07()
{
	RUN_STOP_IF_ERROR(g_pFixture->RollCylinderOn());

	RUN_STOP_IF_ERROR(g_pFixture->RollCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待旋转气缸伸出", true);
	}

	Sleep(500);

	RETURN_STATE(&CLogicContinueRun::OnFixtureTransport08, true);
}

CStatus CLogicContinueRun::OnFixtureTransport08()
{
	m_mapPos["治具搬运Z轴当前目标位置"] = VAR_FIXTURE_D("治具搬运Z轴放下盖位置");

	RUN_STOP_IF_ERROR(g_pFixture->TransportZMove(m_mapPos["治具搬运Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(g_pFixture->IsTransportZInPos(m_mapPos["治具搬运Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴到位", true);
	}
	
	RUN_STOP_IF_ERROR(g_pFixture->BrokenVacuumOn());

	Sleep(200);

	RUN_STOP_IF_ERROR(g_pFixture->VacuumOff());

	RETURN_STATE(&CLogicContinueRun::OnFixtureTransport09, true);
}

CStatus CLogicContinueRun::OnFixtureTransport09()
{
	m_mapPos["治具搬运Z轴当前目标位置"] = VAR_FIXTURE_D("治具搬运Z轴安全位置");

	RUN_STOP_IF_ERROR(g_pFixture->TransportZMove(m_mapPos["治具搬运Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(g_pFixture->IsTransportZInPos(m_mapPos["治具搬运Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴到位", true);
	}

	RETURN_STATE(&CLogicContinueRun::OnFixtureTransport10, true);
}

CStatus CLogicContinueRun::OnFixtureTransport10()
{
	RUN_STOP_IF_ERROR(g_pFixture->RollCylinderOff());

	m_mapPos["治具搬运Y轴当前目标位置"] = VAR_FIXTURE_D("治具搬运Y轴取上盖位置");

	RUN_STOP_IF_ERROR(g_pFixture->TransportYMove(m_mapPos["治具搬运Y轴当前目标位置"]));

	RUN_STOP_IF_ERROR(g_pFixture->IsTransportYInPos(m_mapPos["治具搬运Y轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Y轴到位", true);
	}

	RETURN_STATE(&CLogicContinueRun::OnFixtureTransport11, true);
}

CStatus CLogicContinueRun::OnFixtureTransport11()
{
	RUN_STOP_IF_ERROR(g_pFixture->RollCylinderOff());

	RUN_STOP_IF_ERROR(g_pFixture->RollCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待旋转气缸缩回", true);
	}

	Sleep(500);

	RETURN_STATE(&CLogicContinueRun::OnFixtureTransport12, true);
}

CStatus CLogicContinueRun::OnFixtureTransport12()
{
	RUN_STOP_IF_ERROR(g_pFixture->VacuumOn());

	m_mapPos["治具搬运Z轴当前目标位置"] = VAR_FIXTURE_D("治具搬运Z轴取上盖位置");

	RUN_STOP_IF_ERROR(g_pFixture->TransportZMove(m_mapPos["治具搬运Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(g_pFixture->IsTransportZInPos(m_mapPos["治具搬运Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴到位", true);
	}

	m_mapTick["治具吸真空计时"] = GetTickCount();

	RETURN_STATE(&CLogicContinueRun::OnFixtureTransport13, true);
}

CStatus CLogicContinueRun::OnFixtureTransport13()
{
	RUN_STOP_IF_ERROR(g_pFixture->VacuumOn());

	RUN_STOP_IF_ERROR(g_pFixture->VacuumStatus());

	if (m_sRet != "On") {
		if (GetTickCount() - m_mapTick["治具吸真空计时"] < (DWORD)VAR_FIXTURE_I("治具吸真空超时")) {
			RETURN_SELF("等待真空信号", false);
		}
	}

	RETURN_STATE(&CLogicContinueRun::OnFixtureTransport14, true);
}

CStatus CLogicContinueRun::OnFixtureTransport14()
{
	m_mapPos["治具搬运Z轴当前目标位置"] = VAR_FIXTURE_D("治具搬运Z轴安全位置");

	RUN_STOP_IF_ERROR(g_pFixture->TransportZMove(m_mapPos["治具搬运Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(g_pFixture->IsTransportZInPos(m_mapPos["治具搬运Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴到位", true);
	}

	RETURN_STATE(&CLogicContinueRun::OnFixtureTransport15, true);
}

CStatus CLogicContinueRun::OnFixtureTransport15()
{
	RUN_STOP_IF_ERROR(g_pFixture->RollCylinderOn());

	m_mapPos["治具搬运Y轴当前目标位置"] = VAR_FIXTURE_D("治具搬运Y轴A轨放上盖位置");

	RUN_STOP_IF_ERROR(g_pFixture->TransportYMove(m_mapPos["治具搬运Y轴当前目标位置"]));

	RUN_STOP_IF_ERROR(g_pFixture->IsTransportYInPos(m_mapPos["治具搬运Y轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Y轴到位", true);
	}

	RETURN_STATE(&CLogicContinueRun::OnFixtureTransport16, true);
}

CStatus CLogicContinueRun::OnFixtureTransport16()
{
	RUN_STOP_IF_ERROR(g_pFixture->RollCylinderOn());

	RUN_STOP_IF_ERROR(g_pFixture->RollCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待旋转气缸伸出", true);
	}

	Sleep(500);

	RETURN_STATE(&CLogicContinueRun::OnFixtureTransport17, true);
}

CStatus CLogicContinueRun::OnFixtureTransport17()
{
	m_mapPos["治具搬运Z轴当前目标位置"] = VAR_FIXTURE_D("治具搬运Z轴放上盖位置");

	RUN_STOP_IF_ERROR(g_pFixture->TransportZMove(m_mapPos["治具搬运Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(g_pFixture->IsTransportZInPos(m_mapPos["治具搬运Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴到位", true);
	}
	
	RUN_STOP_IF_ERROR(g_pFixture->BrokenVacuumOn());

	Sleep(200);

	RUN_STOP_IF_ERROR(g_pFixture->VacuumOff());

	RETURN_STATE(&CLogicContinueRun::OnFixtureTransport18, true);
}

CStatus CLogicContinueRun::OnFixtureTransport18()
{
	m_mapPos["治具搬运Z轴当前目标位置"] = VAR_FIXTURE_D("治具搬运Z轴安全位置");

	RUN_STOP_IF_ERROR(g_pFixture->TransportZMove(m_mapPos["治具搬运Z轴当前目标位置"]));

	RUN_STOP_IF_ERROR(g_pFixture->IsTransportZInPos(m_mapPos["治具搬运Z轴当前目标位置"]));

	if (m_sRet != "Yes") {
		RETURN_SELF("等待治具搬运Z轴到位", true);
	}

	RETURN_STATE(&CLogicContinueRun::OnFixtureTransport00, true);
}

CStatus CLogicContinueRun::OnBeltA00()
{
	RUN_STOP_IF_ERROR(g_pBeltA->FixtureAssembleStopCylinderOn());

	RUN_STOP_IF_ERROR(g_pBeltA->FixtureAssembleStopCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待治具合盖位阻挡气缸伸出", true);
	}

	m_mapTick["皮带A转动计时"] = GetTickCount();

	RETURN_STATE(&CLogicContinueRun::OnBeltA01, true);
}

CStatus CLogicContinueRun::OnBeltA01()
{
	RUN_STOP_IF_ERROR(g_pBeltA->BeltOn(!VAR_BELTA_B("皮带正转方向")));

	if (GetTickCount() - m_mapTick["皮带A转动计时"] < 3000) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(g_pBeltA->BeltOff());

	RUN_STOP_IF_ERROR(g_pBeltA->MainBoardSetPosCylinderOn());

	m_mapTick["皮带A计时"] = GetTickCount();

	RETURN_STATE(&CLogicContinueRun::OnBeltA02, true);
}

CStatus CLogicContinueRun::OnBeltA02()
{
	if (GetTickCount() - m_mapTick["皮带A计时"] < 500) {
		RETURN_SELF("", false);
	}

	RETURN_STATE(&CLogicContinueRun::OnBeltA03, true);
}

CStatus CLogicContinueRun::OnBeltA03()
{
	RUN_STOP_IF_ERROR(g_pBeltA->MainBoardSetPosCylinderOff());

	RUN_STOP_IF_ERROR(g_pBeltA->MainBoardSetPosCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待主板装配位侧定位气缸缩回", true);
	}

	m_mapTick["皮带A转动计时"] = GetTickCount();

	RETURN_STATE(&CLogicContinueRun::OnBeltA04, true);
}

CStatus CLogicContinueRun::OnBeltA04()
{
	RUN_STOP_IF_ERROR(g_pBeltA->BeltOn(VAR_BELTA_B("皮带正转方向")));

	if (GetTickCount() - m_mapTick["皮带A转动计时"] < 3000) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(g_pBeltA->BeltOff());

	RUN_STOP_IF_ERROR(g_pBeltA->FixtureAssembleSetPosCylinderOn());

	m_mapTick["皮带A计时"] = GetTickCount();

	RETURN_STATE(&CLogicContinueRun::OnBeltA05, true);
}

CStatus CLogicContinueRun::OnBeltA05()
{
	if (GetTickCount() - m_mapTick["皮带A计时"] < 500) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(g_pBeltA->FixtureAssembleSetPosCylinderOff());

	RUN_STOP_IF_ERROR(g_pBeltA->FixtureAssembleSetPosCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具装配位侧定位气缸缩回", true);
	}

	RETURN_STATE(&CLogicContinueRun::OnBeltA06, true);
}

CStatus CLogicContinueRun::OnBeltA06()
{
	RUN_STOP_IF_ERROR(g_pBeltA->FixtureAssembleStopCylinderOff());

	RUN_STOP_IF_ERROR(g_pBeltA->FixtureAssembleStopCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具合盖位阻挡气缸缩回", true);
	}

	m_mapTick["皮带A转动计时"] = GetTickCount();

	RETURN_STATE(&CLogicContinueRun::OnBeltA07, true);
}

CStatus CLogicContinueRun::OnBeltA07()
{
	RUN_STOP_IF_ERROR(g_pBeltA->BeltOn(VAR_BELTA_B("皮带正转方向")));

	if (GetTickCount() - m_mapTick["皮带A转动计时"] < 3000) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(g_pBeltA->BeltOff());

	RETURN_STATE(&CLogicContinueRun::OnBeltA00, true);
}

CStatus CLogicContinueRun::OnBeltB00()
{
	RUN_STOP_IF_ERROR(g_pBeltB->FixtureAssembleStopCylinderOn());

	RUN_STOP_IF_ERROR(g_pBeltB->FixtureAssembleStopCylinderStatus());

	if (m_sRet != "On") {
		RETURN_SELF("等待治具合盖位阻挡气缸伸出", true);
	}

	m_mapTick["皮带B转动计时"] = GetTickCount();

	RETURN_STATE(&CLogicContinueRun::OnBeltB01, true);
}

CStatus CLogicContinueRun::OnBeltB01()
{
	RUN_STOP_IF_ERROR(g_pBeltB->BeltOn(!VAR_BELTB_B("皮带正转方向")));

	if (GetTickCount() - m_mapTick["皮带B转动计时"] < 3000) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(g_pBeltB->BeltOff());

	RUN_STOP_IF_ERROR(g_pBeltB->MainBoardSetPosCylinderOn());

	m_mapTick["皮带B计时"] = GetTickCount();

	RETURN_STATE(&CLogicContinueRun::OnBeltB02, true);
}

CStatus CLogicContinueRun::OnBeltB02()
{
	if (GetTickCount() - m_mapTick["皮带B计时"] < 500) {
		RETURN_SELF("", false);
	}

	RETURN_STATE(&CLogicContinueRun::OnBeltB03, true);
}

CStatus CLogicContinueRun::OnBeltB03()
{
	RUN_STOP_IF_ERROR(g_pBeltB->MainBoardSetPosCylinderOff());

	RUN_STOP_IF_ERROR(g_pBeltB->MainBoardSetPosCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待主板装配位侧定位气缸缩回", true);
	}

	m_mapTick["皮带B转动计时"] = GetTickCount();

	RETURN_STATE(&CLogicContinueRun::OnBeltB04, true);
}

CStatus CLogicContinueRun::OnBeltB04()
{
	RUN_STOP_IF_ERROR(g_pBeltB->BeltOn(VAR_BELTA_B("皮带正转方向")));

	if (GetTickCount() - m_mapTick["皮带B转动计时"] < 3000) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(g_pBeltB->BeltOff());

	RUN_STOP_IF_ERROR(g_pBeltB->FixtureAssembleSetPosCylinderOn());

	m_mapTick["皮带B计时"] = GetTickCount();

	RETURN_STATE(&CLogicContinueRun::OnBeltB05, true);
}

CStatus CLogicContinueRun::OnBeltB05()
{
	if (GetTickCount() - m_mapTick["皮带B计时"] < 500) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(g_pBeltB->FixtureAssembleSetPosCylinderOff());

	RUN_STOP_IF_ERROR(g_pBeltB->FixtureAssembleSetPosCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具装配位侧定位气缸缩回", true);
	}

	RETURN_STATE(&CLogicContinueRun::OnBeltB06, true);
}

CStatus CLogicContinueRun::OnBeltB06()
{
	RUN_STOP_IF_ERROR(g_pBeltB->FixtureAssembleStopCylinderOff());

	RUN_STOP_IF_ERROR(g_pBeltB->FixtureAssembleStopCylinderStatus());

	if (m_sRet != "Off") {
		RETURN_SELF("等待治具合盖位阻挡气缸缩回", true);
	}

	m_mapTick["皮带B转动计时"] = GetTickCount();

	RETURN_STATE(&CLogicContinueRun::OnBeltB07, true);
}

CStatus CLogicContinueRun::OnBeltB07()
{
	RUN_STOP_IF_ERROR(g_pBeltB->BeltOn(VAR_BELTB_B("皮带正转方向")));

	if (GetTickCount() - m_mapTick["皮带B转动计时"] < 3000) {
		RETURN_SELF("", false);
	}

	RUN_STOP_IF_ERROR(g_pBeltB->BeltOff());

	RETURN_STATE(&CLogicContinueRun::OnBeltB00, true);
}
