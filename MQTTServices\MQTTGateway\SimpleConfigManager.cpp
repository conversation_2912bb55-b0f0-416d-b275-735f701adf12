#include "SimpleConfigManager.h"
#include <QFile>
#include <QDir>
#include <QJsonParseError>
#include <QDebug>
#include <algorithm>

SimpleConfigManager::SimpleConfigManager() {
    // 初始化默认配置
}

bool SimpleConfigManager::LoadAllConfigs(const std::string& configDir) {
    // 尝试多个可能的配置目录路径
    std::vector<std::string> possiblePaths = {
        configDir,
        "config",
        "../config",
        "../../config",
        "../../../config",
        "./MQTTServices/build/MQTTGateway/Debug/config",
        "D:/ASUS/Desktop/FStation/MQTTServices/build/MQTTGateway/Debug/config"
    };

    std::string actualConfigDir;
    bool foundConfig = false;

    for (const auto& path : possiblePaths) {
        QDir dir(QString::fromStdString(path));
        if (dir.exists()) {
            actualConfigDir = path;
            foundConfig = true;
            qInfo() << "找到配置目录:" << QString::fromStdString(path);
            break;
        }
    }

    if (!foundConfig) {
        qCritical() << "配置目录不存在，已尝试以下路径:";
        for (const auto& path : possiblePaths) {
            qCritical() << "  -" << QString::fromStdString(path);
        }
        return false;
    }

    std::string basePath = actualConfigDir + "/";
    
    // 加载所有配置文件
    bool success = true;
    success &= LoadGatewayConfig(basePath + "gateway.json");
    success &= LoadDataPointsConfig(basePath + "data_points.json");
    success &= LoadEventsConfig(basePath + "events.json");
    success &= LoadCommandsConfig(basePath + "commands.json");
    success &= LoadSubscriptionsConfig(basePath + "subscriptions.json");
    success &= LoadPublishTopicsConfig(basePath + "publish_topics.json");
    
    if (success) {
        success = ValidateConfigs();
    }
    
    return success;
}

bool SimpleConfigManager::LoadGatewayConfig(const std::string& configPath) {
    QJsonDocument doc = LoadJsonFile(configPath);
    if (doc.isNull()) {
        return false;
    }
    
    QJsonObject root = doc.object();
    
    // 加载设备配置
    QJsonObject device = root["device"].toObject();
    m_gatewayConfig.device.deviceId = device["deviceId"].toString().toStdString();
    m_gatewayConfig.device.secret = device["secret"].toString().toStdString();
    m_gatewayConfig.device.userName = device["userName"].toString().toStdString();
    
    // 加载MQTT配置
    QJsonObject mqtt = root["mqtt"].toObject();
    m_gatewayConfig.mqtt.broker = mqtt["broker"].toString().toStdString();
    m_gatewayConfig.mqtt.port = mqtt["port"].toInt();
    m_gatewayConfig.mqtt.version = mqtt["version"].toString().toStdString();
    m_gatewayConfig.mqtt.keepAliveInterval = mqtt["keepAliveInterval"].toInt();
    m_gatewayConfig.mqtt.cleanSession = mqtt["cleanSession"].toBool();
    m_gatewayConfig.mqtt.automaticReconnect = mqtt["automaticReconnect"].toBool();
    m_gatewayConfig.mqtt.connectionTimeout = mqtt["connectionTimeout"].toInt();
    
    // 加载Socket配置
    QJsonObject socket = root["socket"].toObject();
    m_gatewayConfig.socket.listenPort = socket["listenPort"].toInt();
    m_gatewayConfig.socket.bindAddress = socket["bindAddress"].toString().toStdString();
    m_gatewayConfig.socket.maxConnections = socket["maxConnections"].toInt();
    m_gatewayConfig.socket.keepAlive = socket["keepAlive"].toBool();
    
    // 加载功能配置
    QJsonObject features = root["features"].toObject();
    m_gatewayConfig.features.defaultReportCycleS = features["defaultReportCycleS"].toInt();
    m_gatewayConfig.features.requestDataEnabled = features["requestDataEnabled"].toBool();
    m_gatewayConfig.features.propertyGetEnabled = features["propertyGetEnabled"].toBool();
    m_gatewayConfig.features.commandsEnabled = features["commandsEnabled"].toBool();
    m_gatewayConfig.features.eventsEnabled = features["eventsEnabled"].toBool();

    // 加载Performance配置
    QJsonObject performance = root["performance"].toObject();
    m_gatewayConfig.performance.maxQueueSize = performance["maxQueueSize"].toInt();
    m_gatewayConfig.performance.publishTimeoutS = performance["publishTimeoutS"].toInt();
    m_gatewayConfig.performance.responseTimeoutS = performance["responseTimeoutS"].toInt();
    m_gatewayConfig.performance.maxRetries = performance["maxRetries"].toInt();

    qInfo() << "网关基础配置加载成功 - DeviceId:" << QString::fromStdString(m_gatewayConfig.device.deviceId);
    qInfo() << "性能配置 - 发布超时:" << m_gatewayConfig.performance.publishTimeoutS << "s"
            << "响应超时:" << m_gatewayConfig.performance.responseTimeoutS << "s";
    return true;
}

bool SimpleConfigManager::LoadDataPointsConfig(const std::string& configPath) {
    QJsonDocument doc = LoadJsonFile(configPath);
    if (doc.isNull()) {
        return false;
    }
    
    QJsonObject root = doc.object();
    QJsonObject dataPoints = root["data_points"].toObject();
    
    m_dataPoints.clear();
    
    // 加载各类数据点
    QStringList categories = {"device_status", "hardware_monitor", "production_trace", "fault_info", "measurement_data"};
    
    for (const QString& category : categories) {
        QJsonArray points = dataPoints[category].toArray();
        for (const auto& point : points) {
            QJsonObject pointObj = point.toObject();
            DataPointConfig config;
            config.code = pointObj["code"].toString().toStdString();
            config.name = pointObj["name"].toString().toStdString();
            config.type = pointObj["type"].toString().toStdString();
            config.source = pointObj["source"].toString().toStdString();
            config.unit = pointObj["unit"].toString().toStdString();
            config.category = category.toStdString();
            config.readonly = pointObj["readonly"].toBool();
            
            m_dataPoints.push_back(config);
        }
    }
    
    qInfo() << "数据点配置加载成功，共" << m_dataPoints.size() << "个数据点";
    return true;
}

bool SimpleConfigManager::LoadEventsConfig(const std::string& configPath) {
    QJsonDocument doc = LoadJsonFile(configPath);
    if (doc.isNull()) {
        return false;
    }

    QJsonObject root = doc.object();
    QJsonArray events = root["events"].toArray();

    m_events.clear();

    for (const auto& event : events) {
        QJsonObject eventObj = event.toObject();
        EventConfig config;
        config.type = eventObj["type"].toString().toStdString();
        config.name = eventObj["name"].toString().toStdString();
        config.description = eventObj["description"].toString().toStdString();
        config.qos = eventObj["qos"].toInt();
        config.requiresResponse = eventObj["requires_response"].toBool();
        config.timeoutMs = eventObj["timeout_ms"].toInt();
        config.maxRetries = eventObj["max_retries"].toInt();
        config.requiresRealtimeData = eventObj["requires_realtime_data"].toBool();

        // 加载属性列表
        QJsonArray properties = eventObj["properties"].toArray();
        for (const auto& prop : properties) {
            config.properties.push_back(prop.toString().toStdString());
        }

        m_events.push_back(config);
    }

    qInfo() << "事件配置加载成功，共" << m_events.size() << "种事件类型";
    return true;
}

bool SimpleConfigManager::LoadCommandsConfig(const std::string& configPath) {
    QJsonDocument doc = LoadJsonFile(configPath);
    if (doc.isNull()) {
        return false;
    }

    QJsonObject root = doc.object();
    QJsonArray commands = root["commands"].toArray();

    m_commands.clear();

    for (const auto& command : commands) {
        QJsonObject commandObj = command.toObject();
        CommandConfig config;
        config.type = commandObj["type"].toString().toStdString();
        config.name = commandObj["name"].toString().toStdString();
        config.description = commandObj["description"].toString().toStdString();
        config.responseRequired = commandObj["response_required"].toBool();
        config.timeoutMs = commandObj["timeout_ms"].toInt();

        // 加载属性列表
        QJsonArray properties = commandObj["properties"].toArray();
        for (const auto& prop : properties) {
            config.properties.push_back(prop.toString().toStdString());
        }

        m_commands.push_back(config);
    }

    qInfo() << "命令配置加载成功，共" << m_commands.size() << "种命令类型";
    return true;
}

bool SimpleConfigManager::LoadSubscriptionsConfig(const std::string& configPath) {
    QJsonDocument doc = LoadJsonFile(configPath);
    if (doc.isNull()) {
        return false;
    }

    QJsonObject root = doc.object();
    QJsonArray subscriptions = root["mqtt_subscriptions"].toArray();

    m_subscriptions.clear();

    for (const auto& subscription : subscriptions) {
        QJsonObject subObj = subscription.toObject();
        SubscriptionConfig config;
        config.name = subObj["name"].toString().toStdString();
        config.topicTemplate = subObj["topic_template"].toString().toStdString();
        config.qos = subObj["qos"].toInt();
        config.enabled = subObj["enabled"].toBool();
        config.handler = subObj["handler"].toString().toStdString();
        config.description = subObj["description"].toString().toStdString();

        m_subscriptions.push_back(config);
    }

    qInfo() << "订阅配置加载成功，共" << m_subscriptions.size() << "个订阅主题";
    return true;
}

bool SimpleConfigManager::LoadPublishTopicsConfig(const std::string& configPath) {
    QJsonDocument doc = LoadJsonFile(configPath);
    if (doc.isNull()) {
        return false;
    }

    QJsonObject root = doc.object();
    QJsonArray publishTopics = root["mqtt_publish_topics"].toArray();

    m_publishTopics.clear();

    for (const auto& topic : publishTopics) {
        QJsonObject topicObj = topic.toObject();
        PublishTopicConfig config;
        config.name = topicObj["name"].toString().toStdString();
        config.topicTemplate = topicObj["topic_template"].toString().toStdString();
        config.qos = topicObj["qos"].toInt();
        config.retain = topicObj["retain"].toBool();
        config.description = topicObj["description"].toString().toStdString();
        config.messageType = topicObj["message_type"].toString().toStdString();

        m_publishTopics.push_back(config);
    }

    qInfo() << "发布主题配置加载成功，共" << m_publishTopics.size() << "个发布主题";
    return true;
}

// 查找方法实现
const EventConfig* SimpleConfigManager::FindEventConfig(const std::string& eventType) const {
    auto it = std::find_if(m_events.begin(), m_events.end(),
                          [&eventType](const EventConfig& config) {
                              return config.type == eventType;
                          });
    return (it != m_events.end()) ? &(*it) : nullptr;
}

const CommandConfig* SimpleConfigManager::FindCommandConfig(const std::string& commandType) const {
    auto it = std::find_if(m_commands.begin(), m_commands.end(),
                          [&commandType](const CommandConfig& config) {
                              return config.type == commandType;
                          });
    return (it != m_commands.end()) ? &(*it) : nullptr;
}

const SubscriptionConfig* SimpleConfigManager::FindSubscriptionConfig(const std::string& name) const {
    auto it = std::find_if(m_subscriptions.begin(), m_subscriptions.end(),
                          [&name](const SubscriptionConfig& config) {
                              return config.name == name;
                          });
    return (it != m_subscriptions.end()) ? &(*it) : nullptr;
}

const PublishTopicConfig* SimpleConfigManager::FindPublishTopicConfig(const std::string& name) const {
    auto it = std::find_if(m_publishTopics.begin(), m_publishTopics.end(),
                          [&name](const PublishTopicConfig& config) {
                              return config.name == name;
                          });
    return (it != m_publishTopics.end()) ? &(*it) : nullptr;
}

// 数据点查询方法
std::vector<DataPointConfig> SimpleConfigManager::GetDataPointsByCategory(const std::string& category) const {
    std::vector<DataPointConfig> result;
    std::copy_if(m_dataPoints.begin(), m_dataPoints.end(), std::back_inserter(result),
                [&category](const DataPointConfig& config) {
                    return config.category == category;
                });
    return result;
}

std::vector<DataPointConfig> SimpleConfigManager::GetDataPointsBySource(const std::string& source) const {
    std::vector<DataPointConfig> result;
    std::copy_if(m_dataPoints.begin(), m_dataPoints.end(), std::back_inserter(result),
                [&source](const DataPointConfig& config) {
                    return config.source == source;
                });
    return result;
}

// 配置验证
bool SimpleConfigManager::ValidateConfigs() const {
    // 验证基础配置
    if (m_gatewayConfig.device.deviceId.empty()) {
        qCritical() << "设备ID不能为空";
        return false;
    }

    if (m_gatewayConfig.mqtt.broker.empty()) {
        qCritical() << "MQTT Broker地址不能为空";
        return false;
    }

    if (m_dataPoints.empty()) {
        qCritical() << "数据点配置不能为空";
        return false;
    }

    if (m_events.empty()) {
        qCritical() << "事件配置不能为空";
        return false;
    }

    if (m_subscriptions.empty()) {
        qCritical() << "订阅配置不能为空";
        return false;
    }

    if (m_publishTopics.empty()) {
        qCritical() << "发布主题配置不能为空";
        return false;
    }

    qInfo() << "配置验证通过";
    return true;
}

// 工具方法
QJsonDocument SimpleConfigManager::LoadJsonFile(const std::string& filePath) const {
    QFile file(QString::fromStdString(filePath));
    if (!file.open(QIODevice::ReadOnly)) {
        qCritical() << "无法打开配置文件:" << QString::fromStdString(filePath);
        return QJsonDocument();
    }

    QByteArray data = file.readAll();
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);

    if (error.error != QJsonParseError::NoError) {
        qCritical() << "JSON解析失败:" << QString::fromStdString(filePath)
                   << "错误:" << error.errorString();
        return QJsonDocument();
    }

    return doc;
}

bool SimpleConfigManager::ValidateJsonStructure(const QJsonObject& json, const std::string& configType) const {
    // 基础结构验证
    if (!json.contains("version")) {
        qCritical() << "配置文件缺少version字段:" << QString::fromStdString(configType);
        return false;
    }

    return true;
}
