﻿#pragma once

#include "resource.h"

#include "ColorList.h"
using namespace yzBase;

// CDialogWarnCount 对话框

class CDialogWarnCount : public CDialogEx
{
	DECLARE_DYNAMIC(CDialogWarnCount)

public:
	CDialogWarnCount(CWnd* pParent = NULL);   // 标准构造函数
	virtual ~CDialogWarnCount();

// 对话框数据
	enum { IDD = IDD_DIALOG_WARN_COUNT };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持
	virtual BOOL OnInitDialog();

	DECLARE_MESSAGE_MAP()
	afx_msg void OnTimer(UINT_PTR nIDEvent);

public:
	CString Load();

protected:
	CColorList			m_list;
	CImageList			m_imgList;
	unsigned int		m_nToday;
};
