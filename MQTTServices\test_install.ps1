# MQTT网关安装测试脚本
# 测试安装包的完整性

Write-Host "=== MQTT网关安装测试 ===" -ForegroundColor Green

$InstallDir = "./install_test"
$BinDir = "$InstallDir/bin"
$ConfigDir = "$InstallDir/share/fstation/config"

# 检查目录结构
Write-Host "`n1. 检查目录结构..." -ForegroundColor Yellow
$RequiredDirs = @($BinDir, $ConfigDir)
foreach ($Dir in $RequiredDirs) {
    if (Test-Path $Dir) {
        Write-Host "✓ $Dir 存在" -ForegroundColor Green
    } else {
        Write-Host "✗ $Dir 不存在" -ForegroundColor Red
    }
}

# 检查可执行文件
Write-Host "`n2. 检查可执行文件..." -ForegroundColor Yellow
$ExeFile = "$BinDir/MQTTGateway.exe"
if (Test-Path $ExeFile) {
    $FileInfo = Get-Item $ExeFile
    Write-Host "✓ MQTTGateway.exe 存在 (大小: $($FileInfo.Length) 字节)" -ForegroundColor Green
} else {
    Write-Host "✗ MQTTGateway.exe 不存在" -ForegroundColor Red
}

# 检查配置文件
Write-Host "`n3. 检查配置文件..." -ForegroundColor Yellow
$ConfigFiles = @(
    "gateway.json",
    "data_points.json", 
    "events.json",
    "commands.json",
    "subscriptions.json",
    "publish_topics.json"
)

foreach ($ConfigFile in $ConfigFiles) {
    $FilePath = "$ConfigDir/$ConfigFile"
    if (Test-Path $FilePath) {
        Write-Host "✓ $ConfigFile 存在" -ForegroundColor Green
    } else {
        Write-Host "✗ $ConfigFile 不存在" -ForegroundColor Red
    }
}

# 检查依赖库
Write-Host "`n4. 检查依赖库..." -ForegroundColor Yellow
$RequiredDlls = @(
    "paho-mqtt3as.dll",
    "paho-mqttpp3.dll", 
    "libcrypto-3-x64.dll",
    "libssl-3-x64.dll",
    "vcruntime140.dll",
    "msvcp140.dll"
)

foreach ($Dll in $RequiredDlls) {
    $DllPath = "$BinDir/$Dll"
    if (Test-Path $DllPath) {
        Write-Host "✓ $Dll 存在" -ForegroundColor Green
    } else {
        Write-Host "✗ $Dll 不存在" -ForegroundColor Red
    }
}

# 检查Qt库
Write-Host "`n5. 检查Qt库..." -ForegroundColor Yellow
$QtDlls = @(
    "Qt6Core.dll",
    "Qt6Network.dll"
)

$QtMissing = 0
foreach ($QtDll in $QtDlls) {
    $QtPath = "$BinDir/$QtDll"
    if (Test-Path $QtPath) {
        Write-Host "✓ $QtDll 存在" -ForegroundColor Green
    } else {
        Write-Host "✗ $QtDll 不存在" -ForegroundColor Red
        $QtMissing++
    }
}

# 检查platforms插件
$PlatformsDir = "$BinDir/platforms"
if (Test-Path $PlatformsDir) {
    Write-Host "✓ platforms插件目录存在" -ForegroundColor Green
} else {
    Write-Host "✗ platforms插件目录不存在" -ForegroundColor Red
}

# 总结
Write-Host "`n=== 测试总结 ===" -ForegroundColor Green
if ($QtMissing -gt 0) {
    Write-Host "⚠ 缺少 $QtMissing 个Qt库文件，程序可能无法运行" -ForegroundColor Yellow
    Write-Host "建议手动复制Qt库文件或使用windeployqt工具" -ForegroundColor Yellow
} else {
    Write-Host "✓ 所有必要文件都已安装" -ForegroundColor Green
}

Write-Host "`n安装目录: $(Resolve-Path $InstallDir)" -ForegroundColor Cyan
