# 科瑞F站装板机数采清单（专业版）

---

## 目录

1. 设备运行状态与基础信息
2. 设备硬件资源监控
3. 生产过程与主板追溯参数
4. 故障与报警信息
5. 设备参数与测高数据

---

## 1. 设备运行状态与基础信息

| 参数名称   | 数据代码 | 是否存在 | 数据来源 | 需求 | 数据定义           | 示例          | 数据类型 | 单位 | 访问权限 | 取值范围 | 备注 |
| ---------- | -------- | -------- | -------- | ---- | ------------------ | ------------- | -------- | ---- | -------- | -------- | ---- |
| 正常生产   | A00002   | 否       | 设备获取 | 是   | 正常运行状态       | True or False | bool     |      | 只读     |          |      |
| 运行暂停   | A00003   | 否       | 设备获取 | 是   | 暂停状态           | True or False | bool     |      | 只读     |          |      |
| 设备故障   | A00004   | 是       | 设备获取 | 是   | 故障状态           | True or False | bool     |      | 只读     |          |      |
| 待机状态   | A00006   | 是       | 设备获取 | 是   | 待出、待料         | True or False | bool     |      | 只读     |          |      |
| 机型程序名 | A00010   | 是       | 设备获取 | 是   | 当前生产程序名     | 2AC112-0      | string   |      | 只读     |          |      |
| 运行周期   | A00014   | 是       | 设备获取 | 是   | 单个生产周期（秒） | 25.3          | decimal  | s    | 只读     |          |      |
| 累计产能   | A00015   | 是       | 设备获取 | 是   | 每班累计产能       | 3500          | int      | PCS  | 只读     |          |      |

---

## 2. 设备硬件资源监控

| 参数名称     | 数据代码 | 是否存在 | 数据来源  | 需求 | 数据定义     | 示例 | 数据类型 | 单位 | 访问权限 | 备注 |
| ------------ | -------- | -------- | --------- | ---- | ------------ | ---- | -------- | ---- | -------- | ---- |
| 内存容量     | A00020   | 否       | Agent获取 | 是   | 内存容量     |      | int      | MB   | 只读     |      |
| 磁盘容量     | A00021   | 否       | Agent获取 | 是   | 磁盘容量     |      | int      | GB   | 只读     |      |
| C盘容量      | A00022   | 否       | Agent获取 | 是   | C盘容量      |      | int      | GB   | 只读     |      |
| D盘容量      | A00023   | 否       | Agent获取 | 是   | D盘容量      |      | int      | GB   | 只读     |      |
| E盘容量      | A00024   | 否       | Agent获取 | 是   | E盘容量      |      | int      | GB   | 只读     |      |
| 磁盘剩余容量 | A00025   | 否       | Agent获取 | 是   | 磁盘剩余容量 |      | int      | GB   | 只读     |      |
| C盘剩余容量  | A00026   | 否       | Agent获取 | 是   | C盘剩余容量  |      | int      | GB   | 只读     |      |
| D盘剩余容量  | A00027   | 否       | Agent获取 | 是   | D盘剩余容量  |      | int      | GB   | 只读     |      |
| E盘剩余容量  | A00028   | 否       | Agent获取 | 是   | E盘剩余容量  |      | int      | GB   | 只读     |      |
| CPU利用率    | A00029   | 否       | Agent获取 | 是   | CPU利用率    |      | float    | %    | 只读     |      |
| 内存利用率   | A00030   | 否       | Agent获取 | 是   | 内存利用率   |      | float    | %    | 只读     |      |

---

## 3. 生产过程与主板追溯参数

| 参数名称     | 数据代码 | 是否存在 | 数据来源 | 需求 | 数据定义                | 示例                      | 数据类型 | 单位 | 访问权限 | 备注         |
| ------------ | -------- | -------- | -------- | ---- | ----------------------- | ------------------------- | -------- | ---- | -------- | ------------ |
| 主板SN号     | C00001   | 是       | 设备获取 | 是   | 当前扫描主板SN          | 2001000168005843127309215 | string   |      | 只读     | MQTT接入协议 |
| 设备资产编码 | C00002   | 是       | 设备获取 | 是   | 当前设备SN              | A******                   | string   |      | 只读     |              |
| 轨道号       | C00003   | 是       | 设备获取 | 是   | 当前主板生产轨道号      | 1/2/3/4...                | int      |      | 只读     |              |
| 面别         | C00004   | 是       | 设备获取 | 是   | 当前主板生产面别        | B/T                       | enum     |      | 只读     |              |
| 程序名       | C00005   | 是       | 设备获取 | 是   | 当前主板生产程序名      | 2AC112-0                  | string   |      | 只读     |              |
| 程序路径     | C00006   | 是       | 服务器   | 是   | 服务器下发程序路径      |                           | string   |      | 只读     |              |
| 实际加工周期 | C00007   | 否       | 设备获取 | 是   | 当前主板实际生产时间    | 25.153                    | decimal  | s    | 只读     |              |
| 等前时间     | C00008   | 否       | 设备获取 | 是   | 等待PCB进入设备的时间   | 8.103                     | decimal  | s    | 只读     |              |
| 等后时间     | C00009   | 否       | 设备获取 | 是   | PCB到达出口后到离开时间 | 8.103                     | decimal  | s    | 只读     |              |

---

## 4. 故障与报警信息

| 参数名称     | 数据代码 | 是否存在 | 数据来源 | 需求 | 数据定义             | 示例                    | 数据类型 | 单位 | 访问权限 | 备注 |
| ------------ | -------- | -------- | -------- | ---- | -------------------- | ----------------------- | -------- | ---- | -------- | ---- |
| 故障代码     | C00010   | 否       | 设备获取 | 是   | 故障时的设备故障代码 | A00001                  | string   |      | 只读     |      |
| 故障信息     | C00011   | 否       | 设备获取 | 是   | 故障时的设备故障信息 | 机器人连接失败          | string   |      | 只读     |      |
| 故障开始时间 | C00012   | 否       | 设备获取 | 是   | 故障发生时间         | 2024-01-11 16:02:21.165 | datetime |      | 只读     |      |
| 故障结束时间 | C00013   | 否       | 设备获取 | 是   | 故障结束时间         | 2024-01-11 16:02:21.165 | datetime |      | 只读     |      |
| 故障时间     | C00014   | 否       | 设备获取 | 是   | 故障持续时间（秒）   | 22.112                  | decimal  | s    | 只读     |      |

---

## 5. 设备参数与测高数据

| 参数名称       | 数据代码 | 数据类别 | 是否存在 | 数据来源 | 需求 | 数据定义 | 备注 |
| -------------- | -------- | -------- | -------- | -------- | ---- | -------- | ---- |
| 1轨测高实际值1 | B40008   | 设备参数 | 是       | 设备获取 | 是   |          |      |
| 1轨测高实际值2 | B40009   | 设备参数 | 是       | 设备获取 | 是   |          |      |
| 1轨测高实际值3 | B40010   | 设备参数 | 是       | 设备获取 | 是   |          |      |
| 1轨测高实际值4 | B40011   | 设备参数 | 是       | 设备获取 | 是   |          |      |
| 2轨测高实际值1 | B40012   | 设备参数 | 是       | 设备获取 | 是   |          |      |
| 2轨测高实际值2 | B40013   | 设备参数 | 是       | 设备获取 | 是   |          |      |
| 2轨测高实际值3 | B40014   | 设备参数 | 是       | 设备获取 | 是   |          |      |
| 2轨测高实际值4 | B40015   | 设备参数 | 是       | 设备获取 | 是   |          |      |
| 测高设定值     | B40016   | 设备参数 | 是       | 设备获取 | 是   |          |      |

---

> 本清单为F站装板机数采参数全表，适用于SCADA平台数据采集、设备监控、生产追溯、故障分析等场景。所有参数建议严格按照本表定义进行采集与上报，便于平台统一管理与后续扩展。
