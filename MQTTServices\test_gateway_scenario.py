#!/usr/bin/env python3
"""
模拟网关场景的MQTT性能测试
测试大消息和复杂主题的性能
"""

import paho.mqtt.client as mqtt
import json
import time
import threading

# MQTT 配置
MQTT_BROKER = "192.168.88.44"
MQTT_PORT = 1883
DEVICE_ID = "A320021760"
CLIENT_ID = f"gateway_test_{int(time.time())}"

class GatewayScenarioTester:
    def __init__(self):
        self.results = []
        self.lock = threading.Lock()
        self.client = None
        
    def on_connect(self, client, userdata, flags, rc):
        if rc == 0:
            print(f"✅ 连接成功到 EMQX")
        else:
            print(f"❌ 连接失败: {rc}")
    
    def on_publish(self, client, userdata, mid):
        """发布完成回调"""
        end_time = time.time()
        with self.lock:
            for result in self.results:
                if result['mid'] == mid and 'end_time' not in result:
                    result['end_time'] = end_time
                    result['duration'] = (end_time - result['start_time']) * 1000
                    print(f"✅ 消息 {result['test_id']} 完成: {result['duration']:.1f}ms")
                    break
    
    def create_large_property_response(self):
        """创建类似网关的大型属性响应消息"""
        services = []
        
        # DefaultService - 模拟41个数据点
        default_service = {
            "service_id": "DefaultService",
            "event_time": "2025-07-31 02:20:00.000",
            "properties": {}
        }
        
        # 添加41个数据点
        data_points = [
            "A00002", "A00003", "A00004", "A00006", "A00010", "A00011", "A00012", "A00013", "A00014", "A00015",
            "B00001", "B00002", "B00003", "B00004", "B00005", "B00006", "B00007", "B00008", "B00009", "B00010",
            "C00001", "C00002", "C00003", "C00004", "C00005", "C00006", "C00007", "C00008", "C00009", "C00010",
            "D00001", "D00002", "D00003", "D00004", "D00005", "D00006", "D00007", "D00008", "D00009", "D00010",
            "E00001"
        ]
        
        for dp in data_points:
            if dp.startswith('A') or dp.startswith('E'):
                default_service["properties"][dp] = False
            elif dp.startswith('B') or dp.startswith('C') or dp.startswith('D'):
                default_service["properties"][dp] = 0
            else:
                default_service["properties"][dp] = ""
        
        services.append(default_service)
        
        # StatusService
        status_service = {
            "service_id": "StatusService",
            "event_time": "2025-07-31 02:20:00.000",
            "properties": {
                "device_status_code": "",
                "device_status_desc": "",
                "device_status_enum": "standby"
            }
        }
        services.append(status_service)
        
        return {"services": services}
    
    def test_gateway_property_response(self, count=5):
        """测试网关属性响应场景"""
        print(f"\n=== 测试网关属性响应场景 ===")
        
        # 清空结果
        with self.lock:
            self.results = []
        
        # 创建大型响应消息
        large_response = self.create_large_property_response()
        payload = json.dumps(large_response, separators=(',', ':'))  # 紧凑格式
        
        print(f"📊 测试参数:")
        print(f"   消息大小: {len(payload)} 字节")
        print(f"   数据点数: {len(large_response['services'][0]['properties'])}")
        print(f"   服务数: {len(large_response['services'])}")
        
        # 发送测试消息
        for i in range(count):
            request_id = f"gateway_test_{int(time.time() * 1000)}_{i}"
            topic = f"$oc/devices/{DEVICE_ID}/sys/properties/get/response/request_id={request_id}"
            
            start_time = time.time()
            result = self.client.publish(topic, payload, qos=1)  # 使用QoS 1，模拟网关
            
            with self.lock:
                self.results.append({
                    'mid': result.mid,
                    'start_time': start_time,
                    'test_id': i,
                    'request_id': request_id,
                    'topic': topic,
                    'size': len(payload)
                })
            
            print(f"📤 发送消息 {i}: RequestId={request_id}")
            time.sleep(1)  # 1秒间隔
        
        # 等待所有消息完成
        print(f"⏳ 等待 {count} 个大消息完成...")
        time.sleep(10)
        
        # 分析结果
        self.analyze_gateway_results()
    
    def analyze_gateway_results(self):
        """分析网关场景测试结果"""
        with self.lock:
            completed = [r for r in self.results if 'duration' in r]
            total = len(self.results)
        
        if not completed:
            print(f"❌ 没有消息完成")
            return
        
        durations = [r['duration'] for r in completed]
        
        print(f"\n📊 网关场景性能统计:")
        print(f"   总消息数: {total}")
        print(f"   完成数: {len(completed)}")
        print(f"   成功率: {len(completed)/total*100:.1f}%")
        print(f"   平均延迟: {sum(durations)/len(durations):.1f} ms")
        print(f"   最小延迟: {min(durations):.1f} ms")
        print(f"   最大延迟: {max(durations):.1f} ms")
        
        # 显示每个消息的详细信息
        print(f"\n📋 详细结果:")
        for r in completed:
            print(f"   消息 {r['test_id']}: {r['duration']:.1f}ms")
        
        # 未完成的消息
        incomplete = [r for r in self.results if 'duration' not in r]
        if incomplete:
            print(f"\n⚠️ 未完成的消息:")
            for r in incomplete:
                elapsed = (time.time() - r['start_time']) * 1000
                print(f"   消息 {r['test_id']}: 已等待 {elapsed:.1f}ms")
    
    def run_test(self):
        """运行测试"""
        print("=== 网关场景MQTT性能测试 ===")
        print(f"目标: {MQTT_BROKER}:{MQTT_PORT}")
        
        # 创建MQTT客户端
        self.client = mqtt.Client(CLIENT_ID)
        self.client.on_connect = self.on_connect
        self.client.on_publish = self.on_publish
        
        try:
            # 连接到MQTT Broker
            self.client.connect(MQTT_BROKER, MQTT_PORT, 60)
            self.client.loop_start()
            
            time.sleep(2)  # 等待连接建立
            
            # 测试网关属性响应场景
            self.test_gateway_property_response(3)
            
            print("\n=== 分析结论 ===")
            print("如果这个测试显示延迟很低（<100ms），那么问题可能在于：")
            print("1. 网关的MQTT客户端配置")
            print("2. 网关的消息处理逻辑")
            print("3. 网关使用的MQTT库版本或配置")
            print("4. 网关的线程模型或异步处理")
            
        except Exception as e:
            print(f"❌ 测试异常: {e}")
        finally:
            if self.client:
                self.client.loop_stop()
                self.client.disconnect()

def main():
    tester = GatewayScenarioTester()
    tester.run_test()

if __name__ == "__main__":
    main()
