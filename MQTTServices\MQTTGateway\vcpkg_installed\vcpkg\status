Package: vcpkg-cmake-config
Version: 2024-05-23
Architecture: x64-windows
Multi-Arch: same
Abi: 8b1ae8f4be6cff022eadef50d38cffca5e75f23d30fc7e280b912b164b7e8ea1
Status: install ok installed

Package: vcpkg-cmake
Version: 2024-04-23
Architecture: x64-windows
Multi-Arch: same
Abi: 8112fdacac7427b4feea02a5a660bd2fb97e5b898a970d195ff6370d43a794fb
Status: install ok installed

Package: fmt
Version: 11.0.2
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 6647f91c0c936ab9aa0fb3dac98e312815d7c54d746fe6416fe714199b8843b9
Description: {fmt} is an open-source formatting library providing a fast and safe alternative to C stdio and C++ iostreams.
Status: purge ok not-installed

Package: jsoncpp
Version: 1.9.6
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: d985007e613515a66e09a90b4c9da6e5edfc9ebffb5b89d4aed50b87a19eaeb1
Description: JsonCpp is a C++ library that allows manipulating JSON values, including serialization and deserialization to and from strings. It can also preserve existing comment in unserialization/serialization steps, making it a convenient format to store user input files.
Status: purge ok not-installed

Package: vcpkg-cmake-get-vars
Version: 2025-05-29
Depends: vcpkg-cmake
Architecture: x64-windows
Multi-Arch: same
Abi: 6fc3112bb2922bda535f50cc6b50bea5ed7333a064b036e2edcc1e25bb0afce9
Status: install ok installed

Package: openssl
Version: 3.5.0
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config, vcpkg-cmake-get-vars
Architecture: x64-windows
Multi-Arch: same
Abi: 253bb4bf9b62871c6de32cf1f4c52f2ae7a93f34adc6df60993705d9b45216d6
Description: OpenSSL is an open source project that provides a robust, commercial-grade, and full-featured toolkit for the Transport Layer Security (TLS) and Secure Sockets Layer (SSL) protocols. It is also a general-purpose cryptography library.
Status: install ok installed

Package: paho-mqtt
Version: 1.3.14
Depends: openssl, vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: e04391966b12706516130ee4c3638115c5aea5fff27cbc92a9901830db5167b9
Description: Paho project provides open-source client implementations of MQTT and MQTT-SN messaging protocols aimed at new, existing, and emerging applications for the Internet of Things
Status: install ok installed

Package: paho-mqttpp3
Version: 1.5.2
Depends: paho-mqtt, vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: b007d5291c8dfd0a13536928c2bc1560b13ebf8780d8468612fc9cf8d93d5193
Description: Paho project provides open-source C++ wrapper for Paho C library
Default-Features: ssl
Status: install ok installed

Package: paho-mqttpp3
Feature: ssl
Depends: openssl
Architecture: x64-windows
Multi-Arch: same
Description: Build with SSL support
Status: install ok installed

Package: spdlog
Version: 1.15.3
Depends: fmt, vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 1f8a15631737c8fdbeb2a6ffe5bd886cf439c7839d7dd101fbc19e50c2f3eefc
Description: Very fast, header-only/compiled, C++ logging library.
Status: purge ok not-installed

