x64-windows-static-md/
x64-windows-static-md/debug/
x64-windows-static-md/debug/lib/
x64-windows-static-md/debug/lib/paho-mqttpp3-static.lib
x64-windows-static-md/include/
x64-windows-static-md/include/mqtt/
x64-windows-static-md/include/mqtt/async_client.h
x64-windows-static-md/include/mqtt/buffer_ref.h
x64-windows-static-md/include/mqtt/buffer_view.h
x64-windows-static-md/include/mqtt/callback.h
x64-windows-static-md/include/mqtt/client.h
x64-windows-static-md/include/mqtt/connect_options.h
x64-windows-static-md/include/mqtt/create_options.h
x64-windows-static-md/include/mqtt/delivery_token.h
x64-windows-static-md/include/mqtt/disconnect_options.h
x64-windows-static-md/include/mqtt/event.h
x64-windows-static-md/include/mqtt/exception.h
x64-windows-static-md/include/mqtt/export.h
x64-windows-static-md/include/mqtt/iaction_listener.h
x64-windows-static-md/include/mqtt/iasync_client.h
x64-windows-static-md/include/mqtt/iclient_persistence.h
x64-windows-static-md/include/mqtt/message.h
x64-windows-static-md/include/mqtt/platform.h
x64-windows-static-md/include/mqtt/properties.h
x64-windows-static-md/include/mqtt/reason_code.h
x64-windows-static-md/include/mqtt/response_options.h
x64-windows-static-md/include/mqtt/server_response.h
x64-windows-static-md/include/mqtt/ssl_options.h
x64-windows-static-md/include/mqtt/string_collection.h
x64-windows-static-md/include/mqtt/subscribe_options.h
x64-windows-static-md/include/mqtt/thread_queue.h
x64-windows-static-md/include/mqtt/token.h
x64-windows-static-md/include/mqtt/topic.h
x64-windows-static-md/include/mqtt/topic_matcher.h
x64-windows-static-md/include/mqtt/types.h
x64-windows-static-md/include/mqtt/will_options.h
x64-windows-static-md/lib/
x64-windows-static-md/lib/paho-mqttpp3-static.lib
x64-windows-static-md/share/
x64-windows-static-md/share/paho-mqttpp3/
x64-windows-static-md/share/paho-mqttpp3/copyright
x64-windows-static-md/share/paho-mqttpp3/vcpkg.spdx.json
x64-windows-static-md/share/paho-mqttpp3/vcpkg_abi_info.txt
x64-windows-static-md/share/pahomqttcpp/
x64-windows-static-md/share/pahomqttcpp/PahoMqttCppConfig.cmake
x64-windows-static-md/share/pahomqttcpp/PahoMqttCppConfigVersion.cmake
x64-windows-static-md/share/pahomqttcpp/PahoMqttCppTargets-debug.cmake
x64-windows-static-md/share/pahomqttcpp/PahoMqttCppTargets-release.cmake
x64-windows-static-md/share/pahomqttcpp/PahoMqttCppTargets.cmake
