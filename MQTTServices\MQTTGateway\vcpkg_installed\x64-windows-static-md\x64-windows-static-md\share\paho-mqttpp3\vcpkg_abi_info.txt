cmake 3.30.1
features core;ssl
openssl d7791f0fdf61fd6ccba9c7b41fc33760c2590cbe080a3ba2487c8442e2fc3a41
paho-mqtt 737af9ed4088d83ee6d9a3148cf0baac1fc23767537f87bafabdfc560f8c394a
portfile.cmake f9851f193500982410a907b4716b8e27ab10636273cbf7c54d44fca39c5343f1
ports.cmake a9f8f745f9f4d225039f9810e79670f10f38c7455222865616ce430a06f75d1f
post_build_checks 2
powershell 7.2.24
triplet x64-windows-static-md
triplet_abi 340b298dc62b444353afe2799ba0d05407d0d1489d6ef0f3625de5068c01857b-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
vcpkg-cmake 8112fdacac7427b4feea02a5a660bd2fb97e5b898a970d195ff6370d43a794fb
vcpkg-cmake-config 8b1ae8f4be6cff022eadef50d38cffca5e75f23d30fc7e280b912b164b7e8ea1
vcpkg.json e6b92ac64402b63a39ad729659590a3e150a641e5b6926552a3209c53eac12c0
vcpkg_check_features 943b217e0968d64cf2cb9c272608e6a0b497377e792034f819809a79e1502c2b
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
