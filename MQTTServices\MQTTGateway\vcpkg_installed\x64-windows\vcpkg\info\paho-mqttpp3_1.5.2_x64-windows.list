x64-windows/
x64-windows/bin/
x64-windows/bin/paho-mqttpp3.dll
x64-windows/bin/paho-mqttpp3.pdb
x64-windows/debug/
x64-windows/debug/bin/
x64-windows/debug/bin/paho-mqttpp3.dll
x64-windows/debug/bin/paho-mqttpp3.pdb
x64-windows/debug/lib/
x64-windows/debug/lib/paho-mqttpp3.lib
x64-windows/include/
x64-windows/include/mqtt/
x64-windows/include/mqtt/async_client.h
x64-windows/include/mqtt/buffer_ref.h
x64-windows/include/mqtt/buffer_view.h
x64-windows/include/mqtt/callback.h
x64-windows/include/mqtt/client.h
x64-windows/include/mqtt/connect_options.h
x64-windows/include/mqtt/create_options.h
x64-windows/include/mqtt/delivery_token.h
x64-windows/include/mqtt/disconnect_options.h
x64-windows/include/mqtt/event.h
x64-windows/include/mqtt/exception.h
x64-windows/include/mqtt/export.h
x64-windows/include/mqtt/iaction_listener.h
x64-windows/include/mqtt/iasync_client.h
x64-windows/include/mqtt/iclient_persistence.h
x64-windows/include/mqtt/message.h
x64-windows/include/mqtt/platform.h
x64-windows/include/mqtt/properties.h
x64-windows/include/mqtt/reason_code.h
x64-windows/include/mqtt/response_options.h
x64-windows/include/mqtt/server_response.h
x64-windows/include/mqtt/ssl_options.h
x64-windows/include/mqtt/string_collection.h
x64-windows/include/mqtt/subscribe_options.h
x64-windows/include/mqtt/thread_queue.h
x64-windows/include/mqtt/token.h
x64-windows/include/mqtt/topic.h
x64-windows/include/mqtt/topic_matcher.h
x64-windows/include/mqtt/types.h
x64-windows/include/mqtt/will_options.h
x64-windows/lib/
x64-windows/lib/paho-mqttpp3.lib
x64-windows/share/
x64-windows/share/paho-mqttpp3/
x64-windows/share/paho-mqttpp3/copyright
x64-windows/share/paho-mqttpp3/vcpkg.spdx.json
x64-windows/share/paho-mqttpp3/vcpkg_abi_info.txt
x64-windows/share/pahomqttcpp/
x64-windows/share/pahomqttcpp/PahoMqttCppConfig.cmake
x64-windows/share/pahomqttcpp/PahoMqttCppConfigVersion.cmake
x64-windows/share/pahomqttcpp/PahoMqttCppTargets-debug.cmake
x64-windows/share/pahomqttcpp/PahoMqttCppTargets-release.cmake
x64-windows/share/pahomqttcpp/PahoMqttCppTargets.cmake
