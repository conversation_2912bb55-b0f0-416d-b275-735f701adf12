#pragma once

// 版本信息
#define MQTT_GATEWAY_VERSION_MAJOR @PROJECT_VERSION_MAJOR@
#define MQTT_GATEWAY_VERSION_MINOR @PROJECT_VERSION_MINOR@
#define MQTT_GATEWAY_VERSION_PATCH @PROJECT_VERSION_PATCH@

#define MQTT_GATEWAY_VERSION_STRING "@PROJECT_VERSION@"
#define MQTT_GATEWAY_BUILD_DATE __DATE__
#define MQTT_GATEWAY_BUILD_TIME __TIME__

// 产品信息
#define MQTT_GATEWAY_PRODUCT_NAME "FStation MQTT Gateway"
#define MQTT_GATEWAY_COMPANY_NAME "FStation Team"
#define MQTT_GATEWAY_COPYRIGHT "Copyright (C) 2024 FStation Team"
#define MQTT_GATEWAY_DESCRIPTION "MQTT网关程序用于FStation与SCADA平台通信"

// 构建信息
#ifdef _DEBUG
#define MQTT_GATEWAY_BUILD_TYPE "Debug"
#else
#define MQTT_GATEWAY_BUILD_TYPE "Release"
#endif

#ifdef _WIN64
#define MQTT_GATEWAY_PLATFORM "x64"
#else
#define MQTT_GATEWAY_PLATFORM "x86"
#endif 