﻿// DialogRobot.cpp : 实现文件
//

#include "stdafx.h"
#include "FStation.h"
#include "DialogRobot.h"
#include "afxdialogex.h"

#include "LogicMgr.h"

#include "Sys.h"
#include "Pro.h"

// CDialogRobot 对话框

UINT ReloadModel(LPVOID lp)
{
	DWORD nTick = GetTickCount();
	// 重新加载模板
	switch (VAR_ROBOT_I("重新加载模板"))
	{
	case 1:
		for (int i=0; i<4; i++)
		{
			delete g_pImageFlowUp[i];

			CString strPath = CString(GetModulePath().c_str());
			CString strProPath;
			strProPath.Format("%s\\Pro\\%s",  strPath.GetBuffer(), CSys::m_strPro.c_str());

			g_pImageFlowUp[i] = CImageFlow::CreateInstance("上相机图像处理流程", strProPath, g_pCamera, "Up");
		}
		break;
	case 2:
		for (int i=0; i<4; i++)
		{
			delete g_pImageFlowMark[i];

			CString strPath = CString(GetModulePath().c_str());
			CString strProPath;
			strProPath.Format("%s\\Pro\\%s",  strPath.GetBuffer(), CSys::m_strPro.c_str());

			g_pImageFlowMark[i] = CImageFlow::CreateInstance("上相机Mark图像处理流程", strProPath, g_pCamera, "Up");
		}
		break;
	case 3:
		for (int i=0; i<4; i++)
		{
			delete g_pImageFlowDn[i];

			CString strPath = CString(GetModulePath().c_str());
			CString strProPath;
			strProPath.Format("%s\\Pro\\%s",  strPath.GetBuffer(), CSys::m_strPro.c_str());

			g_pImageFlowDn[i] = CImageFlow::CreateInstance("下相机图像处理流程", strProPath, g_pCamera, "Dn");
		}
		break;
	case 4:
		for (int i=0; i<4; i++)
		{
			delete g_pImageFlowScanCodeMainBoard[i];

			CString strPath = CString(GetModulePath().c_str());
			CString strProPath;
			strProPath.Format("%s\\Pro\\%s",  strPath.GetBuffer(), CSys::m_strPro.c_str());

			g_pImageFlowScanCodeMainBoard[i] = CImageFlow::CreateInstance("主板二维码扫描图像处理流程", strProPath, g_pCamera, "Up");
		}
		break;
	case 5:
		for (int i=0; i<4; i++)
		{
			delete g_pImageFlowUp[i];
			delete g_pImageFlowDn[i];
			delete g_pImageFlowMark[i];
			delete g_pImageFlowScanCodeMainBoard[i];

			CString strPath = CString(GetModulePath().c_str());
			CString strProPath;
			strProPath.Format("%s\\Pro\\%s",  strPath.GetBuffer(), CSys::m_strPro.c_str());

			g_pImageFlowUp[i] = CImageFlow::CreateInstance("上相机图像处理流程", strProPath, g_pCamera, "Up");
			g_pImageFlowDn[i] = CImageFlow::CreateInstance("下相机图像处理流程", strProPath, g_pCamera, "Dn");
			g_pImageFlowMark[i] = CImageFlow::CreateInstance("上相机Mark图像处理流程", strProPath, g_pCamera, "Up");
			g_pImageFlowScanCodeMainBoard[i] = CImageFlow::CreateInstance("主板二维码扫描图像处理流程", strProPath, g_pCamera, "Up");
		}
		break;
	default:
		break;
	}

	CString sRet;
	sRet.Format("重载模板[%d]耗时: %d\n", VAR_ROBOT_I("重新加载模板"), GetTickCount() - nTick);

//	REPORT(sRet, emLogLevelNormal);

	VAR_ROBOT("重新加载模板") = 0;
	
	return 0;
}

IMPLEMENT_DYNAMIC(CDialogRobot, CDialogEx)

CDialogRobot::CDialogRobot(CPoint pt, CWnd* pParent /*=NULL*/)
	: CDialogEx(CDialogRobot::IDD, pParent)
{
	m_pt = pt;
}

CDialogRobot::~CDialogRobot()
{
}

void CDialogRobot::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_BUTTON1, m_btnReset);
	DDX_Control(pDX, IDC_BUTTON2, m_btnStart);
	DDX_Control(pDX, IDC_BUTTON3, m_btnPause);
	DDX_Control(pDX, IDC_BUTTON4, m_btnStop);
	DDX_Control(pDX, IDC_SLIDER1, m_slider1);
}

BEGIN_MESSAGE_MAP(CDialogRobot, CDialogEx)
	ON_WM_HSCROLL()
	ON_WM_TIMER()
	ON_BN_CLICKED(IDC_BUTTON1, &CDialogRobot::OnBnClickedButton1)
	ON_BN_CLICKED(IDC_BUTTON2, &CDialogRobot::OnBnClickedButton2)
	ON_BN_CLICKED(IDC_BUTTON3, &CDialogRobot::OnBnClickedButton3)
	ON_BN_CLICKED(IDC_BUTTON4, &CDialogRobot::OnBnClickedButton4)
	ON_BN_CLICKED(IDC_BUTTON5, &CDialogRobot::OnBnClickedButton5)
	ON_BN_CLICKED(IDC_BUTTON6, &CDialogRobot::OnBnClickedButton6)
	ON_BN_CLICKED(IDC_BUTTON7, &CDialogRobot::OnBnClickedButton7)
	ON_BN_CLICKED(IDC_BUTTON8, &CDialogRobot::OnBnClickedButton8)
	ON_BN_CLICKED(IDC_BUTTON9, &CDialogRobot::OnBnClickedButton9)
	ON_BN_CLICKED(IDC_BUTTON10, &CDialogRobot::OnBnClickedButton10)
	ON_BN_CLICKED(IDC_BUTTON11, &CDialogRobot::OnBnClickedButton11)
	ON_BN_CLICKED(IDC_BUTTON12, &CDialogRobot::OnBnClickedButton12)
	ON_BN_CLICKED(IDC_BUTTON13, &CDialogRobot::OnBnClickedButton13)
	ON_BN_CLICKED(IDC_BUTTON14, &CDialogRobot::OnBnClickedButton14)
	ON_BN_CLICKED(IDC_BUTTON15, &CDialogRobot::OnBnClickedButton15)
	ON_BN_CLICKED(IDC_BUTTON16, &CDialogRobot::OnBnClickedButton16)
	ON_BN_CLICKED(IDC_BUTTON17, &CDialogRobot::OnBnClickedButton17)
	ON_BN_CLICKED(IDC_BUTTON18, &CDialogRobot::OnBnClickedButton18)
	ON_BN_CLICKED(IDC_BUTTON19, &CDialogRobot::OnBnClickedButton19)
	ON_BN_CLICKED(IDC_BUTTON20, &CDialogRobot::OnBnClickedButton20)
	ON_BN_CLICKED(IDC_BUTTON21, &CDialogRobot::OnBnClickedButton21)
	ON_BN_CLICKED(IDC_BUTTON22, &CDialogRobot::OnBnClickedButton22)
	ON_BN_CLICKED(IDC_BUTTON23, &CDialogRobot::OnBnClickedButton23)
	ON_BN_CLICKED(IDC_BUTTON24, &CDialogRobot::OnBnClickedButton24)
	ON_EN_CHANGE(IDC_EDIT1, &CDialogRobot::OnEnChangeEdit1)
END_MESSAGE_MAP()

BOOL CDialogRobot::PreTranslateMessage( MSG* pMsg )
{
	if ((WM_SYSKEYDOWN == pMsg->message && VK_F4 == pMsg->wParam) || (pMsg->message == WM_KEYDOWN && pMsg->wParam == VK_ESCAPE)) { //屏蔽Alt + F4
		return TRUE;
	}

	return CDialogEx::PreTranslateMessage(pMsg);
}

BOOL CDialogRobot::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	CRect rc;

	GetWindowRect(&rc);

	MoveWindow(m_pt.x, m_pt.y, rc.Width(), rc.Height());

	CString strPath = CString(GetModulePath().c_str());
	strPath += "\\Sys\\images\\";

	CString strName[] = { 
		"resetbk.bmp", 
		"start.bmp", 
		"pause.bmp", 
		"stop.bmp"
	};

	CColorButton *pBtn[] = { 
		&m_btnReset, 
		&m_btnStart, 
		&m_btnPause, 
		&m_btnStop
	};

	for (int i=0; i<4; i++)
	{
		pBtn[i]->SetColor(RGB(240, 240, 240), RGB(0, 0, 0));

		pBtn[i]->SetImagePos(5, 0);
		pBtn[i]->SetIcon(strPath + strName[i], strPath + strName[i], false);

		pBtn[i]->SetFontSize(20, true);
		pBtn[i]->SetTextPos(68, 0);

	}

	m_slider1.SetRange(1, 100);
	m_slider1.SetTicFreq(1);
	m_slider1.SetPos(VAR_ROBOT_I("机械手速度百分比"));

	SetDlgItemInt(IDC_EDIT1, m_slider1.GetPos());

	SetTimer(99, 100, NULL);

	return TRUE;
}

void CDialogRobot::OnTimer(UINT_PTR nIDEvent)
{
	if (nIDEvent == 99) {
		UpdateButtonState();
	}

	if (nIDEvent == 1) {
		KillTimer(nIDEvent);
		g_pRobot->Save();
	}

	CDialogEx::OnTimer(nIDEvent);
}

void CDialogRobot::OnHScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar)
{
	UpdateData();

	SetDlgItemInt(IDC_EDIT1, m_slider1.GetPos());

	VAR_ROBOT("机械手速度百分比") = m_slider1.GetPos();

	SetTimer(1, 100, NULL);

	CDialogEx::OnHScroll(nSBCode, nPos, pScrollBar);
}

// CDialogRobot 消息处理程序

void CDialogRobot::OnEnChangeEdit1()
{
	UpdateData();

	int nVal = 0;

	nVal = GetDlgItemInt(IDC_EDIT1);

	if (nVal < 1) {
		nVal = 1;
		SetDlgItemInt(IDC_EDIT1, 1);
	}

	if (nVal > 100) {
		nVal = 100;
		SetDlgItemInt(IDC_EDIT1, 100);
	}

	m_slider1.SetPos(nVal);

	SetTimer(1, 100, NULL);

	VAR_ROBOT("机械手速度百分比") = nVal;
}

void CDialogRobot::OnBnClickedButton1()
{
	CLogicMgr::m_mapThread["Robot"].pThread->Stop();

	for (int i=0; i<4; i++)
	{
		g_pRobot->PickCylinderOff(i);
	}

	AfxMessageBox("复位完成");
}

void CDialogRobot::OnBnClickedButton2()
{
	CLogicMgr::m_mapThread["Robot"].pThread->Start();
}

void CDialogRobot::OnBnClickedButton3()
{
	CLogicMgr::m_mapThread["Robot"].pThread->Pause();
}

void CDialogRobot::OnBnClickedButton4()
{
	CLogicMgr::m_mapThread["Robot"].pThread->Stop();
}

void CDialogRobot::OnBnClickedButton5()
{
	int nID = IDC_BUTTON5;

	CString str;
	GetDlgItemText(nID, str);

	if (str.Find("开") >= 0) {
		VAR_ROBOT("纯收板模式") = true;
		SetDlgItemText(nID, "纯收板模式-关");
	}
	else {
		VAR_ROBOT("纯收板模式") = false;
		SetDlgItemText(nID, "纯收板模式-开");
	}
}

void CDialogRobot::OnBnClickedButton6()
{
	PREMISSION_CTRL();

	g_pSockRobot->CloseSocket();

	if (CSys::m_bEngineer) {
		g_pSockRobot->InitClient("127.0.0.1", 8000);
	}
	else {
		g_pSockRobot->InitClient("192.168.125.1", 8000);
	}

	Sleep(5000);

	ROBOTPOINT stRobPnt;

	if (g_pRobot->GetPos(stRobPnt) == "OK") {
		AfxMessageBox("机械手连接成功");
	}
	else {
		AfxMessageBox("机械手连接失败，请查看机械手软件处理异常！");
	}
}

void CDialogRobot::OnBnClickedButton7()
{
	PREMISSION_CTRL();

	VAR_ROBOT("TRAY盘进料标志") = true;
}

void CDialogRobot::OnBnClickedButton8()
{
	PREMISSION_CTRL();

	VAR_ROBOT("TRAY盘退料标志") = true;
}

void CDialogRobot::OnBnClickedButton9()
{
	PREMISSION_CTRL();

	VAR_BELTA("机械手装配主板完成标志") = true;
}

void CDialogRobot::OnBnClickedButton10()
{
	PREMISSION_CTRL();

	VAR_BELTB("机械手装配主板完成标志") = true;
}

void CDialogRobot::OnBnClickedButton11()
{
	PREMISSION_CTRL();

	g_pRobot->UpCameraLightOn();

	vector<CImageFlow*> vImageFlow;

	vImageFlow.push_back(g_pImageFlowUp[0]);		

	yzBase::CImageProcessWindow* pImageProcess = yzBase::CImageProcessWindow::CreateInstance(&vImageFlow);
	pImageProcess->Show(CLogicMgr::m_mapThread["Robot"].pThread->GetStatus() == emRun);

	delete pImageProcess;

	g_pRobot->UpCameraLightOff();

	g_pRobot->RegisterCamera();

	VAR_ROBOT("重新加载模板") = 1;

	AfxBeginThread(ReloadModel, 0);
}

void CDialogRobot::OnBnClickedButton12()
{
	PREMISSION_CTRL();

	g_pRobot->UpCameraLightOn();

	vector<CImageFlow*> vImageFlow;

	vImageFlow.push_back(g_pImageFlowMark[0]);

	yzBase::CImageProcessWindow* pImageProcess = yzBase::CImageProcessWindow::CreateInstance(&vImageFlow);
	pImageProcess->Show(CLogicMgr::m_mapThread["Robot"].pThread->GetStatus() == emRun);

	delete pImageProcess;

	g_pRobot->UpCameraLightOff();

	g_pRobot->RegisterCamera();

	VAR_ROBOT("重新加载模板") = 2;

	AfxBeginThread(ReloadModel, 0);
}

void CDialogRobot::OnBnClickedButton13()
{
	PREMISSION_CTRL();

	g_pRobot->DnCameraLightOn();

	vector<CImageFlow*> vImageFlow;

	vImageFlow.push_back(g_pImageFlowDn[0]);

	yzBase::CImageProcessWindow* pImageProcess = yzBase::CImageProcessWindow::CreateInstance(&vImageFlow);
	pImageProcess->Show(CLogicMgr::m_mapThread["Robot"].pThread->GetStatus() == emRun);

	delete pImageProcess;

	g_pRobot->DnCameraLightOff();

	g_pRobot->RegisterCamera();

	VAR_ROBOT("重新加载模板") = 3;

	AfxBeginThread(ReloadModel, 0);
}

void CDialogRobot::OnBnClickedButton14()
{
	PREMISSION_CTRL();

	g_pRobot->TrayCameraLightOn();

	vector<CImageFlow*> vImageFlow;

	vImageFlow.push_back(g_pImageFlowTray);		

	yzBase::CImageProcessWindow* pImageProcess = yzBase::CImageProcessWindow::CreateInstance(&vImageFlow);
	pImageProcess->Show(CLogicMgr::m_mapThread["Robot"].pThread->GetStatus() == emRun);

	delete pImageProcess;

	g_pRobot->TrayCameraLightOff();

	g_pRobot->RegisterCamera();

	VAR_ROBOT("重新加载模板") = 4;
}

void CDialogRobot::OnBnClickedButton15()
{
	PREMISSION_CTRL();

	g_pRobot->RegisterCamera();

	g_pRobot->TrayCameraLightOn();

	g_pRobot->ProcPrepare(0);
	g_pRobot->PushImageFlow(g_pImageFlowTray, NULL, g_pImageWndTray, "TRAY相机标定", "", "", false, false, -1, false, 0);
	g_pRobot->ProcStart(0);

	g_pCamera->Trigger("Tray");

	CString sRet;

	DWORD nTick = 0;
	nTick = GetTickCount();
	while (true)
	{
		if (GetTickCount() - nTick > 3000) {
			AfxMessageBox("图像处理超时");
			return;
		}

		bool bFlag = false;

		g_pImageFlowTray->GetFinishFlag(bFlag);
		if (bFlag) {
			break;
		}
	}

	double nPixelX = 0, nPixelY = 0, nPixelR = 0, nTestPntX = 0, nTestPntY = 0;

	bool bExistFlag = false;

	for (int i=0; i<VAR_ROBOT_I("TRAY盘主板行数"); i++)
	{
		for (int j=0; j<VAR_ROBOT_I("TRAY盘主板列数"); j++)
		{
			sRet = g_pImageFlowTray->GetResult(nPixelX, nPixelY, nPixelR, bExistFlag, 0, i, j, 0);

			if (sRet == "OK") {
				g_pRobot->SaveMainBoardBackPos(i * VAR_ROBOT_I("TRAY盘主板列数") + j, nPixelX, nPixelY, true);
			}
			else {
				g_pRobot->SaveMainBoardBackPos(i * VAR_ROBOT_I("TRAY盘主板列数") + j, 0.0, 0.0, false);
			}
		}
	}

	g_pRobot->TrayCameraLightOff();

	g_pRobot->LoadMainBoardBackPos();

	AfxMessageBox("计算成功");
}

void CDialogRobot::OnBnClickedButton16()
{
	PREMISSION_CTRL();

	g_pRobot->UpCameraLightOn();

	yzBase::CImageProcessWindow* pImageProcess = yzBase::CImageProcessWindow::CreateInstance(g_pImageFlowScanCodeMainBoard[0]);
	pImageProcess->Show();

	delete pImageProcess;

	g_pRobot->UpCameraLightOff();

	g_pRobot->RegisterCamera();

	VAR_ROBOT("重新加载模板") = 4;

	AfxBeginThread(ReloadModel, 0);

// 	int nID = IDC_BUTTON16;
// 
// 	CString str;
// 	GetDlgItemText(nID, str);
// 
// 	if (str.Find("开") >= 0) {
// 		g_pRobot->PickCylinderVacuumOn(0);
// 		SetDlgItemText(nID, "吸嘴1\n真空-关");
// 	}
// 	else {
// 		g_pRobot->PickCylinderBrokenVacuumOn(0);
// 		Sleep(100);
// 		g_pRobot->PickCylinderVacuumOff(0);
// 		SetDlgItemText(nID, "吸嘴1\n真空-开");
// 	}
}

void CDialogRobot::OnBnClickedButton17()
{
	PREMISSION_CTRL();

	g_pRobot->UpCameraLightOn();

	yzBase::CImageProcessWindow* pImageProcess = yzBase::CImageProcessWindow::CreateInstance(g_pImageFlowScanCodeFixture);
	pImageProcess->Show();

	delete pImageProcess;

	g_pRobot->UpCameraLightOff();

	g_pRobot->RegisterCamera();

// 	int nID = IDC_BUTTON17;
// 
// 	CString str;
// 	GetDlgItemText(nID, str);
// 
// 	if (str.Find("开") >= 0) {
// 		g_pRobot->PickCylinderVacuumOn(1);
// 		SetDlgItemText(nID, "吸嘴2\n真空-关");
// 	}
// 	else {
// 		g_pRobot->PickCylinderBrokenVacuumOn(1);
// 		Sleep(100);
// 		g_pRobot->PickCylinderVacuumOff(1);
// 		SetDlgItemText(nID, "吸嘴2\n真空-开");
// 	}
}

void CDialogRobot::OnBnClickedButton18()
{
// 	int nID = IDC_BUTTON18;
// 
// 	CString str;
// 	GetDlgItemText(nID, str);
// 
// 	if (str.Find("开") >= 0) {
// 		g_pRobot->PickCylinderVacuumOn(2);
// 		SetDlgItemText(nID, "吸嘴3\n真空-关");
// 	}
// 	else {
// 		g_pRobot->PickCylinderBrokenVacuumOn(2);
// 		Sleep(100);
// 		g_pRobot->PickCylinderVacuumOff(2);
// 		SetDlgItemText(nID, "吸嘴3\n真空-开");
// 	}
}

void CDialogRobot::OnBnClickedButton19()
{
// 	int nID = IDC_BUTTON19;
// 
// 	CString str;
// 	GetDlgItemText(nID, str);
// 
// 	if (str.Find("开") >= 0) {
// 		g_pRobot->PickCylinderVacuumOn(3);
// 		SetDlgItemText(nID, "吸嘴4\n真空-关");
// 	}
// 	else {
// 		g_pRobot->PickCylinderBrokenVacuumOn(3);
// 		Sleep(100);
// 		g_pRobot->PickCylinderVacuumOff(3);
// 		SetDlgItemText(nID, "吸嘴4\n真空-开");
// 	}
}

void CDialogRobot::OnBnClickedButton20()
{
	PREMISSION_CTRL();

	VAR_ROBOT("A轨自动计算补偿") = true;
}

void CDialogRobot::OnBnClickedButton21()
{
	PREMISSION_CTRL();

	VAR_ROBOT("B轨自动计算补偿") = true;
}

void CDialogRobot::OnBnClickedButton22()
{
	PREMISSION_CTRL();

	CString sFeedA, sFeedB;

	for (int i=0; i<4; i++)
	{
		sFeedA.Format("A轨位置%d贴装补偿X", i + 1);
		sFeedB.Format("B轨位置%d贴装补偿X", i + 1);
		VAR_ROBOT(sFeedB) = VAR_ROBOT_D(sFeedA);

		sFeedA.Format("A轨位置%d贴装补偿Y", i + 1);
		sFeedB.Format("B轨位置%d贴装补偿Y", i + 1);
		VAR_ROBOT(sFeedB) = VAR_ROBOT_D(sFeedA);

		sFeedA.Format("A轨位置%d贴装补偿R", i + 1);
		sFeedB.Format("B轨位置%d贴装补偿R", i + 1);
		VAR_ROBOT(sFeedB) = VAR_ROBOT_D(sFeedA);
	}
}

void CDialogRobot::OnBnClickedButton23()
{
	PREMISSION_CTRL();

	CString sFeedA, sFeedB;

	for (int i=0; i<4; i++)
	{
		sFeedA.Format("A轨位置%d贴装补偿X", i + 1);
		sFeedB.Format("B轨位置%d贴装补偿X", i + 1);
		VAR_ROBOT(sFeedA) = VAR_ROBOT_D(sFeedB);

		sFeedA.Format("A轨位置%d贴装补偿Y", i + 1);
		sFeedB.Format("B轨位置%d贴装补偿Y", i + 1);
		VAR_ROBOT(sFeedA) = VAR_ROBOT_D(sFeedB);

		sFeedA.Format("A轨位置%d贴装补偿R", i + 1);
		sFeedB.Format("B轨位置%d贴装补偿R", i + 1);
		VAR_ROBOT(sFeedA) = VAR_ROBOT_D(sFeedB);
	}
}

void CDialogRobot::OnBnClickedButton24()
{
	PREMISSION_CTRL();

	VAR_ROBOT("重新加载模板") = 5;

	AfxBeginThread(ReloadModel, 0);
}

void CDialogRobot::UpdateButtonState()
{
	CString strPath = CString(GetModulePath().c_str());
	strPath += "\\Sys\\images\\";

	EnumStatus eStat = CLogicMgr::m_mapThread["Robot"].pThread->GetStatus();

	if (eStat == emStop) {
		m_btnReset.SetIcon(strPath + "resetbk.bmp", strPath + "resetbk.bmp");
		m_btnReset.EnableWindow(TRUE);
	}
	else {
		m_btnReset.SetIcon(strPath + "resetbk_d.bmp", strPath + "resetbk_d.bmp");
		m_btnReset.EnableWindow(FALSE);
	}

	if (eStat == emPause || eStat == emStop) {
		m_btnStart.SetIcon(strPath + "start.bmp", strPath + "start.bmp");
		m_btnStart.EnableWindow(TRUE);
	}
	else {
		m_btnStart.SetIcon(strPath + "start_d.bmp", strPath + "start_d.bmp");
		m_btnStart.EnableWindow(FALSE);
	}

	if (eStat == emRun) {
		m_btnPause.SetIcon(strPath + "pause.bmp", strPath + "pause.bmp");
		m_btnPause.EnableWindow(TRUE);
	}
	else {
		m_btnPause.SetIcon(strPath + "pause_d.bmp", strPath + "pause_d.bmp");
		m_btnPause.EnableWindow(FALSE);
	}

	if (eStat == emRun || eStat == emPause) {
		m_btnStop.SetIcon(strPath + "stop.bmp", strPath + "stop.bmp");
		m_btnStop.EnableWindow(TRUE);
	}
	else {
		m_btnStop.SetIcon(strPath + "stop_d.bmp", strPath + "stop_d.bmp");
		m_btnStop.EnableWindow(FALSE);
	}

	if (VAR_ROBOT_B("纯收板模式")) {
		SetDlgItemText(IDC_BUTTON5, "纯收板模式-关");
	}
	else {
		SetDlgItemText(IDC_BUTTON5, "纯收板模式-开");
	}
}
