{"version": "1.0", "description": "MQTT订阅配置 - 基于SCADA v1.13协议标准", "mqtt_subscriptions": [{"name": "property_get_requests", "topic_template": "$oc/devices/{deviceId}/sys/properties/get/+", "qos": 2, "enabled": true, "handler": "HandlePropertyGetRequest", "description": "接收平台属性查询请求"}, {"name": "property_set_requests", "topic_template": "$oc/devices/{deviceId}/sys/properties/set/+", "qos": 2, "enabled": true, "handler": "HandlePropertySetRequest", "description": "接收平台属性设置请求"}, {"name": "command_requests", "topic_template": "$oc/devices/{deviceId}/sys/commands/+", "qos": 2, "enabled": true, "handler": "HandleCommandRequest", "description": "接收平台命令下发请求"}, {"name": "event_responses", "topic_template": "$oc/devices/{deviceId}/sys/events/up/response/+", "qos": 2, "enabled": true, "handler": "HandleEventResponse", "description": "接收平台对设备事件上报的响应"}], "topic_settings": {"device_id_placeholder": "{deviceId}", "request_id_placeholder": "{requestId}", "wildcard_single": "+", "wildcard_multi": "#"}}